{"name": "gin-vue-admin", "version": "2.3.5", "private": true, "scripts": {"serve": "vite --host --mode development", "test": "cross-env NODE_ENV=development NODE_OPTIONS='--max_old_space_size=4096' vite build --mode development", "limit-test": "npm install increase-memory-limit-fixbug cross-env -g --force && npm run fix-memory-limit && node ./limit && npm run test", "build": "NODE_OPTIONS='--max_old_space_size=4096' vite build --mode production", "limit-build": "npm install increase-memory-limit-fixbug cross-env -g --force && npm run fix-memory-limit && node ./limit && npm run build", "preview": "vite preview", "fix-memory-limit": "cross-env LIMIT=4096 increase-memory-limit"}, "dependencies": {"@aws-sdk/client-s3": "3.583.0", "@element-plus/icons": "0.0.11", "agora-rtm-sdk": "^1.4.3-203", "axios": "^0.19.2", "benz-amr-recorder": "^1.1.3", "core-js": "^3.6.5", "dayjs": "^1.11.10", "echarts": "^5.4.2", "element-plus": "^1.1.0-beta.8", "file-saver": "^2.0.5", "highlight.js": "^10.6.0", "less": "^4.1.2", "less-loader": "^10.2.0", "lodash-es": "^4.17.21", "marked": "^2.0.0", "mitt": "^3.0.0", "moment": "^2.29.1", "path": "^0.12.7", "qs": "^6.8.0", "quill": "^1.3.7", "screenfull": "^5.0.2", "script-ext-html-webpack-plugin": "^2.1.4", "script-loader": "^0.7.2", "sortablejs": "^1.15.0", "spark-md5": "^3.0.1", "vue": "~3.2.0", "vue-infinite-scroll": "^2.0.2", "vue-particle-line": "^0.1.4", "vue-router": "^4.0.0-0", "vue-scroller": "^2.2.4", "vue3-video-play": "^1.3.2", "vuedraggable": "^4.1.0", "vuex": "^4.0.0-0", "vuex-persist": "^2.1.0", "xlsx": "^0.17.4"}, "devDependencies": {"@vitejs/plugin-legacy": "^1.4.4", "@vitejs/plugin-vue": "^2.3.3", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/compiler-sfc": "^3.2.26", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.3", "cross-env": "^7.0.3", "dotenv": "^10.0.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^7.0.0", "sass": "^1.26.5", "sass-loader": "^8.0.2", "vite": "^2.8.0", "vite-plugin-importer": "^0.2.5", "vue-loader": "^14.2.4"}, "volta": {"node": "16.20.0", "npm": "8.19.4"}}