user root;
worker_processes auto;

pid /nginx.pid;

events {
        worker_connections 10240;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;
    real_ip_header CF-Connecting-IP;
    server_tokens off;
    client_max_body_size 10m;
    keepalive_timeout 65;
    sendfile on;
    tcp_nodelay on;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:2m;
    gzip_vary on;
    log_format main '$remote_addr - $remote_user [$time_local] "$request" '
                '$status $body_bytes_sent "$http_referer" '
                '"$http_user_agent" "$http_x_forwarded_for"';

    access_log off;
    charset UTF-8;


    server{
        listen 80;
        server_name _;

        index index.html;
        root /var/www/bakbak/dist;
        location / {
            try_files $uri $uri/ @router;
            index index.html;
        }
        location @router {
                rewrite ^.*$ /index.html last;
        }

        location ~ /\. {
            deny all;
        }
    }
}
