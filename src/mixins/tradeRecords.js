import {calcPercent, div2, numberTo3} from "@/utils/number"

export default {
    data() {
        return {
            tradeTypeOptions: [],
        }
    },
    methods: {
        formatTradeTypeName(v) {
            let i = this.tradeTypeOptions.find((item)=>{
                return item.value === v
            })
            return i === undefined ? ["未知", "warning"] : [i.label, i.tagType];
        },
        formatCost(item) {
            let a = 0.0
            a = parseFloat(item.diamonds) + parseFloat(item.deduction)
            return a
        }
    }
}
