import {getUnionsList} from "@/api/unions";

// 工会
export default {
    name: "Unios",
    data() {
        return {
            unionDict: {},
            unionList: [],
        }
    },
    methods: {
        formatUnion(i) {
            if (i > 0) {
                return this.unionDict[i]
            } else {
                return ""
            }
        },
        loadAllUnions(){
            getUnionsList().then(res=>{
                if (res.code === 0) {
                    let aUnions = {}
                    res.data.list.forEach(item=>{
                        aUnions[item.id] = item.name
                    })
                    this.unionDict = aUnions
                    this.unionList = res.data.list
                }
            })
        },
    }
}
