import {calcPercent, div2, numberTo3} from "@/utils/number"

export default {
    methods: {
        formatCRCallOfPer(item) {
            // 人均通话次数 通话次数/通话人数
            return parseFloat(div2(item.call_count, item.call_users).toFixed(0))
        },
        formatCRCallDurationOfPer(item) {
            // 人均通话时长 通话时长/通话人数
            let a = item.call_duration
            let b = item.call_users
            return numberTo3(parseFloat(div2(a, b).toFixed(0)))
        },
        formatCRCallPerRate(item) {
            // 通话人数占比（%） 通话人数/DAU
            return calcPercent(item.call_users, item.dau)[0]
        },
        formatCRInvalidCallRate(item) {
            // 30秒以下通话占比 30秒以下通话次数/通话次数 *100%
            return calcPercent(item.invalid_call_count, item.call_count)[0]
        },
    }
}
