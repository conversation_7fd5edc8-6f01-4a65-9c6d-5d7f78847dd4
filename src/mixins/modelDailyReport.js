import {calcPercent, div2, numberTo3} from "@/utils/number"

export default {
    methods: {
        formatMDRTotalIncome(item) {
            let res = parseFloat(parseFloat(item.total_income).toFixed(2))
            return [numberTo3(res), res]
        },
        formatMDRGrossProfit(item) {
            let res = parseFloat((parseFloat(item.total_income) - parseFloat(item.total_cost)).toFixed(2))
            return [numberTo3(res), res]
        },
        formatMDRProfitMargin(item) {
            // 利润率 毛利/总营收*100%
            let tIncome = 0
            let grossProfit = 0
            // 毛利
            grossProfit = this.formatMDRGrossProfit(item)[1]
            // 总营收
            tIncome = this.formatMDRTotalIncome(item)[1]
            return calcPercent(grossProfit, tIncome)[0]
        },
        formatMDRActiveROIWant(item) {
            // 最新1/(1-(other_cost/total_income))*100%
            let divisor = 0
            if (item.other_cost !== undefined && item.total_income !== undefined && item.total_income !== 0 && item.total_income !== "0") {
                divisor = 1 - (parseFloat(item.other_cost) / parseFloat(item.total_income))
            }
            return calcPercent(1.0, divisor)
        },
        formatMDRActiveROI(item) {
            // 总营收/消耗*100%
            // return calcPercent(item.total_income, item.cost)
            return calcPercent(item.total_income, item.total_cost)
        },
        formatMDRAddROI(item) {
            // 新增收入/消耗*100%
            return calcPercent(item.new_user_income, item.cost)
        },
        formatMDRRetainedROI(item) {
            // 活跃ROI-新增ROI
            let resRate = this.formatMDRActiveROI(item)[1] - this.formatMDRAddROI(item)[1]
            let rateFloatStr = resRate.toFixed(2)
            return `${rateFloatStr}%`
        },
        formatMDRCPI(item) {
            // 消耗/广告注册
            let a = item.cost
            let b = item.install
            return numberTo3(div2(a, b))
        },
        formatMDRInstallPayRate(item) {
            // 新增付费率%  新增付费人数/新增总人数
            return calcPercent(item.paid_new_users, item.install)[0]
        },
        formatMDRARPPU(item) {
            // 新增ARPPU  新增收入/新增付费人数
            let a = item.new_user_income
            let b = item.paid_new_users
            return numberTo3(div2(a, b))
        },
        formatMDRRetainedPayRate(item) {
            // 留存付费率(%)  留存付费人数/留存总人数
            return calcPercent(item.paid_old_users, item.retention_user_active)[0]
        },
        formatMDRRetainedARPPU(item) {
            // 留存ARPPU  留存收入/留存付费人数
            let a = item.old_user_income
            let b = item.paid_old_users
            return numberTo3(div2(a, b))
        },
        formatMDRNoOfAddPayPer(item) {
            // 新增人均付费次数  新增付费次数/新增付费人数
            let a = item.new_user_paid_count
            let b = item.paid_new_users
            return numberTo3(div2(a, b))
        },
        formatMDRNoOfRetainedPayPer(item) {
            // 留存人均付费次数  留存付费次数/留存付费人数
            let a = item.old_user_paid_count
            let b = item.paid_old_users
            return numberTo3(div2(a, b))
        },
        formatMDRCallAnswerRatio(item) {
            // 总接听率(%)  user_call_answer_count/user_call_count
            return calcPercent(item.user_call_answer_count, item.user_call_count)[0]
        },
        formatMDRNewCustomerConversionRatio(item) {
            // 新客转化率(%)   new_customer_paid_users/new_customer
            return calcPercent(item.new_customer_paid_users, item.new_customer)[0]
        },
        formatMDROldCustomerPaidRatio(item) {
            // 老客复购率(%)   old_customer_paid_users/old_customer
            return calcPercent(item.old_customer_paid_users, item.old_customer)[0]
        },
        formatMDROtherCostRatio(item) {
            // 其他成本占比  (other_cost/total_income)
            return calcPercent(item.other_cost, item.total_income)[0]
        },
        formatMDRInvalidCallRatio(item) {
            // invalid_call_count / call_count
            return calcPercent(item.invalid_call_count, item.call_count)[0]
        },
        formatMDRDayOneRate(item) {
            return calcPercent(item.day_one_retained, item.install)[0]
        },
        formatMDRDaySevenRate(item) {
            return calcPercent(item.day_seven_retained, item.install)[0]
        },
        formatMDRNewPaidDayOneRate(item) {
            return calcPercent(item.new_paid_day_one_retained, item.ltv_paid_users)[0]
        },
        formatMDRNewPaidDaySevenRate(item) {
            return calcPercent(item.new_paid_day_seven_retained, item.ltv_paid_users)[0]
        },
        formatMDRSOfCallPer(item) {
            // 人均通话时长（s）  通话时长/通话人数
            let a = item.call_duration
            let b = item.call_user_count
            return numberTo3(div2(a, b))
        },
        formatMDRCountOfCallPer(item) {
            // 人均通话次数  通话次数/通话人数
            let a = item.call_count
            let b = item.call_user_count
            return numberTo3(div2(a, b))
        },
    }
}
