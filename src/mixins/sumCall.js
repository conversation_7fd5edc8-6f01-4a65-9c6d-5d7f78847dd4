import {calcPercent, div2, numberTo3} from "@/utils/number"

export default {
    methods: {
        formatSCTotalAnsweringRate(item) {
            // 主播接听数/主播被呼叫数
            let answer_count = parseFloat(item.pay_call_answer_count) + parseFloat(item.free_call_answer_count)
            let call_count = parseFloat(item.pay_call_count) + parseFloat(item.free_call_count)
            return calcPercent(answer_count, call_count)[0]
        },
        formatSCTotalAnsweringCount(item) {
            // 总通话次数  总通话次数应该是invalid_call_count+valid_calls_count
            let res = item.valid_calls_count + item.invalid_call_count
            return [numberTo3(res), res]
        },
        formatSCInvalidCallRate(item) {
            // 30秒以下通话占比(%)  30秒以下通话数/总通话数
            let call_count = this.formatSCTotalAnsweringCount(item)[1]
            return calcPercent(item.invalid_call_count, call_count)[0]
        },
        formatSCNumberOfCallsPer(item) {
            // 人均通话次数  总通话次数/通话主播数
            let call_count = this.formatSCTotalAnsweringCount(item)[1]
            return numberTo3(div2(call_count, item.call_anchor_count))
        },
        formatSCMinOfCallsPer(item) {
            // 人均通话时长(min)  总通话时长/通话主播数
            let totalAnsweringS = 0
            let sOfPer = 0.0
            let mOfPer = 0.0
            totalAnsweringS = item.pay_call_duration + item.free_call_duration
            if (item.call_anchor_count !== 0 && item.call_anchor_count !== undefined) {
                sOfPer = parseFloat((totalAnsweringS / item.call_anchor_count).toFixed(2))
            }
            mOfPer = parseInt(sOfPer / 60)
            return `${mOfPer}min`
        },
        formatSCSOfPayCallsPer(item) {
            // 付费次均通话时长(s)  付费通话时长/付费通话次数
            // pay_call_duration / pay_call_answer_count
            let sOfPer = 0.0
            if (item.pay_call_count !== 0 && item.pay_call_count !== undefined) {
                sOfPer = parseFloat((item.pay_call_duration / item.pay_call_count).toFixed(2))
            }
            return `${sOfPer}s`
        },
        formatSCSOfFreeCallsPer(item) {
            // 免费通话次均时长(s)   免费通话时长/免费通话次数
            // free_call_duration / free_call_answer_count
            let sOfPer = 0.0
            if (item.free_call_answer_count !== 0 && item.free_call_answer_count !== undefined) {
                sOfPer = parseFloat((item.free_call_duration / item.free_call_answer_count).toFixed(2))
            }
            return `${sOfPer}s`
        },

        formatSCNumberOfAnswerPer(item) {
            // 人均接听次数   总通话次数/通话主播数
            let sOfPer = 0.0
            if (item.call_anchor_count !== 0 && item.call_anchor_count !== undefined) {
                sOfPer = parseFloat((this.formatSCTotalAnsweringCount(item)[1] / item.call_anchor_count).toFixed(2))
            }
            return sOfPer
        },
        formatSCNumberOfFreeAnswerPer(item) {
            // 免费人均接听次数  免费通话次数/免费通话主播数
            // free_call_answer_count / free_call_people_count
            let sOfPer = 0.0
            if (item.free_call_people_count !== 0 && item.free_call_people_count !== undefined) {
                sOfPer = parseFloat((item.free_call_answer_count / item.free_call_people_count).toFixed(2))
            }
            return sOfPer
        },
        formatSCNumberOfPayAnswerPer(item) {
            // 付费人均接听次数  付费通话次数/付费通话主播数
            // pay_call_answer_count  / pay_call_people_count
            let sOfPer = 0.0
            if (item.pay_call_people_count !== 0 && item.pay_call_people_count !== undefined) {
                sOfPer = parseFloat((item.pay_call_answer_count / item.pay_call_people_count).toFixed(2))
            }
            return sOfPer
        },














    }
}
