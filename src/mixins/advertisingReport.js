import {calcPercent, div2} from "@/utils/number"

export default {
    methods: {
        formateARNewInstallROI(item) {
            return calcPercent(item.new_user_income, item.advertising_cost)
        },
        formateARActiveROIWant(item) {
            // 1/(1-other_cost/income) * 100%
            let otherDivIncome = 0
            if (item.income !== undefined && parseFloat(item.income) !== 0) {
                otherDivIncome = parseFloat(item.other_cost) / parseFloat(item.income)
            }
            return calcPercent(1.0, 1.0 - otherDivIncome)
        },
        formateARActiveROI(item) {
            // return calcPercent(item.income, item.advertising_cost)
            return calcPercent(item.income, (parseFloat(item.advertising_cost) + parseFloat(item.other_cost)))
        },
        formateARCPI(item) {
            return div2(item.advertising_cost, item.install)
        },
    }
}
