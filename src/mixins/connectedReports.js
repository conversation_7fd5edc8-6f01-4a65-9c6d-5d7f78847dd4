import {calcPercent, div2, numberTo3} from "@/utils/number"

export default {
    methods: {
        formateCoRCallType(v) {
            let s = ""
            switch (v) {
                case 1:
                    s = "用户主叫"
                    break
                case 2:
                    s = "主播主叫"
                    break
                default:
                    s = "未知"
            }
            return s
        },
        formateIsTransfer(v) {
            let s = ""
            switch (v) {
                case 1:
                    s = "转接"
                    break
                case 0:
                    s = "直呼"
                    break
                default:
                    s = "未知"
            }
            return s
        },
        formateCoRpartner(v) {
            let s = ""
            switch (v) {
                case 1:
                    s = "是"
                    break
                case 0:
                    s = "否"
                    break
                default:
                    s = "未知"
            }
            return s
        },
        formateCoRstate(v) {
            let s = ""
            switch (v) {
                case 1:
                    s = "通话生成"
                    break
                case 2:
                    s = "通话进行"
                    break
                case 3:
                    s = "拒绝接听"
                    break
                case 4:
                    s = "通话结束"
                    break
                default:
                    s = "未知"
            }
            return s
        },
        formateCoRDstWorkingCondition(v) {
            let s = ""
            switch (v) {
                case 1:
                    s = "兼职"
                    break
                case 2:
                    s = "全职"
                    break
                default:
                    s = "未知"
            }
            return s
        },


    }
}
