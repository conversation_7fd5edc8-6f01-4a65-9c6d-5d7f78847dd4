import {calcPercent, div2, numberTo3} from "@/utils/number"

export default {
    methods: {
        formateRRTotalIncome(item) {
            let totalIncome = parseFloat(item.new_user_income) + parseFloat(item.old_user_income)
            return numberTo3(totalIncome.toFixed(2))
        },
        formateRRAllPayRate(item) {
            // 总付费率(%)  (留存付费用户数+新增付费用户数)/DAU
            let a = parseFloat(item.new_paid_users + item.old_paid_users)
            let b = parseFloat(item.dau)
            return calcPercent(a, b)
        },

        formateRRAllARPPU(item) {
            // 总ARPPU   总付费金额/总付费人数
            let a = parseFloat(item.new_user_income) + parseFloat(item.old_user_income)
            let b = parseFloat(item.new_paid_users + item.old_paid_users)
            return div2(a, b)
        },
        formateRRPayRateARPPU(item) {
            let res = this.formateRRAllPayRate(item)[1] * this.formateRRAllARPPU(item)
            return res.toFixed(2)
        },
        formateRRNewPayRate(item) {
            // 新增付费率(%)  新增付费人数/新增
            let a = parseFloat(item.new_paid_users)
            let b = parseFloat(item.install)
            return calcPercent(a, b)
        },
        formateRRNewARPPU(item) {
            // 新增ARPPU   新增付费金额/新增付费人数
            let a = parseFloat(item.new_user_income)
            let b = parseFloat(item.new_paid_users)
            return div2(a, b)
        },
        formateRRNewPayRateNewARPPU(item) {
            // 新增付费率*新增ARPPU
            let a = this.formateRRNewPayRate(item)[1]
            let b = this.formateRRNewARPPU(item)
            return parseFloat(parseFloat(a * b).toFixed(2))
        },
        formateRRNewPayOfPer(item) {
            // 新增人均付费次数   新增付费次数/新增付费人数
            let a = parseFloat(item.new_user_paid_count)
            let b = parseFloat(item.new_paid_users)
            return div2(a, b)
        },
        formateRROldPayRate(item) {
            // 留存付费率(%)  留存付费人数/留存
            let a = parseFloat(item.old_paid_users)
            let b = parseFloat(item.retention_user)
            return calcPercent(a, b)
        },
        formateRROldARPPU(item) {
            // 留存ARPPU  留存付费金额/留存付费人数
            let a = parseFloat(item.old_user_income)
            let b = parseFloat(item.old_paid_users)
            return div2(a, b)
        },
        formateRROldPayRateOldARPPU(item) {
            // 留存付费率*留存ARPPU
            let a = this.formateRROldPayRate(item)[1]
            let b = this.formateRROldARPPU(item)
            return parseFloat(parseFloat(a * b).toFixed(2))
        },
        formateRROldPayOfPer(item) {
            // 留存人均付费次数   留存付费次数/留存付费人数
            let a = parseFloat(item.old_user_paid_count)
            let b = parseFloat(item.old_paid_users)
            return div2(a, b)
        },
        formateRRNewCustomerPayRate(item) {
            // 新客转化率(%)  今日付费新客/今日新客数*100%
            let a = parseFloat(item.new_customer_paid_users)
            let b = parseFloat(item.new_customer)
            return calcPercent(a, b)
        },
        formateRRNewCustomerARPPU(item) {
            // 新客ARPPU  新客付费金额/新客付费人数
            let a = parseFloat(item.new_customer_income)
            let b = parseFloat(item.new_customer_paid_users)
            return div2(a, b)
        },
        formateRRNewCustomerPayRateNewCustomerARPPU(item) {
            // 新客转化率*新客ARPPU
            let a = this.formateRRNewCustomerPayRate(item)[1]
            let b = this.formateRRNewCustomerARPPU(item)
            return parseFloat(parseFloat(a * b).toFixed(2))
        },
        formateRROldCustomerPayRate(item) {
            // 老客复购率(%)  今日付费老客/今日老客数*100%
            let a = parseFloat(item.old_customer_paid_users)
            let b = parseFloat(item.old_customer)
            return calcPercent(a, b)
        },
        formateRROldCustomerARPPU(item) {
            // 老客ARPPU 老客付费金额/老客付费人数
            let a = parseFloat(item.old_customer_income)
            let b = parseFloat(item.old_customer_paid_users)
            return div2(a, b)
        },
        formateRROldCustomerPayRateOldCustomerARPPU(item) {
            // 老客复购率*老客ARPPU
            let a = this.formateRROldCustomerPayRate(item)[1]
            let b = this.formateRROldCustomerARPPU(item)
            return parseFloat(parseFloat(a * b).toFixed(2))
        },
        formateRROldCustomerPayOfPer(item) {
            // 老客人均付费次数   老客付费次数/老客付费人数
            let a = parseFloat(item.old_customer_paid_count)
            let b = parseFloat(item.old_customer_paid_users)
            return div2(a, b)
        },

    }
}
