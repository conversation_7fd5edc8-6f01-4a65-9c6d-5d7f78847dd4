import {calcPercent, div2, numberTo3} from "@/utils/number"

export default {
    data() {
        return {
            adsGroupOptions: [
                {value: 'date', label: '日期'},
                {value: 'app_id', label: 'app名称'},
                {value: 'country_code', label: '投放国家'},
                {value: 'language', label: '投放语种'},
                {value: 'optimizer', label: '优化师'},
                {value: 'channel', label: '投放平台'},
                {value: 'campaign_name', label: '广告系列名'},
            ],
            designerOptions: [
                {value: 'shy', label: '孙慧颖'},
                {value: 'wzj', label: '王子剑'},
            ],
        }
    },
    methods: {
        formateAdsRCPI(item) {
            // CPI=消耗/安装
            return numberTo3(div2(item.cost, item.install))
        },
        formateAdsRCTR(item) {
            // CTR=点击/展示
            return calcPercent(item.click, item.impression)[0]
        },
        formateAdsRCVR(item) {
            // CVR=安装/点击
            return calcPercent(item.install, item.click)[0]
        },
        formateAdsRIR(item) {
            // IR=安装/展示
            return calcPercent(item.install, item.impression)[0]
        },
        formateAdsRCPC(item) {
            // CPC=消耗/点击
            return numberTo3(div2(item.cost, item.click))
        },
        formateAdsRROI(item) {
            // ROI=支付金额/消耗
            return calcPercent(item.revenue, item.cost)[0]
        },
        formateAdsRpay_rate(item) {
            // 付费率=支付人数/安装
            return calcPercent(item.paid_user, item.install)[0]
        },
        formateAdsRARPPU(item) {
            // ARPPU=支付金额/支付人数
            return numberTo3(div2(item.revenue, item.paid_user))
        },
        formateAdsRpay_of_per(item) {
            // 人均付费次数=支付次数/支付人数
            return numberTo3(div2(item.paid_count, item.paid_user))
        },


    }
}
