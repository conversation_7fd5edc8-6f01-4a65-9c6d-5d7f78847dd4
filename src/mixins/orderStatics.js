import {calcPercent, div2, numberTo3} from "@/utils/number"
import {getUsdPrice} from "@/utils/order"

export default {
  name: "orderStaticsMixins",
  data() {
    return {
      formatTotalIncome(v) {
        if (v === null) {
          v = []
        }
        let tPrice = 0
        let paymentPriceDict = {}
        v.forEach((item) => {
          let pPrice = this.formatOrderPrice(parseFloat(item.total_price), item.payment)
          tPrice += pPrice
          paymentPriceDict[item.payment] = pPrice
        })
        tPrice = parseFloat(tPrice.toFixed(2))
        return [tPrice, paymentPriceDict]
      },
      formatTotalNewUserIncome(v) {
        if (v === null) {
          v = []
        }
        let tPrice = 0
        let paymentPriceDict = {}
        v.forEach((item) => {
          let pPrice = this.formatOrderPrice(parseFloat(item.total_price), item.payment)
          tPrice += pPrice
          paymentPriceDict[item.payment] = pPrice
        })
        tPrice = parseFloat(tPrice.toFixed(2))
        return [tPrice, paymentPriceDict]
      },
      formatTotalRefund(v) {
        if (v === null) {
          v = []
        }
        let tPrice = 0
        let paymentPriceDict = {}
        v.forEach((item) => {
          let pPrice = this.formatOrderPrice(parseFloat(item.total_price), item.payment)
          tPrice += pPrice
          paymentPriceDict[item.payment] = pPrice
        })
        tPrice = parseFloat(tPrice.toFixed(2))
        return [tPrice, paymentPriceDict]
      },
      formatTotalPayCount(v) {
        if (v === null) {
          v = []
        }
        let tPrice = 0
        let paymentPriceDict = {}
        v.forEach((item) => {
          tPrice += item.total
          paymentPriceDict[item.payment] = item.total
        })
        return [tPrice, paymentPriceDict]
      },
      formatTotalPayUserCount(v) {
        if (v === null) {
          v = []
        }
        let tPrice = 0
        let paymentPriceDict = {}
        v.forEach((item) => {
          tPrice += item.total
          paymentPriceDict[item.payment] = item.total
        })
        return [tPrice, paymentPriceDict]
      },
      formatTotalNewPayCount(v) {
        if (v === null) {
          v = []
        }
        let tPrice = 0
        let paymentPriceDict = {}
        v.forEach((item) => {
          tPrice += item.total
          paymentPriceDict[item.payment] = item.total
        })
        return [tPrice, paymentPriceDict]
      },
      formatTotalNewPayUserCount(v) {
        if (v === null) {
          v = []
        }
        let tPrice = 0
        let paymentPriceDict = {}
        v.forEach((item) => {
          tPrice += item.total
          paymentPriceDict[item.payment] = item.total
        })
        return [tPrice, paymentPriceDict]
      },
      formatOrderPrice(price, payment) {
        return getUsdPrice(price, payment)
      },
    }
  },
  methods: {}
}
