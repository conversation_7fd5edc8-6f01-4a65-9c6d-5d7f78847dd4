import { getDict } from '@/utils/dictionary'
import { formatTimeToStr } from '@/utils/date'
import moment from 'moment'
import { ElLoading } from 'element-plus'
import { mapGetters } from 'vuex'
import { numberTo3, numberTo3Int } from '@/utils/number'

export default {
  computed: {
    ...mapGetters('user', ['token', 'userInfo', 'appList', 'appIds', 'formatAppName', 'formatPayWayName', 'uniosList', 'uniosDict', 'formatUniosName', 'formatUniosManagerId', 'appDict', 'payWaysList', 'payWaysDict', 'payItemDict', 'tradeTypeList', 'tradeTypeDict', 'formatTradeType']),
  },
  data() {
    return {
      queryTxt: '展开',
      queryTxtIcon: 'el-icon-arrow-down',
      queryTxtState: false,
      page: 1,
      total: 10,
      pageSize: 10,
      pageSizeList: [10, 30, 50, 100, 1000, 5000],
      pageSizeListNormal: [10, 30, 50, 100],
      tableData: [],
      searchInfo: {},
      progressLoading: {},
      dateRangeShortcuts: [
        {
          text: '昨日',
          value: () => {
            const start = moment()
            start.add(-1, 'day')
            const startStr = start.format('YYYY-MM-DD 00:00:00')
            const endStr = start.format('YYYY-MM-DD 23:59:59')
            const newStartDate = moment(startStr)
            const newEndDate = moment(endStr)
            return [newStartDate, newEndDate]
          },
        },
        {
          text: '今日',
          value: () => {
            const start = moment()
            const startStr = start.format('YYYY-MM-DD 00:00:00')
            const endStr = start.format('YYYY-MM-DD 23:59:59')
            const newStartDate = moment(startStr)
            const newEndDate = moment(endStr)
            return [newStartDate, newEndDate]
          },
        },
        {
          text: '上周',
          value: () => {
            const startStr = moment().weekday(-6).format('YYYY-MM-DD 00:00:00')
            const endStr = moment().weekday(-6).add(6, 'd').format('YYYY-MM-DD 23:59:59')
            const newStartDate = moment(startStr)
            const newEndDate = moment(endStr)
            return [newStartDate, newEndDate]
          },
        },
        {
          text: '本周',
          value: () => {
            const startStr = moment().weekday(1).format('YYYY-MM-DD 00:00:00')
            const endStr = moment().weekday(7).format('YYYY-MM-DD 23:59:59')
            const newStartDate = moment(startStr)
            const newEndDate = moment(endStr)
            return [newStartDate, newEndDate]
          },
        },
        {
          text: '上月',
          value: () => {
            const start = moment(moment().format('YYYY-MM-01 00:00:00')).add(-1, 'month')
            const end = moment(moment().format('YYYY-MM-01 00:00:00'))
            return [start, end]
          },
        },
        {
          text: '本月',
          value: () => {
            const start = moment(moment().format('YYYY-MM-01 00:00:00'))
            const end = moment(moment().format('YYYY-MM-01 00:00:00')).add(1, 'month')
            return [start, end]
          },
        },
        {
          text: '过去7天',
          value: () => {
            const end = moment()
            const start = moment().add(-7, 'day')
            console.log(start, end)
            return [start.toDate(), end.toDate()]
          },
        },
      ],
      genderOptions: [
        { value: 1, label: '未知' },
        { value: 2, label: '女' },
        { value: 3, label: '男' },
      ],
      levelsOptions: [
        { value: 1, label: 'L1' },
        { value: 2, label: 'L2' },
        { value: 3, label: 'L3' },
        { value: 4, label: 'L4' },
        { value: 100, label: '100' },
      ],
      intimateLevelsOptions: [
        { value: -1, label: '全部' },
        { value: 0, label: 'L0' },
        { value: 1, label: 'L1' },
        { value: 2, label: 'L2' },
        { value: 3, label: 'L3' },
        { value: 4, label: 'L4' },
      ],
      anchorFakeOptions: [
        { value: 0, label: '真主播' },
        { value: 1, label: '列表假主播' },
        { value: 2, label: '转接假主播' },
        { value: 3, label: '匹配假主播' },
        { value: 4, label: '付费后假主播' },
      ],
      fakeOptions: [
        { value: 0, label: '真主播' },
        { value: 1, label: '非绿色' },
        { value: 2, label: '绿色' },
      ],
      fakeOptionsMap: {
        0: '真主播',
        1: '非绿色',
        2: '绿色',
      },
      authStateOptions: [
        { value: 0, label: '未认证' },
        { value: 1, label: '认证中' },
        { value: 2, label: '已认证' },
        { value: 4, label: '已封禁' },
      ],
      recoveryStateOptions: [
        { value: 0, label: '未封禁' },
        { value: 1, label: '已封禁' },
      ],
      onlineOptions: [
        { value: 1, label: '离线' },
        { value: 2, label: '忙线' },
        { value: 3, label: '在线' },
      ],
      onlineOptionsMap: { 1: '离线', 2: '忙线', 3: '在线' },
      countryCodeOptions: [
        { value: 'AF', label: 'Afghanistan' },
        { value: 'AX', label: 'Åland Islands' },
        { value: 'AL', label: 'Albania' },
        { value: 'DZ', label: 'Algeria' },
        { value: 'AS', label: 'American Samoa' },
        { value: 'AD', label: 'Andorra' },
        { value: 'AO', label: 'Angola' },
        { value: 'AI', label: 'Anguilla' },
        { value: 'AQ', label: 'Antarctica' },
        { value: 'AG', label: 'Antigua and Barbuda' },
        { value: 'AR', label: 'Argentina' },
        { value: 'AM', label: 'Armenia' },
        { value: 'AW', label: 'Aruba' },
        { value: 'AU', label: 'Australia' },
        { value: 'AT', label: 'Austria' },
        { value: 'AZ', label: 'Azerbaijan' },
        { value: 'BS', label: 'Bahamas' },
        { value: 'BH', label: 'Bahrain' },
        { value: 'BD', label: 'Bangladesh' },
        { value: 'BB', label: 'Barbados' },
        { value: 'BY', label: 'Belarus' },
        { value: 'BE', label: 'Belgium' },
        { value: 'BZ', label: 'Belize' },
        { value: 'BJ', label: 'Benin' },
        { value: 'BM', label: 'Bermuda' },
        { value: 'BT', label: 'Bhutan' },
        { value: 'BO', label: 'Bolivia' },
        { value: 'BQ', label: 'Bonaire, Sint Eustatius and Saba' },
        { value: 'BA', label: 'Bosnia and Herzegovina' },
        { value: 'BW', label: 'Botswana' },
        { value: 'BV', label: 'Bouvet Island' },
        { value: 'BR', label: 'Brazil' },
        { value: 'IO', label: 'British Indian Ocean Territory' },
        { value: 'BN', label: 'Brunei Darussalam' },
        { value: 'BG', label: 'Bulgaria' },
        { value: 'BF', label: 'Burkina Faso' },
        { value: 'BI', label: 'Burundi' },
        { value: 'CV', label: 'Cabo Verde' },
        { value: 'KH', label: 'Cambodia' },
        { value: 'CM', label: 'Cameroon' },
        { value: 'CA', label: 'Canada' },
        { value: 'KY', label: 'Cayman Islands' },
        { value: 'CF', label: 'Central African Republic' },
        { value: 'TD', label: 'Chad' },
        { value: 'CL', label: 'Chile' },
        { value: 'CN', label: 'China' },
        { value: 'CX', label: 'Christmas Island' },
        { value: 'CC', label: 'Cocos (Keeling) Islands' },
        { value: 'CO', label: 'Colombia' },
        { value: 'KM', label: 'Comoros' },
        { value: 'CG', label: 'Congo' },
        { value: 'CD', label: 'Congo, Democratic Republic of the' },
        { value: 'CK', label: 'Cook Islands' },
        { value: 'CR', label: 'Costa Rica' },
        { value: 'CI', label: `Côte d'Ivoire` },
        { value: 'HR', label: 'Croatia' },
        { value: 'CU', label: 'Cuba' },
        { value: 'CW', label: 'Curaçao' },
        { value: 'CY', label: 'Cyprus' },
        { value: 'CZ', label: 'Czech Republic' },
        { value: 'DK', label: 'Denmark' },
        { value: 'DJ', label: 'Djibouti' },
        { value: 'DM', label: 'Dominica' },
        { value: 'DO', label: 'Dominican Republic' },
        { value: 'EC', label: 'Ecuador' },
        { value: 'EG', label: 'Egypt' },
        { value: 'SV', label: 'El Salvador' },
        { value: 'GQ', label: 'Equatorial Guinea' },
        { value: 'ER', label: 'Eritrea' },
        { value: 'EE', label: 'Estonia' },
        { value: 'SZ', label: 'Eswatini' },
        { value: 'ET', label: 'Ethiopia' },
        { value: 'FK', label: 'Falkland Islands (Malvinas)' },
        { value: 'FO', label: 'Faroe Islands' },
        { value: 'FJ', label: 'Fiji' },
        { value: 'FI', label: 'Finland' },
        { value: 'FR', label: 'France' },
        { value: 'GF', label: 'French Guiana' },
        { value: 'PF', label: 'French Polynesia' },
        { value: 'TF', label: 'French Southern Territories' },
        { value: 'GA', label: 'Gabon' },
        { value: 'GM', label: 'Gambia' },
        { value: 'GE', label: 'Georgia' },
        { value: 'DE', label: 'Germany' },
        { value: 'GH', label: 'Ghana' },
        { value: 'GI', label: 'Gibraltar' },
        { value: 'GR', label: 'Greece' },
        { value: 'GL', label: 'Greenland' },
        { value: 'GD', label: 'Grenada' },
        { value: 'GP', label: 'Guadeloupe' },
        { value: 'GU', label: 'Guam' },
        { value: 'GT', label: 'Guatemala' },
        { value: 'GG', label: 'Guernsey' },
        { value: 'GN', label: 'Guinea' },
        { value: 'GW', label: 'Guinea-Bissau' },
        { value: 'GY', label: 'Guyana' },
        { value: 'HT', label: 'Haiti' },
        { value: 'HM', label: 'Heard Island and McDonald Islands' },
        { value: 'VA', label: 'Holy See (Vatican City State)' },
        { value: 'HN', label: 'Honduras' },
        { value: 'HK', label: 'Hong Kong' },
        { value: 'HU', label: 'Hungary' },
        { value: 'IS', label: 'Iceland' },
        { value: 'IN', label: 'India' },
        { value: 'ID', label: 'Indonesia' },
        { value: 'IR', label: 'Iran, Islamic Republic of' },
        { value: 'IQ', label: 'Iraq' },
        { value: 'IE', label: 'Ireland' },
        { value: 'IM', label: 'Isle of Man' },
        { value: 'IL', label: 'Israel' },
        { value: 'IT', label: 'Italy' },
        { value: 'JM', label: 'Jamaica' },
        { value: 'JP', label: 'Japan' },
        { value: 'JE', label: 'Jersey' },
        { value: 'JO', label: 'Jordan' },
        { value: 'KZ', label: 'Kazakhstan' },
        { value: 'KE', label: 'Kenya' },
        { value: 'KI', label: 'Kiribati' },
        { value: 'KP', label: "Korea, Democratic People's Republic of" },
        { value: 'KR', label: 'Korea, Republic of' },
        { value: 'KW', label: 'Kuwait' },
        { value: 'KG', label: 'Kyrgyzstan' },
        { value: 'LA', label: "Lao People's Democratic Republic" },
        { value: 'LV', label: 'Latvia' },
        { value: 'LB', label: 'Lebanon' },
        { value: 'LS', label: 'Lesotho' },
        { value: 'LR', label: 'Liberia' },
        { value: 'LY', label: 'Libya' },
        { value: 'LI', label: 'Liechtenstein' },
        { value: 'LT', label: 'Lithuania' },
        { value: 'LU', label: 'Luxembourg' },
        { value: 'MO', label: 'Macao' },
        { value: 'MK', label: 'North Macedonia' },
        { value: 'MG', label: 'Madagascar' },
        { value: 'MW', label: 'Malawi' },
        { value: 'MY', label: 'Malaysia' },
        { value: 'MV', label: 'Maldives' },
        { value: 'ML', label: 'Mali' },
        { value: 'MT', label: 'Malta' },
        { value: 'MH', label: 'Marshall Islands' },
        { value: 'MQ', label: 'Martinique' },
        { value: 'MR', label: 'Mauritania' },
        { value: 'MU', label: 'Mauritius' },
        { value: 'YT', label: 'Mayotte' },
        { value: 'MX', label: 'Mexico' },
        { value: 'FM', label: 'Micronesia, Federated States of' },
        { value: 'MD', label: 'Moldova, Republic of' },
        { value: 'MC', label: 'Monaco' },
        { value: 'MN', label: 'Mongolia' },
        { value: 'ME', label: 'Montenegro' },
        { value: 'MS', label: 'Montserrat' },
        { value: 'MA', label: 'Morocco' },
        { value: 'MZ', label: 'Mozambique' },
        { value: 'MM', label: 'Myanmar' },
        { value: 'NA', label: 'Namibia' },
        { value: 'NR', label: 'Nauru' },
        { value: 'NP', label: 'Nepal' },
        { value: 'NL', label: 'Netherlands' },
        { value: 'NC', label: 'New Caledonia' },
        { value: 'NZ', label: 'New Zealand' },
        { value: 'NI', label: 'Nicaragua' },
        { value: 'NE', label: 'Niger' },
        { value: 'NG', label: 'Nigeria' },
        { value: 'NU', label: 'Niue' },
        { value: 'NF', label: 'Norfolk Island' },
        { value: 'MP', label: 'Northern Mariana Islands' },
        { value: 'NO', label: 'Norway' },
        { value: 'OM', label: 'Oman' },
        { value: 'PK', label: 'Pakistan' },
        { value: 'PW', label: 'Palau' },
        { value: 'PS', label: 'Palestine, State of' },
        { value: 'PA', label: 'Panama' },
        { value: 'PG', label: 'Papua New Guinea' },
        { value: 'PY', label: 'Paraguay' },
        { value: 'PE', label: 'Peru' },
        { value: 'PH', label: 'Philippines' },
        { value: 'PN', label: 'Pitcairn' },
        { value: 'PL', label: 'Poland' },
        { value: 'PT', label: 'Portugal' },
        { value: 'PR', label: 'Puerto Rico' },
        { value: 'QA', label: 'Qatar' },
        { value: 'RE', label: 'Réunion' },
        { value: 'RO', label: 'Romania' },
        { value: 'RU', label: 'Russian Federation' },
        { value: 'RW', label: 'Rwanda' },
        { value: 'BL', label: 'Saint Barthélemy' },
        { value: 'SH', label: 'Saint Helena, Ascension and Tristan da Cunha' },
        { value: 'KN', label: 'Saint Kitts and Nevis' },
        { value: 'LC', label: 'Saint Lucia' },
        { value: 'MF', label: 'Saint Martin (French part)' },
        { value: 'PM', label: 'Saint Pierre and Miquelon' },
        { value: 'VC', label: 'Saint Vincent and the Grenadines' },
        { value: 'WS', label: 'Samoa' },
        { value: 'SM', label: 'San Marino' },
        { value: 'ST', label: 'Sao Tome and Principe' },
        { value: 'SA', label: 'Saudi Arabia' },
        { value: 'SN', label: 'Senegal' },
        { value: 'RS', label: 'Serbia' },
        { value: 'SC', label: 'Seychelles' },
        { value: 'SL', label: 'Sierra Leone' },
        { value: 'SG', label: 'Singapore' },
        { value: 'SX', label: 'Sint Maarten (Dutch part)' },
        { value: 'SK', label: 'Slovakia' },
        { value: 'SI', label: 'Slovenia' },
        { value: 'SB', label: 'Solomon Islands' },
        { value: 'SO', label: 'Somalia' },
        { value: 'ZA', label: 'South Africa' },
        { value: 'GS', label: 'South Georgia and the South Sandwich Islands' },
        { value: 'SS', label: 'South Sudan' },
        { value: 'ES', label: 'Spain' },
        { value: 'LK', label: 'Sri Lanka' },
        { value: 'SD', label: 'Sudan' },
        { value: 'SR', label: 'Suriname' },
        { value: 'SJ', label: 'Svalbard and Jan Mayen' },
        { value: 'SE', label: 'Sweden' },
        { value: 'CH', label: 'Switzerland' },
        { value: 'SY', label: 'Syrian Arab Republic' },
        { value: 'TW', label: 'Taiwan, Province of China' },
        { value: 'TJ', label: 'Tajikistan' },
        { value: 'TZ', label: 'Tanzania, United Republic of' },
        { value: 'TH', label: 'Thailand' },
        { value: 'TL', label: 'Timor-Leste' },
        { value: 'TG', label: 'Togo' },
        { value: 'TK', label: 'Tokelau' },
        { value: 'TO', label: 'Tonga' },
        { value: 'TT', label: 'Trinidad and Tobago' },
        { value: 'TN', label: 'Tunisia' },
        { value: 'TR', label: 'Turkey' },
        { value: 'TM', label: 'Turkmenistan' },
        { value: 'TC', label: 'Turks and Caicos Islands' },
        { value: 'TV', label: 'Tuvalu' },
        { value: 'UG', label: 'Uganda' },
        { value: 'UA', label: 'Ukraine' },
        { value: 'AE', label: 'United Arab Emirates' },
        { value: 'GB', label: 'United Kingdom of Great Britain and Northern Ireland' },
        { value: 'US', label: 'United States of America' },
        { value: 'UM', label: 'United States Minor Outlying Islands' },
        { value: 'UY', label: 'Uruguay' },
        { value: 'UZ', label: 'Uzbekistan' },
        { value: 'VU', label: 'Vanuatu' },
        { value: 'VE', label: 'Venezuela (Bolivarian Republic of)' },
        { value: 'VN', label: 'Viet Nam' },
        { value: 'VG', label: 'Virgin Islands (British)' },
        { value: 'VI', label: 'Virgin Islands (U.S.)' },
        { value: 'WF', label: 'Wallis and Futuna' },
        { value: 'EH', label: 'Western Sahara' },
        { value: 'YE', label: 'Yemen' },
        { value: 'ZM', label: 'Zambia' },
        { value: 'ZW', label: 'Zimbabwe' }
      ],
      anchorLangOptions: [
        { value: 'zh', label: '中文' },
        { value: 'en', label: '英语' },
        { value: 'hi', label: '印度语' },
      ],
      channelOptions: [
        { value: 1, disabled: false, label: '渠道全选' },
        { value: 2, disabled: false, label: 'Organic' },
        { value: 3, disabled: false, label: 'FaceBook' },
        { value: 4, disabled: false, label: 'Google' },
      ],
      connectedReportsStateOptions: [
        { value: null, label: '全部' },
        { value: 1, label: '通话生成' },
        { value: 2, label: '通话进行' },
        { value: 3, label: '拒绝接听' },
        { value: 4, label: '通话结束' },
      ],
      orderStateOptions: [
        { value: 1, label: '交易创建', tagType: 'warning' },
        { value: 2, label: '未支付', tagType: '' },
        { value: 3, label: '交易失败', tagType: 'danger' },
        { value: 4, label: '已完成', tagType: 'success' },
        { value: 5, label: '订单完结', tagType: 'success' },
        { value: 6, label: '失败', tagType: 'info' },
      ],
      productTypeOptions: [
        // 产品类型 1VIP 2钻石 3订阅VIP(续费) 4弹窗VIP 5弹窗钻石 6.新手礼包
        { value: 1, label: 'VIP' },
        { value: 2, label: '钻石' },
        { value: 3, label: '订阅VIP(续费)' },
        { value: 4, label: '弹窗VIP' },
        { value: 5, label: '弹窗钻石' },
        { value: 6, label: '新手礼包' },
      ],
      roleOptions: [
        { value: 1, label: '用户' },
        { value: 2, label: '主播' },
        { value: 3, label: '客服' },
        { value: 4, label: '测试' },
      ],
      durationOptions: [
        { value: 1, label: '10秒以下' },
        { value: 2, label: '10-30' },
        { value: 3, label: '30-60' },
        { value: 4, label: '60-120' },
        { value: 5, label: '120-300' },
        { value: 6, label: '300秒以上' },
      ],
    }
  },
  methods: {
    nFunc() {
    },
    getFileExt(name) {
      let ext = ''
      if (name.lastIndexOf('.') !== -1) {
        ext = name.substr(name.lastIndexOf('.') + 1)
      }
      return ext
    },
    guid() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
        var r = Math.random() * 16 | 0
        var v = c === 'x' ? r : (r & 0x3 | 0x8)
        return v.toString(16)
      })
    },
    uuidFileName(ext) {
      if (ext) {
        return this.guid() + '.' + ext
      } else {
        return this.guid()
      }
    },
    getTableHeaderProp(tableColumns) {
      const tHeader = []
      const filterV = []
      if (tableColumns.length > 0) {
        tableColumns.forEach((item) => {
          tHeader.push(item.label)
          filterV.push(item.property)
        })
      }
      return [tHeader, filterV]
    },
    copyText(data) {
      if (data === '' || data === null || data === undefined) {
        this.$message.error('内容为空,不可复制')
        return
      }
      const oInput = document.createElement('input')
      oInput.value = data
      document.body.appendChild(oInput)
      oInput.select() // 选择对象
      document.execCommand('Copy') // 执行浏览器复制命令
      oInput.style.display = 'none'
      this.$message.success('复制成功')
    },
    queryTxtChange() {
      this.queryTxtState = !this.queryTxtState
      if (this.queryTxtState) {
        this.queryTxt = '收起'
        this.queryTxtIcon = 'el-icon-arrow-up'
      } else {
        this.queryTxt = '展开'
        this.queryTxtIcon = 'el-icon-arrow-down'
      }
    },
    handleProgressLoading() {
      this.progressLoading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
    },
    handleImgExceed() {
      this.$message.error('如果需要更换,请先删除,再上传')
    },
    formatChannel(v) {
      const i = this.channelOptions.find((item) => {
        return item.value === v
      })
      return i === undefined ? ['未知', 'warning'] : [i.label, i.tagType]
    },
    formatOrderState(v) {
      const i = this.orderStateOptions.find((item) => {
        return item.value === v
      })
      return i === undefined ? ['未知', 'warning'] : [i.label, i.tagType]
    },
    formatPayment(v) {
      const i = this.payWaysList.find((item) => {
        return item.value === v
      })
      return i === undefined ? '' : i.label
    },
    formatProductType(v) {
      const i = this.productTypeOptions.find((item) => {
        return item.value === v
      })
      return i === undefined ? '未知产品类型' : i.label
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map((v) =>
        filterVal.map((j) => {
          return v[j]
        })
      )
    },

    isFakeAnchor(userInfo) {
      if (userInfo.fake !== 0) {
        return true
      }
      return userInfo.anchor_fake !== 0
    },
    numF2(v) {
      return parseFloat(parseFloat(v).toFixed(2))
      // return numberTo3(v)
    },
    numberTo3(value) {
      if (!value) return 0
      // 获取整数部分
      const intPart = Math.trunc(value)
      // 整数部分处理，增加,
      const intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
      // 预定义小数部分
      let floatPart = ''
      // 将数值截取为小数部分和整数部分
      const valueArray = value.toString().split('.')
      if (valueArray.length === 2) { // 有小数部分
        floatPart = valueArray[1].toString() // 取得小数部分
        return intPartFormat + '.' + floatPart
      }
      return intPartFormat + floatPart
    },
    formaOfficialNoticesFrequencyPartner: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '首次进入'
            break
          case 2:
            text = '每日一次'
            break
          case 3:
            text = '每次进入app'
            break
        }
        return text
      } else {
        return ''
      }
    },

    formaOfficialNoticesStatusPartner: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '上线'
            break
          case 2:
            text = '下线'
            break
        }
        return text
      } else {
        return ''
      }
    },

    formaDramaTacticSendMomentPartner: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = 'SayHi后'
            break
          case 2:
            text = '喜欢主播后'
            break
          case 3:
            text = '详情页切换主播照片后'
            break
          case 4:
            text = '拨打视频后'
            break
          case 5:
            text = '发送私信后'
            break
          case 6:
            text = '注册成功后'
            break
          case 7:
            text = '第二天访问后'
            break
          case 8:
            text = '第三天访问后'
            break
        }
        return text
      } else {
        return ''
      }
    },

    formaDramaTacticCategoryPartner: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '用户策略信'
            break
          case 2:
            text = '喜欢策略信'
            break
          case 3:
            text = '预览策略信'
            break
          case 4:
            text = '消息策略信'
            break
          case 5:
            text = '主播策略信'
            break
        }
        return text
      } else {
        return ''
      }
    },

    formaDramaTacticStatePartner: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '上线'
            break
          case 2:
            text = '下线'
            break
        }
        return text
      } else {
        return ''
      }
    },

    formaSettlementResultPartner: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = true
            break
          case 2:
            text = false
            break
        }
        return text
      } else {
        return ''
      }
    },
    formaCallStatePartner: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = 'generate'
            break
          case 2:
            text = 'process'
            break
          case 3:
            text = 'reject'
            break
          case 4:
            text = 'succeed'
            break
        }
        return text
      } else {
        return ''
      }
    },
    formaDramaTypeOptionsPartner: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '文本'
            break
          case 2:
            text = '图片'
            break
          case 3:
            text = '视频'
            break
          case 4:
            text = '呼叫'
            break
          case 5:
            text = '问题'
            break
          case 6:
            text = '回答'
            break
        }
        return text
      } else {
        return ''
      }
    },
    formaCallStateUnionPartner: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 4:
            text = 'succeed'
            break
          default:
            text = 'fail'
        }
        return text
      } else {
        return ''
      }
    },
    formaCallFreePartner: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case true:
            text = 'Free'
            break
          case false:
            text = 'Paid'
            break
        }
        return text
      } else {
        return ''
      }
    },
    formaCallTypePartner: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '用户主叫'
            break
          case 2:
            text = '主播主叫'
            break
          default:
            text = '未知'
        }
        return text
      } else {
        return ''
      }
    },
    formaTasksRewardPartner: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '免费通话卡（真）'
            break
          case 2:
            text = '免费通话卡（假）'
            break
          case 3:
            text = '金币'
            break
          case 4:
            text = 'VIP'
            break
          default:
            text = '未知'
        }
        return text
      } else {
        return ''
      }
    },
    formaTasksStatusPartner: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '未上架'
            break
          case 2:
            text = '上架中'
            break
          default:
            text = '未知'
        }
        return text
      } else {
        return ''
      }
    },
    formaTagsCategoryPartner: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '默认值'
            break
          default:
            text = '未知'
        }
        return text
      } else {
        return ''
      }
    },
    formaTasksPartner: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '签到任务'
            break
          case 2:
            text = '任务中心'
            break
          default:
            text = '未知'
        }
        return text
      } else {
        return ''
      }
    },
    formaPartner: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '合作方'
            break
          case 0:
            text = '内部包'
            break
          default:
            text = '未知'
        }
        return text
      } else {
        return ''
      }
    },
    formatCountryCode: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 'IN':
            text = 'India'
            break
          case 'SA':
            text = 'Saudi Arabia'
            break
          case 'PH':
            text = 'Philippines'
            break
          case 'VN':
            text = 'Viet Nam'
            break
          case 'UA':
            text = 'Ukraine'
            break
          case 'UK':
            text = 'United Kingdom'
            break
          case 'TR':
            text = 'Turkey'
            break
          case 'TH':
            text = 'Thailand'
            break
          case 'RU':
            text = 'Russian'
            break
          case 'PK':
            text = 'Pakistan'
            break
          case 'MA':
            text = 'Morocco'
            break
          case 'MX':
            text = 'Mexico'
            break
          case 'KW':
            text = 'Kuwait'
            break
          case 'MY':
            text = 'Malaysia'
            break
          case 'IQ':
            text = 'Iraq'
            break
          case 'ID':
            text = 'Indonesia'
            break
          case 'BR':
            text = 'Brazil'
            break
          case 'CN':
            text = 'China'
            break
          case 'CO':
            text = 'Colombia'
            break
          case 'KR':
            text = 'South Korea'
            break
          case 'US':
            text = 'America'
            break
          default:
            text = bool
        }
        return text
      } else {
        return ''
      }
    },
    formatAnchorFake: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '一类主播'
            break
          case 2:
            text = '二类主播'
            break
          case 3:
            text = '三类主播'
            break
          case 4:
            text = '四类主播'
            break
          default:
            text = '~'
        }
        return text
      } else {
        return ''
      }
    },
    formatLoginType: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '账号密码登陆'
            break
          case 2:
            text = '手机号登录'
            break
          case 3:
            text = '设备号登陆'
            break
          case 4:
            text = '谷歌'
            break
          case 5:
            text = 'FB'
            break
          default:
            text = '~'
        }
        return text
      } else {
        return ''
      }
    },
    formatOnline: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '离线'
            break
          case 2:
            text = '忙线'
            break
          case 3:
            text = '在线'
            break
          default:
            text = '~'
        }
        return text
      } else {
        return ''
      }
    },
    formatOnlineType: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = 'info'
            break
          case 2:
            text = 'warning'
            break
          case 3:
            text = 'success'
            break
          default:
            text = 'info'
        }
        return text
      } else {
        return 'info'
      }
    },
    formatSex: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 2:
            text = '女'
            break
          case 3:
            text = '男'
            break
          default:
            text = '未知'
        }
        return text
      } else {
        return ''
      }
    },
    formatUserLike: function(v) {
      let text
      switch (v) {
        case 1:
          text = '未知'
          break
        case 2:
          text = 'Female'
          break
        case 3:
          text = 'Male'
          break
        case 4:
          text = 'Both'
          break
        default:
          text = '未知'
      }
      return text
    },

    formatInform: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '视频页'
            break
          case 2:
            text = '聊天页'
            break
          default:
            text = '通话页'
        }
        return text
      } else {
        return ''
      }
    },

    formatSettingType: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '星座匹配视频'
            break
          case 2:
            text = '个人资料页视频'
            break
          case 3:
            text = 'discove页面视频'
            break
          case 4:
            text = '假通话视频'
            break
          case 5:
            text = '假通话视频模糊后的声音'
            break
          case 6:
            text = 'discove页面是否显示'
            break
          default:
            text = '未知'
        }
        return text
      } else {
        return ''
      }
    },
    formatState: function(state) {
      if (state !== null) {
        return state === 1 ? '正常' : '异常'
      } else {
        return ''
      }
    },
    formatRecovery: function(recovery) {
      return recovery === 0 ? '正常' : '封禁'
    },

    formatBlockedState(row) {
      if (row.state !== null) {
        // return state === 1 ? 'normal' : 'Blocked'
        return row.state === 0 ? 'normal' : 'Blocked'
      } else {
        return ''
      }
    },
    formatRole: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '用户'
            break
          case 2:
            text = '主播'
            break
          case 3:
            text = '客服'
            break
        }
        return text
      } else {
        return ''
      }
    },
    formatWorkingCondition: function(bool) {
      if (bool !== null) {
        return bool === 1 ? '兼职' : '全职'
      } else {
        return ''
      }
    },

    formatSettlementResult: function(bool) {
      if (bool !== null) {
        return bool === 1 ? '结算' : '不结算'
      } else {
        return ''
      }
    },
    dateFormat(row, column) {
      var date = row[column.property]
      if (date == undefined) {
        return ''
      }
      return moment(date).format('YYYY-MM-DD HH:mm:ss')
    },
    formatBoolean: function(bool) {
      if (bool !== null) {
        return bool ? '是' : '否'
      } else {
        return ''
      }
    },
    div(a, b) {
      let aFloat = +a
      let bFloat = +b
      if (Number.isNaN(aFloat)) {
        aFloat = 0
      }
      if (Number.isNaN(bFloat)) {
        bFloat = 0
      }
      let res = 0
      if (bFloat !== 0) {
        res = aFloat / bFloat
      }
      return res
    },
    div0(a, b) {
      const res = this.div(a, b)
      const res2Str = res.toFixed(0)
      return parseFloat(res2Str)
    },
    div1(a, b) {
      const res = this.div(a, b)
      const res2Str = res.toFixed(1)
      return parseFloat(res2Str)
    },
    div2(a, b) {
      const res = this.div(a, b)
      const res2Str = res.toFixed(2)
      return parseFloat(res2Str)
    },
    div3(a, b) {
      const res = this.div(a, b)
      const res3Str = res.toFixed(3)
      return parseFloat(res3Str)
    },
    div4(a, b) {
      const res = this.div(a, b)
      const res4Str = res.toFixed(4)
      return parseFloat(res4Str)
    },

    percent2(a, b) {
      const res = this.div(a, b)
      const res4Str = res.toFixed(4)
      const res4F = parseFloat(res4Str)
      const res2Per = res4F * 100
      if (res2Per === 0) {
        return 0
      }
      const res2PerStr = res2Per.toFixed(2) + '%'
      return res2PerStr
    },
    percent0(a, b) {
      const res = this.div(a, b)
      const res4Str = res.toFixed(4)
      const res4F = parseFloat(res4Str)
      const res2Per = res4F * 100
      if (res2Per === 0) {
        return 0
      }
      const res2PerStr = res2Per.toFixed(0) + '%'
      return res2PerStr
    },

    divFloat0Digits(a, b) {
      let rate = 0
      if (parseFloat(b) !== 0 && b !== undefined) {
        rate = parseFloat(a) / parseFloat(b)
      }
      return parseFloat(rate.toFixed())
    },
    formatSCTotalAnsweringH(item) {
      // 总通话时长(h:m:s)
      return this.formateSeconds(item.pay_call_duration + item.free_call_duration)
    },
    strCheckN3F2(v) {
      const valueNew = parseFloat(v) || 0
      return numberTo3(valueNew.toFixed(2))
    },
    strCheckN3int(v) {
      const valueNew = parseInt(v) || 0
      return numberTo3Int(valueNew)
    },
    formateSecondsHM(endTime) {
      let secondTime = parseInt(endTime)// 将传入的秒的值转化为Number
      let min = 0// 初始化分
      let h = 0// 初始化小时
      let result = ''
      if (secondTime >= 60) { // 如果秒数大于60，将秒数转换成整数
        min = parseInt(secondTime / 60)// 获取分钟，除以60取整数，得到整数分钟
        secondTime = parseInt(secondTime % 60)// 获取秒数，秒数取佘，得到整数秒数
        if (min >= 60) { // 如果分钟大于60，将分钟转换成小时
          h = parseInt(min / 60)// 获取小时，获取分钟除以60，得到整数小时
          min = parseInt(min % 60) // 获取小时后取佘的分，获取分钟除以60取佘的分
        }
      }
      result = `${h.toString().padStart(2, '0')}:${min.toString().padStart(2, '0')}`
      return result
    },
    formateTPIncome(item) {
      const income = parseFloat(item.calls_earned) + parseFloat(item.free_calls_earned)
      return income / 1000
    },
    formateTPCalculateRatio(item) {
      const rate = item.calculate_ratio * 100
      return `${rate}%`
    },
    formateSeconds(endTime) {
      let secondTime = parseInt(endTime)// 将传入的秒的值转化为Number
      let min = 0// 初始化分
      let h = 0// 初始化小时
      let result = ''
      if (secondTime >= 60) { // 如果秒数大于60，将秒数转换成整数
        min = parseInt(secondTime / 60)// 获取分钟，除以60取整数，得到整数分钟
        secondTime = parseInt(secondTime % 60)// 获取秒数，秒数取佘，得到整数秒数
        if (min >= 60) { // 如果分钟大于60，将分钟转换成小时
          h = parseInt(min / 60)// 获取小时，获取分钟除以60，得到整数小时
          min = parseInt(min % 60) // 获取小时后取佘的分，获取分钟除以60取佘的分
        }
      }
      result = `${h.toString().padStart(2, '0')}:${min.toString().padStart(2, '0')}:${secondTime.toString().padStart(2, '0')}`
      return result
    },
    formatDateYMD(time) {
      if (time !== null && time !== '') {
        var date = new Date(time)
        return formatTimeToStr(date, 'yyyy-MM-dd')
      } else {
        return ''
      }
    },

    formatDateHMS(time) {
      if (time !== null && time !== '') {
        var date = new Date(time)
        return formatTimeToStr(date, 'hh:mm:ss')
      } else {
        return ''
      }
    },
    getDateStartEnd(time) {
      let date, start, end
      if (time !== null && time !== '') {
        date = new Date(time)
      } else {
        date = new Date()
      }
      start = formatTimeToStr(date, 'yyyy-MM-dd') + ' 00:00:00'
      end = formatTimeToStr(date, 'yyyy-MM-dd') + ' 23:59:59'
      return [start, end]
    },
    getDateStartEndFromWeek(v) {
      let dataRes = []
      const now = new Date()
      const dataStrList = v.split(':')
      if (dataStrList.length === 2) {
        dataRes = [dataStrList[0] + ' 00:00:00', dataStrList[1] + ' 23:59:59']
      } else {
        dataRes = [now.format('yyyy-MM-dd') + ' 00:00:00', now.format('yyyy-MM-dd') + ' 23:59:59']
      }
      return dataRes
    },

    formatDate(time) {
      if (time !== null && time !== '') {
        var date = new Date(time)
        return formatTimeToStr(date, 'yyyy-MM-dd hh:mm:ss')
      } else {
        return ''
      }
    },
    formatTime10Stamp(v) {
      let dateStr = ''
      if (v !== 0 && ('' + v).length === 10) {
        const d = moment.unix(v)
        dateStr = d.format('YYYY-MM-DD HH:mm:ss')
      }
      return dateStr
    },
    filterDict(value, type) {
      const rowLabel = this[type + 'Options'] && this[type + 'Options'].filter(item => item.value === value)
      return rowLabel && rowLabel[0] && rowLabel[0].label
    },
    async getDict(type) {
      const dicts = await getDict(type)
      this[type + 'Options'] = dicts
      return dicts
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.getTableData()
    },
    handleCurrentChange(val) {
      this.page = val
      this.getTableData()
    },
    // @params beforeFunc function 请求发起前执行的函数 默认为空函数
    // @params afterFunc function 请求完成后执行的函数 默认为空函数
    async getTableData(beforeFunc = () => {
    }, afterFunc = () => {
    }) {
      beforeFunc()
      const table = await this.listApi({ page: this.page, pageSize: this.pageSize, ...this.searchInfo })
      if (table.code === 0) {
        this.tableData = table.data.list || []
        this.total = table.data.total
        this.page = table.data.page
        this.pageSize = table.data.pageSize
      }
      afterFunc()
    }
  }
}
