import {calcPercent, div2, numberTo3} from "@/utils/number"

export default {
    name: "orderRecordsMixins",
    data() {
        return {
            infoTypeOptions: [
                {value: -1, label: '全部'},
                {value: 0, label: 'Fb'},
                {value: 1, label: 'Af'},
            ],
            stateOptions: [
                {value: 1, label: '已创建'},
                {value: 2, label: '无ID'},
                {value: 3, label: '请求错误'},
                {value: 4, label: '读响应错误'},
                {value: 5, label: 'Json反序列号错误'},
                {value: 200, label: '成功'},
            ],

        }
    },
    methods: {

        formatInfoType(v) {
            let i = this.infoTypeOptions.find((item) => {
                return item.value === v
            })
            return i === undefined ? "" : i.label
        },
        formatStateOptions(v) {
            let i = this.stateOptions.find((item) => {
                return item.value === v
            })
            return i === undefined ? "" : i.label
        },

    }
}
