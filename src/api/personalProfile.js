
import service from '@/utils/request'

// @Tags PersonalProfile
// @Summary 创建PersonalProfile
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.PersonalProfile true "创建PersonalProfile"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /personalProfile/createPersonalProfile [post]
export const createPersonalProfile = (data) => {
  return service({
    url: '/personalProfile/createPersonalProfile',
    method: 'post',
    data
  })
}

// @Tags PersonalProfile
// @Summary 删除PersonalProfile
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.PersonalProfile true "删除PersonalProfile"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /personalProfile/deletePersonalProfile [delete]
export const deletePersonalProfile = (data) => {
  return service({
    url: '/personalProfile/deletePersonalProfile',
    method: 'delete',
    data
  })
}

// @Tags PersonalProfile
// @Summary 删除PersonalProfile
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除PersonalProfile"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /personalProfile/deletePersonalProfile [delete]
export const deletePersonalProfileByIds = (data) => {
  return service({
    url: '/personalProfile/deletePersonalProfileByIds',
    method: 'delete',
    data
  })
}

// @Tags PersonalProfile
// @Summary 更新PersonalProfile
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.PersonalProfile true "更新PersonalProfile"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /personalProfile/updatePersonalProfile [put]
export const updatePersonalProfile = (data) => {
  return service({
    url: '/personalProfile/updatePersonalProfile',
    method: 'put',
    data
  })
}

// @Tags PersonalProfile
// @Summary 用id查询PersonalProfile
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.PersonalProfile true "用id查询PersonalProfile"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /personalProfile/findPersonalProfile [get]
export const findPersonalProfile = (params) => {
  return service({
    url: '/personalProfile/findPersonalProfile',
    method: 'get',
    params
  })
}

// @Tags PersonalProfile
// @Summary 分页获取PersonalProfile列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取PersonalProfile列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /personalProfile/getPersonalProfileList [get]
export const getPersonalProfileList = (params) => {
  return service({
    url: '/personalProfile/getPersonalProfileList',
    method: 'get',
    params
  })
}
