import service from '@/utils/request'

// @Tags AnchorSubsidy
// @Summary 创建AnchorSubsidy
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorSubsidy true "创建AnchorSubsidy"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /anchorSubsidy/createAnchorSubsidy [post]
export const createAnchorSubsidy = (data) => {
  return service({
    url: '/anchorSubsidy/createAnchorSubsidy',
    method: 'post',
    data
  })
}

// @Tags AnchorSubsidy
// @Summary 删除AnchorSubsidy
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorSubsidy true "删除AnchorSubsidy"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /anchorSubsidy/deleteAnchorSubsidy [delete]
export const deleteAnchorSubsidy = (data) => {
  return service({
    url: '/anchorSubsidy/deleteAnchorSubsidy',
    method: 'delete',
    data
  })
}

// @Tags AnchorSubsidy
// @Summary 删除AnchorSubsidy
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除AnchorSubsidy"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /anchorSubsidy/deleteAnchorSubsidy [delete]
export const deleteAnchorSubsidyByIds = (data) => {
  return service({
    url: '/anchorSubsidy/deleteAnchorSubsidyByIds',
    method: 'delete',
    data
  })
}

// @Tags AnchorSubsidy
// @Summary 更新AnchorSubsidy
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorSubsidy true "更新AnchorSubsidy"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /anchorSubsidy/updateAnchorSubsidy [put]
export const updateAnchorSubsidy = (data) => {
  return service({
    url: '/anchorSubsidy/updateAnchorSubsidy',
    method: 'put',
    data
  })
}

// @Tags AnchorSubsidy
// @Summary 用id查询AnchorSubsidy
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.AnchorSubsidy true "用id查询AnchorSubsidy"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /anchorSubsidy/findAnchorSubsidy [get]
export const findAnchorSubsidy = (params) => {
  return service({
    url: '/anchorSubsidy/findAnchorSubsidy',
    method: 'get',
    params
  })
}

// @Tags AnchorSubsidy
// @Summary 分页获取AnchorSubsidy列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取AnchorSubsidy列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /anchorSubsidy/getAnchorSubsidyList [get]
export const getAnchorSubsidyList = (params) => {
  return service({
    url: '/anchorSubsidy/getAnchorSubsidyList',
    method: 'get',
    params
  })
}
