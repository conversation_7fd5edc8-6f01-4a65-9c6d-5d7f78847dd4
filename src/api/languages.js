import service from '@/utils/request'

export const findLanguages = (params) => {
  return service({url: '/languages/findOne', method: 'get', params})
}

export const findLanguagesAll = (params) => {
  return service({url: '/languages/findAll', method: 'get', params})
}

export const getLanguagesList = (params) => {
  return service({url: '/languages/getPage', method: 'get', params})
}

export const createLanguages = (data) => {
  return service({url: '/languages/create', method: 'post', data})
}

export const deleteLanguages = (data) => {
  return service({url: '/languages/delete', method: 'delete', data})
}

export const deleteLanguagesByIds = (data) => {
  return service({url: '/languages/batchDelete', method: 'delete', data})
}

export const updateLanguages = (data) => {
  return service({url: '/languages/update', method: 'put', data})
}
