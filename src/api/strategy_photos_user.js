import service from '@/utils/request'

export const findStrategyPhotosUser = (params) => {
  return service({url: '/strategyPhotosUser/findOne', method: 'get', params})
}

export const getStrategyPhotosUserList = (params) => {
  return service({url: '/strategyPhotosUser/getPage', method: 'get', params})
}

export const createStrategyPhotosUser = (data) => {
  return service({url: '/strategyPhotosUser/create', method: 'post', data})
}

export const deleteStrategyPhotosUser = (data) => {
  return service({url: '/strategyPhotosUser/delete', method: 'delete', data})
}

export const deleteStrategyPhotosUserByIds = (data) => {
  return service({url: '/strategyPhotosUser/batchDelete', method: 'delete', data})
}

export const updateStrategyPhotosUser = (data) => {
  return service({url: '/strategyPhotosUser/update', method: 'put', data})
}
