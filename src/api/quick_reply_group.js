import service from '@/utils/request'

export const findQuickReplyGroup = (params) => {
  return service({url: '/quickReplyGroup/findOne', method: 'get', params})
}

export const getQuickReplyGroupList = (params) => {
  return service({url: '/quickReplyGroup/getPage', method: 'get', params})
}

export const createQuickReplyGroup = (data) => {
  return service({url: '/quickReplyGroup/create', method: 'post', data})
}
export const createQuickReplyGroupNew = (data) => {
  return service({url: '/quickReplyGroup/createNew', method: 'post', data})
}


export const deleteQuickReplyGroup = (data) => {
  return service({url: '/quickReplyGroup/delete', method: 'delete', data})
}

export const deleteQuickReplyGroupByIds = (data) => {
  return service({url: '/quickReplyGroup/batchDelete', method: 'delete', data})
}

export const updateQuickReplyGroup = (data) => {
  return service({url: '/quickReplyGroup/update', method: 'put', data})
}
