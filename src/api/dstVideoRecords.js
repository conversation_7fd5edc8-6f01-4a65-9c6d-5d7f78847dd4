import service from '@/utils/request'

export const findDstVideoRecords = (params) => {
  return service({url: '/dstVideoRecords/findOne', method: 'get', params})
}

export const getDstVideoRecordsList = (params) => {
  return service({url: '/dstVideoRecords/getPage', method: 'get', params})
}

export const createDstVideoRecords = (data) => {
  return service({url: '/dstVideoRecords/create', method: 'post', data})
}

export const deleteDstVideoRecords = (data) => {
  return service({url: '/dstVideoRecords/delete', method: 'delete', data})
}

export const deleteDstVideoRecordsByIds = (data) => {
  return service({url: '/dstVideoRecords/batchDelete', method: 'delete', data})
}

export const updateDstVideoRecords = (data) => {
  return service({url: '/dstVideoRecords/update', method: 'put', data})
}
