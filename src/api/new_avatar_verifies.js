
import service from '@/utils/request'

// @Tags NewAvatarVerifies
// @Summary 创建NewAvatarVerifies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.NewAvatarVerifies true "创建NewAvatarVerifies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /newAvatarVerifies/createNewAvatarVerifies [post]
export const createNewAvatarVerifies = (data) => {
  return service({
    url: '/newAvatarVerifies/createNewAvatarVerifies',
    method: 'post',
    data
  })
}

// @Tags NewAvatarVerifies
// @Summary 删除NewAvatarVerifies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.NewAvatarVerifies true "删除NewAvatarVerifies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /newAvatarVerifies/deleteNewAvatarVerifies [delete]
export const deleteNewAvatarVerifies = (data) => {
  return service({
    url: '/newAvatarVerifies/deleteNewAvatarVerifies',
    method: 'delete',
    data
  })
}

// @Tags NewAvatarVerifies
// @Summary 删除NewAvatarVerifies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除NewAvatarVerifies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /newAvatarVerifies/deleteNewAvatarVerifies [delete]
export const deleteNewAvatarVerifiesByIds = (data) => {
  return service({
    url: '/newAvatarVerifies/deleteNewAvatarVerifiesByIds',
    method: 'delete',
    data
  })
}

// @Tags NewAvatarVerifies
// @Summary 更新NewAvatarVerifies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.NewAvatarVerifies true "更新NewAvatarVerifies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /newAvatarVerifies/updateNewAvatarVerifies [put]
export const updateNewAvatarVerifies = (data) => {
  return service({
    url: '/newAvatarVerifies/updateNewAvatarVerifies',
    method: 'put',
    data
  })
}

// @Tags NewAvatarVerifies
// @Summary 用id查询NewAvatarVerifies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.NewAvatarVerifies true "用id查询NewAvatarVerifies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /newAvatarVerifies/findNewAvatarVerifies [get]
export const findNewAvatarVerifies = (params) => {
  return service({
    url: '/newAvatarVerifies/findNewAvatarVerifies',
    method: 'get',
    params
  })
}

// @Tags NewAvatarVerifies
// @Summary 分页获取NewAvatarVerifies列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取NewAvatarVerifies列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /newAvatarVerifies/getNewAvatarVerifiesList [get]
export const getNewAvatarVerifiesList = (params) => {
  return service({
    url: '/newAvatarVerifies/getNewAvatarVerifiesList',
    method: 'get',
    params
  })
}
