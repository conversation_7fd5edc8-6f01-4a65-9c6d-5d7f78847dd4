
import service from '@/utils/request'

// @Tags StrategyTime
// @Summary 创建StrategyTime
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.StrategyTime true "创建StrategyTime"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /strategyTime/createStrategyTime [post]
export const createStrategyTime = (data) => {
  return service({
    url: '/strategyTime/createStrategyTime',
    method: 'post',
    data
  })
}

// @Tags StrategyTime
// @Summary 删除StrategyTime
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.StrategyTime true "删除StrategyTime"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /strategyTime/deleteStrategyTime [delete]
export const deleteStrategyTime = (data) => {
  return service({
    url: '/strategyTime/deleteStrategyTime',
    method: 'delete',
    data
  })
}

// @Tags StrategyTime
// @Summary 删除StrategyTime
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除StrategyTime"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /strategyTime/deleteStrategyTime [delete]
export const deleteStrategyTimeByIds = (data) => {
  return service({
    url: '/strategyTime/deleteStrategyTimeByIds',
    method: 'delete',
    data
  })
}

// @Tags StrategyTime
// @Summary 更新StrategyTime
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.StrategyTime true "更新StrategyTime"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /strategyTime/updateStrategyTime [put]
export const updateStrategyTime = (data) => {
  return service({
    url: '/strategyTime/updateStrategyTime',
    method: 'put',
    data
  })
}

// @Tags StrategyTime
// @Summary 用id查询StrategyTime
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.StrategyTime true "用id查询StrategyTime"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /strategyTime/findStrategyTime [get]
export const findStrategyTime = (params) => {
  return service({
    url: '/strategyTime/findStrategyTime',
    method: 'get',
    params
  })
}

// @Tags StrategyTime
// @Summary 分页获取StrategyTime列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取StrategyTime列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /strategyTime/getStrategyTimeList [get]
export const getStrategyTimeList = (params) => {
  return service({
    url: '/strategyTime/getStrategyTimeList',
    method: 'get',
    params
  })
}
