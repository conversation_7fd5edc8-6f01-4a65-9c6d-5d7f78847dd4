import service from '@/utils/request'

export const findWithdrawalAccountBank = (params) => {
    return service({url: '/withdrawalAccountBank/findOne', method: 'get', params})
}

export const getWithdrawalAccountBankList = (params) => {
    return service({url: '/withdrawalAccountBank/getPage', method: 'get', params})
}

export const createWithdrawalAccountBank = (data) => {
    return service({url: '/withdrawalAccountBank/create', method: 'post', data})
}

export const saveWithdrawalAccountBank = (data) => {
    return service({url: '/withdrawalAccountBank/save', method: 'post', data})
}

export const deleteWithdrawalAccountBank = (data) => {
    return service({url: '/withdrawalAccountBank/delete', method: 'delete', data})
}

export const deleteWithdrawalAccountBankByIds = (data) => {
    return service({url: '/withdrawalAccountBank/batchDelete', method: 'delete', data})
}

export const updateWithdrawalAccountBank = (data) => {
    return service({url: '/withdrawalAccountBank/update', method: 'put', data})
}
