import service from '@/utils/request'

export const findStrategySendContent = (params) => {
  return service({url: '/strategySendContent/findOne', method: 'get', params})
}

export const getStrategySendContentList = (params) => {
  return service({url: '/strategySendContent/getPage', method: 'get', params})
}

export const getStrategySendTypeList = (params) => {
  return service({url: '/strategySendContent/sendTypeList', method: 'get', params})
}

export const createStrategySendContent = (data) => {
  return service({url: '/strategySendContent/create', method: 'post', data})
}

export const deleteStrategySendContent = (data) => {
  return service({url: '/strategySendContent/delete', method: 'delete', data})
}

export const deleteStrategySendContentByIds = (data) => {
  return service({url: '/strategySendContent/batchDelete', method: 'delete', data})
}

export const updateStrategySendContent = (data) => {
  return service({url: '/strategySendContent/update', method: 'put', data})
}
