import service from '@/utils/request'

export const findUserLevels = (params) => {
  return service({url: '/userLevels/findOne', method: 'get', params})
}

export const getUserLevelsList = (params) => {
  return service({url: '/userLevels/getPage', method: 'get', params})
}

export const createUserLevels = (data) => {
  return service({url: '/userLevels/create', method: 'post', data})
}

export const deleteUserLevels = (data) => {
  return service({url: '/userLevels/delete', method: 'delete', data})
}

export const deleteUserLevelsByIds = (data) => {
  return service({url: '/userLevels/batchDelete', method: 'delete', data})
}

export const updateUserLevels = (data) => {
  return service({url: '/userLevels/update', method: 'put', data})
}

export const updateVideoLevel = (data) => {
  return service({url: '/userLevels/updateVideoLevel', method: 'put', data})
}

export const updateMsgLevel = (data) => {
  return service({url: '/userLevels/updateMsgLevel', method: 'put', data})
}
