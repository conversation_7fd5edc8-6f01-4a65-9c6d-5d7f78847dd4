import service from '@/utils/request'

export const findAdAlgorithmSetting = (params) => {
    return service({url: '/adAlgorithmSetting/findOne', method: 'get', params})
}

export const getAdAlgorithmSettingList = (params) => {
    return service({url: '/adAlgorithmSetting/getPage', method: 'get', params})
}

export const getAdTypeList = (params) => {
    return service({url: '/adAlgorithmSetting/getAdTypeList', method: 'get', params})
}

export const getAdPlatformTypeList = (params) => {
    return service({url: '/adAlgorithmSetting/getAdPlatformTypeList', method: 'get', params})
}

export const createAdAlgorithmSetting = (data) => {
    return service({url: '/adAlgorithmSetting/create', method: 'post', data})
}

export const deleteAdAlgorithmSetting = (data) => {
    return service({url: '/adAlgorithmSetting/delete', method: 'delete', data})
}

export const deleteAdAlgorithmSettingByIds = (data) => {
    return service({url: '/adAlgorithmSetting/batchDelete', method: 'delete', data})
}

export const updateAdAlgorithmSetting = (data) => {
    return service({url: '/adAlgorithmSetting/update', method: 'put', data})
}
