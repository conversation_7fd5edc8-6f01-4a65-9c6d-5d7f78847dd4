import service from '@/utils/request'

export const findManagerWithdrawalProfits = (params) => {
  return service({url: '/managerWithdrawalProfits/findOne', method: 'get', params})
}

export const getManagerWithdrawalProfitsList = (params) => {
  return service({url: '/managerWithdrawalProfits/getPage', method: 'get', params})
}

export const getPageUnionManager = (params) => {
  return service({url: '/managerWithdrawalProfits/getPageUnionManager', method: 'get', params})
}

export const createManagerWithdrawalProfits = (data) => {
  return service({url: '/managerWithdrawalProfits/create', method: 'post', data})
}

export const deleteManagerWithdrawalProfits = (data) => {
  return service({url: '/managerWithdrawalProfits/delete', method: 'delete', data})
}

export const deleteManagerWithdrawalProfitsByIds = (data) => {
  return service({url: '/managerWithdrawalProfits/batchDelete', method: 'delete', data})
}

export const updateManagerWithdrawalProfits = (data) => {
  return service({url: '/managerWithdrawalProfits/update', method: 'put', data})
}
