
import service from '@/utils/request'

// @Tags QuestionAndAnswers
// @Summary 创建QuestionAndAnswers
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.QuestionAndAnswers true "创建QuestionAndAnswers"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /questionAndAnswers/createQuestionAndAnswers [post]
export const createQuestionAndAnswers = (data) => {
  return service({
    url: '/questionAndAnswers/createQuestionAndAnswers',
    method: 'post',
    data
  })
}

// @Tags QuestionAndAnswers
// @Summary 删除QuestionAndAnswers
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.QuestionAndAnswers true "删除QuestionAndAnswers"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /questionAndAnswers/deleteQuestionAndAnswers [delete]
export const deleteQuestionAndAnswers = (data) => {
  return service({
    url: '/questionAndAnswers/deleteQuestionAndAnswers',
    method: 'delete',
    data
  })
}

// @Tags QuestionAndAnswers
// @Summary 删除QuestionAndAnswers
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除QuestionAndAnswers"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /questionAndAnswers/deleteQuestionAndAnswers [delete]
export const deleteQuestionAndAnswersByIds = (data) => {
  return service({
    url: '/questionAndAnswers/deleteQuestionAndAnswersByIds',
    method: 'delete',
    data
  })
}

// @Tags QuestionAndAnswers
// @Summary 更新QuestionAndAnswers
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.QuestionAndAnswers true "更新QuestionAndAnswers"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /questionAndAnswers/updateQuestionAndAnswers [put]
export const updateQuestionAndAnswers = (data) => {
  return service({
    url: '/questionAndAnswers/updateQuestionAndAnswers',
    method: 'put',
    data
  })
}

// @Tags QuestionAndAnswers
// @Summary 用id查询QuestionAndAnswers
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.QuestionAndAnswers true "用id查询QuestionAndAnswers"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /questionAndAnswers/findQuestionAndAnswers [get]
export const findQuestionAndAnswers = (params) => {
  return service({
    url: '/questionAndAnswers/findQuestionAndAnswers',
    method: 'get',
    params
  })
}

// @Tags QuestionAndAnswers
// @Summary 分页获取QuestionAndAnswers列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取QuestionAndAnswers列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /questionAndAnswers/getQuestionAndAnswersList [get]
export const getQuestionAndAnswersList = (params) => {
  return service({
    url: '/questionAndAnswers/getQuestionAndAnswersList',
    method: 'get',
    params
  })
}
