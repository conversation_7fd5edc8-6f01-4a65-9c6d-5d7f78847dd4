
import service from '@/utils/request'

export const getPage = (params) => {
  return service({
    url: '/userFeedback/getPage',
    method: 'get',
    params
  })
}

export const GetFeedBackType = (params) => {
  return service({
    url: '/userFeedback/GetFeedBackType',
    method: 'get',
    params
  })
}

export const GetFeedBackState = (params) => {
  return service({
    url: '/userFeedback/GetFeedBackState',
    method: 'get',
    params
  })
}

export const updateState = (params) => {
  return service({
    url: '/userFeedback/update',
    method: 'put',
    params
  })
}
