import service from '@/utils/request'

// @Tags OrderStatics
// @Summary 创建OrderStatics
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.OrderStatics true "创建OrderStatics"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /orderStatics/createOrderStatics [post]
export const createOrderStatics = (data) => {
  return service({
    url: '/orderStatics/createOrderStatics',
    method: 'post',
    data
  })
}

// @Tags OrderStatics
// @Summary 删除OrderStatics
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.OrderStatics true "删除OrderStatics"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /orderStatics/deleteOrderStatics [delete]
export const deleteOrderStatics = (data) => {
  return service({
    url: '/orderStatics/deleteOrderStatics',
    method: 'delete',
    data
  })
}

// @Tags OrderStatics
// @Summary 删除OrderStatics
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除OrderStatics"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /orderStatics/deleteOrderStatics [delete]
export const deleteOrderStaticsByIds = (data) => {
  return service({
    url: '/orderStatics/deleteOrderStaticsByIds',
    method: 'delete',
    data
  })
}

// @Tags OrderStatics
// @Summary 更新OrderStatics
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.OrderStatics true "更新OrderStatics"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /orderStatics/updateOrderStatics [put]
export const updateOrderStatics = (data) => {
  return service({
    url: '/orderStatics/updateOrderStatics',
    method: 'put',
    data
  })
}

// @Tags OrderStatics
// @Summary 用id查询OrderStatics
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.OrderStatics true "用id查询OrderStatics"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /orderStatics/findOrderStatics [get]
export const findOrderStatics = (params) => {
  return service({
    url: '/orderStatics/findOrderStatics',
    method: 'get',
    params
  })
}

// @Tags OrderStatics
// @Summary 分页获取OrderStatics列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取OrderStatics列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /orderStatics/getOrderStaticsList [get]
export const getOrderStaticsList = (params) => {
  return service({
    url: '/orderStatics/getOrderStaticsList',
    method: 'get',
    params
  })
}

export const getTodayOrderStatics = (params) => {
  return service({
    url: '/orderStatics/getTodayOrderStatics',
    method: 'get',
    params
  })
}
