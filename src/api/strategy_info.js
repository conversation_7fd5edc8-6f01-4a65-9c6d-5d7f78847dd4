import service from '@/utils/request'
import {ElMessage} from "element-plus";

const handleFileError = (res, fileName) => {
  if (typeof (res.data) !== 'undefined') {
    if (res.data.type === 'application/json') {
      const reader = new FileReader()
      reader.onload = function () {
        const message = JSON.parse(reader.result).msg
        ElMessage({
          showClose: true,
          message: message,
          type: 'error'
        })
      }
      reader.readAsText(new Blob([res.data]))
    }
  } else {
    var downloadUrl = window.URL.createObjectURL(new Blob([res]))
    var a = document.createElement('a')
    a.style.display = 'none'
    a.href = downloadUrl
    a.download = fileName
    var event = new MouseEvent('click')
    a.dispatchEvent(event)
  }
}
export const findStrategyInfo = (params) => {
  return service({url: '/strategyInfo/findOne', method: 'get', params})
}
export const downloadTemplate = (params) => {
  return service({url: '/strategyInfo/downloadTemplate', method: 'get', params, responseType: 'blob'}).then((res) => {
    handleFileError(res, 'ExcelTemplate.xlsx')
  })
}

export const getStrategyInfoList = (params) => {
  return service({url: '/strategyInfo/getPage', method: 'get', params})
}

export const createStrategyInfo = (data) => {
  return service({url: '/strategyInfo/create', method: 'post', data})
}

export const deleteStrategyInfo = (data) => {
  return service({url: '/strategyInfo/delete', method: 'delete', data})
}

export const deleteStrategyInfoByIds = (data) => {
  return service({url: '/strategyInfo/batchDelete', method: 'delete', data})
}

export const updateStrategyInfo = (data) => {
  return service({url: '/strategyInfo/update', method: 'put', data})
}
