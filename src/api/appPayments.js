
import service from '@/utils/request'

// @Tags AppPayments
// @Summary 创建AppPayments
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AppPayments true "创建AppPayments"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /appPayments/createAppPayments [post]
export const createAppPayments = (data) => {
  return service({
    url: '/appPayments/createAppPayments',
    method: 'post',
    data
  })
}

// @Tags AppPayments
// @Summary 删除AppPayments
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AppPayments true "删除AppPayments"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /appPayments/deleteAppPayments [delete]
export const deleteAppPayments = (data) => {
  return service({
    url: '/appPayments/deleteAppPayments',
    method: 'post',
    data
  })
}

// @Tags AppPayments
// @Summary 删除AppPayments
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除AppPayments"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /appPayments/deleteAppPayments [delete]
export const deleteAppPaymentsByIds = (data) => {
  return service({
    url: '/appPayments/deleteAppPaymentsByIds',
    method: 'post',
    data
  })
}

// @Tags AppPayments
// @Summary 更新AppPayments
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AppPayments true "更新AppPayments"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /appPayments/updateAppPayments [put]
export const updateAppPayments = (data) => {
  return service({
    url: '/appPayments/updateAppPayments',
    method: 'post',
    data
  })
}

// @Tags AppPayments
// @Summary 用id查询AppPayments
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.AppPayments true "用id查询AppPayments"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /appPayments/findAppPayments [get]
export const findAppPayments = (params) => {
  return service({
    url: '/appPayments/findAppPayments',
    method: 'get',
    params
  })
}

// @Tags AppPayments
// @Summary 分页获取AppPayments列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取AppPayments列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /appPayments/getAppPaymentsList [get]
export const getAppPaymentsList = (params) => {
  return service({
    url: '/appPayments/getAppPaymentsList',
    method: 'get',
    params
  })
}
