import service from '@/utils/request'

export const findAnchorCoverRecommendationsDetails = (params) => {
  return service({url: '/anchorCoverRecommendationsDetails/findOne', method: 'get', params})
}

export const getAnchorCoverRecommendationsDetailsList = (params) => {
  return service({url: '/anchorCoverRecommendationsDetails/getPage', method: 'get', params})
}
export const getAnchorCoverRecommendationsDetailsAll = (params) => {
  return service({url: '/anchorCoverRecommendationsDetails/getAll', method: 'get', params})
}

export const getAllCoverUrl = (params) => {
  return service({url: '/anchorCoverRecommendationsDetails/getAllCoverUrl', method: 'get', params})
}

export const createAnchorCoverRecommendationsDetails = (data) => {
  return service({url: '/anchorCoverRecommendationsDetails/create', method: 'post', data})
}

export const batchCreateCoverDetails = (data) => {
  return service({url: '/anchorCoverRecommendationsDetails/batchCreate', method: 'post', data})
}

export const coverDetailsSave = (data) => {
  return service({url: '/anchorCoverRecommendationsDetails/save', method: 'post', data})
}
export const coverDetailsDelete = (data) => {
  return service({url: '/anchorCoverRecommendationsDetails/coverDelete', method: 'post', data})
}

export const deleteAnchorCoverRecommendationsDetails = (data) => {
  return service({url: '/anchorCoverRecommendationsDetails/delete', method: 'delete', data})
}

export const deleteAnchorCoverRecommendationsDetailsByIds = (data) => {
  return service({url: '/anchorCoverRecommendationsDetails/batchDelete', method: 'delete', data})
}

export const updateAnchorCoverRecommendationsDetails = (data) => {
  return service({url: '/anchorCoverRecommendationsDetails/update', method: 'put', data})
}
export const restoreAnchorCoverRecommendationsDetails = (data) => {
  return service({url: '/anchorCoverRecommendationsDetails/restore', method: 'put', data})
}
export const updateAuditTags = (data) => {
  return service({url: '/users/updateAuditTags', method: 'post', data})
}
