import service from '@/utils/request'

export const findWithdrawalMethod = (params) => {
  return service({url: '/withdrawalMethod/findOne', method: 'get', params})
}

export const findWithdrawalMethodAll = (params) => {
  return service({url: '/withdrawalMethod/findAll', method: 'get', params})
}

export const getWithdrawalMethodList = (params) => {
  return service({url: '/withdrawalMethod/getPage', method: 'get', params})
}

export const createWithdrawalMethod = (data) => {
  return service({url: '/withdrawalMethod/create', method: 'post', data})
}

export const deleteWithdrawalMethod = (data) => {
  return service({url: '/withdrawalMethod/delete', method: 'delete', data})
}

export const deleteWithdrawalMethodByIds = (data) => {
  return service({url: '/withdrawalMethod/batchDelete', method: 'delete', data})
}

export const updateWithdrawalMethod = (data) => {
  return service({url: '/withdrawalMethod/update', method: 'put', data})
}
