
import service from '@/utils/request'

// @Tags Unions
// @Summary 创建Unions
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Unions true "创建Unions"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /unions/createUnions [post]
export const createUnions = (data) => {
  return service({
    url: '/unions/createUnions',
    method: 'post',
    data
  })
}

// @Tags Unions
// @Summary 删除Unions
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Unions true "删除Unions"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /unions/deleteUnions [delete]
export const deleteUnions = (data) => {
  return service({
    url: '/unions/deleteUnions',
    method: 'delete',
    data
  })
}

export const deleteUnionManagerId = (data) => {
  return service({
    url: '/unions/deleteUnionManagerId',
    method: 'delete',
    data
  })
}

// @Tags Unions
// @Summary 删除Unions
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除Unions"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /unions/deleteUnions [delete]
export const deleteUnionsByIds = (data) => {
  return service({
    url: '/unions/deleteUnionsByIds',
    method: 'delete',
    data
  })
}

// @Tags Unions
// @Summary 更新Unions
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Unions true "更新Unions"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /unions/updateUnions [put]
export const updateUnions = (data) => {
  return service({
    url: '/unions/updateUnions',
    method: 'put',
    data
  })
}

// @Tags Unions
// @Summary 用id查询Unions
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.Unions true "用id查询Unions"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /unions/findUnions [get]
export const findUnions = (params) => {
  return service({
    url: '/unions/findUnions',
    method: 'get',
    params
  })
}

// @Tags Unions
// @Summary 分页获取Unions列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取Unions列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /unions/getUnionsList [get]
export const getUnionsList = (params) => {
  return service({
    url: '/unions/getUnionsList',
    method: 'get',
    params
  })
}

export const getUnionsAll = (params) => {
  return service({
    url: '/unions/getUnionsAll',
    method: 'get',
    params
  })
}
