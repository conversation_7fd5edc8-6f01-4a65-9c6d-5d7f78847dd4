import service from '@/utils/request'

// @Tags FbAdAccounts
// @Summary 创建FbAdAccounts
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.FbAdAccounts true "创建FbAdAccounts"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /fbAdAccounts/createFbAdAccounts [post]
export const createFbAdAccounts = (data) => {
  return service({
    url: '/fbAdAccounts/createFbAdAccounts',
    method: 'post',
    data
  })
}

// @Tags FbAdAccounts
// @Summary 删除FbAdAccounts
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.FbAdAccounts true "删除FbAdAccounts"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /fbAdAccounts/deleteFbAdAccounts [delete]
export const deleteFbAdAccounts = (data) => {
  return service({
    url: '/fbAdAccounts/deleteFbAdAccounts',
    method: 'delete',
    data
  })
}

// @Tags FbAdAccounts
// @Summary 删除FbAdAccounts
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除FbAdAccounts"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /fbAdAccounts/deleteFbAdAccounts [delete]
export const deleteFbAdAccountsByIds = (data) => {
  return service({
    url: '/fbAdAccounts/deleteFbAdAccountsByIds',
    method: 'delete',
    data
  })
}

// @Tags FbAdAccounts
// @Summary 更新FbAdAccounts
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.FbAdAccounts true "更新FbAdAccounts"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /fbAdAccounts/updateFbAdAccounts [put]
export const updateFbAdAccounts = (data) => {
  return service({
    url: '/fbAdAccounts/updateFbAdAccounts',
    method: 'put',
    data
  })
}

// @Tags FbAdAccounts
// @Summary 用id查询FbAdAccounts
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.FbAdAccounts true "用id查询FbAdAccounts"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /fbAdAccounts/findFbAdAccounts [get]
export const findFbAdAccounts = (params) => {
  return service({
    url: '/fbAdAccounts/findFbAdAccounts',
    method: 'get',
    params
  })
}

// @Tags FbAdAccounts
// @Summary 分页获取FbAdAccounts列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取FbAdAccounts列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /fbAdAccounts/getFbAdAccountsList [get]
export const getFbAdAccountsList = (params) => {
  return service({
    url: '/fbAdAccounts/getFbAdAccountsList',
    method: 'get',
    params
  })
}
