import service from '@/utils/request'

export const findAnchorViolationRecords = (params) => {
  return service({url: '/anchorViolationRecords/findOne', method: 'get', params})
}

export const getAnchorViolationRecordsList = (params) => {
  return service({url: '/anchorViolationRecords/getPage', method: 'get', params})
}

export const anchorBanStateList = (params) => {
  return service({url: '/anchorViolationRecords/anchorBanStateList', method: 'get', params})
}

export const examineStateList = (params) => {
  return service({url: '/anchorViolationRecords/examineStateList', method: 'get', params})
}

export const createAnchorViolationRecords = (data) => {
  return service({url: '/anchorViolationRecords/create', method: 'post', data})
}

export const deleteAnchorViolationRecords = (data) => {
  return service({url: '/anchorViolationRecords/delete', method: 'delete', data})
}

export const deleteAnchorViolationRecordsByIds = (data) => {
  return service({url: '/anchorViolationRecords/batchDelete', method: 'delete', data})
}

export const updateAnchorViolationRecords = (data) => {
  return service({url: '/anchorViolationRecords/update', method: 'put', data})
}

export const sendMsgUserList = (params) => {
  return service({url: '/anchorViolationRecords/sendMsgUserList', method: 'get', params})
}

export const callUserList = (params) => {
  return service({url: '/anchorViolationRecords/callUserList', method: 'get', params})
}

export const incomeUserList = (params) => {
  return service({url: '/anchorViolationRecords/incomeUserList', method: 'get', params})
}

export const recentMsgHistory = (params) => {
  return service({url: '/anchorViolationRecords/recentMsgHistory', method: 'get', params})
}
