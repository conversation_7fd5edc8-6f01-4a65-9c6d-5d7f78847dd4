import service from '@/utils/request'

// @Tags AdvertisingReport
// @Summary 创建AdvertisingReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AdvertisingReport true "创建AdvertisingReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /advertisingReport/createAdvertisingReport [post]
export const createAdvertisingReport = (data) => {
  return service({
    url: '/advertisingReport/createAdvertisingReport',
    method: 'post',
    data
  })
}

// @Tags AdvertisingReport
// @Summary 删除AdvertisingReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AdvertisingReport true "删除AdvertisingReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /advertisingReport/deleteAdvertisingReport [delete]
export const deleteAdvertisingReport = (data) => {
  return service({
    url: '/advertisingReport/deleteAdvertisingReport',
    method: 'delete',
    data
  })
}

// @Tags AdvertisingReport
// @Summary 删除AdvertisingReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除AdvertisingReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /advertisingReport/deleteAdvertisingReport [delete]
export const deleteAdvertisingReportByIds = (data) => {
  return service({
    url: '/advertisingReport/deleteAdvertisingReportByIds',
    method: 'delete',
    data
  })
}

// @Tags AdvertisingReport
// @Summary 更新AdvertisingReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AdvertisingReport true "更新AdvertisingReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /advertisingReport/updateAdvertisingReport [put]
export const updateAdvertisingReport = (data) => {
  return service({
    url: '/advertisingReport/updateAdvertisingReport',
    method: 'put',
    data
  })
}

// @Tags AdvertisingReport
// @Summary 用id查询AdvertisingReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.AdvertisingReport true "用id查询AdvertisingReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /advertisingReport/findAdvertisingReport [get]
export const findAdvertisingReport = (params) => {
  return service({
    url: '/advertisingReport/findAdvertisingReport',
    method: 'get',
    params
  })
}

// @Tags AdvertisingReport
// @Summary 分页获取AdvertisingReport列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取AdvertisingReport列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /advertisingReport/getAdvertisingReportList [get]
export const getAdvertisingReportList = (params) => {
  return service({
    url: '/advertisingReport/getAdvertisingReportList',
    method: 'get',
    params
  })
}
