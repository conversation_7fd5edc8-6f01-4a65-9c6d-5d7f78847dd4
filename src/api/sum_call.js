import service from '@/utils/request'

// @Tags SumCall
// @Summary 创建SumCall
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SumCall true "创建SumCall"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /sumCall/createSumCall [post]
export const createSumCall = (data) => {
  return service({
    url: '/sumCall/createSumCall',
    method: 'post',
    data
  })
}

// @Tags SumCall
// @Summary 删除SumCall
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SumCall true "删除SumCall"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /sumCall/deleteSumCall [delete]
export const deleteSumCall = (data) => {
  return service({
    url: '/sumCall/deleteSumCall',
    method: 'delete',
    data
  })
}

// @Tags SumCall
// @Summary 删除SumCall
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除SumCall"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /sumCall/deleteSumCall [delete]
export const deleteSumCallByIds = (data) => {
  return service({
    url: '/sumCall/deleteSumCallByIds',
    method: 'delete',
    data
  })
}

// @Tags SumCall
// @Summary 更新SumCall
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SumCall true "更新SumCall"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /sumCall/updateSumCall [put]
export const updateSumCall = (data) => {
  return service({
    url: '/sumCall/updateSumCall',
    method: 'put',
    data
  })
}

// @Tags SumCall
// @Summary 用id查询SumCall
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.SumCall true "用id查询SumCall"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /sumCall/findSumCall [get]
export const findSumCall = (params) => {
  return service({
    url: '/sumCall/findSumCall',
    method: 'get',
    params
  })
}

// @Tags SumCall
// @Summary 分页获取SumCall列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取SumCall列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /sumCall/getSumCallList [get]
export const getSumCallList = (params) => {
  return service({
    url: '/sumCall/getSumCallList',
    method: 'get',
    params
  })
}
