import service from '@/utils/request'

export const syncProducts = (data) => {
  return service({
    url: '/products/syncProducts',
    method: 'post',
    data
  })
}

// @Tags Products
// @Summary 创建Products
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Products true "创建Products"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /products/createProducts [post]
export const createProducts = (data) => {
  return service({
    url: '/products/createProducts',
    method: 'post',
    data
  })
}

// @Tags Products
// @Summary 删除Products
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Products true "删除Products"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /products/deleteProducts [delete]
export const deleteProducts = (data) => {
  return service({
    url: '/products/deleteProducts',
    method: 'post',
    data
  })
}

// @Tags Products
// @Summary 删除Products
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除Products"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /products/deleteProducts [delete]
export const deleteProductsByIds = (data) => {
  return service({
    url: '/products/deleteProductsByIds',
    method: 'post',
    data
  })
}

// @Tags Products
// @Summary 更新Products
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Products true "更新Products"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /products/updateProducts [put]
export const updateProducts = (data) => {
  return service({
    url: '/products/updateProducts',
    method: 'post',
    data
  })
}

// @Tags Products
// @Summary 用id查询Products
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.Products true "用id查询Products"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /products/findProducts [get]
export const findProducts = (params) => {
  return service({
    url: '/products/findProducts',
    method: 'get',
    params
  })
}

// @Tags Products
// @Summary 分页获取Products列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取Products列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /products/getProductsList [get]
export const getProductsList = (params) => {
  return service({
    url: '/products/getProductsList',
    method: 'get',
    params
  })
}

export const getProductsType = (params) => {
  return service({
    url: '/products/getProductsType',
    method: 'get',
    params
  })
}

export const getSkuList = (params) => {
  return service({
    url: '/products/getSkuList',
    method: 'get',
    params
  })
}

export const getAppSkuList = (params) => {
  return service({
    url: '/products/getAppSkuList',
    method: 'get',
    params
  })
}
