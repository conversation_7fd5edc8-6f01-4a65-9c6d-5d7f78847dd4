import service from '@/utils/request'

export const findAnchorRewardLogs = (params) => {
  return service({url: '/anchorRewardLogs/findOne', method: 'get', params})
}

export const getAnchorRewardLogsList = (params) => {
  return service({url: '/anchorRewardLogs/getPage', method: 'get', params})
}

export const createAnchorRewardLogs = (data) => {
  return service({url: '/anchorRewardLogs/create', method: 'post', data})
}

export const deleteAnchorRewardLogs = (data) => {
  return service({url: '/anchorRewardLogs/delete', method: 'delete', data})
}

export const deleteAnchorRewardLogsByIds = (data) => {
  return service({url: '/anchorRewardLogs/batchDelete', method: 'delete', data})
}

export const updateAnchorRewardLogs = (data) => {
  return service({url: '/anchorRewardLogs/update', method: 'put', data})
}
