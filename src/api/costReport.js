import service from '@/utils/request'

// @Tags CostReport
// @Summary 创建CostReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.CostReport true "创建CostReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /costReport/createCostReport [post]
export const createCostReport = (data) => {
  return service({
    url: '/costReport/createCostReport',
    method: 'post',
    data
  })
}

// @Tags CostReport
// @Summary 删除CostReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.CostReport true "删除CostReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /costReport/deleteCostReport [delete]
export const deleteCostReport = (data) => {
  return service({
    url: '/costReport/deleteCostReport',
    method: 'delete',
    data
  })
}

// @Tags CostReport
// @Summary 删除CostReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除CostReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /costReport/deleteCostReport [delete]
export const deleteCostReportByIds = (data) => {
  return service({
    url: '/costReport/deleteCostReportByIds',
    method: 'delete',
    data
  })
}

// @Tags CostReport
// @Summary 更新CostReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.CostReport true "更新CostReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /costReport/updateCostReport [put]
export const updateCostReport = (data) => {
  return service({
    url: '/costReport/updateCostReport',
    method: 'put',
    data
  })
}

// @Tags CostReport
// @Summary 用id查询CostReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.CostReport true "用id查询CostReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /costReport/findCostReport [get]
export const findCostReport = (params) => {
  return service({
    url: '/costReport/findCostReport',
    method: 'get',
    params
  })
}

// @Tags CostReport
// @Summary 分页获取CostReport列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取CostReport列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /costReport/getCostReportList [get]
export const getCostReportList = (params) => {
  return service({
    url: '/costReport/getCostReportList',
    method: 'get',
    params
  })
}
