import service from '@/utils/request'

// @Tags AdsReport
// @Summary 创建AdsReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AdsReport true "创建AdsReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adsReport/createAdsReport [post]
export const createAdsReport = (data) => {
    return service({
        url: '/adsReport/createAdsReport',
        method: 'post',
        data
    })
}

// @Tags AdsReport
// @Summary 删除AdsReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AdsReport true "删除AdsReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adsReport/deleteAdsReport [delete]
export const deleteAdsReport = (data) => {
    return service({
        url: '/adsReport/deleteAdsReport',
        method: 'delete',
        data
    })
}

// @Tags AdsReport
// @Summary 删除AdsReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除AdsReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adsReport/deleteAdsReport [delete]
export const deleteAdsReportByIds = (data) => {
    return service({
        url: '/adsReport/deleteAdsReportByIds',
        method: 'delete',
        data
    })
}

// @Tags AdsReport
// @Summary 更新AdsReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AdsReport true "更新AdsReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /adsReport/updateAdsReport [put]
export const updateAdsReport = (data) => {
    return service({
        url: '/adsReport/updateAdsReport',
        method: 'put',
        data
    })
}

// @Tags AdsReport
// @Summary 用id查询AdsReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.AdsReport true "用id查询AdsReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /adsReport/findAdsReport [get]
export const findAdsReport = (params) => {
    return service({
        url: '/adsReport/findAdsReport',
        method: 'get',
        params
    })
}

// @Tags AdsReport
// @Summary 分页获取AdsReport列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取AdsReport列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adsReport/getAdsReportList [get]
export const getAdsReportList = (params) => {
    return service({
        url: '/adsReport/getAdsReportList',
        method: 'get',
        params
    })
}
