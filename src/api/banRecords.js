import service from '@/utils/request'

export const findBanRecords = (params) => {
  return service({url: '/banRecords/findOne', method: 'get', params})
}

export const getBanRecordsList = (params) => {
  return service({url: '/banRecords/getPage', method: 'get', params})
}

export const banRecordTypeList = (params) => {
  return service({url: '/banRecords/banRecordTypeList', method: 'get', params})
}

export const createBanRecords = (data) => {
  return service({url: '/banRecords/create', method: 'post', data})
}
export const banAnchor = (data) => {
  return service({url: '/banRecords/banAnchor', method: 'post', data})
}

export const deleteBanRecords = (data) => {
  return service({url: '/banRecords/delete', method: 'delete', data})
}

export const deleteBanRecordsByIds = (data) => {
  return service({url: '/banRecords/batchDelete', method: 'delete', data})
}

export const updateBanRecords = (data) => {
  return service({url: '/banRecords/update', method: 'put', data})
}
