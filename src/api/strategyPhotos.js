import service from '@/utils/request'

export const findStrategyPhotos = (params) => {
  return service({url: '/strategyPhotos/findOne', method: 'get', params})
}

export const findStrategyPhotosDetail = (params) => {
  return service({url: '/strategyPhotos/findOneDetail', method: 'get', params})
}

export const getStrategyPhotosList = (params) => {
  return service({url: '/strategyPhotos/getPage', method: 'get', params})
}
export const getPhotoTypeMapList = (params) => {
  return service({url: '/strategyPhotos/getPhotoTypeMapList', method: 'get', params})
}

export const createStrategyPhotos = (data) => {
  return service({url: '/strategyPhotos/create', method: 'post', data})
}
export const createStrategyPhotosDetail = (data) => {
  return service({url: '/strategyPhotos/createDetail', method: 'post', data})
}

export const deleteStrategyPhotos = (data) => {
  return service({url: '/strategyPhotos/delete', method: 'delete', data})
}
export const deleteStrategyPhotosDetail = (data) => {
  return service({url: '/strategyPhotos/deleteDetail', method: 'delete', data})
}

export const deleteStrategyPhotosByIds = (data) => {
  return service({url: '/strategyPhotos/batchDelete', method: 'delete', data})
}

export const updateStrategyPhotos = (data) => {
  return service({url: '/strategyPhotos/update', method: 'put', data})
}
export const updateStrategyPhotosDetail = (data) => {
  return service({url: '/strategyPhotos/updateStrategyPhotos', method: 'put', data})
}
