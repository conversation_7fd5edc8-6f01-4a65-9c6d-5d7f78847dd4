import service from '@/utils/request'

export const findContentReview = (params) => {
  return service({url: '/contentReview/findOne', method: 'get', params})
}

export const getContentReviewList = (params) => {
  return service({url: '/contentReview/getPage', method: 'get', params})
}

export const createContentReview = (data) => {
  return service({url: '/contentReview/create', method: 'post', data})
}

export const deleteContentReview = (data) => {
  return service({url: '/contentReview/delete', method: 'delete', data})
}

export const clearMsgContentReview = (data) => {
  return service({url: '/contentReview/clearMsg', method: 'post', data})
}
export const clearImgContentReview = (data) => {
  return service({url: '/contentReview/clearImg', method: 'post', data})
}

export const deleteContentReviewByIds = (data) => {
  return service({url: '/contentReview/batchDelete', method: 'delete', data})
}

export const updateContentReview = (data) => {
  return service({url: '/contentReview/update', method: 'put', data})
}
