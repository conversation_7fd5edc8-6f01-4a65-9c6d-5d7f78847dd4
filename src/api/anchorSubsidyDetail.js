import service from '@/utils/request'

// @Tags AnchorSubsidyDetail
// @Summary 创建AnchorSubsidyDetail
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorSubsidyDetail true "创建AnchorSubsidyDetail"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /anchorSubsidyDetail/createAnchorSubsidyDetail [post]
export const createAnchorSubsidyDetail = (data) => {
  return service({
    url: '/anchorSubsidyDetail/createAnchorSubsidyDetail',
    method: 'post',
    data
  })
}

// @Tags AnchorSubsidyDetail
// @Summary 删除AnchorSubsidyDetail
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorSubsidyDetail true "删除AnchorSubsidyDetail"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /anchorSubsidyDetail/deleteAnchorSubsidyDetail [delete]
export const deleteAnchorSubsidyDetail = (data) => {
  return service({
    url: '/anchorSubsidyDetail/deleteAnchorSubsidyDetail',
    method: 'delete',
    data
  })
}

// @Tags AnchorSubsidyDetail
// @Summary 删除AnchorSubsidyDetail
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除AnchorSubsidyDetail"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /anchorSubsidyDetail/deleteAnchorSubsidyDetail [delete]
export const deleteAnchorSubsidyDetailByIds = (data) => {
  return service({
    url: '/anchorSubsidyDetail/deleteAnchorSubsidyDetailByIds',
    method: 'delete',
    data
  })
}

// @Tags AnchorSubsidyDetail
// @Summary 更新AnchorSubsidyDetail
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorSubsidyDetail true "更新AnchorSubsidyDetail"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /anchorSubsidyDetail/updateAnchorSubsidyDetail [put]
export const updateAnchorSubsidyDetail = (data) => {
  return service({
    url: '/anchorSubsidyDetail/updateAnchorSubsidyDetail',
    method: 'put',
    data
  })
}

// @Tags AnchorSubsidyDetail
// @Summary 用id查询AnchorSubsidyDetail
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.AnchorSubsidyDetail true "用id查询AnchorSubsidyDetail"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /anchorSubsidyDetail/findAnchorSubsidyDetail [get]
export const findAnchorSubsidyDetail = (params) => {
  return service({
    url: '/anchorSubsidyDetail/findAnchorSubsidyDetail',
    method: 'get',
    params
  })
}

// @Tags AnchorSubsidyDetail
// @Summary 分页获取AnchorSubsidyDetail列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取AnchorSubsidyDetail列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /anchorSubsidyDetail/getAnchorSubsidyDetailList [get]
export const getAnchorSubsidyDetailList = (params) => {
  return service({
    url: '/anchorSubsidyDetail/getAnchorSubsidyDetailList',
    method: 'get',
    params
  })
}
