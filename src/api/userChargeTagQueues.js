import service from '@/utils/request'

export const findUserChargeTagQueues = (params) => {
  return service({url: '/userChargeTagQueues/findOne', method: 'get', params})
}

export const getUserChargeTagQueuesList = (params) => {
  return service({url: '/userChargeTagQueues/getPage', method: 'get', params})
}
export const getUserChargeTagQueuesListNew = (params) => {
  return service({url: '/userChargeTagQueues/getPageNew', method: 'get', params})
}

export const createUserChargeTagQueues = (data) => {
  return service({url: '/userChargeTagQueues/create', method: 'post', data})
}

export const deleteUserChargeTagQueues = (data) => {
  return service({url: '/userChargeTagQueues/delete', method: 'delete', data})
}

export const deleteUserChargeTagQueuesByIds = (data) => {
  return service({url: '/userChargeTagQueues/batchDelete', method: 'delete', data})
}

export const updateUserChargeTagQueues = (data) => {
  return service({url: '/userChargeTagQueues/update', method: 'put', data})
}
