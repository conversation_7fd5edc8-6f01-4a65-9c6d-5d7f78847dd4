import service from '@/utils/request'

// @Tags UserPhotoAlbums
// @Summary 创建UserPhotoAlbums
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserPhotoAlbums true "创建UserPhotoAlbums"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /userPhotoAlbums/createUserPhotoAlbums [post]
export const createUserPhotoAlbums = (data) => {
  return service({
    url: '/userPhotoAlbums/createUserPhotoAlbums',
    method: 'post',
    data
  })
}

// @Tags UserPhotoAlbums
// @Summary 删除UserPhotoAlbums
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserPhotoAlbums true "删除UserPhotoAlbums"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /userPhotoAlbums/deleteUserPhotoAlbums [delete]
export const deleteUserPhotoAlbums = (data) => {
  return service({
    url: '/userPhotoAlbums/deleteUserPhotoAlbums',
    method: 'delete',
    data
  })
}

// @Tags UserPhotoAlbums
// @Summary 删除UserPhotoAlbums
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除UserPhotoAlbums"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /userPhotoAlbums/deleteUserPhotoAlbums [delete]
export const deleteUserPhotoAlbumsByIds = (data) => {
  return service({
    url: '/userPhotoAlbums/deleteUserPhotoAlbumsByIds',
    method: 'delete',
    data
  })
}

// @Tags UserPhotoAlbums
// @Summary 更新UserPhotoAlbums
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserPhotoAlbums true "更新UserPhotoAlbums"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /userPhotoAlbums/updateUserPhotoAlbums [put]
export const updateUserPhotoAlbums = (data) => {
  return service({
    url: '/userPhotoAlbums/updateUserPhotoAlbums',
    method: 'put',
    data
  })
}

// @Tags UserPhotoAlbums
// @Summary 用id查询UserPhotoAlbums
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.UserPhotoAlbums true "用id查询UserPhotoAlbums"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /userPhotoAlbums/findUserPhotoAlbums [get]
export const findUserPhotoAlbums = (params) => {
  return service({
    url: '/userPhotoAlbums/findUserPhotoAlbums',
    method: 'get',
    params
  })
}

// @Tags UserPhotoAlbums
// @Summary 分页获取UserPhotoAlbums列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取UserPhotoAlbums列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /userPhotoAlbums/getUserPhotoAlbumsList [get]
export const getUserPhotoAlbumsList = (params) => {
  return service({
    url: '/userPhotoAlbums/getUserPhotoAlbumsList',
    method: 'get',
    params
  })
}
