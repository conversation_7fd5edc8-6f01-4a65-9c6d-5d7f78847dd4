import service from '@/utils/request'

// @Tags IPQuery
// @Summary 分页获取IPQuery列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取IPQuery列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /IpQuery/getIPQueryList [get]
export const getIPQueryList = (params) => {
  return service({
    url: '/ipQuery/getIPQueryList',
    method: 'get',
    params
  })
}
