import service from '@/utils/request'

// @Tags AppsTa
// @Summary 创建AppsTa
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AppsTa true "创建AppsTa"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /appsTa/createAppsTa [post]
export const createAppsTa = (data) => {
    return service({
        url: '/appsTa/createAppsTa',
        method: 'post',
        data
    })
}

// @Tags AppsTa
// @Summary 删除AppsTa
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AppsTa true "删除AppsTa"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /appsTa/deleteAppsTa [delete]
export const deleteAppsTa = (data) => {
    return service({
        url: '/appsTa/deleteAppsTa',
        method: 'delete',
        data
    })
}

// @Tags AppsTa
// @Summary 删除AppsTa
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除AppsTa"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /appsTa/deleteAppsTa [delete]
export const deleteAppsTaByIds = (data) => {
    return service({
        url: '/appsTa/deleteAppsTaByIds',
        method: 'delete',
        data
    })
}

// @Tags AppsTa
// @Summary 更新AppsTa
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AppsTa true "更新AppsTa"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /appsTa/updateAppsTa [put]
export const updateAppsTa = (data) => {
    return service({
        url: '/appsTa/updateAppsTa',
        method: 'put',
        data
    })
}

// @Tags AppsTa
// @Summary 用id查询AppsTa
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.AppsTa true "用id查询AppsTa"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /appsTa/findAppsTa [get]
export const findAppsTa = (params) => {
    return service({
        url: '/appsTa/findAppsTa',
        method: 'get',
        params
    })
}

// @Tags AppsTa
// @Summary 分页获取AppsTa列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取AppsTa列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /appsTa/getAppsTaList [get]
export const getAppsTaList = (params) => {
    return service({
        url: '/appsTa/getAppsTaList',
        method: 'get',
        params
    })
}
