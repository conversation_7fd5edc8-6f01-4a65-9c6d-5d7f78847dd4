import service from '@/utils/request'

// @Tags OrderRecords
// @Summary 创建OrderRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.OrderRecords true "创建OrderRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /orderRecords/createOrderRecords [post]
export const createOrderRecords = (data) => {
    return service({
        url: '/orderRecords/createOrderRecords',
        method: 'post',
        data
    })
}

// @Tags OrderRecords
// @Summary 删除OrderRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.OrderRecords true "删除OrderRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /orderRecords/deleteOrderRecords [delete]
export const deleteOrderRecords = (data) => {
    return service({
        url: '/orderRecords/deleteOrderRecords',
        method: 'delete',
        data
    })
}

// @Tags OrderRecords
// @Summary 删除OrderRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除OrderRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /orderRecords/deleteOrderRecords [delete]
export const deleteOrderRecordsByIds = (data) => {
    return service({
        url: '/orderRecords/deleteOrderRecordsByIds',
        method: 'delete',
        data
    })
}

// @Tags OrderRecords
// @Summary 更新OrderRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.OrderRecords true "更新OrderRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /orderRecords/updateOrderRecords [put]
export const updateOrderRecords = (data) => {
    return service({
        url: '/orderRecords/updateOrderRecords',
        method: 'put',
        data
    })
}

// @Tags OrderRecords
// @Summary 用id查询OrderRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.OrderRecords true "用id查询OrderRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /orderRecords/findOrderRecords [get]
export const findOrderRecords = (params) => {
    return service({
        url: '/orderRecords/findOrderRecords',
        method: 'get',
        params
    })
}

// @Tags OrderRecords
// @Summary 分页获取OrderRecords列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取OrderRecords列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /orderRecords/getOrderRecordsList [get]
export const getOrderRecordsList = (params) => {
    return service({
        url: '/orderRecords/getOrderRecordsList',
        method: 'get',
        params
    })
}
