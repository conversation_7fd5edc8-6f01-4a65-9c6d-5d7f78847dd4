import service from '@/utils/request'

export const findWithdrawalAccount = (params) => {
    return service({url: '/withdrawalAccount/findOne', method: 'get', params})
}

export const getExportExcelInfo = (params) => {
    return service({url: '/withdrawalAccount/getExportExcelInfo', method: 'get', params})
}

export const getWithdrawalAccountList = (params) => {
    return service({url: '/withdrawalAccount/getPage', method: 'get', params})
}

export const getPageWithAccountInfo = (params) => {
    return service({url: '/withdrawalAccount/getPageWithAccountInfo', method: 'get', params})
}

export const createWithdrawalAccount = (data) => {
    return service({url: '/withdrawalAccount/create', method: 'post', data})
}

export const deleteWithdrawalAccount = (data) => {
    return service({url: '/withdrawalAccount/delete', method: 'delete', data})
}

export const deleteWithdrawalAccountByIds = (data) => {
    return service({url: '/withdrawalAccount/batchDelete', method: 'delete', data})
}

export const updateWithdrawalAccount = (data) => {
    return service({url: '/withdrawalAccount/update', method: 'put', data})
}
