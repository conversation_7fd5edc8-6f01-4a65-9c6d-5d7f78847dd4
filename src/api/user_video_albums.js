import service from '@/utils/request'

// @Tags UserVideoAlbums
// @Summary 创建UserVideoAlbums
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserVideoAlbums true "创建UserVideoAlbums"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /userVideoAlbums/createUserVideoAlbums [post]
export const createUserVideoAlbums = (data) => {
  return service({
    url: '/userVideoAlbums/createUserVideoAlbums',
    method: 'post',
    data
  })
}

// @Tags UserVideoAlbums
// @Summary 删除UserVideoAlbums
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserVideoAlbums true "删除UserVideoAlbums"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /userVideoAlbums/deleteUserVideoAlbums [delete]
export const deleteUserVideoAlbums = (data) => {
  return service({
    url: '/userVideoAlbums/deleteUserVideoAlbums',
    method: 'delete',
    data
  })
}

// @Tags UserVideoAlbums
// @Summary 删除UserVideoAlbums
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除UserVideoAlbums"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /userVideoAlbums/deleteUserVideoAlbums [delete]
export const deleteUserVideoAlbumsByIds = (data) => {
  return service({
    url: '/userVideoAlbums/deleteUserVideoAlbumsByIds',
    method: 'delete',
    data
  })
}

// @Tags UserVideoAlbums
// @Summary 更新UserVideoAlbums
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserVideoAlbums true "更新UserVideoAlbums"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /userVideoAlbums/updateUserVideoAlbums [put]
export const updateUserVideoAlbums = (data) => {
  return service({
    url: '/userVideoAlbums/updateUserVideoAlbums',
    method: 'put',
    data
  })
}

// @Tags UserVideoAlbums
// @Summary 用id查询UserVideoAlbums
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.UserVideoAlbums true "用id查询UserVideoAlbums"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /userVideoAlbums/findUserVideoAlbums [get]
export const findUserVideoAlbums = (params) => {
  return service({
    url: '/userVideoAlbums/findUserVideoAlbums',
    method: 'get',
    params
  })
}

// @Tags UserVideoAlbums
// @Summary 分页获取UserVideoAlbums列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取UserVideoAlbums列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /userVideoAlbums/getUserVideoAlbumsList [get]
export const getUserVideoAlbumsList = (params) => {
  return service({
    url: '/userVideoAlbums/getUserVideoAlbumsList',
    method: 'get',
    params
  })
}

export const getUserFaceVerifyList = (params) => {
  return service({
    url: '/userVideoAlbums/getUserFaceVerifyList',
    method: 'get',
    params
  })
}
