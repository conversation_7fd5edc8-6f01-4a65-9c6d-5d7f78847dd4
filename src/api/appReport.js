import service from '@/utils/request'

// @Tags AppReport
// @Summary 创建AppReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AppReport true "创建AppReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /appReport/createAppReport [post]
export const createAppReport = (data) => {
  return service({
    url: '/appReport/createAppReport',
    method: 'post',
    data
  })
}

// @Tags AppReport
// @Summary 删除AppReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AppReport true "删除AppReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /appReport/deleteAppReport [delete]
export const deleteAppReport = (data) => {
  return service({
    url: '/appReport/deleteAppReport',
    method: 'delete',
    data
  })
}

// @Tags AppReport
// @Summary 删除AppReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除AppReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /appReport/deleteAppReport [delete]
export const deleteAppReportByIds = (data) => {
  return service({
    url: '/appReport/deleteAppReportByIds',
    method: 'delete',
    data
  })
}

// @Tags AppReport
// @Summary 更新AppReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AppReport true "更新AppReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /appReport/updateAppReport [put]
export const updateAppReport = (data) => {
  return service({
    url: '/appReport/updateAppReport',
    method: 'put',
    data
  })
}

// @Tags AppReport
// @Summary 用id查询AppReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.AppReport true "用id查询AppReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /appReport/findAppReport [get]
export const findAppReport = (params) => {
  return service({
    url: '/appReport/findAppReport',
    method: 'get',
    params
  })
}

// @Tags AppReport
// @Summary 分页获取AppReport列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取AppReport列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /appReport/getAppReportList [get]
export const getAppReportList = (params) => {
  return service({
    url: '/appReport/getAppReportList',
    method: 'get',
    params
  })
}
