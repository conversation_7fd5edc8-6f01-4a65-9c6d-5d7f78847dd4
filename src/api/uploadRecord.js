import service from '@/utils/request'

export const findUploadRecord = (params) => {
  return service({url: '/uploadRecord/findOne', method: 'get', params})
}

export const getUploadRecordList = (params) => {
  return service({url: '/uploadRecord/getPage', method: 'get', params})
}

export const createUploadRecord = (data) => {
  return service({url: '/uploadRecord/create', method: 'post', data})
}
export const batchCreateUploadRecord = (data) => {
  return service({url: '/uploadRecord/batchCreate', method: 'post', data})
}

export const deleteUploadRecord = (data) => {
  return service({url: '/uploadRecord/delete', method: 'delete', data})
}

export const deleteUploadRecordByIds = (data) => {
  return service({url: '/uploadRecord/batchDelete', method: 'delete', data})
}

export const updateUploadRecord = (data) => {
  return service({url: '/uploadRecord/update', method: 'put', data})
}
