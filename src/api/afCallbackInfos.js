import service from '@/utils/request'

export const findAfCallbackInfos = (params) => {
  return service({url: '/afCallbackInfos/findOne', method: 'get', params})
}

export const getAfCallbackInfosList = (params) => {
  return service({url: '/afCallbackInfos/getPage', method: 'get', params})
}

export const createAfCallbackInfos = (data) => {
  return service({url: '/afCallbackInfos/create', method: 'post', data})
}

export const deleteAfCallbackInfos = (data) => {
  return service({url: '/afCallbackInfos/delete', method: 'delete', data})
}

export const deleteAfCallbackInfosByIds = (data) => {
  return service({url: '/afCallbackInfos/batchDelete', method: 'delete', data})
}

export const updateAfCallbackInfos = (data) => {
  return service({url: '/afCallbackInfos/update', method: 'put', data})
}
