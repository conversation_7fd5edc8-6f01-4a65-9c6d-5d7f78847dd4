import service from '@/utils/request'

export const findAdLocationSetting = (params) => {
    return service({url: '/adLocationSetting/findOne', method: 'get', params})
}

export const getAdLocationSettingList = (params) => {
    return service({url: '/adLocationSetting/getPage', method: 'get', params})
}

export const getAdTypeList = (params) => {
    return service({url: '/adLocationSetting/getAdTypeList', method: 'get', params})
}

export const createAdLocationSetting = (data) => {
    return service({url: '/adLocationSetting/create', method: 'post', data})
}

export const deleteAdLocationSetting = (data) => {
    return service({url: '/adLocationSetting/delete', method: 'delete', data})
}

export const deleteAdLocationSettingByIds = (data) => {
    return service({url: '/adLocationSetting/batchDelete', method: 'delete', data})
}

export const updateAdLocationSetting = (data) => {
    return service({url: '/adLocationSetting/update', method: 'put', data})
}
