import service from '@/utils/request'

export const findAnchorOperationLogs = (params) => {
  return service({url: '/anchorOperationLogs/findOne', method: 'get', params})
}

export const getAnchorOperationLogsList = (params) => {
  return service({url: '/anchorOperationLogs/getPage', method: 'get', params})
}

export const createAnchorOperationLogs = (data) => {
  return service({url: '/anchorOperationLogs/create', method: 'post', data})
}

export const deleteAnchorOperationLogs = (data) => {
  return service({url: '/anchorOperationLogs/delete', method: 'delete', data})
}

export const deleteAnchorOperationLogsByIds = (data) => {
  return service({url: '/anchorOperationLogs/batchDelete', method: 'delete', data})
}

export const updateAnchorOperationLogs = (data) => {
  return service({url: '/anchorOperationLogs/update', method: 'put', data})
}
