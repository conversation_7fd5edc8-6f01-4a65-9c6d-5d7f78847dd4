import service from '@/utils/request'

// @Tags AnchorTrafficPools
// @Summary 创建AnchorTrafficPools
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorTrafficPools true "创建AnchorTrafficPools"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /anchorTrafficPools/createAnchorTrafficPools [post]
export const createAnchorTrafficPools = (data) => {
    return service({
        url: '/anchorTrafficPools/createAnchorTrafficPools',
        method: 'post',
        data
    })
}

// @Tags AnchorTrafficPools
// @Summary 删除AnchorTrafficPools
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorTrafficPools true "删除AnchorTrafficPools"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /anchorTrafficPools/deleteAnchorTrafficPools [delete]
export const deleteAnchorTrafficPools = (data) => {
    return service({
        url: '/anchorTrafficPools/deleteAnchorTrafficPools',
        method: 'delete',
        data
    })
}

// @Tags AnchorTrafficPools
// @Summary 删除AnchorTrafficPools
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除AnchorTrafficPools"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /anchorTrafficPools/deleteAnchorTrafficPools [delete]
export const deleteAnchorTrafficPoolsByIds = (data) => {
    return service({
        url: '/anchorTrafficPools/deleteAnchorTrafficPoolsByIds',
        method: 'delete',
        data
    })
}

// @Tags AnchorTrafficPools
// @Summary 更新AnchorTrafficPools
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorTrafficPools true "更新AnchorTrafficPools"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /anchorTrafficPools/updateAnchorTrafficPools [put]
export const updateAnchorTrafficPools = (data) => {
    return service({
        url: '/anchorTrafficPools/updateAnchorTrafficPools',
        method: 'put',
        data
    })
}

// @Tags AnchorTrafficPools
// @Summary 用id查询AnchorTrafficPools
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.AnchorTrafficPools true "用id查询AnchorTrafficPools"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /anchorTrafficPools/findAnchorTrafficPools [get]
export const findAnchorTrafficPools = (params) => {
    return service({
        url: '/anchorTrafficPools/findAnchorTrafficPools',
        method: 'get',
        params
    })
}

// @Tags AnchorTrafficPools
// @Summary 分页获取AnchorTrafficPools列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取AnchorTrafficPools列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /anchorTrafficPools/getAnchorTrafficPoolsList [get]
export const getAnchorTrafficPoolsList = (params) => {
    return service({
        url: '/anchorTrafficPools/getAnchorTrafficPoolsList',
        method: 'get',
        params
    })
}
