
import service from '@/utils/request'

// @Tags Inform
// @Summary 创建Inform
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Inform true "创建Inform"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /inform/createInform [post]
export const createInform = (data) => {
  return service({
    url: '/inform/createInform',
    method: 'post',
    data
  })
}

// @Tags Inform
// @Summary 删除Inform
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Inform true "删除Inform"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /inform/deleteInform [delete]
export const deleteInform = (data) => {
  return service({
    url: '/inform/deleteInform',
    method: 'delete',
    data
  })
}

// @Tags Inform
// @Summary 删除Inform
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除Inform"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /inform/deleteInform [delete]
export const deleteInformByIds = (data) => {
  return service({
    url: '/inform/deleteInformByIds',
    method: 'delete',
    data
  })
}

// @Tags Inform
// @Summary 更新Inform
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Inform true "更新Inform"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /inform/updateInform [put]
export const updateInform = (data) => {
  return service({
    url: '/inform/updateInform',
    method: 'put',
    data
  })
}

// @Tags Inform
// @Summary 用id查询Inform
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.Inform true "用id查询Inform"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /inform/findInform [get]
export const findInform = (params) => {
  return service({
    url: '/inform/findInform',
    method: 'get',
    params
  })
}

// @Tags Inform
// @Summary 分页获取Inform列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取Inform列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /inform/getInformList [get]
export const getInformList = (params) => {
  return service({
    url: '/inform/getInformList',
    method: 'get',
    params
  })
}
