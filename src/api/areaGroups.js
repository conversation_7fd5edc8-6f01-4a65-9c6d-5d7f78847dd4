import service from '@/utils/request'

export const findAreaGroups = (params) => {
  return service({ url: '/areaGroups/findAreaGroups', method: 'get', params })
}

export const getAreaGroupsList = (params) => {
  return service({ url: '/areaGroups/getAreaGroupsList', method: 'get', params })
}

export const createAreaGroups = (data) => {
  return service({ url: '/areaGroups/createAreaGroups', method: 'post', data })
}

export const deleteAreaGroups = (data) => {
  return service({ url: '/areaGroups/deleteAreaGroups', method: 'post', data })
}

export const deleteAreaGroupsByIds = (data) => {
  return service({ url: '/areaGroups/batchDelete', method: 'delete', data })
}

export const updateAreaGroups = (data) => {
  return service({ url: '/areaGroups/updateAreaGroups', method: 'post', data })
}

export const getAreaGroupsConfig = (params) => {
  return service({ url: '/areaGroups/config', method: 'get', params })
}
