
import service from '@/utils/request'

// @Tags DramaDetail
// @Summary 创建DramaDetail
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.DramaDetail true "创建DramaDetail"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /dramaDetail/createDramaDetail [post]
export const createDramaDetail = (data) => {
  return service({
    url: '/dramaDetail/createDramaDetail',
    method: 'post',
    data
  })
}

// @Tags DramaDetail
// @Summary 删除DramaDetail
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.DramaDetail true "删除DramaDetail"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /dramaDetail/deleteDramaDetail [delete]
export const deleteDramaDetail = (data) => {
  return service({
    url: '/dramaDetail/deleteDramaDetail',
    method: 'delete',
    data
  })
}

// @Tags DramaDetail
// @Summary 删除DramaDetail
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除DramaDetail"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /dramaDetail/deleteDramaDetail [delete]
export const deleteDramaDetailByIds = (data) => {
  return service({
    url: '/dramaDetail/deleteDramaDetailByIds',
    method: 'delete',
    data
  })
}

// @Tags DramaDetail
// @Summary 更新DramaDetail
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.DramaDetail true "更新DramaDetail"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /dramaDetail/updateDramaDetail [put]
export const updateDramaDetail = (data) => {
  return service({
    url: '/dramaDetail/updateDramaDetail',
    method: 'put',
    data
  })
}

// @Tags DramaDetail
// @Summary 用id查询DramaDetail
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.DramaDetail true "用id查询DramaDetail"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /dramaDetail/findDramaDetail [get]
export const findDramaDetail = (params) => {
  return service({
    url: '/dramaDetail/findDramaDetail',
    method: 'get',
    params
  })
}

// @Tags DramaDetail
// @Summary 分页获取DramaDetail列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取DramaDetail列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /dramaDetail/getDramaDetailList [get]
export const getDramaDetailList = (params) => {
  return service({
    url: '/dramaDetail/getDramaDetailList',
    method: 'get',
    params
  })
}

export const getDramaDetailTree = (params) => {
  return service({
    url: '/dramaDetail/getDramaDetailTree',
    method: 'get',
    params
  })
}

//
export const getDramaDetailFindAll = (params) => {
  return service({
    url: '/dramaDetail/getDramaDetailFindAll',
    method: 'get',
    params
  })
}
