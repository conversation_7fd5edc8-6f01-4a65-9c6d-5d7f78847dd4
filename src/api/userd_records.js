import service from '@/utils/request'

// @Tags UserdRecords
// @Summary 创建UserdRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserdRecords true "创建UserdRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /userdRecords/createUserdRecords [post]
export const createUserdRecords = (data) => {
    return service({
        url: '/userdRecords/createUserdRecords',
        method: 'post',
        data
    })
}

// @Tags UserdRecords
// @Summary 删除UserdRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserdRecords true "删除UserdRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /userdRecords/deleteUserdRecords [delete]
export const deleteUserdRecords = (data) => {
    return service({
        url: '/userdRecords/deleteUserdRecords',
        method: 'delete',
        data
    })
}

// @Tags UserdRecords
// @Summary 删除UserdRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除UserdRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /userdRecords/deleteUserdRecords [delete]
export const deleteUserdRecordsByIds = (data) => {
    return service({
        url: '/userdRecords/deleteUserdRecordsByIds',
        method: 'delete',
        data
    })
}

// @Tags UserdRecords
// @Summary 更新UserdRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserdRecords true "更新UserdRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /userdRecords/updateUserdRecords [put]
export const updateUserdRecords = (data) => {
    return service({
        url: '/userdRecords/updateUserdRecords',
        method: 'put',
        data
    })
}

// @Tags UserdRecords
// @Summary 用id查询UserdRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.UserdRecords true "用id查询UserdRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /userdRecords/findUserdRecords [get]
export const findUserdRecords = (params) => {
    return service({
        url: '/userdRecords/findUserdRecords',
        method: 'get',
        params
    })
}

// @Tags UserdRecords
// @Summary 分页获取UserdRecords列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取UserdRecords列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /userdRecords/getUserdRecordsList [get]
export const getUserdRecordsList = (params) => {
    return service({
        url: '/userdRecords/getUserdRecordsList',
        method: 'get',
        params
    })
}
