import service from '@/utils/request'

// @Tags TradeRecords
// @Summary 创建TradeRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.TradeRecords true "创建TradeRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /tradeRecords/createTradeRecords [post]
export const createTradeRecords = (data) => {
  return service({
    url: '/tradeRecords/createTradeRecords',
    method: 'post',
    data
  })
}

// @Tags TradeRecords
// @Summary 删除TradeRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.TradeRecords true "删除TradeRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /tradeRecords/deleteTradeRecords [delete]
export const deleteTradeRecords = (data) => {
  return service({
    url: '/tradeRecords/deleteTradeRecords',
    method: 'delete',
    data
  })
}

// @Tags TradeRecords
// @Summary 删除TradeRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除TradeRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /tradeRecords/deleteTradeRecords [delete]
export const deleteTradeRecordsByIds = (data) => {
  return service({
    url: '/tradeRecords/deleteTradeRecordsByIds',
    method: 'delete',
    data
  })
}

// @Tags TradeRecords
// @Summary 更新TradeRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.TradeRecords true "更新TradeRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /tradeRecords/updateTradeRecords [put]
export const updateTradeRecords = (data) => {
  return service({
    url: '/tradeRecords/updateTradeRecords',
    method: 'put',
    data
  })
}

// @Tags TradeRecords
// @Summary 用id查询TradeRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.TradeRecords true "用id查询TradeRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /tradeRecords/findTradeRecords [get]
export const findTradeRecords = (params) => {
  return service({
    url: '/tradeRecords/findTradeRecords',
    method: 'get',
    params
  })
}

// @Tags TradeRecords
// @Summary 分页获取TradeRecords列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取TradeRecords列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /tradeRecords/getTradeRecordsList [get]
export const getTradeRecordsList = (params) => {
  return service({
    url: '/tradeRecords/getTradeRecordsList',
    method: 'get',
    params
  })
}
export const getUserTradeRecordsList = (params) => {
  return service({
    url: '/tradeRecords/getUserTradeRecordsList',
    method: 'get',
    params
  })
}
export const getAnchorTradeRecordsList = (params) => {
  return service({
    url: '/tradeRecords/getAnchorTradeRecordsList',
    method: 'get',
    params
  })
}
export const getTradeTypeDict = (params) => {
  return service({
    url: '/tradeRecords/getTradeTypeDict',
    method: 'get',
    params
  })
}
