import service from '@/utils/request'

export const syncAppGifts = (data) => {
  return service({
    url: '/gifts/syncAppGifts',
    method: 'post',
    data
  })
}

// @Tags Gifts
// @Summary 创建Gifts
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Gifts true "创建Gifts"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /gifts/createGifts [post]
export const createGifts = (data) => {
  return service({
    url: '/gifts/createGifts',
    method: 'post',
    data
  })
}

// @Tags Gifts
// @Summary 删除Gifts
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Gifts true "删除Gifts"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /gifts/deleteGifts [delete]
export const deleteGifts = (data) => {
  return service({
    url: '/gifts/deleteGifts',
    method: 'delete',
    data
  })
}

// @Tags Gifts
// @Summary 删除Gifts
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除Gifts"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /gifts/deleteGifts [delete]
export const deleteGiftsByIds = (data) => {
  return service({
    url: '/gifts/deleteGiftsByIds',
    method: 'delete',
    data
  })
}

// @Tags Gifts
// @Summary 更新Gifts
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Gifts true "更新Gifts"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /gifts/updateGifts [put]
export const updateGifts = (data) => {
  return service({
    url: '/gifts/updateGifts',
    method: 'post',
    data
  })
}

// @Tags Gifts
// @Summary 用id查询Gifts
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.Gifts true "用id查询Gifts"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /gifts/findGifts [get]
export const findGifts = (params) => {
  return service({
    url: '/gifts/findGifts',
    method: 'get',
    params
  })
}

// @Tags Gifts
// @Summary 分页获取Gifts列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取Gifts列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /gifts/getGiftsList [get]
export const getGiftsList = (params) => {
  return service({
    url: '/gifts/getGiftsList',
    method: 'get',
    params
  })
}
