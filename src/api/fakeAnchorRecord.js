import service from '@/utils/request'

export const findFakeAnchorRecord = (params) => {
    return service({url: '/fakeAnchorRecord/findOne', method: 'get', params})
}

export const getFakeAnchorRecordList = (params) => {
    return service({url: '/fakeAnchorRecord/getPage', method: 'get', params})
}

export const createFakeAnchorRecord = (data) => {
    return service({url: '/fakeAnchorRecord/create', method: 'post', data})
}

export const deleteFakeAnchorRecord = (data) => {
    return service({url: '/fakeAnchorRecord/delete', method: 'delete', data})
}

export const deleteFakeAnchorRecordByIds = (data) => {
    return service({url: '/fakeAnchorRecord/batchDelete', method: 'delete', data})
}

export const updateFakeAnchorRecord = (data) => {
    return service({url: '/fakeAnchorRecord/update', method: 'put', data})
}
