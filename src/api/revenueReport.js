import service from '@/utils/request'

// @Tags RevenueReport
// @Summary 创建RevenueReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.RevenueReport true "创建RevenueReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /revenueReport/createRevenueReport [post]
export const createRevenueReport = (data) => {
  return service({
    url: '/revenueReport/createRevenueReport',
    method: 'post',
    data
  })
}

// @Tags RevenueReport
// @Summary 删除RevenueReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.RevenueReport true "删除RevenueReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /revenueReport/deleteRevenueReport [delete]
export const deleteRevenueReport = (data) => {
  return service({
    url: '/revenueReport/deleteRevenueReport',
    method: 'delete',
    data
  })
}

// @Tags RevenueReport
// @Summary 删除RevenueReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除RevenueReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /revenueReport/deleteRevenueReport [delete]
export const deleteRevenueReportByIds = (data) => {
  return service({
    url: '/revenueReport/deleteRevenueReportByIds',
    method: 'delete',
    data
  })
}

// @Tags RevenueReport
// @Summary 更新RevenueReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.RevenueReport true "更新RevenueReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /revenueReport/updateRevenueReport [put]
export const updateRevenueReport = (data) => {
  return service({
    url: '/revenueReport/updateRevenueReport',
    method: 'put',
    data
  })
}

// @Tags RevenueReport
// @Summary 用id查询RevenueReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.RevenueReport true "用id查询RevenueReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /revenueReport/findRevenueReport [get]
export const findRevenueReport = (params) => {
  return service({
    url: '/revenueReport/findRevenueReport',
    method: 'get',
    params
  })
}

// @Tags RevenueReport
// @Summary 分页获取RevenueReport列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取RevenueReport列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /revenueReport/getRevenueReportList [get]
export const getRevenueReportList = (params) => {
  return service({
    url: '/revenueReport/getRevenueReportList',
    method: 'get',
    params
  })
}
