
import service from '@/utils/request'

// @Tags AnchorDailyStatistics
// @Summary 创建AnchorDailyStatistics
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorDailyStatistics true "创建AnchorDailyStatistics"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /anchorDailyStatistics/createAnchorDailyStatistics [post]
export const createAnchorDailyStatistics = (data) => {
  return service({
    url: '/anchorDailyStatistics/createAnchorDailyStatistics',
    method: 'post',
    data
  })
}

// @Tags AnchorDailyStatistics
// @Summary 删除AnchorDailyStatistics
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorDailyStatistics true "删除AnchorDailyStatistics"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /anchorDailyStatistics/deleteAnchorDailyStatistics [delete]
export const deleteAnchorDailyStatistics = (data) => {
  return service({
    url: '/anchorDailyStatistics/deleteAnchorDailyStatistics',
    method: 'delete',
    data
  })
}

// @Tags AnchorDailyStatistics
// @Summary 删除AnchorDailyStatistics
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除AnchorDailyStatistics"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /anchorDailyStatistics/deleteAnchorDailyStatistics [delete]
export const deleteAnchorDailyStatisticsByIds = (data) => {
  return service({
    url: '/anchorDailyStatistics/deleteAnchorDailyStatisticsByIds',
    method: 'delete',
    data
  })
}

// @Tags AnchorDailyStatistics
// @Summary 更新AnchorDailyStatistics
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorDailyStatistics true "更新AnchorDailyStatistics"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /anchorDailyStatistics/updateAnchorDailyStatistics [put]
export const updateAnchorDailyStatistics = (data) => {
  return service({
    url: '/anchorDailyStatistics/updateAnchorDailyStatistics',
    method: 'put',
    data
  })
}

// @Tags AnchorDailyStatistics
// @Summary 用id查询AnchorDailyStatistics
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.AnchorDailyStatistics true "用id查询AnchorDailyStatistics"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /anchorDailyStatistics/findAnchorDailyStatistics [get]
export const findAnchorDailyStatistics = (params) => {
  return service({
    url: '/anchorDailyStatistics/findAnchorDailyStatistics',
    method: 'get',
    params
  })
}

// @Tags AnchorDailyStatistics
// @Summary 分页获取AnchorDailyStatistics列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取AnchorDailyStatistics列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /anchorDailyStatistics/getAnchorDailyStatisticsList [get]
export const getAnchorDailyStatisticsList = (params) => {
  return service({
    url: '/anchorDailyStatistics/getAnchorDailyStatisticsList',
    method: 'get',
    params
  })
}
