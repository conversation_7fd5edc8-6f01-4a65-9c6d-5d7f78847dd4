import service from '@/utils/request'

export const findGameLists = (params) => {
    return service({url: '/gameLists/findOne', method: 'get', params})
}
export const findAllGameLists = (params) => {
    return service({url: '/gameLists/findAll', method: 'get', params})
}

export const getGameListsList = (params) => {
    return service({url: '/gameLists/getPage', method: 'get', params})
}

export const createGameLists = (data) => {
    return service({url: '/gameLists/create', method: 'post', data})
}

export const deleteGameLists = (data) => {
    return service({url: '/gameLists/delete', method: 'delete', data})
}

export const deleteGameListsByIds = (data) => {
    return service({url: '/gameLists/batchDelete', method: 'delete', data})
}

export const updateGameLists = (data) => {
    return service({url: '/gameLists/update', method: 'put', data})
}
