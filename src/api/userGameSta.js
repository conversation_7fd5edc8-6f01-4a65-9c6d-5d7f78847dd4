import service from '@/utils/request'

export const findUserGameSta = (params) => {
    return service({url: '/userGameSta/findOne', method: 'get', params})
}

export const getUserGameStaList = (params) => {
    return service({url: '/userGameSta/getPage', method: 'get', params})
}
export const getDayAll = (params) => {
    return service({url: '/userGameSta/getDayAll', method: 'get', params})
}

export const getUserGameStaSum = (params) => {
    return service({url: '/userGameSta/getSum', method: 'get', params})
}

export const createUserGameSta = (data) => {
    return service({url: '/userGameSta/create', method: 'post', data})
}

export const deleteUserGameSta = (data) => {
    return service({url: '/userGameSta/delete', method: 'delete', data})
}

export const deleteUserGameStaByIds = (data) => {
    return service({url: '/userGameSta/batchDelete', method: 'delete', data})
}

export const updateUserGameSta = (data) => {
    return service({url: '/userGameSta/update', method: 'put', data})
}
