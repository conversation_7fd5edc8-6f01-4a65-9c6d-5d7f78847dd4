import service from '@/utils/request'

// @Tags CallReport
// @Summary 创建CallReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.CallReport true "创建CallReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /callReport/createCallReport [post]
export const createCallReport = (data) => {
  return service({
    url: '/callReport/createCallReport',
    method: 'post',
    data
  })
}

// @Tags CallReport
// @Summary 删除CallReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.CallReport true "删除CallReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /callReport/deleteCallReport [delete]
export const deleteCallReport = (data) => {
  return service({
    url: '/callReport/deleteCallReport',
    method: 'delete',
    data
  })
}

// @Tags CallReport
// @Summary 删除CallReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除CallReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /callReport/deleteCallReport [delete]
export const deleteCallReportByIds = (data) => {
  return service({
    url: '/callReport/deleteCallReportByIds',
    method: 'delete',
    data
  })
}

// @Tags CallReport
// @Summary 更新CallReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.CallReport true "更新CallReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /callReport/updateCallReport [put]
export const updateCallReport = (data) => {
  return service({
    url: '/callReport/updateCallReport',
    method: 'put',
    data
  })
}

// @Tags CallReport
// @Summary 用id查询CallReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.CallReport true "用id查询CallReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /callReport/findCallReport [get]
export const findCallReport = (params) => {
  return service({
    url: '/callReport/findCallReport',
    method: 'get',
    params
  })
}

// @Tags CallReport
// @Summary 分页获取CallReport列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取CallReport列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /callReport/getCallReportList [get]
export const getCallReportList = (params) => {
  return service({
    url: '/callReport/getCallReportList',
    method: 'get',
    params
  })
}
