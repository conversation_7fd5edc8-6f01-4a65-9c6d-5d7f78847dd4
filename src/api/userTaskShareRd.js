import service from '@/utils/request'

export const findUserTaskShareRd = (params) => {
  return service({url: '/userTaskShareRd/findOne', method: 'get', params})
}

export const getUserTaskShareRdList = (params) => {
  return service({url: '/userTaskShareRd/getPage', method: 'get', params})
}
export const taskShareReviewStateMapList = (params) => {
  return service({url: '/userTaskShareRd/taskShareReviewStateMapList', method: 'get', params})
}

export const createUserTaskShareRd = (data) => {
  return service({url: '/userTaskShareRd/create', method: 'post', data})
}

export const deleteUserTaskShareRd = (data) => {
  return service({url: '/userTaskShareRd/delete', method: 'delete', data})
}

export const deleteUserTaskShareRdByIds = (data) => {
  return service({url: '/userTaskShareRd/batchDelete', method: 'delete', data})
}

export const updateUserTaskShareRd = (data) => {
  return service({url: '/userTaskShareRd/update', method: 'put', data})
}
