import service from '@/utils/request'

export const findWithdrawalAccountGcash = (params) => {
    return service({url: '/withdrawalAccountGcash/findOne', method: 'get', params})
}

export const getWithdrawalAccountGcashList = (params) => {
    return service({url: '/withdrawalAccountGcash/getPage', method: 'get', params})
}

export const createWithdrawalAccountGcash = (data) => {
    return service({url: '/withdrawalAccountGcash/create', method: 'post', data})
}

export const saveWithdrawalAccountGcash = (data) => {
    return service({url: '/withdrawalAccountGcash/save', method: 'post', data})
}

export const deleteWithdrawalAccountGcash = (data) => {
    return service({url: '/withdrawalAccountGcash/delete', method: 'delete', data})
}

export const deleteWithdrawalAccountGcashByIds = (data) => {
    return service({url: '/withdrawalAccountGcash/batchDelete', method: 'delete', data})
}

export const updateWithdrawalAccountGcash = (data) => {
    return service({url: '/withdrawalAccountGcash/update', method: 'put', data})
}
