import service from '@/utils/request'

export const findStrategyVideoUser = (params) => {
  return service({url: '/strategyVideoUser/findOne', method: 'get', params})
}

export const getStrategyVideoUserList = (params) => {
  return service({url: '/strategyVideoUser/getPage', method: 'get', params})
}

export const createStrategyVideoUser = (data) => {
  return service({url: '/strategyVideoUser/create', method: 'post', data})
}

export const deleteStrategyVideoUser = (data) => {
  return service({url: '/strategyVideoUser/delete', method: 'delete', data})
}

export const deleteStrategyVideoUserByIds = (data) => {
  return service({url: '/strategyVideoUser/batchDelete', method: 'delete', data})
}

export const updateStrategyVideoUser = (data) => {
  return service({url: '/strategyVideoUser/update', method: 'put', data})
}
