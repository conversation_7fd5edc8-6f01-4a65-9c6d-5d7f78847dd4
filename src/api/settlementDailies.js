
import service from '@/utils/request'

// @Tags SettlementDailies
// @Summary 创建SettlementDailies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SettlementDailies true "创建SettlementDailies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /settlementDailies/createSettlementDailies [post]
export const createSettlementDailies = (data) => {
  return service({
    url: '/settlementDailies/createSettlementDailies',
    method: 'post',
    data
  })
}

// @Tags SettlementDailies
// @Summary 删除SettlementDailies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SettlementDailies true "删除SettlementDailies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /settlementDailies/deleteSettlementDailies [delete]
export const deleteSettlementDailies = (data) => {
  return service({
    url: '/settlementDailies/deleteSettlementDailies',
    method: 'delete',
    data
  })
}

// @Tags SettlementDailies
// @Summary 删除SettlementDailies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除SettlementDailies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /settlementDailies/deleteSettlementDailies [delete]
export const deleteSettlementDailiesByIds = (data) => {
  return service({
    url: '/settlementDailies/deleteSettlementDailiesByIds',
    method: 'delete',
    data
  })
}

// @Tags SettlementDailies
// @Summary 更新SettlementDailies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SettlementDailies true "更新SettlementDailies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /settlementDailies/updateSettlementDailies [put]
export const updateSettlementDailies = (data) => {
  return service({
    url: '/settlementDailies/updateSettlementDailies',
    method: 'put',
    data
  })
}

// @Tags SettlementDailies
// @Summary 用id查询SettlementDailies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.SettlementDailies true "用id查询SettlementDailies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /settlementDailies/findSettlementDailies [get]
export const findSettlementDailies = (params) => {
  return service({
    url: '/settlementDailies/findSettlementDailies',
    method: 'get',
    params
  })
}

// @Tags SettlementDailies
// @Summary 分页获取SettlementDailies列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取SettlementDailies列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /settlementDailies/getSettlementDailiesList [get]
export const getSettlementDailiesList = (params) => {
  return service({
    url: '/settlementDailies/getSettlementDailiesList',
    method: 'get',
    params
  })
}
