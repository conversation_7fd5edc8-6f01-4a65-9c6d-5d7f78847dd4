import service from '@/utils/request'

export const findAnchorCallVideos = (params) => {
  return service({url: '/anchorCallVideos/findOne', method: 'get', params})
}

export const getAnchorCallVideosList = (params) => {
  return service({url: '/anchorCallVideos/getPage', method: 'get', params})
}

export const createAnchorCallVideos = (data) => {
  return service({url: '/anchorCallVideos/create', method: 'post', data})
}

export const deleteAnchorCallVideos = (data) => {
  return service({url: '/anchorCallVideos/delete', method: 'delete', data})
}

export const deleteAnchorCallVideosByIds = (data) => {
  return service({url: '/anchorCallVideos/batchDelete', method: 'delete', data})
}

export const updateAnchorCallVideos = (data) => {
  return service({url: '/anchorCallVideos/update', method: 'put', data})
}
