import service from '@/utils/request'

export const findUserStrategyRd = (params) => {
  return service({url: '/userStrategyRd/findOne', method: 'get', params})
} 

export const getUserStrategyRdList = (params) => { 
  return service({url: '/userStrategyRd/getPage', method: 'get', params})
}

export const createUserStrategyRd = (data) => {
  return service({url: '/userStrategyRd/create', method: 'post', data})
}

export const deleteUserStrategyRd = (data) => { 
  return service({url: '/userStrategyRd/delete', method: 'delete', data})
}

export const deleteUserStrategyRdByIds = (data) => {
  return service({url: '/userStrategyRd/batchDelete', method: 'delete', data})
}

export const updateUserStrategyRd = (data) => {
  return service({url: '/userStrategyRd/update', method: 'put', data})
}

export const getUserNameList = (data) => {
  return service({url: '/user/getUserNameList', method: 'get', data})
}
