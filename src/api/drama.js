
import service from '@/utils/request'

// @Tags Drama
// @Summary 创建Drama
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Drama true "创建Drama"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /drama/createDrama [post]
export const createDrama = (data) => {
  return service({
    url: '/drama/createDrama',
    method: 'post',
    data
  })
}

// @Tags Drama
// @Summary 删除Drama
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Drama true "删除Drama"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /drama/deleteDrama [delete]
export const deleteDrama = (data) => {
  return service({
    url: '/drama/deleteDrama',
    method: 'delete',
    data
  })
}

// @Tags Drama
// @Summary 删除Drama
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除Drama"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /drama/deleteDrama [delete]
export const deleteDramaByIds = (data) => {
  return service({
    url: '/drama/deleteDramaByIds',
    method: 'delete',
    data
  })
}

// @Tags Drama
// @Summary 更新Drama
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Drama true "更新Drama"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /drama/updateDrama [put]
export const updateDrama = (data) => {
  return service({
    url: '/drama/updateDrama',
    method: 'put',
    data
  })
}

// @Tags Drama
// @Summary 用id查询Drama
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.Drama true "用id查询Drama"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /drama/findDrama [get]
export const findDrama = (params) => {
  return service({
    url: '/drama/findDrama',
    method: 'get',
    params
  })
}

// @Tags Drama
// @Summary 分页获取Drama列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取Drama列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /drama/getDramaList [get]
export const getDramaList = (params) => {
  return service({
    url: '/drama/getDramaList',
    method: 'get',
    params
  })
}
