
import service from '@/utils/request'

// @Tags SystemSetting
// @Summary 创建SystemSetting
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SystemSetting true "创建SystemSetting"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /systemSetting/createSystemSetting [post]
export const createSystemSetting = (data) => {
  return service({
    url: '/systemSetting/createSystemSetting',
    method: 'post',
    data
  })
}

// @Tags SystemSetting
// @Summary 删除SystemSetting
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SystemSetting true "删除SystemSetting"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /systemSetting/deleteSystemSetting [delete]
export const deleteSystemSetting = (data) => {
  return service({
    url: '/systemSetting/deleteSystemSetting',
    method: 'delete',
    data
  })
}

// @Tags SystemSetting
// @Summary 删除SystemSetting
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除SystemSetting"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /systemSetting/deleteSystemSetting [delete]
export const deleteSystemSettingByIds = (data) => {
  return service({
    url: '/systemSetting/deleteSystemSettingByIds',
    method: 'delete',
    data
  })
}

// @Tags SystemSetting
// @Summary 更新SystemSetting
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SystemSetting true "更新SystemSetting"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /systemSetting/updateSystemSetting [put]
export const updateSystemSetting = (data) => {
  return service({
    url: '/systemSetting/updateSystemSetting',
    method: 'put',
    data
  })
}

// @Tags SystemSetting
// @Summary 用id查询SystemSetting
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.SystemSetting true "用id查询SystemSetting"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /systemSetting/findSystemSetting [get]
export const findSystemSetting = (params) => {
  return service({
    url: '/systemSetting/findSystemSetting',
    method: 'get',
    params
  })
}

// @Tags SystemSetting
// @Summary 分页获取SystemSetting列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取SystemSetting列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /systemSetting/getSystemSettingList [get]
export const getSystemSettingList = (params) => {
  return service({
    url: '/systemSetting/getSystemSettingList',
    method: 'get',
    params
  })
}
