
import service from '@/utils/request'

// @Tags ConnectedReports
// @Summary 创建ConnectedReports
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ConnectedReports true "创建ConnectedReports"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /connectedReports/createConnectedReports [post]
export const createConnectedReports = (data) => {
  return service({
    url: '/connectedReports/createConnectedReports',
    method: 'post',
    data
  })
}

// @Tags ConnectedReports
// @Summary 删除ConnectedReports
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ConnectedReports true "删除ConnectedReports"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /connectedReports/deleteConnectedReports [delete]
export const deleteConnectedReports = (data) => {
  return service({
    url: '/connectedReports/deleteConnectedReports',
    method: 'delete',
    data
  })
}

// @Tags ConnectedReports
// @Summary 删除ConnectedReports
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除ConnectedReports"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /connectedReports/deleteConnectedReports [delete]
export const deleteConnectedReportsByIds = (data) => {
  return service({
    url: '/connectedReports/deleteConnectedReportsByIds',
    method: 'delete',
    data
  })
}

// @Tags ConnectedReports
// @Summary 更新ConnectedReports
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ConnectedReports true "更新ConnectedReports"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /connectedReports/updateConnectedReports [put]
export const updateConnectedReports = (data) => {
  return service({
    url: '/connectedReports/updateConnectedReports',
    method: 'put',
    data
  })
}

// @Tags ConnectedReports
// @Summary 用id查询ConnectedReports
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.ConnectedReports true "用id查询ConnectedReports"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /connectedReports/findConnectedReports [get]
export const findConnectedReports = (params) => {
  return service({
    url: '/connectedReports/findConnectedReports',
    method: 'get',
    params
  })
}

// @Tags ConnectedReports
// @Summary 分页获取ConnectedReports列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取ConnectedReports列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /connectedReports/getConnectedReportsList [get]
export const getConnectedReportsList = (params) => {
  return service({
    url: '/connectedReports/getConnectedReportsList',
    method: 'get',
    params
  })
}

export const getList = (params) => {
  return service({
    url: '/connectedReports/list',
    method: 'get',
    params
  })
}

export const getRealList = (params) => {
  return service({
    url: '/connectedReports/realList',
    method: 'get',
    params
  })
}

// @Tags ConnectedReports2
// @Summary 分页获取ConnectedReports列表2
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取ConnectedReports列表2"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /connectedReports/getConnectedReportsList [get]
export const getConnectedReportsList2 = (params) => {
  return service({
    url: '/connectedReports/getConnectedReportsList2',
    method: 'get',
    params
  })
}

export const downloadM3u8File = (params) => {
  return service({
    url: '/connectedReports/downloadM3u8File',
    method: 'get',
    params
  })
}

export const getRecentAnchorStaticData = (params) => {
  return service({
    url: '/connectedReports/getRecentAnchorStaticData',
    method: 'get',
    params
  })
}


//获取警告详情
export const getWarningInfos = (params) => {
  return service({
    url: '/anchorWarningInfos/getById',
    method: 'get',
    params
  })
}

//新增警告
export const addWarningInfos = (data) => {
  return service({
    url: '/anchorWarningInfos/create',
    method: 'post',
    data
  })
}

//更新警告
export const updateWarningInfos = (data) => {
  return service({
    url: '/anchorWarningInfos/update',
    method: 'put',
    data
  })
}


