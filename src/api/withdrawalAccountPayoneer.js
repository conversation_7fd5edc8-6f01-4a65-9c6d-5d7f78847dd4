import service from '@/utils/request'

export const findWithdrawalAccountPayoneer = (params) => {
    return service({url: '/withdrawalAccountPayoneer/findOne', method: 'get', params})
}

export const getWithdrawalAccountPayoneerList = (params) => {
    return service({url: '/withdrawalAccountPayoneer/getPage', method: 'get', params})
}

export const createWithdrawalAccountPayoneer = (data) => {
    return service({url: '/withdrawalAccountPayoneer/create', method: 'post', data})
}
export const saveWithdrawalAccountPayoneer = (data) => {
    return service({url: '/withdrawalAccountPayoneer/save', method: 'post', data})
}

export const deleteWithdrawalAccountPayoneer = (data) => {
    return service({url: '/withdrawalAccountPayoneer/delete', method: 'delete', data})
}

export const deleteWithdrawalAccountPayoneerByIds = (data) => {
    return service({url: '/withdrawalAccountPayoneer/batchDelete', method: 'delete', data})
}

export const updateWithdrawalAccountPayoneer = (data) => {
    return service({url: '/withdrawalAccountPayoneer/update', method: 'put', data})
}
