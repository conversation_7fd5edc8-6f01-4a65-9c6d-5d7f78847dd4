import service from '@/utils/request'

export const findDstMsgRecords = (params) => {
  return service({url: '/dstMsgRecords/findOne', method: 'get', params})
}

export const getDstMsgRecordsList = (params) => {
  return service({url: '/dstMsgRecords/getPage', method: 'get', params})
}

export const createDstMsgRecords = (data) => {
  return service({url: '/dstMsgRecords/create', method: 'post', data})
}

export const deleteDstMsgRecords = (data) => {
  return service({url: '/dstMsgRecords/delete', method: 'delete', data})
}

export const deleteDstMsgRecordsByIds = (data) => {
  return service({url: '/dstMsgRecords/batchDelete', method: 'delete', data})
}

export const updateDstMsgRecords = (data) => {
  return service({url: '/dstMsgRecords/update', method: 'put', data})
}
