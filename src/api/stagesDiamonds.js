import service from '@/utils/request'

// @Tags StagesDiamonds
// @Summary 创建StagesDiamonds
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.StagesDiamonds true "创建StagesDiamonds"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /stagesDiamonds/createStagesDiamonds [post]
export const createStagesDiamonds = (data) => {
  return service({
    url: '/stagesDiamonds/createStagesDiamonds',
    method: 'post',
    data
  })
}

// @Tags StagesDiamonds
// @Summary 删除StagesDiamonds
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.StagesDiamonds true "删除StagesDiamonds"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /stagesDiamonds/deleteStagesDiamonds [delete]
export const deleteStagesDiamonds = (data) => {
  return service({
    url: '/stagesDiamonds/deleteStagesDiamonds',
    method: 'post',
    data
  })
}

// @Tags StagesDiamonds
// @Summary 删除StagesDiamonds
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除StagesDiamonds"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /stagesDiamonds/deleteStagesDiamonds [delete]
export const deleteStagesDiamondsByIds = (data) => {
  return service({
    url: '/stagesDiamonds/deleteStagesDiamondsByIds',
    method: 'post',
    data
  })
}

// @Tags StagesDiamonds
// @Summary 更新StagesDiamonds
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.StagesDiamonds true "更新StagesDiamonds"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /stagesDiamonds/updateStagesDiamonds [put]
export const updateStagesDiamonds = (data) => {
  return service({
    url: '/stagesDiamonds/updateStagesDiamonds',
    method: 'post',
    data
  })
}

// @Tags StagesDiamonds
// @Summary 用id查询StagesDiamonds
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.StagesDiamonds true "用id查询StagesDiamonds"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /stagesDiamonds/findStagesDiamonds [get]
export const findStagesDiamonds = (params) => {
  return service({
    url: '/stagesDiamonds/findStagesDiamonds',
    method: 'get',
    params
  })
}

// @Tags StagesDiamonds
// @Summary 分页获取StagesDiamonds列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取StagesDiamonds列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /stagesDiamonds/getStagesDiamondsList [get]
export const getStagesDiamondsList = (params) => {
  return service({
    url: '/stagesDiamonds/getStagesDiamondsList',
    method: 'get',
    params
  })
}
