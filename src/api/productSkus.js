import service from '@/utils/request'

export const syncProductSkus = (data) => {
  return service({
    url: '/productSkus/syncProductSkus',
    method: 'post',
    data
  })
}

// @Tags ProductSkus
// @Summary 创建ProductSkus
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ProductSkus true "创建ProductSkus"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /productSkus/createProductSkus [post]
export const createProductSkus = (data) => {
  return service({
    url: '/productSkus/createProductSkus',
    method: 'post',
    data
  })
}

// @Tags ProductSkus
// @Summary 删除ProductSkus
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ProductSkus true "删除ProductSkus"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /productSkus/deleteProductSkus [delete]
export const deleteProductSkus = (data) => {
  return service({
    url: '/productSkus/deleteProductSkus',
    method: 'delete',
    data
  })
}

// @Tags ProductSkus
// @Summary 删除ProductSkus
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除ProductSkus"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /productSkus/deleteProductSkus [delete]
export const deleteProductSkusByIds = (data) => {
  return service({
    url: '/productSkus/deleteProductSkusByIds',
    method: 'delete',
    data
  })
}

// @Tags ProductSkus
// @Summary 更新ProductSkus
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ProductSkus true "更新ProductSkus"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /productSkus/updateProductSkus [put]
export const updateProductSkus = (data) => {
  return service({
    url: '/productSkus/updateProductSkus',
    method: 'post',
    data
  })
}

// @Tags ProductSkus
// @Summary 用id查询ProductSkus
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.ProductSkus true "用id查询ProductSkus"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /productSkus/findProductSkus [get]
export const findProductSkus = (params) => {
  return service({
    url: '/productSkus/findProductSkus',
    method: 'get',
    params
  })
}

// @Tags ProductSkus
// @Summary 分页获取ProductSkus列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取ProductSkus列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /productSkus/getProductSkusList [get]
export const getProductSkusList = (params) => {
  return service({
    url: '/productSkus/getProductSkusList',
    method: 'get',
    params
  })
}
