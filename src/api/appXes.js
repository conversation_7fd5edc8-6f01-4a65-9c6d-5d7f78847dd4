import service from '@/utils/request'

// @Tags AppXes
// @Summary 创建AppXes
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AppXes true "创建AppXes"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /appXes/createAppXes [post]
export const createAppXes = (data) => {
  return service({
    url: '/appXes/createAppXes',
    method: 'post',
    data
  })
}

// @Tags AppXes
// @Summary 删除AppXes
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AppXes true "删除AppXes"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /appXes/deleteAppXes [delete]
export const deleteAppXes = (data) => {
  return service({
    url: '/appXes/deleteAppXes',
    method: 'delete',
    data
  })
}

// @Tags AppXes
// @Summary 删除AppXes
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除AppXes"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /appXes/deleteAppXes [delete]
export const deleteAppXesByIds = (data) => {
  return service({
    url: '/appXes/deleteAppXesByIds',
    method: 'delete',
    data
  })
}

// @Tags AppXes
// @Summary 更新AppXes
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AppXes true "更新AppXes"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /appXes/updateAppXes [put]
export const updateAppXes = (data) => {
  return service({
    url: '/appXes/updateAppXes',
    method: 'put',
    data
  })
}

// @Tags AppXes
// @Summary 用id查询AppXes
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.AppXes true "用id查询AppXes"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /appXes/findAppXes [get]
export const findAppXes = (params) => {
  return service({
    url: '/appXes/findAppXes',
    method: 'get',
    params
  })
}

// @Tags AppXes
// @Summary 分页获取AppXes列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取AppXes列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /appXes/getAppXesList [get]
export const getAppXesList = (params) => {
  return service({
    url: '/appXes/getAppXesList',
    method: 'get',
    params
  })
}

export const getAppSwitch = (params) => {
  return service({
    url: '/appXes/getAppSwitch',
    method: 'get',
    params
  })
}

export const uptAppSwitch = (data) => {
  return service({
    url: '/appXes/updateAppSwitch',
    method: 'put',
    data
  })
}

