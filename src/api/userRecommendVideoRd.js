import service from '@/utils/request'

export const findUserRecommendVideoRd = (params) => {
  return service({url: '/userRecommendVideoRd/findOne', method: 'get', params})
}

export const findAllVideoUrl = (params) => {
  return service({url: '/userRecommendVideoRd/findAllVideoUrl', method: 'get', params})
}

export const getUserRecommendVideoRdList = (params) => {
  return service({url: '/userRecommendVideoRd/getPage', method: 'get', params})
}

export const createUserRecommendVideoRd = (data) => {
  return service({url: '/userRecommendVideoRd/create', method: 'post', data})
}

export const saveUserRecommendVideoRd = (data) => {
  return service({url: '/userRecommendVideoRd/save', method: 'post', data})
}

export const deleteUserRecommendVideoRd = (data) => {
  return service({url: '/userRecommendVideoRd/delete', method: 'delete', data})
}

export const deleteUserRecommendVideoRdByIds = (data) => {
  return service({url: '/userRecommendVideoRd/batchDelete', method: 'delete', data})
}

export const updateUserRecommendVideoRd = (data) => {
  return service({url: '/userRecommendVideoRd/update', method: 'put', data})
}
