import service from '@/utils/request'

// @Tags PayWays
// @Summary 创建PayWays
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.PayWays true "创建PayWays"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /payWays/createPayWays [post]
export const createPayWays = (data) => {
  return service({
    url: '/payWays/createPayWays',
    method: 'post',
    data
  })
}
export const deletePayWayCache = (data) => {
  return service({
    url: '/payWays/deletePayWayCache',
    method: 'post',
    data
  })
}

// @Tags PayWays
// @Summary 删除PayWays
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.PayWays true "删除PayWays"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /payWays/deletePayWays [delete]
export const deletePayWays = (data) => {
  return service({
    url: '/payWays/deletePayWays',
    method: 'post',
    data
  })
}

// @Tags PayWays
// @Summary 删除PayWays
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除PayWays"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /payWays/deletePayWays [delete]
export const deletePayWaysByIds = (data) => {
  return service({
    url: '/payWays/deletePayWaysByIds',
    method: 'post',
    data
  })
}

// @Tags PayWays
// @Summary 更新PayWays
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.PayWays true "更新PayWays"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /payWays/updatePayWays [put]
export const updatePayWays = (data) => {
  return service({
    url: '/payWays/updatePayWays',
    method: 'post',
    data
  })
}

export const updatePaymentSort = (data) => {
  return service({
    url: '/payWays/updatePaymentSort',
    method: 'post',
    data
  })
}

// @Tags PayWays
// @Summary 用id查询PayWays
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.PayWays true "用id查询PayWays"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /payWays/findPayWays [get]
export const findPayWays = (params) => {
  return service({
    url: '/payWays/findPayWays',
    method: 'get',
    params
  })
}

// @Tags PayWays
// @Summary 分页获取PayWays列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取PayWays列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /payWays/getPayWaysList [get]
export const getPayWaysList = (params) => {
  return service({
    url: '/payWays/getPayWaysList',
    method: 'get',
    params
  })
}

export const allPayWaysData = (params) => {
  return service({
    url: '/payWays/allPayWaysData',
    method: 'get',
    params
  })
}
