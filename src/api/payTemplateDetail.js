import service from '@/utils/request'

export const findPayTemplateDetail = (params) => {
  return service({url: '/payTemplateDetail/findOne', method: 'get', params})
}

export const getPayTemplateDetailList = (params) => {
  return service({url: '/payTemplateDetail/getPage', method: 'get', params})
}

export const createPayTemplateDetail = (data) => {
  return service({url: '/payTemplateDetail/create', method: 'post', data})
}

export const deletePayTemplateDetail = (data) => {
  return service({url: '/payTemplateDetail/delete', method: 'delete', data})
}

export const deletePayTemplateDetailByIds = (data) => {
  return service({url: '/payTemplateDetail/batchDelete', method: 'delete', data})
}

export const updatePayTemplateDetail = (data) => {
  return service({url: '/payTemplateDetail/update', method: 'put', data})
}
