import service from '@/utils/request'

export const findUserAppliesNew = (params) => {
  return service({url: '/userAppliesNew/findOne', method: 'get', params})
}

export const getUserAppliesNewList = (params) => {
  return service({url: '/userAppliesNew/getPage', method: 'get', params})
}

export const getFaceVerify = (params) => {
  return service({url: '/userAppliesNew/getFaceVerify', method: 'get', params})
}

export const createUserAppliesNew = (data) => {
  return service({url: '/userAppliesNew/create', method: 'post', data})
}

export const deleteUserAppliesNew = (data) => {
  return service({url: '/userAppliesNew/delete', method: 'delete', data})
}

export const deleteUserAppliesNewByIds = (data) => {
  return service({url: '/userAppliesNew/batchDelete', method: 'delete', data})
}

export const updateUserAppliesNew = (data) => {
  return service({url: '/userAppliesNew/update', method: 'put', data})
}
