import service from '@/utils/request'

export const findUActAwardInfo = (params) => {
  return service({url: '/uActAwardInfo/findOne', method: 'get', params})
}

export const getUActAwardInfoList = (params) => {
  return service({url: '/uActAwardInfo/getPage', method: 'get', params})
}

export const actStateMapList = (params) => {
  return service({url: '/uActAwardInfo/actStateMapList', method: 'get', params})
}

export const actTypeMapList = (params) => {
  return service({url: '/uActAwardInfo/actTypeMapList', method: 'get', params})
}

export const createUActAwardInfo = (data) => {
  return service({url: '/uActAwardInfo/create', method: 'post', data})
}

export const deleteUActAwardInfo = (data) => {
  return service({url: '/uActAwardInfo/delete', method: 'delete', data})
}

export const deleteUActAwardInfoByIds = (data) => {
  return service({url: '/uActAwardInfo/batchDelete', method: 'delete', data})
}

export const updateUActAwardInfo = (data) => {
  return service({url: '/uActAwardInfo/update', method: 'put', data})
}

export const batchAwardStateUpdate = (data) => {
  return service({url: '/uActAwardInfo/batchAwardStateUpdate', method: 'put', data})
}
