import service from '@/utils/request'

export const findCountryStates = (params) => {
  return service({ url: '/countryStates/findOne', method: 'get', params })
}

export const getCountryStatesList = (params) => {
  return service({ url: '/countryStates/getPage', method: 'get', params })
}

export const createCountryStates = (data) => {
  return service({ url: '/countryStates/create', method: 'post', data })
}

export const deleteCountryStates = (data) => {
  return service({ url: '/countryStates/delete', method: 'delete', data })
}

export const deleteCountryStatesByIds = (data) => {
  return service({ url: '/countryStates/batchDelete', method: 'delete', data })
}

export const updateCountryStates = (data) => {
  return service({ url: '/countryStates/update', method: 'put', data })
}

export const allCountryStates = (data) => {
  return service({ url: '/countryStates/config', method: 'get', data })
}
