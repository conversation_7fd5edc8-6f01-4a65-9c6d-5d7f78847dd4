import service from '@/utils/request'

// @Tags ModelDailyReport
// @Summary 创建ModelDailyReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ModelDailyReport true "创建ModelDailyReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /modelDailyReport/createModelDailyReport [post]
export const createModelDailyReport = (data) => {
  return service({
    url: '/modelDailyReport/createModelDailyReport',
    method: 'post',
    data
  })
}

// @Tags ModelDailyReport
// @Summary 删除ModelDailyReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ModelDailyReport true "删除ModelDailyReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /modelDailyReport/deleteModelDailyReport [delete]
export const deleteModelDailyReport = (data) => {
  return service({
    url: '/modelDailyReport/deleteModelDailyReport',
    method: 'delete',
    data
  })
}

// @Tags ModelDailyReport
// @Summary 删除ModelDailyReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除ModelDailyReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /modelDailyReport/deleteModelDailyReport [delete]
export const deleteModelDailyReportByIds = (data) => {
  return service({
    url: '/modelDailyReport/deleteModelDailyReportByIds',
    method: 'delete',
    data
  })
}

// @Tags ModelDailyReport
// @Summary 更新ModelDailyReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ModelDailyReport true "更新ModelDailyReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /modelDailyReport/updateModelDailyReport [put]
export const updateModelDailyReport = (data) => {
  return service({
    url: '/modelDailyReport/updateModelDailyReport',
    method: 'put',
    data
  })
}

// @Tags ModelDailyReport
// @Summary 用id查询ModelDailyReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.ModelDailyReport true "用id查询ModelDailyReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /modelDailyReport/findModelDailyReport [get]
export const findModelDailyReport = (params) => {
  return service({
    url: '/modelDailyReport/findModelDailyReport',
    method: 'get',
    params
  })
}

// @Tags ModelDailyReport
// @Summary 分页获取ModelDailyReport列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取ModelDailyReport列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /modelDailyReport/getModelDailyReportList [get]
export const getModelDailyReportList = (params) => {
  return service({
    url: '/modelDailyReport/getModelDailyReportList',
    method: 'get',
    params
  })
}
