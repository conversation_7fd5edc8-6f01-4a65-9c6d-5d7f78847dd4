import service from '@/utils/request'

export const findRicherRecommends = (params) => {
  return service({url: '/richerRecommends/findOne', method: 'get', params})
}

export const getRicherRecommendsList = (params) => {
  return service({url: '/richerRecommends/getPage', method: 'get', params})
}

export const createRicherRecommends = (data) => {
  return service({url: '/richerRecommends/create', method: 'post', data})
}

export const deleteRicherRecommends = (data) => {
  return service({url: '/richerRecommends/delete', method: 'delete', data})
}

export const deleteRicherRecommendsByIds = (data) => {
  return service({url: '/richerRecommends/batchDelete', method: 'delete', data})
}

export const updateRicherRecommends = (data) => {
  return service({url: '/richerRecommends/update', method: 'put', data})
}
