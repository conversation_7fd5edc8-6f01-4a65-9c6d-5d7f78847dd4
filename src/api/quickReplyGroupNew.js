import service from '@/utils/request'

export const findQuickReplyGroupNew = (params) => {
  return service({url: '/quickReplyGroupNew/findOne', method: 'get', params})
}
export const findAllQuickReplyGroupNew = (params) => {
  return service({url: '/quickReplyGroupNew/findAll', method: 'get', params})
}

export const getQuickReplyGroupNewList = (params) => {
  return service({url: '/quickReplyGroupNew/getPage', method: 'get', params})
}

export const createQuickReplyGroupNew = (data) => {
  return service({url: '/quickReplyGroupNew/create', method: 'post', data})
}

export const deleteQuickReplyGroupNew = (data) => {
  return service({url: '/quickReplyGroupNew/delete', method: 'delete', data})
}

export const deleteQuickReplyGroupNewByIds = (data) => {
  return service({url: '/quickReplyGroupNew/batchDelete', method: 'delete', data})
}

export const updateQuickReplyGroupNew = (data) => {
  return service({url: '/quickReplyGroupNew/update', method: 'put', data})
}
