
import service from '@/utils/request'

export const settle = (data) => {
  return service({
    url: '/withdrawalRecords/settle',
    method: 'post',
    data
  })
}
export const batchUpdateWithdrawalPay = (data) => {
  return service({
    url: '/withdrawalRecords/batchUpdateWithdrawalPay',
    method: 'post',
    data
  })
}
export const addPay = (data) => {
  return service({
    url: '/withdrawalRecords/addPay',
    method: 'post',
    data
  })
}
export const addPayList = (data) => {
  return service({
    url: '/withdrawalRecords/addPayList',
    method: 'post',
    data
  })
}

export const getWithdrawalPayList = (params) => {
  return service({
    url: '/withdrawalRecords/getWithdrawalPayList',
    method: 'get',
    params
  })
}
export const getWithdrawalRecordsLists = (params) => {
  return service({
    url: '/withdrawalRecords/getWithdrawalRecordsList',
    method: 'get',
    params
  })
}

