
import service from '@/utils/request'

// @Tags DramaTactic
// @Summary 创建DramaTactic
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.DramaTactic true "创建DramaTactic"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /dramaTactic/createDramaTactic [post]
export const createDramaTactic = (data) => {
  return service({
    url: '/dramaTactic/createDramaTactic',
    method: 'post',
    data
  })
}

// @Tags DramaTactic
// @Summary 删除DramaTactic
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.DramaTactic true "删除DramaTactic"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /dramaTactic/deleteDramaTactic [delete]
export const deleteDramaTactic = (data) => {
  return service({
    url: '/dramaTactic/deleteDramaTactic',
    method: 'delete',
    data
  })
}

// @Tags DramaTactic
// @Summary 删除DramaTactic
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除DramaTactic"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /dramaTactic/deleteDramaTactic [delete]
export const deleteDramaTacticByIds = (data) => {
  return service({
    url: '/dramaTactic/deleteDramaTacticByIds',
    method: 'delete',
    data
  })
}

// @Tags DramaTactic
// @Summary 更新DramaTactic
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.DramaTactic true "更新DramaTactic"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /dramaTactic/updateDramaTactic [put]
export const updateDramaTactic = (data) => {
  return service({
    url: '/dramaTactic/updateDramaTactic',
    method: 'put',
    data
  })
}

// @Tags DramaTactic
// @Summary 用id查询DramaTactic
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.DramaTactic true "用id查询DramaTactic"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /dramaTactic/findDramaTactic [get]
export const findDramaTactic = (params) => {
  return service({
    url: '/dramaTactic/findDramaTactic',
    method: 'get',
    params
  })
}

// @Tags DramaTactic
// @Summary 分页获取DramaTactic列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取DramaTactic列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /dramaTactic/getDramaTacticList [get]
export const getDramaTacticList = (params) => {
  return service({
    url: '/dramaTactic/getDramaTacticList',
    method: 'get',
    params
  })
}
