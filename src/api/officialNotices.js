
import service from '@/utils/request'

// @Tags OfficialNotices
// @Summary 创建OfficialNotices
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.OfficialNotices true "创建OfficialNotices"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /officialNotices/createOfficialNotices [post]
export const createOfficialNotices = (data) => {
  return service({
    url: '/officialNotices/createOfficialNotices',
    method: 'post',
    data
  })
}

// @Tags OfficialNotices
// @Summary 删除OfficialNotices
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.OfficialNotices true "删除OfficialNotices"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /officialNotices/deleteOfficialNotices [delete]
export const deleteOfficialNotices = (data) => {
  return service({
    url: '/officialNotices/deleteOfficialNotices',
    method: 'delete',
    data
  })
}

// @Tags OfficialNotices
// @Summary 删除OfficialNotices
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除OfficialNotices"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /officialNotices/deleteOfficialNotices [delete]
export const deleteOfficialNoticesByIds = (data) => {
  return service({
    url: '/officialNotices/deleteOfficialNoticesByIds',
    method: 'delete',
    data
  })
}

// @Tags OfficialNotices
// @Summary 更新OfficialNotices
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.OfficialNotices true "更新OfficialNotices"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /officialNotices/updateOfficialNotices [put]
export const updateOfficialNotices = (data) => {
  return service({
    url: '/officialNotices/updateOfficialNotices',
    method: 'put',
    data
  })
}

// @Tags OfficialNotices
// @Summary 用id查询OfficialNotices
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.OfficialNotices true "用id查询OfficialNotices"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /officialNotices/findOfficialNotices [get]
export const findOfficialNotices = (params) => {
  return service({
    url: '/officialNotices/findOfficialNotices',
    method: 'get',
    params
  })
}

// @Tags OfficialNotices
// @Summary 分页获取OfficialNotices列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取OfficialNotices列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /officialNotices/getOfficialNoticesList [get]
export const getOfficialNoticesList = (params) => {
  return service({
    url: '/officialNotices/getOfficialNoticesList',
    method: 'get',
    params
  })
}
