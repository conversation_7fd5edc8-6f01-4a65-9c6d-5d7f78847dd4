import service from '@/utils/request'

export const findUnionViolationRecords = (params) => {
  return service({url: '/unionViolationRecords/findOne', method: 'get', params})
}

export const getUnionViolationRecordsList = (params) => {
  return service({url: '/unionViolationRecords/getPage', method: 'get', params})
}
export const unionBanStateList = (params) => {
  return service({url: '/unionViolationRecords/unionBanStateList', method: 'get', params})
}

export const createUnionViolationRecords = (data) => {
  return service({url: '/unionViolationRecords/create', method: 'post', data})
}

export const deleteUnionViolationRecords = (data) => {
  return service({url: '/unionViolationRecords/delete', method: 'delete', data})
}

export const deleteUnionViolationRecordsByIds = (data) => {
  return service({url: '/unionViolationRecords/batchDelete', method: 'delete', data})
}

export const updateUnionViolationRecords = (data) => {
  return service({url: '/unionViolationRecords/update', method: 'put', data})
}
