import service from '@/utils/request'

export const findIncomeRecords = (params) => {
  return service({url: '/incomeRecords/findOne', method: 'get', params})
}

export const getIncomeRecordsList = (params) => {
  return service({url: '/incomeRecords/getPage', method: 'get', params})
}

export const getSubTypeMapList = () => {
  return service({url: '/incomeRecords/getSubTypeMapList', method: 'get'})
}

export const getStateMapList = () => {
  return service({url: '/incomeRecords/getStateMapList', method: 'get'})
}

export const getIncomeTypeMapList = () => {
  return service({url: '/incomeRecords/getIncomeTypeMapList', method: 'get'})
}

export const createIncomeRecords = (data) => {
  return service({url: '/incomeRecords/create', method: 'post', data})
}

export const deleteIncomeRecords = (data) => {
  return service({url: '/incomeRecords/delete', method: 'delete', data})
}

export const deleteIncomeRecordsByIds = (data) => {
  return service({url: '/incomeRecords/batchDelete', method: 'delete', data})
}

export const updateIncomeRecords = (data) => {
  return service({url: '/incomeRecords/update', method: 'put', data})
}
