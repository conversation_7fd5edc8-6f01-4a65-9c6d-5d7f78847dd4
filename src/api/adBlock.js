import service from '@/utils/request'

export const findAdBlock = (params) => {
    return service({url: '/adBlock/findOne', method: 'get', params})
}

export const getAdBlockList = (params) => {
    return service({url: '/adBlock/getPage', method: 'get', params})
}

export const createAdBlock = (data) => {
    return service({url: '/adBlock/create', method: 'post', data})
}

export const deleteAdBlock = (data) => {
    return service({url: '/adBlock/delete', method: 'delete', data})
}

export const deleteAdBlockByIds = (data) => {
    return service({url: '/adBlock/batchDelete', method: 'delete', data})
}

export const updateAdBlock = (data) => {
    return service({url: '/adBlock/update', method: 'put', data})
}
