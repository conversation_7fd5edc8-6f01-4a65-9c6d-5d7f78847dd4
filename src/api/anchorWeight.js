import service from '@/utils/request'

// @Tags AnchorWeight
// @Summary 更新AnchorWeight
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorWeight true "更新AnchorWeight"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /aw/updateAnchorWeight [put]
export const updateAnchorWeight = (data) => {
  return service({
    url: '/aw/updateAnchorWeight',
    method: 'put',
    data
  })
}

// @Tags AnchorWeight
// @Summary 用id查询AnchorWeight
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.AnchorWeight true "用id查询AnchorWeight"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /aw/findAnchorWeight [get]
export const getAnchorWeight = (params) => {
  return service({
    url: '/aw/getAnchorWeight',
    method: 'get',
    params
  })
}

// @Tags AnchorWeight
// @Summary 分页获取AnchorWeight列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取AnchorWeight列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /aw/getAnchorWeightList [get]
export const getAnchorWeightList = (params) => {
  return service({
    url: '/aw/getAnchorWeightList',
    method: 'get',
    params
  })
}
// @Tags AnchorWeight
// @Summary 更新AnchorWeight
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorWeight true "更新AnchorWeight"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /aw/updateAnchorRoleWeight [put]
export const updateAnchorRoleWeight = (data) => {
  return service({
    url: '/aw/updateAnchorRoleWeight',
    method: 'put',
    data
  })
}
