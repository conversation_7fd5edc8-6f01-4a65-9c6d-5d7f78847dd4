
import service from '@/utils/request'

// @Tags UserWeight
// @Summary 创建UserWeight
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserWeight true "创建UserWeight"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /userWeight/createUserWeight [post]
export const createUserWeight = (data) => {
  return service({
    url: '/userWeight/createUserWeight',
    method: 'post',
    data
  })
}

// @Tags UserWeight
// @Summary 删除UserWeight
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserWeight true "删除UserWeight"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /userWeight/deleteUserWeight [delete]
export const deleteUserWeight = (data) => {
  return service({
    url: '/userWeight/deleteUserWeight',
    method: 'delete',
    data
  })
}

// @Tags UserWeight
// @Summary 删除UserWeight
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除UserWeight"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /userWeight/deleteUserWeight [delete]
export const deleteUserWeightByIds = (data) => {
  return service({
    url: '/userWeight/deleteUserWeightByIds',
    method: 'delete',
    data
  })
}

// @Tags UserWeight
// @Summary 更新UserWeight
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserWeight true "更新UserWeight"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /userWeight/updateUserWeight [put]
export const updateUserWeight = (data) => {
  return service({
    url: '/userWeight/updateUserWeight',
    method: 'put',
    data
  })
}

// @Tags UserWeight
// @Summary 用id查询UserWeight
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.UserWeight true "用id查询UserWeight"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /userWeight/findUserWeight [get]
export const findUserWeight = (params) => {
  return service({
    url: '/userWeight/findUserWeight',
    method: 'get',
    params
  })
}

// @Tags UserWeight
// @Summary 分页获取UserWeight列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取UserWeight列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /userWeight/getUserWeightList [get]
export const getUserWeightList = (params) => {
  return service({
    url: '/userWeight/getUserWeightList',
    method: 'get',
    params
  })
}
