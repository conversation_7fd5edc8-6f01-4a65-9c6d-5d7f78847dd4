import service from '@/utils/request'

export const findAnchorCallVideoDetails = (params) => {
  return service({ url: '/anchorCallVideoDetails/findOne', method: 'get', params })
}

export const getAnchorCallVideoDetailsList = (params) => {
  return service({ url: '/anchorCallVideoDetails/getPage', method: 'get', params })
}

export const createAnchorCallVideoDetails = (data) => {
  return service({ url: '/anchorCallVideoDetails/create', method: 'post', data })
}

export const deleteAnchorCallVideoDetails = (data) => {
  return service({ url: '/anchorCallVideoDetails/delete', method: 'delete', data })
}

export const deleteAnchorCallVideoDetailsByIds = (data) => {
  return service({ url: '/anchorCallVideoDetails/batchDelete', method: 'delete', data })
}

export const updateAnchorCallVideoDetails = (data) => {
  return service({ url: '/anchorCallVideoDetails/update', method: 'put', data })
}
