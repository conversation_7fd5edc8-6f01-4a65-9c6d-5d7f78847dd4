import service from '@/utils/request'

export const findUnionBanRecords = (params) => {
  return service({url: '/unionBanRecords/findOne', method: 'get', params})
}

export const getUnionBanRecordsList = (params) => {
  return service({url: '/unionBanRecords/getPage', method: 'get', params})
}

export const createUnionBanRecords = (data) => {
  return service({url: '/unionBanRecords/create', method: 'post', data})
}

export const banUnion = (data) => {
  return service({url: '/unionBanRecords/banUnion', method: 'post', data})
}

export const deleteUnionBanRecords = (data) => {
  return service({url: '/unionBanRecords/delete', method: 'delete', data})
}

export const deleteUnionBanRecordsByIds = (data) => {
  return service({url: '/unionBanRecords/batchDelete', method: 'delete', data})
}

export const updateUnionBanRecords = (data) => {
  return service({url: '/unionBanRecords/update', method: 'put', data})
}
