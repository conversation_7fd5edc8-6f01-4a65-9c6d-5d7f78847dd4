import service from '@/utils/request'

// @Tags ApplyStaticsPanel
// @Summary 创建ApplyStaticsPanel
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ApplyStaticsPanel true "创建ApplyStaticsPanel"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /applyStaticsPanel/createApplyStaticsPanel [post]
export const createApplyStaticsPanel = (data) => {
    return service({
        url: '/applyStaticsPanel/createApplyStaticsPanel',
        method: 'post',
        data
    })
}

// @Tags ApplyStaticsPanel
// @Summary 删除ApplyStaticsPanel
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ApplyStaticsPanel true "删除ApplyStaticsPanel"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /applyStaticsPanel/deleteApplyStaticsPanel [delete]
export const deleteApplyStaticsPanel = (data) => {
    return service({
        url: '/applyStaticsPanel/deleteApplyStaticsPanel',
        method: 'delete',
        data
    })
}

// @Tags ApplyStaticsPanel
// @Summary 删除ApplyStaticsPanel
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除ApplyStaticsPanel"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /applyStaticsPanel/deleteApplyStaticsPanel [delete]
export const deleteApplyStaticsPanelByIds = (data) => {
    return service({
        url: '/applyStaticsPanel/deleteApplyStaticsPanelByIds',
        method: 'delete',
        data
    })
}

// @Tags ApplyStaticsPanel
// @Summary 更新ApplyStaticsPanel
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.ApplyStaticsPanel true "更新ApplyStaticsPanel"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /applyStaticsPanel/updateApplyStaticsPanel [put]
export const updateApplyStaticsPanel = (data) => {
    return service({
        url: '/applyStaticsPanel/updateApplyStaticsPanel',
        method: 'put',
        data
    })
}

// @Tags ApplyStaticsPanel
// @Summary 用id查询ApplyStaticsPanel
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.ApplyStaticsPanel true "用id查询ApplyStaticsPanel"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /applyStaticsPanel/findApplyStaticsPanel [get]
export const findApplyStaticsPanel = (params) => {
    return service({
        url: '/applyStaticsPanel/findApplyStaticsPanel',
        method: 'get',
        params
    })
}

// @Tags ApplyStaticsPanel
// @Summary 分页获取ApplyStaticsPanel列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取ApplyStaticsPanel列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /applyStaticsPanel/getApplyStaticsPanelList [get]
export const getApplyStaticsPanelList = (data) => {
    return service({
        url: '/applyStaticsPanel/getApplyStaticsPanelList',
        method: 'post',
        data
    })
}
