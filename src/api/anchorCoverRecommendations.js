import service from '@/utils/request'

export const findAnchorCoverRecommendations = (params) => {
  return service({url: '/anchorCoverRecommendations/findOne', method: 'get', params})
}

export const getAnchorCoverRecommendationsList = (params) => {
  return service({url: '/anchorCoverRecommendations/getPage', method: 'get', params})
}

export const createAnchorCoverRecommendations = (data) => {
  return service({url: '/anchorCoverRecommendations/create', method: 'post', data})
}

export const deleteAnchorCoverRecommendations = (data) => {
  return service({url: '/anchorCoverRecommendations/delete', method: 'delete', data})
}

export const deleteAnchorCoverRecommendationsByIds = (data) => {
  return service({url: '/anchorCoverRecommendations/batchDelete', method: 'delete', data})
}

export const updateAnchorCoverRecommendations = (data) => {
  return service({url: '/anchorCoverRecommendations/update', method: 'put', data})
}
