
import service from '@/utils/request'

// @Tags PartnerUsers
// @Summary 创建PartnerUsers
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.PartnerUsers true "创建PartnerUsers"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /partnerUsers/createPartnerUsers [post]
export const createPartnerUsers = (data) => {
  return service({
    url: '/partnerUsers/createPartnerUsers',
    method: 'post',
    data
  })
}

// @Tags PartnerUsers
// @Summary 删除PartnerUsers
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.PartnerUsers true "删除PartnerUsers"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /partnerUsers/deletePartnerUsers [delete]
export const deletePartnerUsers = (data) => {
  return service({
    url: '/partnerUsers/deletePartnerUsers',
    method: 'delete',
    data
  })
}

// @Tags PartnerUsers
// @Summary 删除PartnerUsers
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除PartnerUsers"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /partnerUsers/deletePartnerUsers [delete]
export const deletePartnerUsersByIds = (data) => {
  return service({
    url: '/partnerUsers/deletePartnerUsersByIds',
    method: 'delete',
    data
  })
}

// @Tags PartnerUsers
// @Summary 更新PartnerUsers
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.PartnerUsers true "更新PartnerUsers"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /partnerUsers/updatePartnerUsers [put]
export const updatePartnerUsers = (data) => {
  return service({
    url: '/partnerUsers/updatePartnerUsers',
    method: 'put',
    data
  })
}

// @Tags PartnerUsers
// @Summary 用id查询PartnerUsers
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.PartnerUsers true "用id查询PartnerUsers"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /partnerUsers/findPartnerUsers [get]
export const findPartnerUsers = (params) => {
  return service({
    url: '/partnerUsers/findPartnerUsers',
    method: 'get',
    params
  })
}

// @Tags PartnerUsers
// @Summary 分页获取PartnerUsers列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取PartnerUsers列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /partnerUsers/getPartnerUsersList [get]
export const getPartnerUsersList = (params) => {
  return service({
    url: '/partnerUsers/getPartnerUsersList',
    method: 'get',
    params
  })
}
