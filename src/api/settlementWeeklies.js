
import service from '@/utils/request'

// @Tags SettlementWeeklies
// @Summary 创建SettlementWeeklies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SettlementWeeklies true "创建SettlementWeeklies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /settlementWeeklies/createSettlementWeeklies [post]
export const createSettlementWeeklies = (data) => {
  return service({
    url: '/settlementWeeklies/createSettlementWeeklies',
    method: 'post',
    data
  })
}

// @Tags SettlementWeeklies
// @Summary 删除SettlementWeeklies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SettlementWeeklies true "删除SettlementWeeklies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /settlementWeeklies/deleteSettlementWeeklies [delete]
export const deleteSettlementWeeklies = (data) => {
  return service({
    url: '/settlementWeeklies/deleteSettlementWeeklies',
    method: 'delete',
    data
  })
}

// @Tags SettlementWeeklies
// @Summary 删除SettlementWeeklies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除SettlementWeeklies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /settlementWeeklies/deleteSettlementWeeklies [delete]
export const deleteSettlementWeekliesByIds = (data) => {
  return service({
    url: '/settlementWeeklies/deleteSettlementWeekliesByIds',
    method: 'delete',
    data
  })
}

// @Tags SettlementWeeklies
// @Summary 更新SettlementWeeklies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SettlementWeeklies true "更新SettlementWeeklies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /settlementWeeklies/updateSettlementWeeklies [put]
export const updateSettlementWeeklies = (data) => {
  return service({
    url: '/settlementWeeklies/updateSettlementWeeklies',
    method: 'put',
    data
  })
}

// @Tags SettlementWeeklies
// @Summary 用id查询SettlementWeeklies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.SettlementWeeklies true "用id查询SettlementWeeklies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /settlementWeeklies/findSettlementWeeklies [get]
export const findSettlementWeeklies = (params) => {
  return service({
    url: '/settlementWeeklies/findSettlementWeeklies',
    method: 'get',
    params
  })
}

// @Tags SettlementWeeklies
// @Summary 分页获取SettlementWeeklies列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取SettlementWeeklies列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /settlementWeeklies/getSettlementWeekliesList [get]
export const getSettlementWeekliesList = (params) => {
  return service({
    url: '/settlementWeeklies/getSettlementWeekliesList',
    method: 'get',
    params
  })
}
