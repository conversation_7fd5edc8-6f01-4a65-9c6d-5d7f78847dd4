
import service from '@/utils/request'

// @Tags AnchorSettlementWeeklies
// @Summary 创建AnchorSettlementWeeklies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorSettlementWeeklies true "创建AnchorSettlementWeeklies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /anchorSettlementWeeklies/createAnchorSettlementWeeklies [post]
export const createAnchorSettlementWeeklies = (data) => {
  return service({
    url: '/anchorSettlementWeeklies/createAnchorSettlementWeeklies',
    method: 'post',
    data
  })
}

// @Tags AnchorSettlementWeeklies
// @Summary 删除AnchorSettlementWeeklies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorSettlementWeeklies true "删除AnchorSettlementWeeklies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /anchorSettlementWeeklies/deleteAnchorSettlementWeeklies [delete]
export const deleteAnchorSettlementWeeklies = (data) => {
  return service({
    url: '/anchorSettlementWeeklies/deleteAnchorSettlementWeeklies',
    method: 'delete',
    data
  })
}

// @Tags AnchorSettlementWeeklies
// @Summary 删除AnchorSettlementWeeklies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除AnchorSettlementWeeklies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /anchorSettlementWeeklies/deleteAnchorSettlementWeeklies [delete]
export const deleteAnchorSettlementWeekliesByIds = (data) => {
  return service({
    url: '/anchorSettlementWeeklies/deleteAnchorSettlementWeekliesByIds',
    method: 'delete',
    data
  })
}

// @Tags AnchorSettlementWeeklies
// @Summary 更新AnchorSettlementWeeklies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorSettlementWeeklies true "更新AnchorSettlementWeeklies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /anchorSettlementWeeklies/updateAnchorSettlementWeeklies [put]
export const updateAnchorSettlementWeeklies = (data) => {
  return service({
    url: '/anchorSettlementWeeklies/updateAnchorSettlementWeeklies',
    method: 'put',
    data
  })
}

// @Tags AnchorSettlementWeeklies
// @Summary 用id查询AnchorSettlementWeeklies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.AnchorSettlementWeeklies true "用id查询AnchorSettlementWeeklies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /anchorSettlementWeeklies/findAnchorSettlementWeeklies [get]
export const findAnchorSettlementWeeklies = (params) => {
  return service({
    url: '/anchorSettlementWeeklies/findAnchorSettlementWeeklies',
    method: 'get',
    params
  })
}

// @Tags AnchorSettlementWeeklies
// @Summary 分页获取AnchorSettlementWeeklies列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取AnchorSettlementWeeklies列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /anchorSettlementWeeklies/getAnchorSettlementWeekliesList [get]
export const getAnchorSettlementWeekliesList = (params) => {
  return service({
    url: '/anchorSettlementWeeklies/getAnchorSettlementWeekliesList',
    method: 'get',
    params
  })
}

