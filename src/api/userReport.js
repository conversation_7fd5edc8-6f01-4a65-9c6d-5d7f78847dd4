import service from '@/utils/request'

// @Tags UserReport
// @Summary 创建UserReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserReport true "创建UserReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /userReport/createUserReport [post]
export const createUserReport = (data) => {
  return service({
    url: '/userReport/createUserReport',
    method: 'post',
    data
  })
}

// @Tags UserReport
// @Summary 删除UserReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserReport true "删除UserReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /userReport/deleteUserReport [delete]
export const deleteUserReport = (data) => {
  return service({
    url: '/userReport/deleteUserReport',
    method: 'delete',
    data
  })
}

// @Tags UserReport
// @Summary 删除UserReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除UserReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /userReport/deleteUserReport [delete]
export const deleteUserReportByIds = (data) => {
  return service({
    url: '/userReport/deleteUserReportByIds',
    method: 'delete',
    data
  })
}

// @Tags UserReport
// @Summary 更新UserReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.UserReport true "更新UserReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /userReport/updateUserReport [put]
export const updateUserReport = (data) => {
  return service({
    url: '/userReport/updateUserReport',
    method: 'put',
    data
  })
}

// @Tags UserReport
// @Summary 用id查询UserReport
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.UserReport true "用id查询UserReport"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /userReport/findUserReport [get]
export const findUserReport = (params) => {
  return service({
    url: '/userReport/findUserReport',
    method: 'get',
    params
  })
}

// @Tags UserReport
// @Summary 分页获取UserReport列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取UserReport列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /userReport/getUserReportList [get]
export const getUserReportList = (params) => {
  return service({
    url: '/userReport/getUserReportList',
    method: 'get',
    params
  })
}
