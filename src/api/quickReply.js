import service from '@/utils/request'

export const findQuickReply = (params) => {
  return service({url: '/quickReply/findOne', method: 'get', params})
}

export const getQuickReplyList = (params) => {
  return service({url: '/quickReply/getPage', method: 'get', params})
}

export const createQuickReply = (data) => {
  return service({url: '/quickReply/create', method: 'post', data})
}

export const deleteQuickReply = (data) => {
  return service({url: '/quickReply/delete', method: 'delete', data})
}

export const deleteQuickReplyByIds = (data) => {
  return service({url: '/quickReply/batchDelete', method: 'delete', data})
}

export const updateQuickReply = (data) => {
  return service({url: '/quickReply/update', method: 'put', data})
}
