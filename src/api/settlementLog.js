
import service from '@/utils/request'

// @Tags SettlementLog
// @Summary 创建SettlementLog
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SettlementLog true "创建SettlementLog"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /settlementLog/createSettlementLog [post]
export const createSettlementLog = (data) => {
  return service({
    url: '/settlementLog/createSettlementLog',
    method: 'post',
    data
  })
}

// @Tags SettlementLog
// @Summary 删除SettlementLog
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SettlementLog true "删除SettlementLog"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /settlementLog/deleteSettlementLog [delete]
export const deleteSettlementLog = (data) => {
  return service({
    url: '/settlementLog/deleteSettlementLog',
    method: 'delete',
    data
  })
}

// @Tags SettlementLog
// @Summary 删除SettlementLog
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除SettlementLog"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /settlementLog/deleteSettlementLog [delete]
export const deleteSettlementLogByIds = (data) => {
  return service({
    url: '/settlementLog/deleteSettlementLogByIds',
    method: 'delete',
    data
  })
}

// @Tags SettlementLog
// @Summary 更新SettlementLog
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.SettlementLog true "更新SettlementLog"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /settlementLog/updateSettlementLog [put]
export const updateSettlementLog = (data) => {
  return service({
    url: '/settlementLog/updateSettlementLog',
    method: 'put',
    data
  })
}

// @Tags SettlementLog
// @Summary 用id查询SettlementLog
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.SettlementLog true "用id查询SettlementLog"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /settlementLog/findSettlementLog [get]
export const findSettlementLog = (params) => {
  return service({
    url: '/settlementLog/findSettlementLog',
    method: 'get',
    params
  })
}

// @Tags SettlementLog
// @Summary 分页获取SettlementLog列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取SettlementLog列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /settlementLog/getSettlementLogList [get]
export const getSettlementLogList = (params) => {
  return service({
    url: '/settlementLog/getSettlementLogList',
    method: 'get',
    params
  })
}

export const handleSettlementLog = (data) => {
  return service({
    url: '/settlementLog/handleSettlementLog',
    method: 'post',
    data
  })
}
