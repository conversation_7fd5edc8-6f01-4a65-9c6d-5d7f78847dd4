
import service from '@/utils/request'

// @Tags PersonalityLabels
// @Summary 创建PersonalityLabels
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.PersonalityLabels true "创建PersonalityLabels"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /personalityLabels/createPersonalityLabels [post]
export const createPersonalityLabels = (data) => {
  return service({
    url: '/personalityLabels/createPersonalityLabels',
    method: 'post',
    data
  })
}

// @Tags PersonalityLabels
// @Summary 删除PersonalityLabels
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.PersonalityLabels true "删除PersonalityLabels"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /personalityLabels/deletePersonalityLabels [delete]
export const deletePersonalityLabels = (data) => {
  return service({
    url: '/personalityLabels/deletePersonalityLabels',
    method: 'delete',
    data
  })
}

// @Tags PersonalityLabels
// @Summary 删除PersonalityLabels
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除PersonalityLabels"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /personalityLabels/deletePersonalityLabels [delete]
export const deletePersonalityLabelsByIds = (data) => {
  return service({
    url: '/personalityLabels/deletePersonalityLabelsByIds',
    method: 'delete',
    data
  })
}

// @Tags PersonalityLabels
// @Summary 更新PersonalityLabels
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.PersonalityLabels true "更新PersonalityLabels"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /personalityLabels/updatePersonalityLabels [put]
export const updatePersonalityLabels = (data) => {
  return service({
    url: '/personalityLabels/updatePersonalityLabels',
    method: 'put',
    data
  })
}

// @Tags PersonalityLabels
// @Summary 用id查询PersonalityLabels
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.PersonalityLabels true "用id查询PersonalityLabels"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /personalityLabels/findPersonalityLabels [get]
export const findPersonalityLabels = (params) => {
  return service({
    url: '/personalityLabels/findPersonalityLabels',
    method: 'get',
    params
  })
}

// @Tags PersonalityLabels
// @Summary 分页获取PersonalityLabels列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取PersonalityLabels列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /personalityLabels/getPersonalityLabelsList [get]
export const getPersonalityLabelsList = (params) => {
  return service({
    url: '/personalityLabels/getPersonalityLabelsList',
    method: 'get',
    params
  })
}

//
export const getPersonalityLabelsTree = (params) => {
  return service({
    url: '/personalityLabels/getPersonalityLabelsTree',
    method: 'get',
    params
  })
}
