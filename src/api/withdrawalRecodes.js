
import service from '@/utils/request'

// @Tags WithdrawalRecords
// @Summary 创建WithdrawalRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.WithdrawalRecords true "创建WithdrawalRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /withdrawalRecords/createWithdrawalRecords [post]
export const createWithdrawalRecords = (data) => {
  return service({
    url: '/withdrawalRecords/createWithdrawalRecords',
    method: 'post',
    data
  })
}

// @Tags WithdrawalRecords
// @Summary 删除WithdrawalRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.WithdrawalRecords true "删除WithdrawalRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /withdrawalRecords/deleteWithdrawalRecords [delete]
export const deleteWithdrawalRecords = (data) => {
  return service({
    url: '/withdrawalRecords/deleteWithdrawalRecords',
    method: 'delete',
    data
  })
}

// @Tags WithdrawalRecords
// @Summary 删除WithdrawalRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除WithdrawalRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /withdrawalRecords/deleteWithdrawalRecords [delete]
export const deleteWithdrawalRecordsByIds = (data) => {
  return service({
    url: '/withdrawalRecords/deleteWithdrawalRecordsByIds',
    method: 'delete',
    data
  })
}

// @Tags WithdrawalRecords
// @Summary 更新WithdrawalRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.WithdrawalRecords true "更新WithdrawalRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /withdrawalRecords/updateWithdrawalRecords [put]
export const updateWithdrawalRecords = (data) => {
  return service({
    url: '/withdrawalRecords/updateWithdrawalRecords',
    method: 'put',
    data
  })
}
export const batchExamineWithdrawalRecords = (data) => {
  return service({
    url: '/withdrawalRecords/batchExamineWithdrawalRecords',
    method: 'put',
    data
  })
}

// @Tags WithdrawalRecords
// @Summary 用id查询WithdrawalRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.WithdrawalRecords true "用id查询WithdrawalRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /withdrawalRecords/findWithdrawalRecords [get]
export const findWithdrawalRecords = (params) => {
  return service({
    url: '/withdrawalRecords/findWithdrawalRecords',
    method: 'get',
    params
  })
}

// @Tags WithdrawalRecords
// @Summary 分页获取WithdrawalRecords列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取WithdrawalRecords列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /withdrawalRecords/getWithdrawalRecordsList [get]
export const getWithdrawalRecordsList = (params) => {
  return service({
    url: '/withdrawalRecords/getWithdrawalRecordsList',
    method: 'get',
    params
  })
}

// @Tags WithdrawalRecords
// @Summary 批量更新WithdrawalRecords
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.WithdrawalRecords true "批量更新WithdrawalRecords"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /withdrawalRecords/batchUpdateWithdrawalRecords [put]
export const batchUpdateWithdrawalRecords = (data) => {
  return service({
    url: '/withdrawalRecords/batchUpdateWithdrawalRecords',
    method: 'put',
    data
  })
}
export const examineWithdrawalRecords = (data) => {
  return service({
    url: '/withdrawalRecords/examineWithdrawalRecords',
    method: 'put',
    data
  })
}
