import service from '@/utils/request'

export const findUserGameOrders = (params) => {
    return service({url: '/userGameOrders/findOne', method: 'get', params})
}

export const getUserGameOrdersList = (params) => {
    return service({url: '/userGameOrders/getPage', method: 'get', params})
}

export const createUserGameOrders = (data) => {
    return service({url: '/userGameOrders/create', method: 'post', data})
}

export const deleteUserGameOrders = (data) => {
    return service({url: '/userGameOrders/delete', method: 'delete', data})
}

export const deleteUserGameOrdersByIds = (data) => {
    return service({url: '/userGameOrders/batchDelete', method: 'delete', data})
}

export const updateUserGameOrders = (data) => {
    return service({url: '/userGameOrders/update', method: 'put', data})
}
