import service from '@/utils/request'

export const findProgramConsume = (params) => {
  return service({url: '/programConsume/findOne', method: 'get', params})
}

export const getProgramConsumeList = (params) => {
  return service({url: '/programConsume/getPage', method: 'get', params})
}

export const createProgramConsume = (data) => {
  return service({url: '/programConsume/create', method: 'post', data})
}

export const deleteProgramConsume = (data) => {
  return service({url: '/programConsume/delete', method: 'delete', data})
}

export const deleteProgramConsumeByIds = (data) => {
  return service({url: '/programConsume/batchDelete', method: 'delete', data})
}

export const updateProgramConsume = (data) => {
  return service({url: '/programConsume/update', method: 'put', data})
}
