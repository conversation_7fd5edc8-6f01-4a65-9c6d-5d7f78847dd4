import service from '@/utils/request'

// @Tags AdCampaign
// @Summary 创建AdCampaign
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AdCampaign true "创建AdCampaign"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adCampaign/createAdCampaign [post]
export const createAdCampaign = (data) => {
    return service({
        url: '/adCampaign/createAdCampaign',
        method: 'post',
        data
    })
}

// @Tags AdCampaign
// @Summary 删除AdCampaign
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AdCampaign true "删除AdCampaign"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adCampaign/deleteAdCampaign [delete]
export const deleteAdCampaign = (data) => {
    return service({
        url: '/adCampaign/deleteAdCampaign',
        method: 'delete',
        data
    })
}

// @Tags AdCampaign
// @Summary 删除AdCampaign
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除AdCampaign"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /adCampaign/deleteAdCampaign [delete]
export const deleteAdCampaignByIds = (data) => {
    return service({
        url: '/adCampaign/deleteAdCampaignByIds',
        method: 'delete',
        data
    })
}

// @Tags AdCampaign
// @Summary 更新AdCampaign
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AdCampaign true "更新AdCampaign"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /adCampaign/updateAdCampaign [put]
export const updateAdCampaign = (data) => {
    return service({
        url: '/adCampaign/updateAdCampaign',
        method: 'put',
        data
    })
}

// @Tags AdCampaign
// @Summary 用id查询AdCampaign
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.AdCampaign true "用id查询AdCampaign"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /adCampaign/findAdCampaign [get]
export const findAdCampaign = (params) => {
    return service({
        url: '/adCampaign/findAdCampaign',
        method: 'get',
        params
    })
}

// @Tags AdCampaign
// @Summary 分页获取AdCampaign列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取AdCampaign列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /adCampaign/getAdCampaignList [get]
export const getAdCampaignList = (params) => {
    return service({
        url: '/adCampaign/getAdCampaignList',
        method: 'get',
        params
    })
}
