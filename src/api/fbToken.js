import service from '@/utils/request'

// @Tags facebook
// @Summary 创建facebook
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.facebook true "创建facebook"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /facebook/create [post]
export const createFacebook = (data) => {
    return service({
        url: '/facebook/create',
        method: 'post',
        data
    })
}

// @Tags facebook
// @Summary 删除facebook
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.facebook true "删除facebook"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /facebook/delete [delete]
export const deleteFacebook = (data) => {
    return service({
        url: '/facebook/delete',
        method: 'delete',
        data
    })
}

// @Tags facebook
// @Summary 更新facebook
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.facebook true "更新facebook"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /facebook/update [put]
export const updateFacebook = (data) => {
    return service({
        url: '/facebook/update',
        method: 'put',
        data
    })
}

// @Tags facebook
// @Summary 用id查询facebook
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.facebook true "用id查询facebook"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /facebook/find [get]
export const findFacebook = (params) => {
    return service({
        url: '/facebook/get',
        method: 'get',
        params
    })
}

// @Tags facebook
// @Summary 分页获取facebook列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取facebook列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /facebook/list [get]
export const getFacebookList = (params) => {
    return service({
        url: '/facebook/list',
        method: 'get',
        params
    })
}

export const getFacebookAll = (params) => {
    return service({
        url: '/facebook/all',
        method: 'get',
        params
    })
}
