import service from '@/utils/request'

export const findAnchorViolationStatis = (params) => {
  return service({url: '/anchorViolationStatis/findOne', method: 'get', params})
}

export const findAnchorRecentData = (params) => {
  return service({url: '/anchorViolationStatis/findAnchorRecentData', method: 'get', params})
}

export const getAnchorViolationStatisList = (params) => {
  return service({url: '/anchorViolationStatis/getPage', method: 'get', params})
}

export const createAnchorViolationStatis = (data) => {
  return service({url: '/anchorViolationStatis/create', method: 'post', data})
}

export const deleteAnchorViolationStatis = (data) => {
  return service({url: '/anchorViolationStatis/delete', method: 'delete', data})
}

export const deleteAnchorViolationStatisByIds = (data) => {
  return service({url: '/anchorViolationStatis/batchDelete', method: 'delete', data})
}

export const updateAnchorViolationStatis = (data) => {
  return service({url: '/anchorViolationStatis/update', method: 'put', data})
}
