import service from '@/utils/request'

// @Tags Users
// @Summary 创建Users
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Users true "创建Users"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /users/createUsers [post]
export const createUsers = (data) => {
  return service({
    url: '/users/createUsers',
    method: 'post',
    data
  })
}

export const deleteAuditUser = (data) => {
  return service({
    url: '/users/deleteAuditUser',
    method: 'post',
    data
  })
}

// @Tags Users
// @Summary 删除Users
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Users true "删除Users"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /users/deleteUsers [delete]
export const deleteUsers = (data) => {
  return service({
    url: '/users/deleteUsers',
    method: 'delete',
    data
  })
}

// @Tags Users
// @Summary 删除Users
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除Users"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /users/deleteUsers [delete]
export const deleteUsersByIds = (data) => {
  return service({
    url: '/users/deleteUsersByIds',
    method: 'delete',
    data
  })
}

// @Tags Users
// @Summary 更新Users
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.Users true "更新Users"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /users/updateUsers [put]
export const updateUsers = (data) => {
  return service({
    url: '/users/updateUsers',
    method: 'put',
    data
  })
}

// @Tags Users
// @Summary 用id查询Users
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.Users true "用id查询Users"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /users/findUsers [get]
export const findUsers = (params) => {
  return service({
    url: '/users/findUsers',
    method: 'get',
    params
  })
}

// @Tags Users
// @Summary 分页获取Users列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取Users列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /users/getUsersList [get]
export const getUsersList = (params) => {
  return service({
    url: '/users/getUsersList',
    method: 'get',
    params
  })
}

// @Tags Users
// @Summary 分页获取Users列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取Users列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /users/getUsersList [get]
export const getUsersMonitorList = (params) => {
  return service({
    url: '/users/getUsersMonitorList',
    method: 'get',
    params
  })
}

// @Tags Users
// @Summary 分页获取Users列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取Users列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /users/getAnchorTankList [get]
export const getAnchorTankList = (params) => {
  return service({
    url: '/users/getAnchorTankList',
    method: 'get',
    params
  })
}

// usersAuditList
export const usersAuditList = (params) => {
  return service({
    url: '/users/usersAuditList',
    method: 'get',
    params
  })
}
export const getUserDetail = (params) => {
  return service({
    url: '/users/getUserDetail',
    method: 'get',
    params
  })
}

export const getAuditUsers = (params) => {
  return service({
    url: '/users/getAuditUsers',
    method: 'get',
    params
  })
}

export const updateAuditUsers = (data) => {
  return service({
    url: '/users/updateAuditUsers',
    method: 'put',
    data
  })
}


export const cancelAuditUsers = (data) => {
  return service({
    url: '/users/cancelAuditUsers',
    method: 'put',
    data
  })
}

export const banUser = (data) => {
  return service({
    url: '/users/banUser',
    method: 'post',
    data
  })
}
export const unsealUser = (data) => {
  return service({
    url: '/users/unsealUser',
    method: 'post',
    data
  })
}

export const anchorOnlineCount = (params) => {
  return service({
    url: '/users/anchorOnlineCount',
    method: 'get',
    params
  })
}

export const getWhiteList = (params) => {
  return service({
    url: '/users/getWhiteList',
    method: 'get',
    params
  })
}

export const getAnchorRecentData = (params) => {
  return service({
    url: `/users/getAnchorRecentData/${params}/`,
    method: 'get'
  })
}
export const getAnchorRecentDataV2 = (params) => {
  return service({
    url: `/users/getAnchorRecentDataV2/${params}/`,
    method: 'get'
  })
}

export const addWhiteList = (data) => {
  return service({
    url: '/users/addWhiteList',
    method: 'post',
    data
  })
}

export const delWhiteList = (data) => {
  return service({
    url: '/users/delWhiteList',
    method: 'delete',
    data
  })
}

export const deleteDeviceLanguage = (data) => {
  return service({
    url: '/users/deleteDeviceLanguage',
    method: 'post',
    data
  })
}

export const addDiamonds = (data) => {
  return service({
    url: '/userWallet/addDiamonds',
    method: 'post',
    data
  })
}
export const findOneUserWallet = (params) => {
  return service({
    url: '/userWallet/findOne',
    method: 'get',
    params
  })
}

export const updateUsersFake = (data) => {
  return service({
    url: '/users/updateUsersFake',
    method: 'put',
    data
  })
}
