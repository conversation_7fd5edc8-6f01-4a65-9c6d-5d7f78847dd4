import service from '@/utils/request'

export const findStrategyVideo = (params) => {
  return service({url: '/strategyVideo/findOne', method: 'get', params})
}

export const findStrategyVideoDetail = (params) => {
  return service({url: '/strategyVideo/findOneDetail', method: 'get', params})
}

export const getStrategyVideoList = (params) => {
  return service({url: '/strategyVideo/getPage', method: 'get', params})
}

export const createStrategyVideo = (data) => {
  return service({url: '/strategyVideo/create', method: 'post', data})
}
export const createStrategyVideoDetail = (data) => {
  return service({url: '/strategyVideo/createDetail', method: 'post', data})
}

export const deleteStrategyVideo = (data) => {
  return service({url: '/strategyVideo/delete', method: 'delete', data})
}
export const deleteStrategyVideoDetail = (data) => {
  return service({url: '/strategyVideo/deleteDetail', method: 'delete', data})
}

export const deleteStrategyVideoByIds = (data) => {
  return service({url: '/strategyVideo/batchDelete', method: 'delete', data})
}

export const updateStrategyVideo = (data) => {
  return service({url: '/strategyVideo/update', method: 'put', data})
}
export const updateStrategyVideoDetail = (data) => {
  return service({url: '/strategyVideo/updateStrategyVideo', method: 'put', data})
}
