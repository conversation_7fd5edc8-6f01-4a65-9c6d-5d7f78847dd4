import service from '@/utils/request'

export const findUserTaskV3AwardRd = (params) => {
  return service({url: '/userTaskV3AwardRd/findOne', method: 'get', params})
}

export const getUserTaskV3AwardRdList = (params) => {
  return service({url: '/userTaskV3AwardRd/getPage', method: 'get', params})
}

export const stateMapList = (params) => {
  return service({url: '/userTaskV3AwardRd/stateMapList', method: 'get', params})
}
export const funcTitleMapList = (params) => {
  return service({url: '/userTaskV3AwardRd/funcTitleMapList', method: 'get', params})
}

export const createUserTaskV3AwardRd = (data) => {
  return service({url: '/userTaskV3AwardRd/create', method: 'post', data})
}

export const deleteUserTaskV3AwardRd = (data) => {
  return service({url: '/userTaskV3AwardRd/delete', method: 'delete', data})
}

export const deleteUserTaskV3AwardRdByIds = (data) => {
  return service({url: '/userTaskV3AwardRd/batchDelete', method: 'delete', data})
}

export const updateUserTaskV3AwardRd = (data) => {
  return service({url: '/userTaskV3AwardRd/update', method: 'put', data})
}

export const batchStateUpdate = (data) => {
  return service({url: '/userTaskV3AwardRd/batchStateUpdate', method: 'put', data})
}
