import service from '@/utils/request'

export const findUserCardInfos = (params) => {
  return service({url: '/userCardInfos/findOne', method: 'get', params})
}

export const getUserCardInfosList = (params) => {
  return service({url: '/userCardInfos/getPage', method: 'get', params})
}

export const createUserCardInfos = (data) => {
  return service({url: '/userCardInfos/create', method: 'post', data})
}

export const deleteUserCardInfos = (data) => {
  return service({url: '/userCardInfos/delete', method: 'delete', data})
}

export const deleteUserCardInfosByIds = (data) => {
  return service({url: '/userCardInfos/batchDelete', method: 'delete', data})
}

export const updateUserCardInfos = (data) => {
  return service({url: '/userCardInfos/update', method: 'put', data})
}
export const saveUserCard = (data) => {
  return service({url: '/userCardInfos/saveUserCard', method: 'put', data})
}
