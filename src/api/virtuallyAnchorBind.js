import service from '@/utils/request'

export const findVirtuallyAnchorBind = (params) => {
  return service({url: '/virtuallyAnchorBind/findOne', method: 'get', params})
}

export const getVirtuallyAnchorBindList = (params) => {
  return service({url: '/virtuallyAnchorBind/getPage', method: 'get', params})
}

export const createVirtuallyAnchorBind = (data) => {
  return service({url: '/virtuallyAnchorBind/create', method: 'post', data})
}

export const deleteVirtuallyAnchorBind = (data) => {
  return service({url: '/virtuallyAnchorBind/delete', method: 'delete', data})
}

export const deleteVirtuallyAnchorBindByIds = (data) => {
  return service({url: '/virtuallyAnchorBind/batchDelete', method: 'delete', data})
}

export const updateVirtuallyAnchorBind = (data) => {
  return service({url: '/virtuallyAnchorBind/update', method: 'put', data})
}
