import service from '@/utils/request'

export const findPayTemplate = (params) => {
  return service({url: '/payTemplate/findOne', method: 'get', params})
}

export const getPayTemplateList = (params) => {
  return service({url: '/payTemplate/getPage', method: 'get', params})
}

export const createPayTemplate = (data) => {
  return service({url: '/payTemplate/create', method: 'post', data})
}

export const deletePayTemplate = (data) => {
  return service({url: '/payTemplate/delete', method: 'delete', data})
}

export const deletePayTemplateByIds = (data) => {
  return service({url: '/payTemplate/batchDelete', method: 'delete', data})
}

export const updatePayTemplate = (data) => {
  return service({url: '/payTemplate/update', method: 'put', data})
}
