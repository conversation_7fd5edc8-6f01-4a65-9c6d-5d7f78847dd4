import service from '@/utils/request'

export const findAnchorValue = (params) => {
    return service({url: '/anchorValue/findOne', method: 'get', params})
}

export const getAnchorValueList = (params) => {
    return service({url: '/anchorValue/getPage', method: 'get', params})
}

export const createAnchorValue = (data) => {
    return service({url: '/anchorValue/create', method: 'post', data})
}

export const deleteAnchorValue = (data) => {
    return service({url: '/anchorValue/delete', method: 'delete', data})
}

export const deleteAnchorValueByIds = (data) => {
    return service({url: '/anchorValue/batchDelete', method: 'delete', data})
}

export const updateAnchorValue = (data) => {
    return service({url: '/anchorValue/update', method: 'put', data})
}
