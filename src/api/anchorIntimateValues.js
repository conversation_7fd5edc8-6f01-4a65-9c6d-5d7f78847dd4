import service from '@/utils/request'

export const findAnchorIntimateValues = (params) => {
  return service({url: '/anchorIntimateValues/findOne', method: 'get', params})
}

export const getAnchorIntimateValuesList = (params) => {
  return service({url: '/anchorIntimateValues/getPage', method: 'get', params})
}

export const getAnchorIntimateValuesListData = (params) => {
  return service({url: '/anchorIntimateValues/getPageData', method: 'get', params})
}

export const createAnchorIntimateValues = (data) => {
  return service({url: '/anchorIntimateValues/create', method: 'post', data})
}

export const deleteAnchorIntimateValues = (data) => {
  return service({url: '/anchorIntimateValues/delete', method: 'delete', data})
}

export const deleteAnchorIntimateValuesByIds = (data) => {
  return service({url: '/anchorIntimateValues/batchDelete', method: 'delete', data})
}

export const updateAnchorIntimateValues = (data) => {
  return service({url: '/anchorIntimateValues/update', method: 'put', data})
}
