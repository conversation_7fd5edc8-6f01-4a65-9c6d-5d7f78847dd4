
import service from '@/utils/request'

// @Tags AnchorSettlementDailies
// @Summary 创建AnchorSettlementDailies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorSettlementDailies true "创建AnchorSettlementDailies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /anchorSettlementDailies/createAnchorSettlementDailies [post]
export const createAnchorSettlementDailies = (data) => {
  return service({
    url: '/anchorSettlementDailies/createAnchorSettlementDailies',
    method: 'post',
    data
  })
}

// @Tags AnchorSettlementDailies
// @Summary 删除AnchorSettlementDailies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorSettlementDailies true "删除AnchorSettlementDailies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /anchorSettlementDailies/deleteAnchorSettlementDailies [delete]
export const deleteAnchorSettlementDailies = (data) => {
  return service({
    url: '/anchorSettlementDailies/deleteAnchorSettlementDailies',
    method: 'delete',
    data
  })
}

// @Tags AnchorSettlementDailies
// @Summary 删除AnchorSettlementDailies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除AnchorSettlementDailies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /anchorSettlementDailies/deleteAnchorSettlementDailies [delete]
export const deleteAnchorSettlementDailiesByIds = (data) => {
  return service({
    url: '/anchorSettlementDailies/deleteAnchorSettlementDailiesByIds',
    method: 'delete',
    data
  })
}

// @Tags AnchorSettlementDailies
// @Summary 更新AnchorSettlementDailies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.AnchorSettlementDailies true "更新AnchorSettlementDailies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /anchorSettlementDailies/updateAnchorSettlementDailies [put]
export const updateAnchorSettlementDailies = (data) => {
  return service({
    url: '/anchorSettlementDailies/updateAnchorSettlementDailies',
    method: 'put',
    data
  })
}

// @Tags AnchorSettlementDailies
// @Summary 用id查询AnchorSettlementDailies
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.AnchorSettlementDailies true "用id查询AnchorSettlementDailies"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /anchorSettlementDailies/findAnchorSettlementDailies [get]
export const findAnchorSettlementDailies = (params) => {
  return service({
    url: '/anchorSettlementDailies/findAnchorSettlementDailies',
    method: 'get',
    params
  })
}

// @Tags AnchorSettlementDailies
// @Summary 分页获取AnchorSettlementDailies列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取AnchorSettlementDailies列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /anchorSettlementDailies/getAnchorSettlementDailiesList [get]
export const getAnchorSettlementDailiesList = (params) => {
  return service({
    url: '/anchorSettlementDailies/getAnchorSettlementDailiesList',
    method: 'get',
    params
  })
}
