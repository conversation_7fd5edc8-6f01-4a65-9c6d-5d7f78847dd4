import service from '@/utils/request'

export const findAppOperate = (params) => {
  return service({url: '/appOperate/findOne', method: 'get', params})
}

export const getAppOperateList = (params) => {
  return service({url: '/appOperate/getPage', method: 'get', params})
}

export const createAppOperate = (data) => {
  return service({url: '/appOperate/create', method: 'post', data})
}

export const deleteAppOperate = (data) => {
  return service({url: '/appOperate/delete', method: 'delete', data})
}

export const deleteAppOperateByIds = (data) => {
  return service({url: '/appOperate/batchDelete', method: 'delete', data})
}

export const updateAppOperate = (data) => {
  return service({url: '/appOperate/update', method: 'put', data})
}
