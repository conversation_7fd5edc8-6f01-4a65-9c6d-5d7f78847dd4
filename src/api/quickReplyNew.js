import service from '@/utils/request'

export const findQuickReplyNew = (params) => {
  return service({url: '/quickReplyNew/findOne', method: 'get', params})
}

export const getQuickReplyNewList = (params) => {
  return service({url: '/quickReplyNew/getPage', method: 'get', params})
}

export const createQuickReplyNew = (data) => {
  return service({url: '/quickReplyNew/create', method: 'post', data})
}

export const deleteQuickReplyNew = (data) => {
  return service({url: '/quickReplyNew/delete', method: 'delete', data})
}

export const deleteQuickReplyNewByIds = (data) => {
  return service({url: '/quickReplyNew/batchDelete', method: 'delete', data})
}

export const updateQuickReplyNew = (data) => {
  return service({url: '/quickReplyNew/update', method: 'put', data})
}
