import service from '@/utils/request'

// @Tags TransferPools
// @Summary 创建TransferPools
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.TransferPools true "创建TransferPools"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /transferPools/createTransferPools [post]
export const createTransferPools = (data) => {
    return service({
        url: '/transferPools/createTransferPools',
        method: 'post',
        data
    })
}

// @Tags TransferPools
// @Summary 删除TransferPools
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.TransferPools true "删除TransferPools"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /transferPools/deleteTransferPools [delete]
export const deleteTransferPools = (data) => {
    return service({
        url: '/transferPools/deleteTransferPools',
        method: 'delete',
        data
    })
}

// @Tags TransferPools
// @Summary 删除TransferPools
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除TransferPools"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /transferPools/deleteTransferPools [delete]
export const deleteTransferPoolsByIds = (data) => {
    return service({
        url: '/transferPools/deleteTransferPoolsByIds',
        method: 'delete',
        data
    })
}

// @Tags TransferPools
// @Summary 更新TransferPools
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.TransferPools true "更新TransferPools"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /transferPools/updateTransferPools [put]
export const updateTransferPools = (data) => {
    return service({
        url: '/transferPools/updateTransferPools',
        method: 'post',
        data
    })
}

// @Tags TransferPools
// @Summary 用id查询TransferPools
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.TransferPools true "用id查询TransferPools"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /transferPools/findTransferPools [get]
export const findTransferPools = (params) => {
    return service({
        url: '/transferPools/findTransferPools',
        method: 'get',
        params
    })
}

// @Tags TransferPools
// @Summary 分页获取TransferPools列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取TransferPools列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /transferPools/getTransferPoolsList [get]
export const getTransferPoolsList = (params) => {
    return service({
        url: '/transferPools/getTransferPoolsList',
        method: 'get',
        params
    })
}
