import service from '@/utils/request'

export const topInfo = (params) => {
    return service({ url: '/home_page/topInfo', method: 'get', params })
}
export const topUserInfo = (params) => {
    return service({ url: '/home_page/topUserInfo', method: 'get', params })
}

export const recentIncome = (params) => {
    return service({ url: '/home_page/recentIncome', method: 'get', params })
}

export const dayHourOrderCount = (params) => {
    return service({ url: '/home_page/dayHourOrderCount', method: 'get', params })
}

export const dayHourUserCount = (params) => {
    return service({ url: '/home_page/dayHourUserCount', method: 'get', params })
}
