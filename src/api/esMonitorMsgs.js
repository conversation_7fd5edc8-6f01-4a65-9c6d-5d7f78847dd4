import service from '@/utils/request'

// @Tags EsMonitorMsgs
// @Summary 创建EsMonitorMsgs
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.EsMonitorMsgs true "创建EsMonitorMsgs"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /esMonitorMsgs/createEsMonitorMsgs [post]
export const createEsMonitorMsgs = (data) => {
  return service({
    url: '/esMonitorMsgs/createEsMonitorMsgs',
    method: 'post',
    data
  })
}

// @Tags EsMonitorMsgs
// @Summary 删除EsMonitorMsgs
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.EsMonitorMsgs true "删除EsMonitorMsgs"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /esMonitorMsgs/deleteEsMonitorMsgs [delete]
export const deleteEsMonitorMsgs = (data) => {
  return service({
    url: '/esMonitorMsgs/deleteEsMonitorMsgs',
    method: 'delete',
    data
  })
}

// @Tags EsMonitorMsgs
// @Summary 删除EsMonitorMsgs
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body request.IdsReq true "批量删除EsMonitorMsgs"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"删除成功"}"
// @Router /esMonitorMsgs/deleteEsMonitorMsgs [delete]
export const deleteEsMonitorMsgsByIds = (data) => {
  return service({
    url: '/esMonitorMsgs/deleteEsMonitorMsgsByIds',
    method: 'delete',
    data
  })
}

// @Tags EsMonitorMsgs
// @Summary 更新EsMonitorMsgs
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data body model.EsMonitorMsgs true "更新EsMonitorMsgs"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"更新成功"}"
// @Router /esMonitorMsgs/updateEsMonitorMsgs [put]
export const updateEsMonitorMsgs = (data) => {
  return service({
    url: '/esMonitorMsgs/updateEsMonitorMsgs',
    method: 'put',
    data
  })
}

// @Tags EsMonitorMsgs
// @Summary 用id查询EsMonitorMsgs
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query model.EsMonitorMsgs true "用id查询EsMonitorMsgs"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"查询成功"}"
// @Router /esMonitorMsgs/findEsMonitorMsgs [get]
export const findEsMonitorMsgs = (params) => {
  return service({
    url: '/esMonitorMsgs/findEsMonitorMsgs',
    method: 'get',
    params
  })
}

// @Tags EsMonitorMsgs
// @Summary 分页获取EsMonitorMsgs列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param data query request.PageInfo true "分页获取EsMonitorMsgs列表"
// @Success 200 {string} string "{"success":true,"data":{},"msg":"获取成功"}"
// @Router /esMonitorMsgs/getEsMonitorMsgsList [get]
export const getEsMonitorMsgsList = (params) => {
  return service({
    url: '/esMonitorMsgs/getEsMonitorMsgsList',
    method: 'get',
    params
  })
}

export const getEsMsgs = (params) => {
  return service({
    url: '/esMonitorMsgs/getEsMsgs',
    method: 'get',
    params
  })
}
export const getEsDetailMsgs = (params) => {
  return service({
    url: '/esMonitorMsgs/getEsDetailMsgs',
    method: 'get',
    params
  })
}
export const getNewEsDetailMsgs = (params) => {
  return service({
    url: '/esMonitorMsgs/getNewEsDetailMsgs',
    method: 'get',
    params
  })
}
