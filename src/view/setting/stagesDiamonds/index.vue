<template>
  <div>
    <!--<div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>-->
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" prop="id" label="ID" width="180" />
        <el-table-column align="center" label="更新日期" width="180">
          <template #default="scope">{{ formatDate(scope.row.updated_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="方案名称" prop="name" width="160" show-overflow-tooltip />
        <el-table-column align="center" label="方案详情" min-width="120" show-overflow-tooltip>
          <template #default="scope"> {{ combineSchema(scope.row.scheme) }}</template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="160">
          <template #default="scope">
            <el-button
              type="text"
              icon="el-icon-edit"
              size="small"
              class="table-button"
              @click="updateStagesDiamonds(scope.row)"
            >修改
            </el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" width="30%" top="10px" :before-close="closeDialog" title="弹窗操作">
      <el-form ref="formData" :model="formData" label-position="right" label-width="100px">
        <el-form-item
          label="方案名称:"
          prop="name"
          :rules="{
            required: true,
            message: '方案名称不能为空',
            trigger: 'blur',
          }"
        >
          <el-input v-model="formData.name" clearable placeholder="请输入方案名称" />
        </el-form-item>
        <el-form-item
          v-for="(item, index) in formData.scheme"
          :key="item"
          :label="`钻石${index}:`"
        >
          <div style="display: flex; flex-direction: row">
            <el-input v-model.number="item.diamond" size="small" placeholder="请输入赠送钻石数量" />
            <el-button style="margin-left: 5px" size="small" type="danger" @click="removeDiamond(item)">X</el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" @click="addDiamondItem">增加一个</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createStagesDiamonds,
  deleteStagesDiamonds,
  deleteStagesDiamondsByIds,
  findStagesDiamonds,
  getStagesDiamondsList,
  updateStagesDiamonds,
} from '@/api/stagesDiamonds' //  此处请自行替换地址
import infoList from '@/mixins/infoList'

export default {
  name: 'StagesDiamonds',
  mixins: [infoList],
  data() {
    return {
      listApi: getStagesDiamondsList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        name: '',
        scheme: [],
      },
      schemeInfo: [],
    }
  },
  async created() {
    await this.getTableData()
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteStagesDiamonds(row)
      })
    },
    addDiamondItem() {
      if (this.formData.scheme === undefined) {
        this.formData.scheme = []
      }
      this.formData.scheme.push({ diamond: 0 })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
        this.multipleSelection.map(item => {
          ids.push(item.id)
        })
      const res = await deleteStagesDiamondsByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateStagesDiamonds(row) {
      const res = await findStagesDiamonds({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.stagesDiamondsInfo
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        name: '',
        created_time: 0,
        updated_time: 0,
      }
    },
    async deleteStagesDiamonds(row) {
      const res = await deleteStagesDiamonds({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    removeDiamond(item) {
      const index = this.formData.scheme.indexOf(item)
      if (index !== -1) {
        this.formData.scheme.splice(index, 1)
      }
    },
    async enterDialog() {
      this.$refs['formData'].validate(async valid => {
        if (!valid) return
        if (this.formData.scheme === undefined || this.formData.scheme.length === 0) {
          this.$message.error('必须要有一条方案.')
          return
        }
        let res
        switch (this.type) {
          case 'create':
            res = await createStagesDiamonds(this.formData)
            break
          case 'update':
            res = await updateStagesDiamonds(this.formData)
            break
          default:
            res = await createStagesDiamonds(this.formData)
            break
        }
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '创建/更改成功'
          })
          this.closeDialog()
          this.getTableData()
        }
      })
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    combineSchema(scheme) {
      let ret = ''
      if (scheme === null || scheme.length === 0) {
        return ret
      }
      var day = 0
      scheme.forEach((item) => {
        ret += 'day' + day + ':' + item.diamond + ','
        day++
      })
      return ret
    }
  },
}
</script>

<style>
</style>
