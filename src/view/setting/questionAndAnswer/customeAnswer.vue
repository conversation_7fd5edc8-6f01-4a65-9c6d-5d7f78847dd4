<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
        <el-popover v-model:visible="deleteVisible" placement="top" width="160">
          <p>确定要删除吗？</p>
          <div style="text-align: right; margin-top: 8px;">
            <el-button size="mini" type="text" @click="deleteVisible = false">取消</el-button>
            <el-button size="mini" type="primary" @click="onDelete">确定</el-button>
          </div>
          <template #reference>
            <el-button
              icon="el-icon-delete"
              size="mini"
              style="margin-left: 10px;"
              :disabled="!multipleSelection.length"
            >删除
            </el-button>
          </template>
        </el-popover>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="center" label="日期" width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="ID" prop="id" width="120" />
        <el-table-column align="center" label="文本" prop="text" width="400" />
        <el-table-column align="center" label="状态" prop="status" width="120">
          <template #default="scope">{{ formatBoolean(scope.row.status) }}</template>
        </el-table-column>
        <el-table-column align="center" label="间隔时间" prop="time" width="120" />
        <el-table-column align="center" label="按钮组">
          <template #default="scope">
            <el-button size="mini" icon="el-icon-document" type="text" @click="toDetile(scope.row)">详情</el-button>
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateQuestionAndAnswers(scope.row)">变更</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作" top="1%">
      <el-form
        :model="formData"
        label-position="right"
        label-width="80px"
        style="max-height: 80vh;overflow-y: auto;"
      >
        <el-form-item label="文本:">
          <el-input v-model="formData.text" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="状态:">
          <el-switch
            v-model="formData.status"
            active-color="#13ce66"
            inactive-color="#ff4949"
            active-text="是"
            inactive-text="否"
            clearable
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createQuestionAndAnswers,
  deleteQuestionAndAnswers,
  deleteQuestionAndAnswersByIds,
  updateQuestionAndAnswers,
  findQuestionAndAnswers,
  getQuestionAndAnswersList
} from '@/api/questionAndAnswers' //  此处请自行替换地址
import infoList from '@/mixins/infoList'

export default {
  name: 'CustomeAnswer',
  mixins: [infoList],
  data() {
    return {
      listApi: getQuestionAndAnswersList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      params: [],
      formData: {
        parent_id: 0,
        type: 2,
        text: '',
        status: false,
        time: 0,
      },
      environmentForm: {
        items: [{
          name: '',
          variable: '',
        }]
      }
    }
  },
  async created() {
    this.searchInfo.parent_id = Number(this.$route.params.id)
    this.formData.parent_id = Number(this.$route.params.id)
    await this.getTableData()
  },
  methods: {
    toDetile(row) {
      this.$router.push({
        name: 'anchorAnswer',
        params: {
          id: row.id
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    removeEnvironmentForm(item) {
      var index = this.environmentForm.items.indexOf(item)
      if (index !== -1) {
        this.environmentForm.items.splice(index, 1)
      }
    },
    addEnvironmentForm() {
      this.environmentForm.items.push({
        name: '',
        variable: '',
        description: '',
        key: Date.now()
      })
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      if (this.searchInfo.status === '') {
        this.searchInfo.status = null
      }
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteQuestionAndAnswers(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
                this.multipleSelection.map(item => {
                  ids.push(item.id)
                })
      const res = await deleteQuestionAndAnswersByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateQuestionAndAnswers(row) {
      const res = await findQuestionAndAnswers({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.requestionAndAnswers
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        parent_id: Number(this.$route.params.id),
        type: 2,
        text: '',
        status: false,
        time: 0,
      }
    },
    async deleteQuestionAndAnswers(row) {
      const res = await deleteQuestionAndAnswers({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createQuestionAndAnswers(this.formData)
          break
        case 'update':
          res = await updateQuestionAndAnswers(this.formData)
          break
        default:
          res = await createQuestionAndAnswers(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    }
  },
}
</script>

<style>
</style>
