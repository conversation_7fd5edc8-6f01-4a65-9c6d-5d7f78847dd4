<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <!--
        <el-form-item label="产品名称">
          <el-select multiple collapse-tags v-model="searchInfo.app_id_range" clearable>
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="`${item.app_name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        -->
        <el-form-item label="发出ID">
          <el-input v-model="searchInfo.src_id"></el-input>
        </el-form-item>
        <el-form-item label="接收ID">
          <el-input v-model="searchInfo.dst_id"></el-input>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchInfo.date_range"
            type="daterange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DDTHH:mm:ssZ"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="text" :icon="queryTxtIcon" @click="queryTxtChange">{{ queryTxt }}</el-button>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0"
                     @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="tableData"
        style="width: 100%"
        :data="tableData"
        max-height="590"
        row-key="id">
        <el-table-column align="center" label="发出ID" prop="src_id" width="100" :sortable="true"></el-table-column>
        <el-table-column align="center" label="发出昵称" prop="src_name" width="140"
                         show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="接收ID" prop="dst_id" width="100" :sortable="true"></el-table-column>
        <el-table-column align="center" label="接收昵称" prop="dst_name" width="140"
                         show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="文字内容" prop="send_msg" min-width="180"
                         show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="状态" prop="read_type" width="80">
          <template #default="scope">
            <el-tag :type="scope.row.read_type === '已读' ? 'success':'danger' ">{{scope.row.read_type}}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="发起时间" prop="src_time" width="160"></el-table-column>
        <el-table-column align="center" label="北京时间" prop="beijing_time" width="160"></el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeListNormal"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import {getEsMsgs} from '@/api/esMonitorMsgs' //  此处请自行替换地址
  import infoList from '@/mixins/infoList'
  import esMonitorMsgsMixins from '@/mixins/esMonitorMsgs'

  export default {
    mixins: [infoList, esMonitorMsgsMixins],
    data() {
      return {
        searchInfo: {},
        listApi: getEsMsgs,
      }
    },
    async created() {
      await this.getTableData(this.nFunc, this.tableDataFormat)
    },
    methods: {
      onReset() {
        this.searchInfo = {}
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      onSubmit() {
        this.page = 1
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      getSummaries({columns, data}) {
        // 获取表头和数据
        // :show-summary="this.searchInfo.group_range.length > 0"
        // :summary-method="getSummaries"
        let labelList = []
        let vList = []
        columns.forEach((column, i) => {
          labelList.push(column.label)
          vList.push(column.property)
        })
        console.log(JSON.stringify(labelList))
        console.log(JSON.stringify(vList))
      },
      tableDataFormat() {
        this.tableData.map((item, i, data) => {
          data[i].read_type = data[i].read_type === 2 ? "已读" : "未读"
        })
      },
      handleDownload() {
        let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
        let res = this.getTableHeaderProp(tableColumns)
        let tHeader = res[0]
        let filterV = res[1]
        this.handleProgressLoading()
        import('@/utils/excel').then((excel) => {
          const data = this.formatJson(filterV, this.tableData)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.filename,
            autoWidth: this.autoWidth,
            bookType: this.bookType,
          })
          this.progressLoading.close()
        })
      },
      sizeChange(v) {
        this.pageSize = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      pageChange(v) {
        this.page = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
    },
  }
</script>

<style lang="scss" scoped>

</style>
