<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="用户ID">
          <el-input v-model="searchInfo.user_id" placeholder="请输入用户ID"/>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" type="success" icon="el-icon-plus" @click="openDialog">新增</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table ref="tableData" border style="width: 100%" :data="tableData" row-key="id" :max-height="590">
        <el-table-column align="center" label="日期" prop="created_at_str" min-width="180"/>
        <el-table-column align="center" label="用户ID" prop="user_id" min-width="120"/>
        <el-table-column align="center" label="工会长ID" prop="union_manager_id" min-width="120"/>
        <el-table-column align="center" label="工会名称" prop="union_name" min-width="120"/>
        <el-table-column align="center" label="绑定账号" prop="account_no_info" min-width="120"
                         show-overflow-tooltip/>
        <el-table-column align="center" label="操作" min-width="180">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button"
                       @click="updateWithdrawalAccount(scope.row)">修改
            </el-button>
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button"
                       @click="updateAccountNo(scope.row)">修改账号
            </el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" top="5px" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" :rules="formDataRules" label-position="right" label-width="120px">
        <el-form-item label="用户ID:" prop="user_id">
          <el-input v-model.number="formData.user_id" clearable/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogAccountVisible" top="5px" :before-close="closeAccountDialog" title="联系方式">
      <el-tabs
        v-model="activeTabName"
        type="border-card"
        @tab-click="handleTabClick"
      >
        <el-tab-pane label="Payoneer" name="payoneer">
          <el-form ref="formPayoneerDataRef" :model="formPayoneerData" :rules="formPayoneerDataRules"
                   label-position="right"
                   label-width="120px">
            <el-form-item label="用户ID:" prop="user_id">
              <el-input disabled v-model.number="formPayoneerData.user_id"/>
            </el-form-item>
            <el-form-item label="账号:" prop="account_no">
              <el-input v-model="formPayoneerData.account_no" clearable/>
            </el-form-item>
            <el-form-item label="手续费:" prop="fee">
              <div class="flex-box">
                <el-input v-model="formPayoneerData.fee"/>
                <el-tooltip
                  effect="dark"
                  content="手续费(0-100)"
                  placement="top"
                >
                  <div style="margin-left: 4px;">
                    <i class="el-icon-warning"></i>
                  </div>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="到账时间:" prop="arrival">
              <el-input-number v-model="formPayoneerData.arrival"/>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="savePayoneerData">保存</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="Bank" name="bank">
          <el-form ref="formBankDataRef" :model="formBankData" :rules="formBankDataRules" label-position="right"
                   label-width="120px">
            <el-form-item label="用户ID:" prop="user_id">
              <el-input disabled v-model.number="formBankData.user_id"/>
            </el-form-item>
            <el-form-item label="账号:" prop="account_no">
              <el-input v-model="formBankData.account_no" clearable/>
            </el-form-item>
            <el-form-item label="国家:" prop="country">
              <el-input v-model="formBankData.country" clearable/>
            </el-form-item>
            <el-form-item label="姓名:" prop="account_name">
              <el-input v-model="formBankData.account_name" clearable/>
            </el-form-item>
            <el-form-item label="手机号:" prop="account_phone">
              <el-input v-model="formBankData.account_phone" clearable/>
            </el-form-item>
            <el-form-item label="邮箱:" prop="account_email">
              <el-input v-model="formBankData.account_email" clearable/>
            </el-form-item>
            <el-form-item label="地址:" prop="account_address">
              <el-input v-model="formBankData.account_address" clearable/>
            </el-form-item>
            <el-form-item label="银行码:" prop="bank_code">
              <el-input v-model="formBankData.bank_code" clearable/>
            </el-form-item>
            <el-form-item label="手续费:" prop="fee">
              <div class="flex-box">
                <el-input v-model="formBankData.fee"/>
                <el-tooltip
                  effect="dark"
                  content="手续费(0-100)"
                  placement="top"
                >
                  <div style="margin-left: 4px;">
                    <i class="el-icon-warning"></i>
                  </div>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="到账时间:" prop="arrival">
              <el-input-number v-model="formBankData.arrival"/>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveBankData">保存</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
        <el-tab-pane label="Gcash" name="gcash">
          <el-form ref="formGcashDataRef" :model="formGcashData" :rules="formGcashDataRules" label-position="right"
                   label-width="120px">
            <el-form-item label="用户ID:" prop="user_id">
              <el-input disabled v-model.number="formGcashData.user_id"/>
            </el-form-item>
            <el-form-item label="账号:" prop="account_no">
              <el-input v-model="formGcashData.account_no" clearable/>
            </el-form-item>
            <el-form-item label="国家:" prop="country">
              <el-input v-model="formGcashData.country" clearable/>
            </el-form-item>
            <el-form-item label="手续费:" prop="fee">
              <div class="flex-box">
                <el-input v-model="formGcashData.fee"/>
                <el-tooltip
                  effect="dark"
                  content="手续费(0-100)"
                  placement="top"
                >
                  <div style="margin-left: 4px;">
                    <i class="el-icon-warning"></i>
                  </div>
                </el-tooltip>
              </div>
            </el-form-item>
            <el-form-item label="到账时间:" prop="arrival">
              <el-input-number v-model="formGcashData.arrival"/>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="saveGcashData">保存</el-button>
            </el-form-item>
          </el-form>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
  </div>
</template>

<script>
import {
  createWithdrawalAccount,
  deleteWithdrawalAccount,
  deleteWithdrawalAccountByIds,
  updateWithdrawalAccount,
  findWithdrawalAccount,
  getWithdrawalAccountList,
  getPageWithAccountInfo, getExportExcelInfo
} from '@/api/withdrawalAccount' //  此处请自行替换地址
import {findWithdrawalAccountPayoneer, saveWithdrawalAccountPayoneer} from '@/api/withdrawalAccountPayoneer' //  此处请自行替换地址
import {findWithdrawalAccountBank, saveWithdrawalAccountBank} from '@/api/withdrawalAccountBank' //  此处请自行替换地址
import {findWithdrawalAccountGcash, saveWithdrawalAccountGcash} from '@/api/withdrawalAccountGcash' //  此处请自行替换地址
import XLSX from 'xlsx'
import { saveAs } from 'file-saver'
import infoList from '@/mixins/infoList'

export default {
  name: 'WithdrawalAccount',
  mixins: [infoList],
  data() {
    return {
      listApi: getPageWithAccountInfo,
      dialogFormVisible: false,
      dialogAccountVisible: false,
      dialogAccountUserId: 0,
      activeTabName: '',
      formPayoneerData: {},
      formBankData: {},
      formGcashData: {},
      formGcashDataRules: {
        account_no: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        fee: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        country: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        arrival: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
      formBankDataRules: {
        account_no: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        country: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        account_name: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        account_phone: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        account_email: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        account_address: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        bank_code: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        fee: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        arrival: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
      formPayoneerDataRules: {
        account_no: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        fee: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        arrival: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      formAccountData: {},
      formData: {
        user_id: null,
        union_manager_id: null,
        union_name: null,
      },
      formDataRules: {
        user_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        union_manager_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        union_name: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
      formAccountDataRules: {},
    }
  },
  created() {
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    saveGcashData() {
      this.$refs.formGcashDataRef.validate((valid) => {
        if (valid) {
          saveWithdrawalAccountGcash(this.formGcashData).then(res => {
            if (res.code === 0) {
              this.$message({
                type: 'success',
                message: '成功'
              })
            }
          })
        }
      })
    },
    savePayoneerData() {
      this.$refs.formPayoneerDataRef.validate((valid) => {
        if (valid) {
          saveWithdrawalAccountPayoneer(this.formPayoneerData).then(res => {
            if (res.code === 0) {
              this.$message({
                type: 'success',
                message: '成功'
              })
            }
          })
        }
      })
    },
    saveBankData() {
      this.$refs.formBankDataRef.validate((valid) => {
        if (valid) {
          saveWithdrawalAccountBank(this.formBankData).then(res => {
            if (res.code === 0) {
              this.$message({
                type: 'success',
                message: '成功'
              })
            }
          })
        }
      })
    },
    handleTabClick(pane, ev) {
      let tabName = pane.paneName
      switch (tabName) {
        case 'payoneer':
          findWithdrawalAccountPayoneer({user_id: this.dialogAccountUserId}).then(res => {
            if (res.code === 0) {
              this.formPayoneerData = res.data || {}
              this.formPayoneerData.user_id = this.dialogAccountUserId
            }
          }).catch(
            this.formPayoneerData.user_id = this.dialogAccountUserId
          )
          break
        case 'bank':
          findWithdrawalAccountBank({user_id: this.dialogAccountUserId}).then(res => {
            if (res.code === 0) {
              this.formBankData = res.data || {}
              this.formBankData.user_id = this.dialogAccountUserId
            }
          }).catch(
            this.formBankData.user_id = this.dialogAccountUserId
          )
          break
        case 'gcash':
          findWithdrawalAccountGcash({user_id: this.dialogAccountUserId}).then(res => {
            if (res.code === 0) {
              this.formGcashData = res.data || {}
              this.formGcashData.user_id = this.dialogAccountUserId
            }
          }).catch(
            this.formGcashData.user_id = this.dialogAccountUserId
          )
          break
      }
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteWithdrawalAccount(row)
      })
    },
    async updateWithdrawalAccount(row) {
      const res = await findWithdrawalAccount({id: row.id})
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data
        this.dialogFormVisible = true
      }
    },
    updateAccountNo(row) {
      this.dialogAccountUserId = row.user_id
      this.dialogAccountVisible = true
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {}
    },
    closeAccountDialog() {
      this.formPayoneerData = {}
      this.formBankData = {}
      this.formGcashData = {}
      this.activeTabName = ''
      this.dialogAccountVisible = false
    },
    async deleteWithdrawalAccount(row) {
      const res = await deleteWithdrawalAccount({id: row.id})
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createWithdrawalAccount(this.formData)
          break
        case 'update':
          res = await updateWithdrawalAccount(this.formData)
          break
        default:
          res = await createWithdrawalAccount(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '成功'
        })
        this.closeDialog()
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    s2ab(s) {
      const buf = new ArrayBuffer(s.length)
      const view = new Uint8Array(buf)
      for (let i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xff
      return buf
    },
    async exportExcel() {
      let res = await getExportExcelInfo()
      if (res.code === 0) {
        let resData = res.data || []

        let aoaList = [['主播ID', '工会名', '工会长ID', 'Payoneer-账号', 'Bank-账号', 'Bank-国家', 'Bank-姓名', 'Bank-手机号', 'Bank-邮箱', 'Bank-地址', 'Bank-银行码', 'Gcash-账号', 'Gcash-国家']]
        resData.forEach(v=>{
          aoaList.push([
            v.AccountInfo.user_id,
            v.AccountInfo.union_name,
            v.AccountInfo.union_manager_id,
            v.AccountPayoneerInfo.account_no,
            v.AccountBankInfo.account_no,
            v.AccountBankInfo.country,
            v.AccountBankInfo.account_name,
            v.AccountBankInfo.account_phone,
            v.AccountBankInfo.account_email,
            v.AccountBankInfo.account_address,
            v.AccountBankInfo.bank_code,
            v.AccountGcashInfo.account_name,
            v.AccountGcashInfo.country,
          ])
        })
        const sheet = XLSX.utils.aoa_to_sheet(aoaList);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, sheet, 'Sheet1');
        const wbout = XLSX.write(workbook, {
          bookType: 'xlsx',
          bookSST: false,
          type: 'binary',
        });
        saveAs(
          new Blob([this.s2ab(wbout)], {
            type: 'application/octet-stream',
          }),
          'test.xlsx'
        )
      }
    },

    exportExcelBak() {
      return
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    formatAccountNoInfo(item) {
      let res = ''
      if (item.payoneer_account_no) {
        res += `Payoneer: ${item.payoneer_account_no}`
      }
      if (item.bank_account_no) {
        res += `, Bank: ${item.bank_account_no}`
      }
      if (item.gcash_account_no) {
        res += `, Gcash: ${item.gcash_account_no}`
      }
      return res
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].account_no_info = this.formatAccountNoInfo(item)
      })
    },
  },
}
</script>

<style>
</style>
