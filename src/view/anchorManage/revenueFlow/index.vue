<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="主播ID">
          <el-input v-model="searchInfo.user_id" class="cs-search-input" placeholder="主播ID"/>
        </el-form-item>
        <el-form-item label="时间">

          <el-date-picker
              v-model="searchInfo.date_range"
              type="daterange"
              :shortcuts="dateRangeShortcuts"
              range-separator="到"
              start-placeholder="开始"
              end-placeholder="结束"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">Search</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">Reset</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">
            Export Excel
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
          style="width: 100%"
          tooltip-effect="dark"
          :data="tableData"
          row-key="id"
          max-height="590"
      >
        <el-table-column align="center" label="时间" prop="user_id"/>
        <el-table-column align="center" label="主播ID" prop="user_id"/>
        <el-table-column align="center" label="宝石收益" prop="user_id"/>
        <el-table-column align="center" label="收益来源" prop="user_id"/>
        <el-table-column align="center" label="通话时长" prop="user_id"/>
        <el-table-column align="center" label="礼物名称" prop="user_id"/>
        <el-table-column align="center" label="礼物数量" prop="user_id"/>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            v-show="total > 0"
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {getAnchorSettlementWeekliesList} from '@/api/anchorSettlementWeeklies'
import infoList from '@/mixins/infoList'
import {ElLoading} from 'element-plus'

export default {
  mixins: [infoList],
  data() {
    return {
      listApi: getAnchorSettlementWeekliesList,
      type: '',
      filename: 'anchor-settlement-data',
      autoWidth: true,
      bookType: 'xlsx',
    }
  },
  async created() {
    await this.getTableData()
  },
  methods: {
    handleProgressLoading() {
      this.progressLoading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
    },
    handleDownload() {
      this.handleProgressLoading()
      const that = this
      import('@/utils/excel').then((excel) => {
        let tHeader = []
        let filterVal = []
        if (this.userInfo.union_id !== 0) {
          tHeader = ['ID', 'Union', 'Blocked status', 'Number of calls received this week', 'Number of calls answered this week', 'Answered rate this week', 'Free call duration this week', 'Pay call duration this week', 'Effective call duration this week', 'Text chat salary  this week', 'Gift salary this week', 'Unsettlement income last week', 'Estimate salary', 'Final salary', 'Agency salary', 'Settlement result', '  income this week', 'Online Time', 'detail', '新手收益']
          filterVal = ['user_id', 'union_id', 'state', 'push_total_call_numbers', 'answer_total_call_numbers', 'connect_rate', 'free_call_durations', 'paid_call_durations', 'connect_durations', 'message_revenue', 'gift_total_revenue', 'carry_over_income', 'estimated_income', 'unions_income', 'settlement_result', 'conversion_income', 'online_durations', 'detail', 'award_count']
        } else {
          tHeader = ['ID', 'Union', 'Blocked status', 'Number of calls received this week', 'Number of calls answered this week', 'Answered rate this week', 'Free call duration this week', 'Pay call duration this week', 'Effective call duration this week', 'Text chat salary  this week', 'Gift salary this week', 'Unsettlement income last week', 'Estimate salary', 'Final salary', 'Agency salary', 'Settlement result', '  income this week', 'Online Time', 'detail', '新手收益']
          filterVal = ['user_id', 'union_id', 'state', 'push_total_call_numbers', 'answer_total_call_numbers', 'connect_rate', 'free_call_durations', 'paid_call_durations', 'connect_durations', 'message_revenue', 'gift_total_revenue', 'carry_over_income', 'estimated_income', 'unions_income', 'settlement_result', 'conversion_income', 'online_durations', 'detail', 'award_count']
        }
        const listData = JSON.parse(JSON.stringify(this.tableData))
        // 数据处理
        listData.map(function (currentValue, index, array) {
          array[index].state = that.formatBlockedState(currentValue)
          array[index].union_id = that.formaUnionList(currentValue.union_id)
          array[index].settlement_result = that.formaSettlementResultPartner(currentValue.settlement_result)
        })

        const data = this.formatJson(filterVal, listData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },

    onReset() {
      this.searchInfo = {}
    },
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
  },
}
</script>

<style>
</style>
