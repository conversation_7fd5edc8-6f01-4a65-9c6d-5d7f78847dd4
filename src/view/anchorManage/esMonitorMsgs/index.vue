<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="产品名称">
          <el-select multiple collapse-tags filterable v-model="searchInfo.app_id_range" clearable>
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="`${item.app_name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="用户ID">
          <el-input v-model="searchInfo.src_id"></el-input>
        </el-form-item>
        <el-form-item label="主播ID">
          <el-input v-model="searchInfo.dst_id"></el-input>
        </el-form-item>

        <el-form-item v-show="queryTxtState" label="时间范围">
          <el-date-picker
            v-model="searchInfo.date_range"
            type="daterange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DDTHH:mm:ssZ"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="text" :icon="queryTxtIcon" @click="queryTxtChange">{{ queryTxt }}</el-button>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        style="width: 100%"
        :data="tableData"
        max-height="590"
        row-key="id">
        <el-table-column align="center" label="操作" width="100">
          <template #default="scope">
            <el-button type="text" icon="el-icon-view" size="small" @click="showDetailMsg(scope.row)">查看</el-button>
          </template>
        </el-table-column>
        <!--<el-table-column align="center" label="主键ID" prop="id" min-width="140"></el-table-column>-->
        <el-table-column align="center" label="用户ID" prop="src_id" min-width="140" :sortable="true"></el-table-column>
        <el-table-column align="center" label="主播ID" prop="dst_id" min-width="140" :sortable="true"></el-table-column>
        <el-table-column align="center" label="聊天次数" prop="msg_count" min-width="140"
                         :sortable="true"></el-table-column>
        <el-table-column align="center" label="发起时间" prop="created_at" min-width="140"></el-table-column>
        <el-table-column align="center" label="北京时间" prop="bei_jin_time" min-width="140"></el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeListNormal"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
    <el-drawer v-model="chatMsgVisible" size="30%" :with-header="false">
      <div class="clientBody">
        <h2 class="clientHeader">
          主播ID: {{ recipientId }}
          用户ID: {{ scriveId }}
        </h2>
        <div class="newsCont" ref="contView">
          <div style="text-align: center" v-show="historyShow">
            <el-button type="text" @click="loadHistoryNews">查看历史消息</el-button>
          </div>
          <ul>
            <li class="dataBody" v-for="(item, index) of newsData" :key="index">
              <div class="displayFlex newsLeft" v-if="item.source === scriveId">
                <div class="newshead">
                  <el-avatar icon="el-icon-user-solid"></el-avatar>
                </div>
                <div class="newsTxt">
                  <chatDetailTable
                    :item="item"
                    :index="index"
                    :direction="1"
                  ></chatDetailTable>
                  <p class="messageStatus messageStatusRight">
                    <span>{{ item.src_time }}</span>
                  </p>
                </div>
              </div>
              <div class="displayFlex newsRight" v-else>
                <div class="newsTxt newsTxt2">
                  <chatDetailTable
                    :item="item"
                    :index="index"
                    :direction="2"
                  ></chatDetailTable>
                  <p class="messageStatus messageStatusLeft">
                    <span>{{ item.src_time }}</span>
                  </p>
                </div>
                <div class="newshead">
                  <el-avatar
                    icon="el-icon-user-solid"
                    style="background: cornflowerblue"
                  ></el-avatar>
                </div>
              </div>
            </li>
          </ul>
        </div>
      </div>
    </el-drawer>
  </div>
</template>

<script>
  import chatDetailTable from "@/view/anchorManage/esMonitorMsgs/components/chatDetailTable.vue";
  import {getEsMonitorMsgsList, getEsDetailMsgs} from '@/api/esMonitorMsgs' //  此处请自行替换地址
  import infoList from '@/mixins/infoList'
  import esMonitorMsgsMixins from '@/mixins/esMonitorMsgs'

  export default {
    mixins: [infoList, esMonitorMsgsMixins],
    components: {chatDetailTable},
    data() {
      return {
        recipientId: '',
        scriveId: '',
        historyShow: false,
        newsData: [],
        msgID: 1,
        msgPage: 1,
        msgPageSize: 10,


        searchInfo: {},
        chatMsgVisible: false,
        listApi: getEsMonitorMsgsList,
      }
    },
    async created() {
      await this.getTableData(this.nFunc, this.tableDataFormat)
    },
    methods: {
      loadHistoryNews() {
        this.loadDetailMsg()
      },
      loadDetailMsg() {
        getEsDetailMsgs({id: this.msgID, page: this.msgPage, pageSize: this.msgPageSize}).then(res => {
          console.log(res.data)

          let data = res.data;
          if (data.list === null) {
            this.historyShow = false
            this.$notify.warning("没有更多消息了")
            return
          }
          data.list.forEach((element) => {
            data.list.srcList = data.list.src_msg;
            this.newsData.unshift(element);
          });

          if (this.page === 1) {
            this.$nextTick(() => {
              this.$refs.contView.scrollTop = this.$refs.contView.scrollHeight;
            });
            setTimeout(() => {
              this.$nextTick(() => {
                this.$refs.contView.scrollTop = this.$refs.contView.scrollHeight;
              });
            }, 2000);
          }
          this.historyShow = this.newsData.length < data.total;
          this.msgPage += 1;
        })
      },
      showDetailMsg(row) {
        this.chatMsgVisible = true
        this.msgPage = 1
        this.newsData = []
        this.msgID = row.id
        this.recipientId = row.dst_id
        this.scriveId = row.src_id;
        this.loadDetailMsg()
      },
      onReset() {
        this.searchInfo = {}
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      onSubmit() {
        this.page = 1
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      getSummaries({columns, data}) {
        /*
        // 获取表头和数据
        // :show-summary="this.searchInfo.group_range.length > 0"
        // :summary-method="getSummaries"
        let labelList = []
        let vList = []
        columns.forEach((column, i) => {
          labelList.push(column.label)
          vList.push(column.property)
        })
        console.log(JSON.stringify(labelList))
        console.log(JSON.stringify(vList))
         */
      },
      tableDataFormat() {
        this.tableData.map((item, i, data) => {
          data[i].app_name = this.formatAppName(item.app_id)
          data[i].created_at = this.formatDate(item.created_at)
          data[i].bei_jin_time = this.formatDate(item.bei_jin_time)

        })
      },
      sizeChange(v) {
        this.pageSize = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      pageChange(v) {
        this.page = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
    },
  }
</script>

<style lang="scss" scoped>
  .displayFlex {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  .clientBody {
    position: relative;
    background: #f1f1f1;

    .clientHeader {
      position: absolute;
      width: 100%;
      height: 8vh;
      line-height: 8vh;
      text-align: center;
      color: #fff;
      font-size: 18px;
      background: cornflowerblue;
    }

    .newsCont {
      position: relative;
      height: 86.7vh;
      padding-top: 9vh;
      padding-bottom: 20px;
      box-sizing: border-box;
      overflow-y: auto;
      overflow-x: hidden;
    }

    .dataBody {
      margin-top: 20px;
      padding: 0 20px 20px 20px;
      box-sizing: border-box;
    }

    .newsLeft {
      justify-content: flex-start;
      align-items: inherit;
    }

    .newsRight {
      justify-content: flex-end;
      align-items: inherit;
    }

    .newshead {
      width: 36px;
      height: 36px;

      & img {
        width: 100%;
        height: 100%;
      }

      .el-avatar {
        width: 36px;
        height: 36px;
        line-height: 36px;
      }
    }

    .newsTxt {
      position: relative;
      margin-left: 10px;
      padding: 10px;
      border-radius: 4px;
      color: #333;
      font-size: 14px;
      line-height: 20px;
      background: #fff;
    }

    .newsTxt2 {
      margin-left: 0;
      margin-right: 10px;
    }

    .messageStatus {
      position: absolute;
      bottom: -24px;
      z-index: 1;
      font-size: 12px;
      width: 121px;
    }

    .messageStatusLeft {
      right: 0;
    }

    .messageStatusRight {
      left: 0;
    }

    .loadHistory {
      padding: 8px 16px;
    }
  }
</style>
