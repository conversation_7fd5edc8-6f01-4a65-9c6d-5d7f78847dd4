<template>
  <div class="infoTable newsContent">
    <p v-if="item.message_type === 1">{{ item.src_msg }}</p>
    <p v-else-if="item.message_type === 2 || item.message_type === 4">
      <el-image
        class="newsImg"
        :src="item.src_msg"
        :preview-src-list="item.srcList"
      >
      </el-image>
    </p>
    <p v-else-if="item.message_type === 3" class="audioIcon">
      <!-- <audio :src="item.src_msg" controls></audio> -->
      <svg-icon
        icon-class="play-icon"
        v-show="playIndex !== index"
        @click="play(item, index)"
      />
      <svg-icon
        icon-class="time-out-icon"
        v-show="playIndex === index"
        @click="stop(item, index)"
      />
      <span
        v-show="playIndex !== index"
        class="audioTime"
        :class="direction === 1 ? 'audioTime2' : 'audioTime1'"
      >{{ item.audio_duration | processingNumbers }}s</span
      >
      <span
        v-show="playIndex === index"
        class="audioTime"
        :class="direction === 1 ? 'audioTime2' : 'audioTime1'"
      >{{ playTime }}s
      </span>
    </p>
    <p v-else-if="item.message_type === 5">
      <video :src="item.src_msg" controls></video>
    </p>
    <!-- <p class="messageStatus messageStatusLeft">
                    <span v-if="item.newsStatus == 1"
                      >{{ $t("monitor.haveRead") }}
                    </span>
                    <span v-else>{{ $t("monitor.unread") }} </span>
                  </p> -->
  </div>
</template>

<script>
  import moment from "moment";
  import BenzAMRRecorder from "benz-amr-recorder";

  var amr = "";
  var timeInterval = "";
  export default {
    name: "textDetailTable",
    props: ["item", "index", "direction"],
    data() {
      return {
        playIndex: -1,
        playTime: 0,
      };
    },
    filters: {
      processingNumbers(num) {
        return Math.round(num);
      },
    },
    mounted() {
    },
    methods: {
      play(param, index) {
        amr = new BenzAMRRecorder();
        this.playIndex = index;
        this.playTime = Math.round(param.audio_duration);
        let that = this;
        amr.initWithUrl(param.src_msg).then(function () {
          if (amr.isPlaying()) {
            amr.stop();
          } else {
            amr.play();
            timeInterval = setInterval(() => {
              that.playTime = that.playTime - 1;
              console.log(that.playTime);
            }, 1000);
          }
        });
        amr.onStop(function () {
          that.playIndex = 0;
          clearInterval(timeInterval);
          that.playTime = Math.round(param.audio_duration);
          amr = "";
        });
        amr.onEnded(function () {
          that.playIndex = 0;
          clearInterval(timeInterval);
          that.playTime = Math.round(param.audio_duration);
          amr = "";
        });
      },
      stop(param, index) {
        let that = this;
        if (amr.isPlaying()) {
          amr.stop();
          this.playIndex = 0;
          clearInterval(timeInterval);
          this.playTime = Math.round(param.audio_duration);
          amr = "";
        }
      },
    },
    onbeforeunload() {
      if (amr.isPlaying()) {
        amr.stop();
        this.playIndex = 0;
        clearInterval(timeInterval);
        this.playTime = Math.round(param.audio_duration);
        amr = "";
      }
    },
  };
</script>

<style scoped lang="scss" type="text/css">
  .newsContent {

  &
  video {
    width: 260px;
  }

  &
  audio {
    width: 180px;
    height: 32px;
  }

  }
  .newsImg {

  &
  img {
    width: 180px;
    height: auto;
    object-position: top center;
    object-fit: cover;
  }

  }
  .audioIcon {
    position: relative;
    width: 36px;
    font-size: 30px;
    cursor: pointer;
    color: #67c23a;

  .audioTime {
    position: absolute;
    top: 4px;
    font-size: 14px;
    width: 100px;
    display: inline-block;
  }

  .audioTime1 {
    left: -120px;
    text-align: right;
  }

  .audioTime2 {
    right: -120px;
    text-align: left;
  }

  }
</style>
