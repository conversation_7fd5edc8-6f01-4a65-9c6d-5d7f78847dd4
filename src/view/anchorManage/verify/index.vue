<template>
  <div>
    <!--搜索-->
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="用户ID">
          <el-input v-model="searchInfo.id" placeholder="用户ID" />
        </el-form-item>
        <el-form-item v-if="parseInt(userInfo.union_id) === 0" label="昵称">
          <el-input v-model="searchInfo.nickname" placeholder="昵称" />
        </el-form-item>
        <el-form-item v-if="parseInt(userInfo.union_id) === 0" label="国籍">
          <el-select v-model="searchInfo.countryCode" clearable filterable placeholder="国籍">
            <el-option
              v-for="item in countryCodeOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Union：">
          <el-select v-model="searchInfo.unionId" placeholder="请选择" clearable>
            <el-option
              v-for="item in uniosList"
              :key="item.id"
              :label="`${item.name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model.number="searchInfo.anchorType" clearable placeholder="类型">
            <el-option label="真实主播" :value="1" />
            <el-option label="虚拟主播" :value="2" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="parseInt(userInfo.union_id) === 0" label="申请状态">
          <el-select v-model.number="searchInfo.state" clearable filterable placeholder="申请状态">
            <el-option
              v-for="item in stateOptions"
              :key="item.value"
              :label="`${item.label}`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="封禁状态">
          <el-select v-model.number="searchInfo.recovery" clearable filterable placeholder="封禁状态">
            <el-option
                v-for="item in recoveryStateOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="parseInt(userInfo.union_id) === 0" label="更新时间">
          <el-date-picker
            v-model="searchInfo.updateAtRange"
            type="datetimerange"
            :shortcuts="dateRangeShortcuts"
            range-separator="-"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DDTHH:mm:ssZ"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
        <el-form-item v-if="parseInt(userInfo.union_id) === 0" label="创建时间">
          <el-date-picker
            v-model="searchInfo.createdAtRange"
            type="datetimerange"
            :shortcuts="dateRangeShortcuts"
            range-separator="-"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DDTHH:mm:ssZ"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
          />
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!--列表-->
    <div class="gva-table-box">
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
        @sort-change="tableSortChange"
      >
        <el-table-column v-if="parseInt(userInfo.union_id) === 0" align="center" label="操作" min-width="180" fixed="left">
          <template #default="scope">
            <el-button
              v-show="scope.row.state === 1"
              type="text"
              icon="el-icon-check"
              size="small"
              class="table-button"
              @click="auditUsers(scope.row)"
            >审核
            </el-button>
            <el-button v-show="scope.row.state === 2" disabled type="text">已通过</el-button>
            <el-button v-show="scope.row.state === 3" disabled type="text">已拒绝</el-button>
            <el-button v-show="scope.row.state === 2 || scope.row.state === 3" type="primary" @click="auditUsers(scope.row)">查看信息</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="主播ID" prop="id" min-width="120" fixed />
        <el-table-column align="center" label="昵称" prop="nickname" show-overflow-tooltip min-width="160" />
        <el-table-column align="center" label="头像" min-width="120" style="line-height: 50px">
          <template #default="scope">
            <el-image
              style="width: 40px; height: 40px;line-height: 40px;overflow:visible"
              :src="scope.row.avatar?scope.row.avatar:scope.row.avatarVerify"
              :lazy="true"
              :preview-src-list="scope.row.avatar?[scope.row.avatar]:[scope.row.avatarVerify]"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="最后登录IP" prop="lastLoginIp" min-width="150" />
        <el-table-column v-if="parseInt(userInfo.union_id) === 0" align="center" label="国家" width="120">
          <template #default="scope">{{ formatCountryCode(scope.row.countryCode) }}</template>
        </el-table-column>
        <el-table-column align="center" prop="audit_at" label="审核时间" min-width="180">
          <template #default="scope">{{ formatDate(scope.row.audit_at) }}</template>
        </el-table-column>
        <el-table-column align="center" prop="created_at" label="创建时间" min-width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column align="center" prop="updated_at" label="更新时间" min-width="180">
          <template #default="scope">{{ formatDate(scope.row.updated_at) }}</template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <!--弹窗-->
    <el-dialog
      v-model="auditFormVisible"
      :before-close="closeAuditDialog"
      :fullscreen="false"
      :lock-scroll="false"
      :append-to-body="true"
      title="弹窗操作"
      top="1%"
      width="50%"
    >
      <el-form
        ref="usersForm"
        :model="formData"
        :inline="true"
        label-position="left"
        size="small"
        style="max-height: 80vh;overflow-y: auto;"
      >
        <!--头像-->
        <div class="m-item-box">
          <div class="m-item-box-left">
            <el-checkbox v-model="formData.audit.is_avatar" :true-label="1" :false-label="0" size="large" />
          </div>
          <div class="m-item-box-right">
            <el-form-item label="头像" class="m-photo-box" style="margin-bottom: 0">
              <el-upload
                ref="handleImage"
                :action="`${path}/files/createFiles`"
                :headers="{ 'x-token': token }"
                list-type="picture-card"
                :file-list="avatarFileList"
                :on-preview="handleImagePreview"
                :on-success="handleImageSuccess"
                :on-progress="handleProgressLoading"
                :multiple="false"
                :limit="1"
                accept=".jpg, .jpeg, .png, .gif"
                :on-remove="handleImageRemove"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-form-item>
          </div>
        </div>
        <!--头像-->

        <!--照片墙-->
        <div class="m-item-box">
          <div class="m-item-box-left">
            <el-checkbox
              v-model="formData.audit.is_photo_albums"
              :true-label="1"
              :false-label="0"
              size="large"
            />
          </div>
          <div class="m-item-box-right">
            <el-form-item label="照片墙" class="m-photo-box">
              <draggable
                v-model="formData.photoAlbums"
                class="el-upload-list el-upload-list--picture-card"
                item-key="id"
                @start="isDragging = true"
                @end="isDragging = false"
                @update="updatePhotoAlbumsList"
              >
                <template #item="{element}">
                  <div class="el-upload-list__item">
                    <img :src="element.small_url === '' ? element.url : element.small_url"
                         class="el-upload-list__item-thumbnail">
                    <span class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handleImagePreview(element)"> <!--handlePictureCardPreview(element)-->
                        <i class="el-icon-zoom-in" />
                      </span>
                      <span
                        class="el-upload-list__item-delete"
                        @click="handlePhotoAlbumsRemove(element)"
                      ><!-- handleRemove(element)-->
                        <i class="el-icon-delete" />
                      </span>
                    </span>
                  </div>
                </template>
              </draggable>
              <el-upload
                ref="handlePhotoAlbums"
                :action="`${path}/files/createFiles`"
                :headers="{ 'x-token': token }"
                list-type="picture-card"
                :show-file-list="false"
                :on-success="handlePhotoAlbumsSuccess"
                :on-progress="handleProgressLoading"
                :multiple="true"
                accept=".jpg, .jpeg, .png, .gif"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-form-item>
          </div>
        </div>
        <!--照片墙-->

        <!--视频墙-->
        <div class="m-item-box">
          <div class="m-item-box-left">
            <el-checkbox
              v-model="formData.audit.is_album_video"
              :true-label="1"
              :false-label="0"
              size="large"
            />
          </div>
          <div class="m-item-box-right">
            <el-form-item label="视频墙" class="m-photo-box">
              <draggable
                v-model="formData.albumVideo"
                class="el-upload-list el-upload-list--picture-card"
                item-key="id"
                @start="isDragging = true"
                @end="isDragging = false"
              >
                <template #item="{element}">
                  <div class="el-upload-list__item">
                    <img :src="element.url" class="el-upload-list__item-thumbnail">
                    <span class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handleVideoVisible(element)">
                        <i class="el-icon-zoom-in" />
                      </span>
                      <span
                        class="el-upload-list__item-delete"
                        @click="handleAlbumVideoRemove(element)"
                      >
                        <i class="el-icon-delete" />
                      </span>
                    </span>
                  </div>
                </template>
              </draggable>
              <el-upload
                ref="handleAlbumVideo"
                :action="`${path}/files/createVideo`"
                :headers="{ 'x-token': token }"
                list-type="picture-card"
                :on-success="handleAlbumVideoSuccess"
                :on-progress="handleProgressLoading"
                :show-file-list="false"
                :multiple="false"
                accept=".mp4"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-form-item>
          </div>
        </div>
        <!--视频墙-->

        <!--视频审核-->
        <div class="m-item-box">
          <div class="m-item-box-left">
            <el-checkbox
              v-model="formData.audit.is_audit_video"
              :true-label="1"
              :false-label="0"
              size="large"
            />
          </div>
          <div class="m-item-box-right">
            <el-form-item label="视频审核" class="m-photo-box">
              <draggable
                v-model="formData.auditVideo"
                class="el-upload-list el-upload-list--picture-card"
                item-key="id"
                @start="isDragging = true"
                @end="isDragging = false"
              >
                <template #item="{element}">
                  <div class="el-upload-list__item">
                    <img :src="element.url" class="el-upload-list__item-thumbnail">
                    <span class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handleVideoVisible(element)">
                        <i class="el-icon-zoom-in" />
                      </span>
                      <span
                        class="el-upload-list__item-delete"
                        @click="handleAuditVideoRemove(element)"
                      >
                        <i class="el-icon-delete" />
                      </span>
                    </span>
                  </div>
                </template>
              </draggable>
              <el-upload
                ref="handleAlbumVideo"
                :action="`${path}/files/createVideo`"
                :headers="{ 'x-token': token }"
                list-type="picture-card"
                :on-success="handleAuditVideoSuccess"
                :on-progress="handleProgressLoading"
                :show-file-list="false"
                :multiple="false"
                accept=".mp4"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-form-item>
          </div>
        </div>
        <!--视频审核-->

        <!--基础资料-->
        <div class="m-item-box">
          <div class="m-item-box-left">
            <el-checkbox v-model="formData.audit.is_base" :true-label="1" :false-label="0" size="large" />
          </div>
          <div class="m-item-box-right">
            <el-row style="padding: 0">
              <el-col :span="24">
                <el-form-item label="昵称">
                  <el-input v-model="formData.nickname" clearable placeholder="请输入" />
                </el-form-item>
                <el-form-item label="性别">
                  <el-radio-group v-model="formData.gender">
                    <el-radio
                      v-for="item in genderOptions"
                      :key="item.value"
                      :label="item.value"
                    >{{ item.label }}
                    </el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row style="padding: 0">
              <el-col :span="24">
                <el-form-item label="生日">
                  <el-date-picker v-model="formData.birthday" type="date" placeholder="选择日期" />
                </el-form-item>
                <el-form-item label="身高">
                  <el-input v-model="formData.height" placeholder="请输入身高数值" />
                </el-form-item>
                <el-form-item label="体重">
                  <el-input v-model="formData.weight" placeholder="请输入体重数值" />
                </el-form-item>
              </el-col>

            </el-row>
            <el-row style="padding: 0">
              <el-col :span="24">
                <el-form-item label="语言">
                  <el-select v-model.number="formData.langTags" clearable filterable placeholder="语言">
                    <el-option
                      v-for="item in anchorLangOptions"
                      :key="item.value"
                      :label="`${item.label}(${item.value})`"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item label="标签">
                  <el-select
                    v-model="formData.labels"
                    multiple
                    filterable
                    allow-create
                    clearable
                    default-first-option
                    placeholder="请选择标签"
                  >
                    <el-option-group
                      v-for="group in personalityLabelsOptions"
                      :key="group.id"
                      :label="group.name"
                    >
                      <el-option
                        v-for="item in group.children"
                        :key="item.id"
                        :label="item.name"
                        :value="item.id"
                      />
                    </el-option-group>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row style="padding: 0">
              <el-col :span="24">
                <el-form-item label="个签" style="width: 80%;">
                  <el-input v-model="formData.introduction" type="textarea" />
                </el-form-item>
              </el-col>
            </el-row>
            <el-divider content-position="left" style="margin: 10px 0; width: 80%">补充资料</el-divider>
            <el-row style="padding: 0">
              <el-col :span="8">
                <el-form-item label="工会">
                  <el-select
                    v-model="formData.unionId"
                    clearable
                    filterable
                    remote
                    reserve-keyword
                    placeholder="所属工会"
                  >
                    <el-option
                      v-for="item in uniosList"
                      :key="item.id"
                      :label="`${item.name}(${item.id})`"
                      :value="item.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="主播等级">
                  <el-select v-model="formData.levels" clearable filterable placeholder="主播等级">
                    <el-option
                      v-for="item in levelsOptions"
                      :key="item.value"
                      :label="`${item.label}(${item.value})`"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item label="国籍">
                  <el-select v-model="formData.countryCode" clearable filterable placeholder="国籍">
                    <el-option
                      v-for="item in countryCodeOptions"
                      :key="item.value"
                      :label="`${item.label}(${item.value})`"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
            <el-row style="padding: 0">
              <el-col :span="24">
                <el-form-item label="推荐标签">
                  <draggable
                    v-model="formData.userTags"
                    class="el-upload-list el-upload-list--picture-card"
                    item-key="id"
                    @start="isDragging = true"
                    @end="isDragging = false"
                  >
                    <template #item="{element}">
                      <span style="margin-right: 5px;">
                        <el-check-tag
                          :checked="element.checked"
                          @change="onChangeUserTags(element)"
                        >{{ element.tag_name }}</el-check-tag>
                      </span>
                    </template>
                  </draggable>
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
        <!--基础资料-->
      </el-form>
      <template #footer>
        <div style="display: flex; justify-content: space-between">
          <div>
            <el-popconfirm
              confirm-button-text="确定"
              cancel-button-text="取消"
              title="确定要封禁吗?"
              @confirm="banAnchor"
            >
              <template #reference>
                <el-button size="small" type="danger">封禁</el-button>
              </template>
            </el-popconfirm>
          </div>
          <div>
            <el-button size="small" @click="closeAuditDialog">取 消</el-button>
            <el-button size="small" type="primary" @click="enterAuditDialog">审 核</el-button>
          </div>
        </div>
      </template>
    </el-dialog>
    <!--图片预览-->
    <el-dialog v-model="previewImageVisible" title="图片预览" width="50%" top="1%" center>
      <img :src="previewImagePath" class="avatar video-avatar" style="max-height: 80vh;overflow-y: auto;">
    </el-dialog>
    <!--视频预览-->
    <el-dialog
      v-model="videoVisible"
      title="Notice"
      width="397px"
      top="0"
      destroy-on-close
      center
    >
      <video
        :src="videoUrl"
        class="avatar video-avatar"
        controls="controls"
      >
        您的浏览器不支持视频播放
      </video>
    </el-dialog>
  </div>
</template>

<script>
import { getUnionsList } from '@/api/unions' //  此处请自行替换地址

const path = import.meta.env.VITE_BASE_API
import draggable from 'vuedraggable'
import {
  deleteUsers,
  deleteUsersByIds,
  findUsers,
  usersAuditList,
  getAuditUsers,
  updateAuditUsers,
  banUser
} from '@/api/users' //  此处请自行替换地址
import { getPersonalityLabelsTree } from '@/api/personality_labels'
import infoList from '@/mixins/infoList'

const authStateOptions = [
  { value: 0, label: '未认证' },
  { value: 1, label: '认证中' },
  { value: 2, label: '已认证' },
]

// 1，审核中 2已通过 3被拒绝
const stateOptions = [
  { value: null, label: '全部' },
  { value: 1, label: '审核中' },
  { value: 2, label: '已通过' },
  { value: 3, label: '被拒绝' },
]

export default {
  name: 'Users',
  components: { draggable },
  mixins: [infoList],
  data() {
    return {
      isDragging: false,
      previewImageVisible: false,
      previewImagePath: '',
      videoVisible: false,
      videoUrl: '',
      path: path,
      hideUpload: false,
      avatarFileList: [],
      photoAlbumsFileList: [],
      searchInfo: {
        state: 1
      },
      listApi: usersAuditList,
      auditFormVisible: false,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      unionOptions: [],
      personalityLabelsOptions: [],
      authStateOptions: authStateOptions,
      stateOptions: stateOptions,
      formData: {
        albumVideo: [],
        auditVideo: [],
        callVideo: [],
        constellationVideo: [],
        photoAlbums: [],
        userTags: [],
        nickname: '',
        avatar: '',
        birthday: '',
        countryCode: '',
        anchorFake: 1,
        role: 0,
        avoid: 0,
        audit: {
          is_base: 0,
          is_avatar: 0,
          is_photo_albums: 0,
          is_album_video: 0,
          is_audit_video: 0,
        },
      },
      unionListOptions: [],
    }
  },
  async created() {
    this.searchInfo.recovery = 0
    await this.getTableData()
    await this.getPersonalityLabelsList()
    // await this.getUnionList()
  },
  methods: {
    banAnchor() {
      banUser({
        userId: this.formData.id,
        role: this.formData.role,
        app_id: this.formData.appId,
        isDeleteApply: true
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('成功')
          this.auditFormVisible = false
        }
      })
    },
    onChangeUserTags(e) {
      this.formData.userTags.forEach(function(value, index, array) {
        if (value.id === e.id) {
          array[index].checked = !e.checked
        }
      })
    },
    async updatePhotoAlbumsList(e) {
      // const newIndex = e.newIndex// 新位置下标
      // const oldIndex = e.oldIndex// 原始位置下标
    },
    async getPersonalityLabelsList() {
      const res = await getPersonalityLabelsTree()
      this.personalityLabelsOptions = res.data.list
    },
    handleImageRemove(file, fileList) {
      this.formData.avatar = ''
      this.avatarFileList = []
      this.hideUpload = false
    },
    handleImageSuccess(res) {
      const that = this
      that.avatarFileList = []
      const { data } = res
      if (data.url) {
        that.formData.avatar = data.url
        that.avatarFileList.push(data)
      }
      this.hideUpload = true
      this.progressLoading.close()
    },
    handlePhotoAlbumsRemove(file) {
      this.formData.photoAlbums = this.formData.photoAlbums.filter(function(age) {
        return age.url !== file.url
      })
    },
    async handlePhotoAlbumsSuccess(response, file, fileList) {
      const that = this
      if (!that.formData.photoAlbums) {
        that.formData.photoAlbums = []
      }
      that.formData.photoAlbums.push(file.response.data)
      // 数据去重
      const temp = []
      that.formData.photoAlbums.forEach(function(a) {
        const check = temp.every(function(b) {
          return a.key !== b.key
        })
        check ? temp.push(a) : []
      })
      that.formData.photoAlbums = temp

      // 关闭loading
      this.progressLoading.close()

      this.$refs['handlePhotoAlbums'].clearFiles()
    },
    handleCallVideoSuccess(response, file, fileList) {
      const that = this
      if (!that.formData.callVideo) {
        that.formData.callVideo = []
      }
      that.formData.callVideo.push(file.response.data)

      // 数据去重
      const temp = []
      that.formData.callVideo.forEach(function(a) {
        const check = temp.every(function(b) {
          return a.url !== b.url
        })
        check ? temp.push(a) : []
      })
      that.formData.callVideo = temp

      // 关闭loading
      this.progressLoading.close()

      this.$refs['handleCallVideo'].clearFiles()
    },
    handleCallVideoRemove(file, fileList) {
      this.formData.callVideo = this.formData.callVideo.filter(function(age) {
        return age.url !== file.url
      })
    },
    handleConstellationVideoSuccess(response, file, fileList) {
      const that = this
      that.formData.constellationVideo = []
      fileList.forEach((item, i) => {
        if (item.response !== undefined) {
          that.formData.constellationVideo.push(item.response.data)
        } else {
          that.formData.constellationVideo.push(item)
        }
      })

      // 关闭loading
      this.progressLoading.close()

      this.$refs['handleConstellationVideo'].clearFiles()
    },
    handleConstellationVideoRemove(file, fileList) {
      this.formData.constellationVideo = []
      fileList.forEach((item, i) => {
        this.formData.constellationVideo.push(item)
      })
    },

    handleAlbumVideoSuccess(response, file, fileList) {
      const that = this
      if (!that.formData.albumVideo) {
        that.formData.albumVideo = []
      }
      that.formData.albumVideo.push(file.response.data)
      // 数据去重
      const temp = []
      that.formData.albumVideo.forEach(function(a) {
        const check = temp.every(function(b) {
          return a.url !== b.url
        })
        check ? temp.push(a) : []
      })
      that.formData.albumVideo = temp

      // 关闭loading
      this.progressLoading.close()

      this.$refs['handleAlbumVideo'].clearFiles()
    },
    handleAuditVideoSuccess(response, file, fileList) {
      const that = this
      if (!that.formData.auditVideo) {
        that.formData.auditVideo = []
      }
      that.formData.auditVideo.push(file.response.data)
      // 数据去重
      const temp = []
      that.formData.auditVideo.forEach(function(a) {
        const check = temp.every(function(b) {
          return a.url !== b.url
        })
        check ? temp.push(a) : []
      })
      that.formData.auditVideo = temp

      // 关闭loading
      this.progressLoading.close()

      this.$refs['handleAuditVideo'].clearFiles()
    },
    handleAlbumVideoRemove(file, fileList) {
      this.formData.albumVideo = this.formData.albumVideo.filter(function(age) {
        return age.url !== file.url
      })
    },
    handleAuditVideoRemove(file, fileList) {
      this.formData.auditVideo = this.formData.auditVideo.filter(function(age) {
        return age.url !== file.url
      })
    },
    handleVideoVisible(file) {
      this.videoVisible = true
      this.videoUrl = file.video
    },

    handleImagePreview(file) {
      // 输出的file对象信息有一个tmp_path临时路径名称和一个url地址
      // console.log(file.url)
      // 把url地址赋值到本地
      this.previewImagePath = file.url
      // 打开预览图片的对话框
      this.previewImageVisible = true
    },

    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    tableSortChange({ column, prop, order }) {
      if (prop === 'created_at') {
        this.searchInfo.create_order = order
        this.getTableData()
      }
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteUsers(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
        this.multipleSelection.map(item => {
          ids.push(item.id)
        })
      const res = await deleteUsersByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateUsers(row) {
      const that = this
      const res = await findUsers({ id: row.id })
      that.type = 'update'
      that.avatarFileList = []
      if (res.code === 0) {
        that.formData = res.data.reusers

        let uAvatar = ''
        if (that.formData.avatar !== '') {
          uAvatar = that.formData.avatar
        } else {
          uAvatar = that.formData.avatarVerify
        }
        that.avatarFileList = [{name: '', url: uAvatar}]
        that.dialogFormVisible = true
      }
    },
    async auditUsers(row) {
      const that = this
      const res = await getAuditUsers({ id: row.id })

      that.type = 'audit'
      that.avatarFileList = []
      if (res.code === 0) {
        that.formData = res.data.reusers
        if (that.formData.avatar !== '') {
          that.avatarFileList.push({ name: '', url: that.formData.avatar })
          this.hideUpload = true
        } else {
          that.avatarFileList.push({ name: '', url: that.formData.avatarVerify })
          this.hideUpload = true
        }
        that.auditFormVisible = true
      }
    },
    closeAuditDialog() {
      this.avatarFileList = []
      this.auditFormVisible = false
      this.formData = {
        albumVideo: [],
        callVideo: [],
        constellationVideo: [],
        photoAlbums: [],
        userTags: [],
        nickname: '',
        avatarVerify: '',
        birthday: '',
        countryCode: '',
        anchorFake: 1,
        role: 0,
        avoid: 0,
        audit: {
          is_base: 0,
          is_avatar: 0,
          is_photo_albums: 0,
          is_album_video: 0
        },
      }
      this.getTableData()
    },
    async deleteUsers(row) {
      const res = await deleteUsers({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterAuditDialog() {
      if (this.formData.audit.is_avatar) {
        this.formData.avatar = this.formData.avatar ? this.formData.avatar : this.formData.avatarVerify
      }
      this.handleProgressLoading()
      const that = this
      let res
      switch (this.type) {
        case 'audit':
          res = await updateAuditUsers(this.formData)
          break
        default:
      }
      if (res.code === 0) {
        that.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.progressLoading.close()
        that.closeAuditDialog()
        that.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
      this.photoAlbumsFileList = []
      this.formData = {
        albumVideo: [],
        callVideo: [],
        constellationVideo: [],
        photoAlbums: [],
        userTags: [],
        nickname: '',
        avatarVerify: '',
        birthday: '',
        countryCode: '',
        anchorFake: 1,
        role: 0,
        avoid: 0,
      }
    },
    async getUnionList() {
      const res = await getUnionsList({ page: 1, pageSize: 1000 })
      this.unionListOptions = res.data.list
    },
  },
}
</script>

<style lang="scss" scoped>
  .hide .el-upload--picture-card{
    display: none !important;   /* 上传按钮隐藏 */
  }
  .m-photo-box {
    display: flex;
    flex-direction: column;
  }
  .m-item-box {
    display: flex;

    .m-item-box-left {
      width: 3%;
      margin-right: 5px
    }

    .m-item-box-right {
      width: 90%;
    }
  }

  .upload-container {
    width: 100%;
    display: flex;
    align-items: flex-end;

    .draggable-container {
      display: flex;

      .image-container {
        position: relative;
        width: 152px;
        height: 86px;
        margin-right: 17px;

        img {
          width: 100%;
          height: 100%;
        }

        .close {
          position: absolute;
          top: 4px;
          right: 4px;
          background: rgba(255, 255, 255, 1);
          border-radius: 50%;
          font-weight: 600;
        }
      }
    }

    .el-button {
      background: #EEF5FF;
      border: 1px solid #CFE3FD;
      color: #5E9FF8;
    }
  }
</style>
