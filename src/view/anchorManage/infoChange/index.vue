<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="用户ID">
          <el-input v-model="searchInfo.user_id" clearable placeholder="用户ID" />
        </el-form-item>
        <el-form-item v-if="parseInt(userInfo.union_id) === 0" label="昵称">
          <el-input v-model="searchInfo.nickname" clearable placeholder="昵称" />
        </el-form-item>
        <el-form-item v-if="parseInt(userInfo.union_id) === 0" label="国籍">
          <el-select v-model="searchInfo.countryCode" clearable filterable placeholder="国籍">
            <el-option v-for="item in countryCodeOptions" :key="item.value" :label="`${item.label}(${item.value})`" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="parseInt(userInfo.union_id) === 0" label="审核状态">
          <el-select v-model="searchInfo.state" clearable filterable placeholder="审核状态">
            <el-option v-for="item in stateOptions" :key="item.value" :label="`${item.label}`" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
            v-model="searchInfo.create_date_rangde"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="Start Date"
            end-placeholder="End Date"
          />
        </el-form-item>
        <el-form-item label="审核时间">
          <el-date-picker
            v-model="searchInfo.audit_date_rangde"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            type="datetimerange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="Start Date"
            end-placeholder="End Date"
          />
        </el-form-item>
        <el-form-item label="工会">
          <el-select v-model="searchInfo.union_id" placeholder="请选择" clearable>
            <el-option
              v-for="item in uniosList"
              :key="item.id"
              :label="`${item.name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="主播等级">
          <el-select v-model="searchInfo.levels" clearable filterable placeholder="主播等级">
            <el-option
                v-for="item in levelsOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-tabs class="m-tabs" v-model="tabActiveName" type="card" @tab-click="onSubmit">
        <el-tab-pane label="头像" name="avatar">
          <el-empty description="无数据" v-if="tableData.length === 0"></el-empty>
          <el-row :gutter="10">
            <el-col :span="8" style="margin-bottom: 10px" v-for="(item, i) in tableData" :key="item.id">
              <el-card shadow="hover" class="m-photo-box" :body-style="{ padding: '6px' }">
                <el-row style="align-items: center;" :gutter="10">
                  <el-col :span="7">
                    <h1>人脸认证视频</h1>
                    <el-image :src="parseResource(item.video_img)" fit="cover" @click="onPreviewVideo(item)" class="photo-left-box" style="cursor: pointer" />
                  </el-col>
                  <el-col :span="7">
                    <h1>新头像</h1>
                    <el-image
                      :src="parseResource(item.new_avatar)"
                      :preview-src-list="[parseResource(item.revoke), parseResource(item.new_avatar)]"
                      fit="cover"
                      :initial-index="1"
                      class="photo-left-box"
                    />

                  </el-col>
                  <el-col :span="10">
                    <el-descriptions direction="horizontal" :column="1" size="small" border>
                      <el-descriptions-item label="ID">{{ item.user_id }}</el-descriptions-item>
                      <el-descriptions-item label="名称">{{ item.nickname }}</el-descriptions-item>
                      <el-descriptions-item label="工会">{{ formatUniosName(item.unionId) }}</el-descriptions-item>
                      <el-descriptions-item label="等级">{{ item.levels }}</el-descriptions-item>
                      <el-descriptions-item label="国家">{{ item.countryCode }}</el-descriptions-item>
                      <el-descriptions-item label="创建时间">{{ formatDate(item.create_time) }}</el-descriptions-item>
                      <el-descriptions-item v-if="item.state!=1" label="审核时间">{{ formatDate(item.audit_time) }}</el-descriptions-item>
                      <el-descriptions-item v-if="item.sysUserId && item.sysUserId !== 0" label="操作">{{ userNameObj[item.sysUserId] }}</el-descriptions-item>
                      <el-descriptions-item label="状态">{{ formatState(item.state) }}</el-descriptions-item>
                      <el-descriptions-item label="普通">{{ item.normal }}</el-descriptions-item>
                      <el-descriptions-item label="性感">{{ item.sex }}</el-descriptions-item>
                      <el-descriptions-item label="审核通过">{{ item.passed }}</el-descriptions-item>
                    </el-descriptions>
                  </el-col>
                </el-row>

                <div style="display: flex; flex-direction: row-reverse">
                  <el-button style="margin: 0 10px" v-if="item.state === 1 || item.state === 3" type="success" @click="avatarPass(item)">通过</el-button>
                  <el-button style="margin: 0 10px" v-if="item.state === 1 || item.state === 2" type="danger" @click="avatarRefuse(item)">拒绝</el-button>
                  <el-button style="margin: 0 10px" type="primary" @click="getUserInfo(item)">查看信息</el-button>
                  <el-button style="margin: 0 10px" type="success" @click="showAnchorDataDialog(item.user_id)">
                    数据详情
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>
        <!-- 照片 -->
        <el-tab-pane label="照片" name="photo">
          <el-empty description="无数据" v-if="tableData.length === 0"></el-empty>
          <div v-if="tableData.length > 0">
            <el-button type="primary" @click="selectAllChange" style="margin-left: 10px" size="mini">全选</el-button>
            <el-button type="primary" @click="invertAllChange" style="margin-left: 10px" size="mini">反选</el-button>
            <el-button type="success" @click="photoBatchPass" style="margin-left: 10px" size="mini">批量通过</el-button>
            <el-button type="success" @click="photoBatchRefuse" style="margin-left: 10px" size="mini">批量拒绝</el-button>
          </div>
          <el-row :gutter="10">
            <el-col :span="8" style="margin-bottom: 10px" v-for="(item, i) in tableData" :key="item.id">
              <el-card shadow="hover" class="m-photo-box" :body-style="{ padding: '6px' }">
                <el-row :gutter="10" style="align-items: flex-start;" >
                  <el-col :span="7" style="margin-top: 11px;">
                    <h1 style="margin-bottom: 13px;">人脸认证视频</h1>
                    <el-image :src="parseResource(item.video_img)" fit="cover" @click="onPreviewVideo(item)" class="photo-left-box" style="cursor: pointer" />
                  </el-col>
                  <el-col :span="7">
                    <div class="flex items-center">
                      <el-checkbox v-model="item.checked" label="新照片" size="large" />
                      <!--<h1 style="margin-left: 6px">新照片</h1>-->
                    </div>
                    <el-image :src="parseResource(item.resource)" fit="cover" :preview-src-list="[parseResource(item.resource)]" class="photo-left-box" />
                  
                    <div>
                     
                     <div class="extra-item extra-item1">
                       <el-checkbox-group v-model="item.imgType" >
                         <el-checkbox v-for="types in item.audit_tags" @change="(value) => changeChekBox(item, value, 1,types)" :key="types.id" :value="types.id" :label="types.id">{{ types.name }}</el-checkbox>
                       </el-checkbox-group>
                     </div>
                     <div class="extra-item">
                       <span style="margin-right: 6px;">推荐:</span><el-checkbox @change="imgRecoverChange(item)" v-model="item.is_recommended" label="" />
                     </div>
                   </div>
                  
                  </el-col>
                  <el-col :span="10">
                    <el-descriptions direction="horizontal" :column="1" size="small" border>
                      <el-descriptions-item label="ID">{{ item.user_id }}</el-descriptions-item>
                      <el-descriptions-item label="名称">{{ item.nickname }}</el-descriptions-item>
                      <el-descriptions-item label="工会">{{ formatUniosName(item.unionId) }}</el-descriptions-item>
                      <el-descriptions-item label="等级">{{ item.levels }}</el-descriptions-item>
                      <el-descriptions-item label="国家">{{ item.countryCode }}</el-descriptions-item>
                      <el-descriptions-item label="创建时间">{{ formatDate(item.create_time) }}</el-descriptions-item>
                      <el-descriptions-item v-if="item.state!=1" label="审核时间">{{ formatDate(item.audit_time) }}</el-descriptions-item>
                      <el-descriptions-item v-if="item.sysUserId && item.sysUserId !== 0" label="操作">{{ userNameObj[item.sysUserId] }}</el-descriptions-item>
                      <el-descriptions-item label="状态">{{ formatState(item.state) }}</el-descriptions-item>
                      <el-descriptions-item label="普通">{{ item.normal }}</el-descriptions-item>
                      <el-descriptions-item label="性感">{{ item.sex }}</el-descriptions-item>
                      <el-descriptions-item label="NSFW">{{ item.nsfw }}</el-descriptions-item>
                      <el-descriptions-item label="审核通过">{{ item.passed }}</el-descriptions-item>
                    </el-descriptions>
                  </el-col>
                </el-row>
                <div style="display: flex; flex-direction: row-reverse">
                  <el-button style="margin: 0 10px" v-if="item.state === 1 || item.state === 3" type="success" @click="photoPass(item)">通过</el-button>
                  <el-button style="margin: 0 10px" v-if="item.state === 1 || item.state === 2" type="danger" @click="photoRefuse(item)">拒绝</el-button>
                  <el-button style="margin: 0 10px" type="primary" @click="getUserInfo(item)">查看信息</el-button>
                  <el-button style="margin: 0 10px" type="success" @click="showAnchorDataDialog(item.user_id)">
                    数据详情
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>

        <el-tab-pane label="视频" name="video">
          <el-empty description="无数据" v-if="tableData.length === 0"></el-empty>
          <el-row :gutter="10">
            <el-col :span="8" style="margin-bottom: 10px" v-for="(item, i) in tableData" :key="item.id">
              <el-card shadow="hover" class="m-photo-box" :body-style="{ padding: '6px' }">
                <el-row :gutter="10"  style="min-height: 170px; overflow: hidden;align-items: flex-start;">
                  <el-col :span="7">
                    <h1>人脸认证视频</h1>
                    <el-image :src="parseResource(item.face_video_img)" fit="cover" @click="onPreviewFaceVideo(item)" class="photo-left-box" style="cursor: pointer" />
                  </el-col>
                  <el-col :span="7">
                    <h1>视频</h1>
                    <el-image :src="parseResource(item.img)" fit="cover" @click="onPreviewVideo(item)" class="photo-left-box" style="cursor: pointer" />
                 
                    <div>
                     
                     <div class="extra-item extra-item1">
                       <el-checkbox-group v-model="item.imgType" >
                         <el-checkbox v-for="types in item.audit_tags" @change="(value) => changeChekBox(item, value, 2,types)" :key="types.id" :value="types.id" :label="types.id">{{ types.name }}</el-checkbox>
                       </el-checkbox-group>
                     </div>
                     <div class="extra-item">
                       <span style="margin-right: 6px;">推荐:</span><el-checkbox @change="videoRecoverChange(item)" v-model="item.is_recommended" label="" />
                     </div>
                   </div>
                  </el-col>
                  <el-col :span="10">
                    <el-descriptions direction="horizontal" :column="1" size="small" border>
                      <el-descriptions-item label="ID">{{ item.user_id }}</el-descriptions-item>
                      <el-descriptions-item label="名称">{{ item.nickname }}</el-descriptions-item>
                      <el-descriptions-item label="工会">{{ formatUniosName(item.unionId) }}</el-descriptions-item>
                      <el-descriptions-item label="等级">{{ item.levels }}</el-descriptions-item>
                      <el-descriptions-item label="国家">{{ item.countryCode }}</el-descriptions-item>
                      <el-descriptions-item label="创建时间">{{ formatDate(item.create_time) }}</el-descriptions-item>
                      <el-descriptions-item v-if="item.state!=1" label="审核时间">{{ formatDate(item.audit_time) }}</el-descriptions-item>

                      <el-descriptions-item v-if="item.sysUserId && item.sysUserId !== 0" label="操作">{{ userNameObj[item.sysUserId] }}</el-descriptions-item>
                      <el-descriptions-item label="状态">{{ formatState(item.state) }}</el-descriptions-item>
                      <el-descriptions-item label="普通">{{ item.normal }}</el-descriptions-item>
                      <el-descriptions-item label="性感">{{ item.sex }}</el-descriptions-item>
                      <el-descriptions-item label="NSFW">{{ item.nsfw }}</el-descriptions-item>
                      <el-descriptions-item label="审核通过">{{ item.passed }}</el-descriptions-item>
                    </el-descriptions>
                  </el-col>
                </el-row>
                <div style="display: flex; flex-direction: row-reverse">
                  <el-button style="margin: 0 10px" v-if="item.state === 1 || item.state === 3" type="success" @click="videoPass(item)">通过</el-button>
                  <el-button style="margin: 0 10px" v-if="item.state === 1 || item.state === 2" type="danger" @click="videoRefuse(item)">拒绝</el-button>
                  <el-button style="margin: 0 10px" type="primary" @click="getUserInfo(item)">查看信息</el-button>
                  <el-button style="margin: 0 10px" type="success" @click="showAnchorDataDialog(item.user_id)">
                    数据详情
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>

        <!-- 群发图片 -->
        <el-tab-pane label="群发图片" name="batchPhoto">
          <el-empty description="无数据" v-if="tableData.length === 0"></el-empty>
          <div v-if="tableData.length > 0">
            <el-button type="primary" @click="selectAllChange" style="margin-left: 10px" size="mini">全选</el-button>
            <el-button type="primary" @click="invertAllChange" style="margin-left: 10px" size="mini">反选</el-button>
            <el-button type="success" @click="photoBatchImagePass(2, 'batch')" style="margin-left: 10px" size="mini">批量通过</el-button>
            <el-button type="success" @click="photoBatchImagePass(3, 'batch')" style="margin-left: 10px" size="mini">批量拒绝</el-button>
          </div>
          <el-row :gutter="10">
            <el-col :span="8" style="margin-bottom: 10px" v-for="(item, i) in tableData" :key="item.id">
              <el-card shadow="hover" class="m-photo-box" :body-style="{ padding: '6px' }">
                <el-row :gutter="10">
                  <el-col :span="7" style="margin-top: 11px;">
                    <h1 style="margin-bottom: 13px;">人脸认证视频</h1>
                    <el-image :src="parseResource(item.face_video_img)" fit="cover" @click="onPreviewFaceVideo(item)" class="photo-left-box" style="cursor: pointer" />
                  </el-col>
                  <el-col :span="7">
                    <div class="flex items-center">
                      <el-checkbox v-model="item.checked" label="新照片" size="large" />
                      <!--<h1 style="margin-left: 6px">新照片</h1>-->
                    </div>
                    <el-image :src="parseResource(item.img)" fit="cover" :preview-src-list="[parseResource(item.img)]" class="photo-left-box" />
                  </el-col>
                  <el-col :span="10">
                    <el-descriptions direction="horizontal" :column="1" size="small" border>
                      <el-descriptions-item label="ID">{{ item.user_id }}</el-descriptions-item>
                      <el-descriptions-item label="名称">{{ item.nickname }}</el-descriptions-item>
                      <el-descriptions-item label="工会">{{ formatUniosName(item.unionId) }}</el-descriptions-item>
                      <el-descriptions-item label="等级">{{ item.levels }}</el-descriptions-item>
                      <el-descriptions-item label="国家">{{ item.countryCode }}</el-descriptions-item>
                      <el-descriptions-item label="创建时间">{{ formatDate(item.create_time) }}</el-descriptions-item>
                      <el-descriptions-item v-if="item.state!=1" label="审核时间">{{ formatDate(item.audit_time) }}</el-descriptions-item>

                      <el-descriptions-item v-if="item.sysUserId && item.sysUserId !== 0" label="操作">{{ userNameObj[item.sysUserId] }}</el-descriptions-item>
                      <el-descriptions-item label="状态">{{ formatState(item.state) }}</el-descriptions-item>
                    </el-descriptions>
                  </el-col>
                </el-row>
                <div style="display: flex; flex-direction: row-reverse">
                  <el-button style="margin: 0 10px" v-if="item.state === 1 || item.state === 3" type="success" @click="photoBatchImagePass(2, 'single', item)">通过</el-button>
                  <el-button style="margin: 0 10px" v-if="item.state === 1 || item.state === 2" type="danger" @click="photoBatchImagePass(3, 'single', item)">拒绝</el-button>
                  <el-button style="margin: 0 10px" type="primary" @click="getUserInfo(item)">查看信息</el-button>
                  <el-button style="margin: 0 10px" type="success" @click="showAnchorDataDialog(item.user_id)">
                    数据详情
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>
        <!-- 群发图片-结束 -->

        <!-- 群发消息开始 -->
        <el-tab-pane label="群发消息" name="batchMessage">
          <el-empty description="无数据" v-if="tableData.length === 0"></el-empty>
          <div v-if="tableData.length > 0">
            <el-button type="primary" @click="selectAllChange" style="margin-left: 10px" size="mini">全选</el-button>
            <el-button type="primary" @click="invertAllChange" style="margin-left: 10px" size="mini">反选</el-button>
            <el-button type="success" @click="photoBatchImagePass(2, 'batch')" style="margin-left: 10px" size="mini">批量通过</el-button>
            <el-button type="success" @click="photoBatchImagePass(3, 'batch')" style="margin-left: 10px" size="mini">批量拒绝</el-button>
          </div>
          <el-row :gutter="10">
            <el-col :span="8" style="margin-bottom: 10px" v-for="(item, i) in tableData" :key="item.id">
              <el-card shadow="hover" class="m-photo-box" :body-style="{ padding: '6px' }">
                <el-row :gutter="10">
                  <el-col :span="7" style="margin-top: 11px;">
                    <h1 style="margin-bottom: 13px;">人脸认证视频</h1>
                    <el-image :src="parseResource(item.face_video_img)" fit="cover" @click="onPreviewFaceVideo(item)" class="photo-left-box" style="cursor: pointer" />
                  </el-col>
                  <el-col :span="7">
                    <div class="flex items-center">
                      <el-checkbox v-model="item.checked" label="文案" size="large" />
                      <!--<h1 style="margin-left: 6px">新照片</h1>-->
                    </div>
                    <el-card class="text-right-box">
                      {{ item.content }}
                    </el-card>
                    <!-- <el-image :src="parseResource(item.resource)" fit="cover" :preview-src-list="[parseResource(item.resource)]" class="photo-left-box" /> -->
                  </el-col>
                  <el-col :span="10">
                    <el-descriptions direction="horizontal" :column="1" size="small" border>
                      <el-descriptions-item label="ID">{{ item.user_id }}</el-descriptions-item>
                      <el-descriptions-item label="名称">{{ item.nickname }}</el-descriptions-item>
                      <el-descriptions-item label="工会">{{ formatUniosName(item.unionId) }}</el-descriptions-item>
                      <el-descriptions-item label="等级">{{ item.levels }}</el-descriptions-item>
                      <el-descriptions-item label="国家">{{ item.countryCode }}</el-descriptions-item>
                      <el-descriptions-item label="创建时间">{{ formatDate(item.create_time) }}</el-descriptions-item>
                      <el-descriptions-item v-if="item.state!=1" label="审核时间">{{ formatDate(item.audit_time) }}</el-descriptions-item>
                      <el-descriptions-item v-if="item.sysUserId && item.sysUserId !== 0" label="操作">{{ userNameObj[item.sysUserId] }}</el-descriptions-item>
                      <el-descriptions-item label="状态">{{ formatState(item.state) }}</el-descriptions-item>
                    </el-descriptions>
                  </el-col>
                </el-row>
                <div style="display: flex; flex-direction: row-reverse">
                  <el-button style="margin: 0 10px" v-if="item.state === 1 || item.state === 3" type="success" @click="photoBatchImagePass(2, 'single', item)">通过</el-button>
                  <el-button style="margin: 0 10px" v-if="item.state === 1 || item.state === 2" type="danger" @click="photoBatchImagePass(3, 'single', item)">拒绝</el-button>
                  <el-button style="margin: 0 10px" type="primary" @click="getUserInfo(item)">查看信息</el-button>
                  <el-button style="margin: 0 10px" type="success" @click="showAnchorDataDialog(item.user_id)">
                    数据详情
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>
        <!-- 群发消息结束 -->

        <el-tab-pane label="群发私密视频" name="secretVideo">
          <el-empty description="无数据" v-if="tableData.length === 0"></el-empty>
          <el-row :gutter="10">
            <el-col :span="8" style="margin-bottom: 10px" v-for="(item, i) in tableData" :key="item.id">
              <el-card shadow="hover" class="m-photo-box" :body-style="{ padding: '6px' }">
                <el-row :gutter="10" style="min-height: 170px; overflow: hidden">
                  <el-col :span="7">
                    <h1>人脸认证视频</h1>
                    <el-image :src="parseResource(item.face_video_img)" fit="cover" @click="onPreviewFaceVideo(item)" class="photo-left-box" style="cursor: pointer" />
                  </el-col>
                  <el-col :span="7">
                    <h1>视频</h1>
                    <el-image :src="parseResource(item.img)" fit="cover" @click="onPreviewSecretVideo(item)" class="photo-left-box" style="cursor: pointer" />
                  </el-col>
                  <el-col :span="10">
                    <el-descriptions direction="horizontal" :column="1" size="small" border>
                      <el-descriptions-item label="ID">{{ item.user_id }}</el-descriptions-item>
                      <el-descriptions-item label="名称">{{ item.nickname }}</el-descriptions-item>
                      <el-descriptions-item label="工会">{{ formatUniosName(item.unionId) }}</el-descriptions-item>
                      <el-descriptions-item label="等级">{{ item.levels }}</el-descriptions-item>
                      <el-descriptions-item label="国家">{{ item.countryCode }}</el-descriptions-item>
                      <el-descriptions-item label="创建时间">{{ formatDate(item.create_time) }}</el-descriptions-item>
                      <el-descriptions-item v-if="item.state!=1" label="审核时间">{{ formatDate(item.audit_time) }}</el-descriptions-item>
                      <el-descriptions-item v-if="item.sysUserId && item.sysUserId !== 0" label="操作">{{ userNameObj[item.sysUserId] }}</el-descriptions-item>
                      <el-descriptions-item label="状态">{{ formatState(item.state) }}</el-descriptions-item>
                    </el-descriptions>
                  </el-col>
                </el-row>
                <div style="display: flex; flex-direction: row-reverse">
                  <el-button style="margin: 0 10px" v-if="item.state === 1 || item.state === 3" type="success" @click="photoBatchImagePass(2, 'single', item)">通过</el-button>
                  <el-button style="margin: 0 10px" v-if="item.state === 1 || item.state === 2" type="danger" @click="photoBatchImagePass(3, 'single', item)">拒绝</el-button>
                  <el-button style="margin: 0 10px" type="primary" @click="getUserInfo(item)">查看信息</el-button>
                  <el-button style="margin: 0 10px" type="success" @click="showAnchorDataDialog(item.user_id)">
                    数据详情
                  </el-button>
                </div>
              </el-card>
            </el-col>
          </el-row>
        </el-tab-pane>



      </el-tabs>
      <div class="gva-pagination">
         <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
      <user-info-update-dialog v-if="showUserInfoDialog" @closeFunc="closeDialogUserInfo" ref="userInfoUpdateDialogRef" :user_id="userInfoUpdateId"></user-info-update-dialog>

      <anchor-data-dialog ref="anchorDataDialogRef" :dst_id="anchorDstId"></anchor-data-dialog>
      <el-dialog v-model="videoVisible" width="397px" top="2%" destroy-on-close center>
        <video :src="videoUrl" class="avatar video-avatar" style="width:100%;height: 100%;" controls="controls">
          您的浏览器不支持视频播放
        </video>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import { findOneUserWallet, findUsers } from '@/api/users'
import { getNewAvatarVerifiesList, updateNewAvatarVerifies } from '@/api/new_avatar_verifies'
import { getUserPhotoAlbumsList, updateUserPhotoAlbums } from '@/api/user_photo_albums'
import { getUserVideoAlbumsList, updateUserVideoAlbums } from '@/api/user_video_albums'
import { coverDetailsDelete, coverDetailsSave,updateAuditTags } from '@/api/anchorCoverRecommendationsDetails'
import { deleteUserRecommendVideoRd, saveUserRecommendVideoRd } from '@/api/userRecommendVideoRd'


import { getUserStrategyRdList, updateUserStrategyRd, getUserNameList } from '@/api/userStrategyRd'
import infoList from '@/mixins/infoList'
import { banAnchor } from '@/api/banRecords'
import UserInfoUpdateDialog from '@/components/UserInfoUpdateDialog/index.vue'

const path = import.meta.env.VITE_BASE_API
const stateOptions = [
  {
    value: 1,
    label: '审核中',
  },
  {
    value: 4,
    label: '已审核',
  },
  {
    value: 2,
    label: '通过',
  },
  {
    value: 3,
    label: '拒绝',
  },
]
const stateDict = {
  1: '审核中',
  2: '通过',
  3: '拒绝',
}
export default {
  name: 'AnchorInfoChange',
  components: { UserInfoUpdateDialog, draggable },
  mixins: [infoList],
  data() {
    return {
      showUserInfoDialog:false,

      banDialogFormVisible: false,
      userInfoUpdateId: 0,
      anchorDstId: 0,
      formBanData: {},
      formBanDataRules: {
        ban_reason: [{ required: true, message: '请完善该字段.', trigger: 'blur' }],
      },
      listApi: getNewAvatarVerifiesList,
      tabActiveName: 'avatar',
      videoUrl: '',
      videoVisible: false,
      formData: {},
      unionOptions: [],
      path,
      stateOptions,
      stateDict,
      searchInfo: {
        state: 1,
        role: 2,
      },

      // 用户列表数据
      userNameObj: {},
    }
  },
  async created() {
    // 查询用户列表
    await this.getUserNameList()
    await this.onSubmit()
  },
  methods: {

    closeDialogUserInfo() {
      this.showUserInfoDialog = false
    },

    dataFormat(){
      this.tableData.forEach(item => {
      //   item.audit_tags = [
      //   {
      //     id:2,
      //     name:'性感',
      //     selected:false
      //   },
      //   {
      //     id:3,
      //     name:'普通',
      //     selected:true
      //   },
      // ]
      // item.is_recommended = false
      if(item.audit_tags){
        const params = item.audit_tags.find((items) => items.selected)
          if (params) {
            item.imgType = [params.id]
          } else {
            item.imgType = []
          }
      }else{
        item.imgType = []
      }
      })
    },

    changeChekBox(element, value, type, item) {
      if(element.imgType.length>1){
          // 只能选择一个
          element.imgType = [item.id]
          
      }

      // 照片类型不为空
      // if(value.length>0){
      //     op_type 1-图片 2-视频
      // key_id // 对应的主键id
      // tag_id // 标签id // id 1-没有标签  2-性感  3-普通

      let reqData = {
        op_type: type,
        key_id: element.id,
        user_id: element.user_id,
        tag_id: element.imgType.length > 0 ? element.imgType[0] : 1,
      }

      console.log(reqData)
      updateAuditTags(reqData).then((res) => {
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '设置成功',
          })
        }
      })
    },

    imgRecoverChange(element) {
      if (element.is_recommended) {
        coverDetailsSave({
          anchor_id: element.user_id,
          cover_url: element.resource,
        }).then((res) => {
          if (res.code === 0) {
            this.$message({
              type: 'success',
              message: '成功',
            })
          }
        })
      } else {
        coverDetailsDelete({
          anchor_id: element.user_id,
          cover_url: element.resource,
        }).then((res) => {
          if (res.code === 0) {
            this.$message({
              type: 'success',
              message: '成功',
            })
          }
        })
      }
    },


    videoRecoverChange(element) {
      if (element.is_recommended) {
        saveUserRecommendVideoRd({
          user_id: element.user_id,
          video_url: element.video,
        }).then((res) => {
          if (res.code === 0) {
            this.$message({
              type: 'success',
              message: '成功',
            })
          }
        })
      } else {
        deleteUserRecommendVideoRd({
          user_id: element.user_id,
          video_url: element.video,
        }).then((res) => {
          if (res.code === 0) {
            this.$message({
              type: 'success',
              message: '成功',
            })
          }
        })
      }
    },
    
    // 查询用户列表
    getUserNameList() {
      getUserNameList({}).then((res) => {
        if (res.code === 0) {
          const obj = {}
          res.data.forEach((item) => {
            obj[item.id] = item.nick_name
          })
          this.userNameObj = obj
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    // 批量通过和拒绝 state:2通过3拒绝  type:batch批量，single：单笔
    photoBatchImagePass(state, type, item) {
      // 批量操作
      if (type === 'batch') {
        // 声明请求调用次数
        let reqCount = 0
        // 声明实际调用的数组
        const currentArr = this.tableData.filter((param) => param.checked)
        currentArr.forEach((items) => {
          items.state = state
          // items.rd_type = tab
          if (state === 3) {
            items.reject = 'Reject'
          }
          // console.log(items)
          updateUserStrategyRd(items).then((res) => {
            reqCount++
            if (res.code === 0) {
              this.$message.success(res.msg)
            } else {
              this.$message.error(res.msg)
            }
            // 最后一次的时候在调用列表查询方法
            if (reqCount === currentArr.length) {
              this.onSubmit()
            }
          })
        })
      } else {
        // 单笔操作
        item.state = state
        // item.rd_type = tab
        if (state === 3) {
          item.reject = 'Reject'
        }
        updateUserStrategyRd(item).then((res) => {
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.onSubmit()
          } else {
            this.$message.error(res.msg)
          }
        })
      }
    },
    showAnchorDataDialog(dst_id) {
      this.anchorDstId = dst_id
      this.$refs.anchorDataDialogRef.dialogVisible = true
    },
    showBanAnchorDialog(user_id) {
      findOneUserWallet({ user_id }).then((res) => {
        if (res.code === 0) {
          this.formBanData = {
            record_type: 1,
            ban_id: user_id,
            deduction_ratio: 100,
            ban_days: 0,
            ban_reason: '',
            balance: res.data.un_diamonds,
          }
          this.banDialogFormVisible = true
        }
      })
    },
    closeBanDialog() {
      this.banDialogFormVisible = false
      this.$refs.formBanDataRef.resetFields()
    },
    async enterBanDialog() {
      this.$refs.formBanDataRef.validate(async (v) => {
        if (!v) {
          return
        }
        let res = await banAnchor(this.formBanData)
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功',
          })
          let userRes = await findUsers({ id: this.formBanData.ban_id })
          if (userRes.code === 0) {
            this.formData = userRes.data.reusers
          }
          this.closeBanDialog()
        }
      })
    },
    photoBatchPass() {
      // 声明请求调用次数
      let reqCount = 0
      // 声明实际调用的数组
      const currentArr = this.tableData.filter((param) => param.checked)
      currentArr.forEach((item) => {
        // if (item.checked) {
        item.state = 2
        updateUserPhotoAlbums(item).then((res) => {
          reqCount++
          if (res.code === 0) {
            this.$message.success(res.msg)
          } else {
            this.$message.error(res.msg)
          }

          // 最后一次的时候在调用列表查询方法
          if (reqCount === currentArr.length) {
            this.onSubmit()
          }
        })
        // }
      })
    },
    photoBatchRefuse() {
      // 声明请求调用次数
      let reqCount = 0
      // 声明实际调用的数组
      const currentArr = this.tableData.filter((param) => param.checked)
      currentArr.forEach((item) => {
        // if (item.checked) {
        item.state = 3
        item.reject = 'Reject'
        updateUserPhotoAlbums(item).then((res) => {
          reqCount++
          if (res.code === 0) {
            this.$message.success(res.msg)
          } else {
            this.$message.error(res.msg)
          }
          // 最后一次的时候在调用列表查询方法
          if (reqCount === currentArr.length) {
            this.onSubmit()
          }
        })
        // }
      })
    },
    selectAllChange() {
      this.tableData.forEach((item) => {
        item.checked = true
      })
    },
    invertAllChange() {
      this.tableData.forEach((item) => {
        item.checked = !item.checked
      })
    },
    getUserInfo(item) {
      this.userInfoUpdateId = item.user_id
      // this.$refs.userInfoUpdateDialogRef.dialogVisible = true
      this.showUserInfoDialog = true

    },
    onPreviewVideo(item) {
      this.videoUrl = this.parseResource(item.video)
      this.videoVisible = true
    },
    onPreviewSecretVideo(item) {
      this.videoUrl = this.parseResource(item.content)
      this.videoVisible = true
    },
    onPreviewFaceVideo(item) {
      this.videoUrl = this.parseResource(item.face_video)
      this.videoVisible = true
    },
    formatState(i) {
      return this.stateDict[i]
    },
    parseResource(src) {
      if (src !== null && src !== undefined && src !== '' && !src.startsWith('http')) {
        let match = src.match(/image|avatar|video|message|constellation/g)
        if (match) {
          src = 'https://s3.oklove.top/' + src
        } else {
          src = 'https://oss.bakbak.site/' + src
        }
      }
      return src
    },
    onReset() {
      this.searchInfo = {
        state: 1,
        role: 2,
      }
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.searchInfo.app_id = this.appId
      switch (this.tabActiveName) {
        case 'avatar':
          this.listApi = getNewAvatarVerifiesList
          break
        case 'photo':
          this.listApi = getUserPhotoAlbumsList
          break
        case 'video':
          this.listApi = getUserVideoAlbumsList
          break
        case 'batchPhoto':
          this.searchInfo.rd_type = 2 // 照片
          this.listApi = getUserStrategyRdList
          break
        case 'batchMessage':
          this.searchInfo.rd_type = 1 // 文字
          this.listApi = getUserStrategyRdList
          break
        case 'secretVideo':
          this.searchInfo.rd_type = 3 // 私密视频
          this.listApi = getUserStrategyRdList
          break
      }
      this.getTableData(this.nFunc, this.dataFormat)
    },
    sizeChange(v) {
        this.pageSize = v
        this.getTableData(this.nFunc, this.dataFormat)
      },
      pageChange(v) {
        this.page = v
        this.getTableData(this.nFunc, this.dataFormat)
      },
    avatarPass(item) {
      item.state = 2
      updateNewAvatarVerifies(item).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
        this.onSubmit()
      })
    },
    avatarRefuse(item) {
      item.state = 3
      updateNewAvatarVerifies(item).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
        this.onSubmit()
      })
    },
    photoPass(item) {
      item.state = 2
      updateUserPhotoAlbums(item).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
        this.onSubmit()
      })
    },
    photoRefuse(item) {
      item.state = 3
      item.reject = 'Reject'
      updateUserPhotoAlbums(item).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
        this.onSubmit()
      })
    },
    videoPass(item) {
      item.state = 2
      updateUserVideoAlbums(item).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
        this.onSubmit()
      })
    },
    videoRefuse(item) {
      item.state = 3
      item.reject = 'Reject'
      updateUserVideoAlbums(item).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
        this.onSubmit()
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.m-tabs {
  .el-descriptions__label {
    font-size: 12px;
    padding: 2px !important;
  }

  .el-descriptions__content {
    font-size: 12px;
    padding: 2px !important;
  }
}

.m-avatar-box {
  .el-row {
    padding: 0;
  }
}

.m-photo-box {
  .el-row {
    padding: 0;
  }

  .photo-left-box {
    height: 150px;
    width: 100%;
    overflow: hidden;
  }

  .text-right-box {
    height: 148px;
    .el-card__body {
      width: 100%;
      padding: 6px;
      overflow: scroll;
      word-wrap: break-word;
      height: 100%;
      font-size: 12px;
    }
  }
}

.extra-item1 .el-checkbox-group{
  flex-wrap: wrap;
}
</style>
