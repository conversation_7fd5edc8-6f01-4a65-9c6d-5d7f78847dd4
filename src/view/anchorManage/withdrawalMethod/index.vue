<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="名称">
          <el-input v-model="searchInfo.name" placeholder="请输入名称" />
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" type="success" icon="el-icon-plus" @click="openDialog">新增</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table ref="tableData" style="width: 100%" :data="tableData" row-key="id" :max-height="590">
        <el-table-column align="center" label="日期" prop="created_at_str" min-width="180"/>
        <el-table-column align="center" label="名称" prop="name" min-width="120" />
        <el-table-column align="center" label="Icon" prop="icon" min-width="120" />
        <el-table-column align="center" label="标签列表" prop="label_list" min-width="120" />
        <el-table-column align="center" label="提示信息" prop="notice" min-width="120" />
        <el-table-column align="center" label="注册链接" prop="reg_url" min-width="120" />
        <el-table-column align="center" label="排序" prop="sort" min-width="120" />
        <el-table-column align="center" label="操作" min-width="180">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateWithdrawalMethod(scope.row)">修改</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" top="5px" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" :rules="formDataRules" label-position="right" label-width="120px">
        <el-form-item label="名称:" prop="name">
          <el-input v-model="formData.name" clearable />
        </el-form-item>
        <el-form-item label="Icon:" prop="icon">
          <el-input v-model="formData.icon" clearable />
        </el-form-item>
        <el-form-item label="标签列表:" prop="label_list">
        </el-form-item>
        <el-form-item label="提示信息:" prop="notice">
          <el-input v-model="formData.notice" clearable />
        </el-form-item>
        <el-form-item label="注册链接:" prop="reg_url">
          <el-input v-model="formData.reg_url" clearable />
        </el-form-item>
        <el-form-item label="排序:" prop="sort">
          <el-input v-model.number="formData.sort" clearable/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { createWithdrawalMethod, deleteWithdrawalMethod, deleteWithdrawalMethodByIds, updateWithdrawalMethod, findWithdrawalMethod, getWithdrawalMethodList} from '@/api/withdrawalMethod' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
export default {
  name: 'WithdrawalMethod',
  mixins: [infoList],
  data() {
    return {
      listApi: getWithdrawalMethodList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        name: null,
        icon: null,
        notice: null,
        reg_url: null,
        sort: null,
      },
      formDataRules: {
        name: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        icon: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
    }
  },
  created() {
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteWithdrawalMethod(row)
      })
    },
    async updateWithdrawalMethod(row) {
      const res = await findWithdrawalMethod({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.rewithdrawalMethod
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {}
    },
    async deleteWithdrawalMethod(row) {
      const res = await deleteWithdrawalMethod({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createWithdrawalMethod(this.formData)
          break
        case 'update':
          res = await updateWithdrawalMethod(this.formData)
          break
        default:
          res = await createWithdrawalMethod(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '成功'
        })
        this.closeDialog()
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
      })
    },
  },
}
</script>

<style>
</style>
