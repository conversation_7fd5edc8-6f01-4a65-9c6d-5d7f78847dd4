<template>
  <div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
        <el-popover v-model:visible="deleteVisible" placement="top" width="160">
          <p>确定要删除吗？</p>
          <div style="text-align: right; margin-top: 8px;">
            <el-button size="mini" type="text" @click="deleteVisible = false">取消</el-button>
            <el-button size="mini" type="primary" @click="onDelete">确定</el-button>
          </div>
          <template #reference>
            <el-button icon="el-icon-delete" size="mini" style="margin-left: 10px;" :disabled="!multipleSelection.length">删除</el-button>
          </template>
        </el-popover>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="center" label="创建日期" width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="名称" prop="tag_name" width="120" />
        <el-table-column align="center" label="排序" prop="order_id" width="120" />
        <el-table-column align="center" label="类别" prop="category" width="120">
          <template #default="scope">{{ formaTagsCategoryPartner(scope.row.category) }}</template>
        </el-table-column>
        <el-table-column align="center" label="操作">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateTags(scope.row)">变更</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作" top="1%" width="397px">
      <el-form :model="formData" label-position="right" label-width="80px">
        <el-form-item label="名称:">
          <el-input v-model="formData.tag_name" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="排序:">
          <el-input-number v-model.number="formData.order_id" :min="1" :max="10" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="类别:">
          <el-select v-model="formData.category" clearable placeholder="类别">
            <el-option
              v-for="item in tagsCategoryOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createTags,
  deleteTags,
  deleteTagsByIds,
  updateTags,
  findTags,
  getTagsList
} from '@/api/tags' //  此处请自行替换地址
import infoList from '@/mixins/infoList'

const tagsCategoryOptions = [
  {
    value: 1,
    label: '默认值',
  },
]

export default {
  name: 'Tags',
  mixins: [infoList],
  data() {
    return {
      listApi: getTagsList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      tagsCategoryOptions: tagsCategoryOptions,
      formData: {
        tag_name: '',
        order_id: 0,
        category: 0,
      }
    }
  },
  async created() {
    await this.getTableData()
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteTags(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
        this.multipleSelection.map(item => {
          ids.push(item.id)
        })
      const res = await deleteTagsByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateTags(row) {
      const res = await findTags({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.retags
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        tag_name: '',
        order_id: 0,
        category: 0,
      }
    },
    async deleteTags(row) {
      const res = await deleteTags({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createTags(this.formData)
          break
        case 'update':
          res = await updateTags(this.formData)
          break
        default:
          res = await createTags(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    }
  },
}
</script>

<style>
</style>
