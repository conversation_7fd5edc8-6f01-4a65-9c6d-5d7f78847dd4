<template>
  <div class="video-page">
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="用户ID">
          <el-input v-model="searchInfo.anchor_id" clearable placeholder="用户ID" />
        </el-form-item>
        <el-form-item label="国籍">
          <el-select v-model="searchInfo.country_code" clearable filterable placeholder="国籍">
            <el-option
              v-for="item in countryCodeOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时长">
          <el-select v-model="searchInfo.duration" clearable filterable placeholder="时长">
            <el-option
              v-for="item in durationOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-tabs
        v-model="activeName"
        type="card"
        @tab-click="handleClick"
      >
        <el-tab-pane label="视频审核" name="audit" />
        <el-tab-pane label="普通视频" name="normal" />
        <el-tab-pane label="私密视频" name="private" />
        <el-tab-pane label="通话1" name="call1" />
        <el-tab-pane label="通话2" name="call2" />
        <el-tab-pane label="通话3" name="call3" />
      </el-tabs>
      <el-table ref="multipleTable" style="width: 100%" tooltip-effect="dark" :data="tableData" row-key="id" border>
        <el-table-column align="center" label="主播ID" prop="anchor_call_videos.anchor_id" width="240">
          <template #default="scope">
            <div style="color: #409EFF;" @click="getUserInfo(scope.row.anchor_call_videos.anchor_id)">
              {{ scope.row.anchor_call_videos.anchor_id }}
            </div>
          </template>
        </el-table-column>
        <el-table-column :min-width="480" align="center" label="视频" prop="username">
          <template #default="scope">
            <div class="video-box">
              <div v-for="(item, i) in scope.row.sub_video" :key="i" class="img-list">
                <vue3VideoPlay
                  :auto-play="false"
                  :control-btns="['audioTrack','fullScreen']"
                  :src="item.video_url"
                  :volume="1"
                  height="300px"
                  type="m3u8"
                  width="100%"
                />
                <!-- <video :src="item.video_url" class="chat-img" controls="controls">
                  您的浏览器不支持视频播放
                </video> -->
                <!-- <video :src="item.video_url" class="chat-img" controls="controls">
                  您的浏览器不支持视频播放
                </video> -->
                <div class="block">
                  <span class="demonstration">{{ formatDuration(item.duration) }}</span>
                </div>
                <el-button type="danger" size="small" @click="deleteImg(item)">删除</el-button>
                <el-button v-if="item.video_tab===1" type="success" size="small" @click="updateAnchorCallVideoDetails(item)">分类</el-button>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" top="5px" :before-close="closeDialog" :title="dialogTitle">
      <el-form :model="formData" label-position="right" label-width="120px">
        <el-input v-show="false" v-model.number="formData.id" clearable />
        <el-form-item label="分类">
          <el-select v-model="formData.video_tab" clearable filterable placeholder="分类">
            <el-option
              v-for="item in videoTabOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <user-info-update-dialog v-if="showUserInfoDialog" @closeFunc="closeDialogUserInfo" ref="userInfoUpdateDialogRef" :user_id="userInfoUpdateId"></user-info-update-dialog>

  </div>
</template>


<script>
import { getAnchorCallVideosList } from '@/api/anchorCallVideos'
import { deleteAnchorCallVideoDetails, updateAnchorCallVideoDetails } from '@/api/anchorCallVideoDetails'
import infoList from '@/mixins/infoList'
import UserInfoUpdateDialog from '@/components/UserInfoUpdateDialog/index.vue'

export default {
  name: 'VedioCall',
  components: { UserInfoUpdateDialog },
  mixins: [infoList],
  data() {
    return {
      userInfoUpdateId:0,
      showUserInfoDialog:false,
      listApi: getAnchorCallVideosList,
      dialogFormVisible: false,
      dialogTitle: 'update',
      formData: {
        id: 0,
        video_tab: 1,
      },
      searchInfo: {
        anchor_id: '',
        video_tab: 1,
      },
      pageSizeList: [1, 2, 3, 5],
      pageSize: 1,
      total: 0,
      activeName: 'audit',
      videoTabOptions: [
        { 'label': '普通视频', 'value': 2 },
        { 'label': '私密视频', 'value': 3 },
        { 'label': '通话1', 'value': 4 },
        { 'label': '通话2', 'value': 5 },
        { 'label': '通话3', 'value': 6 },
      ]
    }
  },
  async created() {
    await this.onSubmit()
  },
  methods: {
    closeDialogUserInfo() {
      this.showUserInfoDialog = false
    },
    getUserInfo(id) {
      this.userInfoUpdateId = id
      this.showUserInfoDialog = true
    },
    onReset() {
      this.searchInfo = {
        anchor_id: '',
        video_tab: 1,
      }
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.getTableData()
    },
    deleteImg(item) {
      deleteAnchorCallVideoDetails(item).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
        this.getTableData()
      })
    },
    async updateAnchorCallVideoDetails(row) {
      console.log(row)
      this.formData = row
      this.dialogFormVisible = true
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {}
    },
    enterDialog() {
      updateAnchorCallVideoDetails(this.formData).then((res) => {
        if (res.code === 0) {
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
        this.closeDialog()
        this.getTableData()
      })
    },
    async handleClick(tab, event) {
      this.searchInfo.video_tab = parseInt(tab.index) + 1
      await this.onSubmit()
    },
    formatDuration(sec) {
      let s = '00:00'
      if (sec < 60) {
        const second = sec < 10 ? '0' + sec : sec
        s = '00:' + second
      } else {
        let minute = Math.floor(sec / 60)
        let second = sec % 60
        minute = minute < 10 ? '0' + minute : minute
        second = second < 10 ? '0' + second : second
        s = minute + ':' + second
      }
      return s
    },
  },
}
</script>

<style scoped lang="scss">
.video-box {
  display: flex;
  flex-wrap: wrap;
  .img-list {
    width: 170px;
    margin-bottom: 10px;
    border: 1px solid #e6e8eb;
    margin-right: 6px;
    span {
      text-align: center;
      height: 30px;
      margin-top: -20px;
      color: #f56c6c;
    }
  }
}

.chat-img {
  width: 150px;
  height: 150px;
}
</style>
