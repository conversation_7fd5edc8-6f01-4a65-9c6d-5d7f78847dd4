<template>
    <div>
        <div class="gva-search-box">
            <el-form :inline="true" :model="searchInfo">
                <el-form-item label="用户ID">
                    <el-input v-model="searchInfo.user_id" placeholder="请输入用户ID" />
                </el-form-item>
                <el-form-item>
                    <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
                    <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
                    <el-button size="mini" type="success" icon="el-icon-plus" @click="openDialog">新增</el-button>
                    <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>
                </el-form-item>
            </el-form>
        </div>
        <div class="gva-table-box">
            <el-table ref="tableData" style="width: 100%" :data="tableData" row-key="id" :max-height="590">
                <el-table-column align="center" label="日期" prop="created_at_str" min-width="180"/>
                <el-table-column align="center" label="用户ID" prop="user_id" min-width="120" />
                <el-table-column align="center" label="国家" prop="country" min-width="120" />
                <el-table-column align="center" label="姓名" prop="account_name" min-width="120" />
                <el-table-column align="center" label="手机号" prop="account_phone" min-width="120" />
                <el-table-column align="center" label="邮箱" prop="account_email" min-width="120" />
                <el-table-column align="center" label="地址" prop="account_address" min-width="120" />
                <el-table-column align="center" label="银行码" prop="bank_code" min-width="120" />
                <el-table-column align="center" label="费率" prop="fee" min-width="120" />
                <el-table-column align="center" label="账号" prop="account_no" min-width="120" />
                <el-table-column align="center" label="几小时后到账" prop="arrival" min-width="120" />
                <el-table-column align="center" label="操作" min-width="180">
                    <template #default="scope">
                        <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateWithdrawalAccountBank(scope.row)">修改</el-button>
                        <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="gva-pagination">
                <el-pagination
                  layout="total, sizes, prev, pager, next, jumper"
                  :current-page="page"
                  :page-size="pageSize"
                  :page-sizes="pageSizeList"
                  :total="total"
                  @current-change="pageChange"
                  @size-change="sizeChange"
                />
            </div>
        </div>
        <el-dialog v-model="dialogFormVisible" top="5px" :before-close="closeDialog" title="弹窗操作">
            <el-form :model="formData" :rules="formDataRules" label-position="right" label-width="120px">
                <el-form-item label="用户ID:" prop="user_id">
                    <el-input v-model.number="formData.user_id" clearable/>
                </el-form-item>
                <el-form-item label="国家:" prop="country">
                    <el-input v-model="formData.country" clearable />
                </el-form-item>
                <el-form-item label="姓名:" prop="account_name">
                    <el-input v-model="formData.account_name" clearable />
                </el-form-item>
                <el-form-item label="手机号:" prop="account_phone">
                    <el-input v-model="formData.account_phone" clearable />
                </el-form-item>
                <el-form-item label="邮箱:" prop="account_email">
                    <el-input v-model="formData.account_email" clearable />
                </el-form-item>
                <el-form-item label="地址:" prop="account_address">
                    <el-input v-model="formData.account_address" clearable />
                </el-form-item>
                <el-form-item label="银行码:" prop="bank_code">
                    <el-input v-model="formData.bank_code" clearable />
                </el-form-item>
                <el-form-item label="费率:" prop="fee">
                    <el-input v-model.number="formData.fee" clearable/>
                </el-form-item>
                <el-form-item label="账号:" prop="account_no">
                    <el-input v-model="formData.account_no" clearable />
                </el-form-item>
                <el-form-item label="几小时后到账:" prop="arrival">
                    <el-input v-model.number="formData.arrival" clearable/>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button size="small" @click="closeDialog">取 消</el-button>
                    <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import { createWithdrawalAccountBank, deleteWithdrawalAccountBank, deleteWithdrawalAccountBankByIds, updateWithdrawalAccountBank, findWithdrawalAccountBank, getWithdrawalAccountBankList} from '@/api/withdrawalAccountBank' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
export default {
    name: 'WithdrawalAccountBank',
    mixins: [infoList],
    data() {
        return {
            listApi: getWithdrawalAccountBankList,
            dialogFormVisible: false,
            type: '',
            deleteVisible: false,
            multipleSelection: [],
            formData: {
                user_id: null,
                country: null,
                account_name: null,
                account_phone: null,
                account_email: null,
                account_address: null,
                bank_code: null,
                fee: null,
                account_no: null,
                arrival: null,
            },
            formDataRules: {
                user_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
                country: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
                account_name: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
                account_phone: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
                account_email: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
                account_address: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
                bank_code: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
                fee: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
                account_no: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
                arrival: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
            },
        }
    },
    created() {
        this.getTableData(this.nFunc, this.tableDataFormat)
    },
    methods: {
        onReset() {
            this.searchInfo = {}
        },
        // 条件搜索前端看此方法
        onSubmit() {
            this.page = 1
            this.pageSize = 10
            this.getTableData(this.nFunc, this.tableDataFormat)
        },
        deleteRow(row) {
            this.$confirm('确定要删除吗?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.deleteWithdrawalAccountBank(row)
            })
        },
        async updateWithdrawalAccountBank(row) {
            const res = await findWithdrawalAccountBank({ id: row.id })
            this.type = 'update'
            if (res.code === 0) {
                this.formData = res.data.rewithdrawalAccountBank
                this.dialogFormVisible = true
            }
        },
        closeDialog() {
            this.dialogFormVisible = false
            this.formData = {}
        },
        async deleteWithdrawalAccountBank(row) {
            const res = await deleteWithdrawalAccountBank({ id: row.id })
            if (res.code === 0) {
                this.$message({
                    type: 'success',
                    message: '删除成功'
                })
                if (this.tableData.length === 1 && this.page > 1) {
                    this.page--
                }
                this.getTableData(this.nFunc, this.tableDataFormat)
            }
        },
        async enterDialog() {
            let res
            switch (this.type) {
                case 'create':
                    res = await createWithdrawalAccountBank(this.formData)
                    break
                case 'update':
                    res = await updateWithdrawalAccountBank(this.formData)
                    break
                default:
                    res = await createWithdrawalAccountBank(this.formData)
                    break
            }
            if (res.code === 0) {
                this.$message({
                    type: 'success',
                    message: '成功'
                })
                this.closeDialog()
                await this.getTableData(this.nFunc, this.tableDataFormat)
            }
        },
        openDialog() {
            this.type = 'create'
            this.dialogFormVisible = true
        },
        exportExcel() {
            let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
            let res = this.getTableHeaderProp(tableColumns)
            let tHeader = res[0]
            let filterV = res[1]
            this.handleProgressLoading()
            import('@/utils/excel').then((excel) => {
                const data = this.formatJson(filterV, this.tableData)
                excel.export_json_to_excel({
                    header: tHeader,
                    data,
                    filename: this.filename,
                    autoWidth: this.autoWidth,
                    bookType: this.bookType,
                })
                this.progressLoading.close()
            })
        },
        sizeChange(v) {
            this.pageSize = v
            this.getTableData(this.nFunc, this.tableDataFormat)
        },
        pageChange(v) {
            this.page = v
            this.getTableData(this.nFunc, this.tableDataFormat)
        },
        tableDataFormat() {
            this.tableData.map((item, i, data) => {
                data[i].created_at_str = this.formatDate(item.created_at)
            })
        },
    },
}
</script>

<style>
</style>
