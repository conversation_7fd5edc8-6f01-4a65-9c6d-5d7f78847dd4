<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item v-if="unionIdBool" label="产品名称">
          <el-select multiple collapse-tags v-model="searchInfo.app_ids" clearable filterable>
            <el-option
                v-for="item in appList"
                :key="item.id"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="主播ID">
          <el-input v-model="searchInfo.dst_id"></el-input>
        </el-form-item>
        <el-form-item label="用户ID">
          <el-input v-model="searchInfo.src_id"></el-input>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
              v-model="searchInfo.date_range"
              type="datetimerange"
              :shortcuts="dateRangeShortcuts"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DDTHH:mm:ssZ"
              :default-time="[new Date(2022, 1, 1, 0, 0, 0),new Date(2022, 1, 1, 23, 59, 59)]"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
          style="width: 100%"
          :data="tableData"
          max-height="590"
          row-key="id">
        <el-table-column align="center" label="用户ID" prop="src_id"/>
        <el-table-column align="center" label="主播ID" prop="dst_id"/>
        <el-table-column align="center" label="主播昵称" prop="nickname"></el-table-column>
        <el-table-column align="center" label="发起时间" prop="begin_ts">
          <template #default="scope">{{formatDate(scope.row.begin_ts)}}</template>
        </el-table-column>
        <el-table-column align="center" label="通话时长" prop="duration"/>
        <el-table-column align="center" label="操作">
          <template #default="scope">
            <el-button type="text" icon="el-icon-download" size="small" :disabled="scope.row.duration === 0 || scope.row.video_url === ''"
                       @click="downloadVideo(scope.row)">下载
            </el-button>
            <!--
            <el-button type="text" icon="el-icon-view" :disabled="scope.row.duration === 0 || scope.row.video_url === ''" size="small"
                       @click="viewVideo(scope.row)">查看
            </el-button>
            -->
          </template>
        </el-table-column>

      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="videoDialogVisible" destroy-on-close top="20px" width="30%">
      <!--
      <vue3VideoPlay
          width="400px"
          height="650px"
          :src="playerOptions.src"
          :type="playerOptions.type"
          :autoPlay="false"
          :volume="1"
          :controlBtns="['audioTrack', 'volume', 'pageFullScreen', 'fullScreen']"
      />
      -->
    </el-dialog>
  </div>
</template>

<script>
import {downloadM3u8File, getConnectedReportsList2} from '@/api/connectedReports' //  此处请自行替换地址
import infoList from '@/mixins/infoList'

export default {
    mixins: [infoList],
    data() {
      return {
        playerOptions: {
          src: "https://test-streams.mux.dev/x36xhzz/x36xhzz.m3u8", //视频源
          type: 'm3u8', //视频类型
        },

        unionIdBool: null,
        videoDialogVisible: false,
        m3u8Url: "https://s3.oklove.top/video/1646897606229218990.mp4",
        searchInfo: {
          date_range: [],
          app_ids: [],
          dst_id: null,
          src_id: null,
        },
        listApi: getConnectedReportsList2,
      }
    },
    async created() {
      this.unionIdBool = this.userInfo.union_id === 0
      await this.getTableData()
    },
    methods: {
      // 生成uuid
      generate_uuid(){
        let d = new Date().getTime();
        if(window.performance && typeof window.performance.now === "function"){
          d += performance.now(); //use high-precision timer if available
        }
        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
          const r = (d + Math.random() * 16) % 16 | 0;
          d = Math.floor(d / 16);
          return (c === 'x' ? r : (r & 0x3 | 0x8)).toString(16);
        });
      },
      viewVideo(item) {
        if (item.video_url === "" ) {
          this.$message.error("视频链接为空")
          return
        }
        this.m3u8Url = item.video_url
        this.playerOptions.src = item.video_url
        this.videoDialogVisible = true
      },
      fileSave(data) {
        let downloadUrl = URL.createObjectURL(new Blob([data], { type: "video/mp4" }))
        let a = document.createElement('a')
        a.style.display = 'none'
        a.href = downloadUrl
        a.download = this.generate_uuid() + ".mp4"
        a.click()
      },
      downFile2(res) {
        const reader = new FileReader()
        reader.readAsDataURL(res)
        reader.onload = (e)=>{
          const a = document.createElement("a")
          a.download = this.generate_uuid() + ".mp4"
          a.href = e.target.result
          document.body.appendChild(a)
          a.click()
          document.body.removeChild(a)
        }

      },
      downloadVideo(item) {
        if (!item.video_url.startsWith("http")) {
          item.video_url = "http:" + item.video_url
        }
        downloadM3u8File({src: item.video_url}).then(res => {
        })
      },
      onReset() {
        this.searchInfo = {
          date_range: [],
          app_ids: [],
          dst_id: null,
          src_id: null,
        }
      },
      onSubmit() {
        this.page = 1
        this.pageSize = 10
        this.getTableData()
      },
    },
  }
</script>

<style lang="scss" scoped>

</style>
