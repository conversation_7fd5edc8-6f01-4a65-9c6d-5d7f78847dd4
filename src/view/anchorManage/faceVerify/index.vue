<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="用户ID">
          <el-input v-model="searchInfo.user_id" clearable placeholder="用户ID"/>
        </el-form-item>
        <el-form-item label="国家">
          <el-select v-model="searchInfo.country_code" clearable filterable placeholder="国籍">
            <el-option
              v-for="item in countryCodeOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工会">
          <el-select v-model="searchInfo.union_id" placeholder="请选择" clearable>
            <el-option
              v-for="item in uniosList"
              :key="item.id"
              :label="`${item.name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="主播等级">
          <el-select v-model="searchInfo.levels" clearable filterable placeholder="主播等级">
            <el-option
                v-for="item in levelsOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select v-model="searchInfo.state" clearable filterable placeholder="审核状态">
            <el-option
              v-for="item in stateOptions"
              :key="item.value"
              :label="`${item.label}`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-empty description="无数据" v-if="tableData.length === 0"></el-empty>
      <el-row :gutter="10">
        <el-col :span="6" style="margin-bottom: 10px" v-for="(item, i) in tableData" :key="i">
          <el-card shadow="hover" class="m-photo-box" :body-style="{padding: '6px'}">
            <el-row :gutter="10" style="height: 170px; overflow: hidden">
              <el-col :span="11">
                <el-image
                  :src="parseResource(item.video_img)"
                  fit="cover"
                    @click="onPreviewVideo(item)"
                  class="photo-left-box"
                  style="cursor: pointer"
                />
              </el-col>
              <el-col :span="13" class="r-info">
                <el-descriptions direction="horizontal" :column="1" size="small" border>
                  <el-descriptions-item label="ID">{{ item.user_id }}</el-descriptions-item>
                  <el-descriptions-item label="名称">{{ item.nickname }}</el-descriptions-item>
                  <el-descriptions-item label="工会">{{ formatUniosName(item.union_id) }}</el-descriptions-item>
                  <el-descriptions-item label="等级">{{ item.levels }}</el-descriptions-item>
                  <el-descriptions-item label="国家">{{ item.country_code }}</el-descriptions-item>
                  <el-descriptions-item label="创建时间">{{ formatDate(item.create_time) }}</el-descriptions-item>
                  <el-descriptions-item v-if="item.state!=1" label="审核时间">{{ formatDate(item.audit_time) }}</el-descriptions-item>

                  <el-descriptions-item label="状态">{{ formatState(item.state) }}</el-descriptions-item>
                </el-descriptions>
              </el-col>
            </el-row>
            <div style="display: flex; flex-direction: row-reverse">
              <el-button style="margin: 0 10px" v-if="item.state !== 2" type="success"
                         @click="faceVerifyPass(item)">通过
              </el-button>
              <el-button style="margin: 0 10px" v-if="item.state !== 3" type="danger"
                         @click="faceVerifyRefuse(item)">拒绝
              </el-button>
              <el-button style="margin: 0 10px" type="primary" @click="getUserInfo(item)">查看信息</el-button>
              <el-button type="primary" @click="showAnchorDataDialog(item.user_id)">
                数据详情
              </el-button>
            </div>
          </el-card>
        </el-col>
      </el-row>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>

      <el-dialog
        v-model="videoVisible"
        width="397px"
        top="2%"
        destroy-on-close
        center
      >
        <video
          :src="videoUrl"
          class="avatar video-avatar"
          controls="controls"
        >
          您的浏览器不支持视频播放
        </video>
      </el-dialog>


      <el-dialog v-model="previewImageVisible" title="图片预览" width="50%" top="1%" center>
        <img :src="previewImagePath" class="avatar video-avatar" style="max-height: 80vh;overflow-y: auto;">
      </el-dialog>
    </div>

    <anchor-data-dialog ref="anchorDataDialogRef" :dst_id="anchorDstId" ></anchor-data-dialog>

    <user-info-update-dialog ref="userInfoUpdateDialogRef" :user_id="userInfoUpdateId"></user-info-update-dialog>
  </div>
</template>

<script>
import {ElLoading} from 'element-plus'
import draggable from 'vuedraggable'
import {banUser, cancelAuditUsers, findOneUserWallet, findUsers, unsealUser, updateUsers} from '@/api/users'
import {getFaceVerify, updateUserAppliesNew} from '@/api/userAppliesNew'
import infoList from '@/mixins/infoList'
import {getPersonalityLabelsTree} from "@/api/personality_labels";
import {banAnchor} from "@/api/banRecords";
import UserInfoUpdateDialog from "@/components/UserInfoUpdateDialog/index.vue";

const path = import.meta.env.VITE_BASE_API
const stateOptions = [
  {
    value: 1,
    label: '审核中',
  },
  {
    value: 4,
    label: '已审核',
  },
  {
    value: 2,
    label: '通过',
  },
  {
    value: 3,
    label: '拒绝',
  },
]
const stateDict = {
  1: '审核中',
  2: '通过',
  3: '拒绝',
}
export default {
  name: 'AnchorInfoChange',
  components: {UserInfoUpdateDialog, draggable},
  mixins: [infoList],
  data() {
    return {
      banDialogFormVisible: false,
      formBanData: {},
      formBanDataRules: {
        ban_reason: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
      anchorDstId: 0,
      userInfoUpdateId: 0,
      listApi: getFaceVerify,
      videoUrl: '',
      videoVisible: false,
      previewImageVisible: false,
      isDragging: false,
      previewImagePath: '',
      avatarFileList: [],
      dialogFormVisible: false,
      formData: {},
      unionOptions: [],
      path,
      personalityLabelsOptions: [],
      stateOptions,
      stateDict,
      searchInfo: {
        state: 1,
      },
    }
  },
  async created() {
    await this.onSubmit()
    await this.getPersonalityLabelsList()
  },
  methods: {
    showUserInfoUpdateDialog(dst_id) {
      this.userInfoUpdateId = dst_id
      this.$refs.userInfoUpdateDialogRef.dialogVisible = true
    },
    showBanAnchorDialog(user_id) {
      findOneUserWallet({user_id}).then(res=>{
        if (res.code === 0) {
          this.formBanData = {
            record_type: 1,
            ban_id: user_id,
            deduction_ratio: 100,
            ban_days: 0,
            ban_reason: '',
            balance: res.data.un_diamonds,
          }
          this.banDialogFormVisible = true
        }
      })

    },
    closeBanDialog(){
      this.banDialogFormVisible = false
      this.$refs.formBanDataRef.resetFields()
    },
    async enterBanDialog() {
      this.$refs.formBanDataRef.validate(async (v) => {
        if (!v) {
          return
        }
        let res = await banAnchor(this.formBanData)
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功'
          })
          let userRes = await findUsers({id: this.formBanData.ban_id})
          if (userRes.code === 0) {
            this.formData = userRes.data.reusers
          }
          this.closeBanDialog()
        }
      })
    },
    showAnchorDataDialog(dst_id) {
      this.anchorDstId = dst_id
      this.$refs.anchorDataDialogRef.dialogVisible = true
    },
    async enterDialog() {
      let res
      res = await updateUsers(this.formData)
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    unsealAnchor() {
      unsealUser({userId: this.formData.id}).then(res => {
        if (res.code === 0) {
          this.$message.success("成功")
          this.dialogFormVisible = false
        }
      })
    },
    banAnchor() {
      banUser({
        userId: this.formData.id,
        role: this.formData.role,
        app_id: this.formData.appId,
        isDeleteApply: false
      }).then(res => {
        if (res.code === 0) {
          this.$message.success("成功")
          this.dialogFormVisible = false
        }
      })
    },
    cancleAuth(formData) {
      this.handleProgressLoading()
      cancelAuditUsers({"userId": formData.id}).then(res => {
        // 关闭loading
        this.progressLoading.close()
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.closeDialog()

        } else {
          this.$message({
            type: 'danger',
            message: res.msg
          })
        }
        this.getTableData()
      })
    },
    onChangeUserTags(e) {
      this.formData.userTags.forEach(function (value, index, array) {
        if (value.id === e.id) {
          array[index].checked = !e.checked
        }
      })
    },
    async getPersonalityLabelsList() {
      const res = await getPersonalityLabelsTree()
      this.personalityLabelsOptions = res.data.list
    },
    handleCallVideoSuccess(response, file, fileList) {
      if (!this.formData.callVideo) {
        this.formData.callVideo = []
      }
      this.formData.callVideo.push(file.response.data)

      // 数据去重
      const temp = []
      this.formData.callVideo.forEach(a => {
        const check = temp.every(b => {
          return a.url !== b.url
        })
        check ? temp.push(a) : []
      })
      this.formData.callVideo = temp

      // 关闭loading
      this.progressLoading.close()

      this.$refs['handleCallVideo'].clearFiles()
    },
    handleCallVideoRemove(file, fileList) {
      this.formData.callVideo = this.formData.callVideo.filter(function (age) {
        return age.url !== file.url
      })
    },
    handleConstellationVideoRemove(file, fileList) {
      this.formData.constellationVideo = []
      fileList.forEach((item, i) => {
        this.formData.constellationVideo.push(item)
      })
    },
    handleConstellationVideoSuccess(response, file, fileList) {
      this.formData.constellationVideo = []
      fileList.forEach((item, i) => {
        if (item.response !== undefined) {
          this.formData.constellationVideo.push(item.response.data)
        } else {
          this.formData.constellationVideo.push(item)
        }
      })

      // 关闭loading
      this.progressLoading.close()

      this.$refs['handleConstellationVideo'].clearFiles()
    },
    handleAlbumVideoSuccess(response, file, fileList) {
      if (!this.formData.albumVideo) {
        this.formData.albumVideo = []
      }
      this.formData.albumVideo.push(file.response.data)
      // 数据去重
      const temp = []
      this.formData.albumVideo.forEach(function (a) {
        const check = temp.every(function (b) {
          return a.url !== b.url
        })
        check ? temp.push(a) : []
      })
      this.formData.albumVideo = temp

      // 关闭loading
      this.progressLoading.close()

      this.$refs['handleAlbumVideo'].clearFiles()
    },
    handleAlbumVideoRemove(file, fileList) {
      this.formData.albumVideo = this.formData.albumVideo.filter(function (age) {
        return age.url !== file.url
      })
    },
    handleVideoVisible(file) {
      this.videoVisible = true
      this.videoUrl = file.video
    },
    async handlePhotoAlbumsSuccess(response, file, fileList) {
      const that = this
      if (!that.formData.photoAlbums) {
        that.formData.photoAlbums = []
      }
      that.formData.photoAlbums.push(file.response.data)
      // 数据去重
      const temp = []
      that.formData.photoAlbums.forEach(function (a) {
        const check = temp.every(function (b) {
          return a.key !== b.key
        })
        check ? temp.push(a) : []
      })
      that.formData.photoAlbums = temp

      // 关闭loading
      this.progressLoading.close()

      this.$refs['handlePhotoAlbums'].clearFiles()
    },
    handlePhotoAlbumsRemove(file) {
      this.formData.photoAlbums = this.formData.photoAlbums.filter(function (age) {
        return age.url !== file.url
      })
    },
    async updatePhotoAlbumsList(e) {
      const newIndex = e.newIndex// 新位置下标
      const oldIndex = e.oldIndex// 原始位置下标
    },
    handleImageRemove(file, fileList) {
      this.formData.avatar = ''
      this.avatarFileList = []
    },
    handleProgressLoading() {
      this.progressLoading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
    },
    handleImageSuccess(res) {
      this.avatarFileList = []
      const {data} = res
      if (data.url) {
        this.formData.avatar = data.url
        this.avatarFileList.push(data)
      }
      this.progressLoading.close()
    },
    handleImagePreview(file) {
      this.previewImagePath = file.url
      this.previewImageVisible = true
    },
    closeDialog() {
      this.avatarFileList = []
      this.dialogFormVisible = false
      this.formData = {}
      this.getTableData()
    },
    getUserInfo(item) {
      this.showUserInfoUpdateDialog(item.user_id)
    },
    onPreviewVideo(item) {
      this.videoUrl = this.parseResource(item.video_url)
      this.videoVisible = true
    },
    formatState(i) {
      return this.stateDict[i]
    },
    parseResource(src) {
      if (src !== null && src !== undefined && src !== "" && !src.startsWith("http")) {
        let match = src.match(/image|avatar|video|message|constellation/g)
        if (match) {
          src = 'https://s3.oklove.top/' + src
        } else {
          src = 'https://oss.bakbak.site/' + src
        }
      }
      return src
    },
    onReset() {
      this.searchInfo = {
        state: 1,
      }
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.getTableData()
    },
    faceVerifyPass(item) {
      item.state = 2
      updateUserAppliesNew(item).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg)
        } else {
          this.$message.error(res.msg)
        }
        this.getTableData()
      })
    },
    faceVerifyRefuse(item) {
      item.state = 3
      item.reject = "Reject"
      updateUserAppliesNew(item).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg)
        }
        this.getTableData()
      })
    },
  },
}
</script>

<style lang="scss">
.r-info {
  .el-descriptions__label {
    font-size: 8px;
    padding: 2px !important;
  }

  .el-descriptions__content {
    font-size: 8px;
    padding: 2px !important;
  }
}

.m-avatar-box {
  .el-row {
    padding: 0;
  }
}

.m-photo-box {
  .el-row {
    padding: 0;
  }

  .photo-left-box {
    height: 150px;
    width: 100%;
    overflow: hidden;
  }
}

</style>
