<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="时间范围">
          <el-date-picker
              v-model="searchInfo.date_range"
              type="daterange"
              :shortcuts="dateRangeShortcuts"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="主播ID">
          <el-input v-model="searchInfo.user_id" placeholder="请输入主播ID"/>
        </el-form-item>
        <el-form-item label="级别">
          <el-select v-model="searchInfo.levels" clearable filterable placeholder="主播等级">
            <el-option
                v-for="item in levelsOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工会">
          <el-select v-model="searchInfo.union_id" clearable filterable placeholder="工会">
            <el-option
                v-for="item in uniosList"
                :label="`${item.name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否已补贴" prop="is_subsidy">
          <el-select v-model="searchInfo.is_subsidy_value" clearable placeholder="请选择是否已补贴">
            <el-option key="true" label="是" :value="1"></el-option>
            <el-option key="false" label="否" :value="2"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
          border
          ref="tableData"
          style="width: 100%"
          tooltip-effect="dark"
          :data="tableData"
          @sort-change="tableSort"
          row-key="id"
      >
        <el-table-column align="center" label="日期" prop="date" min-width="180" sortable="custom" fixed="left"/>
        <el-table-column align="center" label="主播ID" prop="user_id" min-width="120"/>
        <el-table-column align="center" label="主播等级" prop="levels" min-width="120"/>
        <el-table-column align="center" label="工会" prop="union_id_str" min-width="120"/>
        <el-table-column align="center" label="在线时长(旧)" prop="online_durations_str" min-width="160" sortable="custom"/>
        <el-table-column align="center" label="在线时长(新)" prop="online_active_duration_str" min-width="160" sortable="custom"/>
        <el-table-column align="center" label="补贴期时长" prop="subsidy_online_durations_str" min-width="160" sortable="custom"/>
        <el-table-column align="center" label="平均通话时长(s)" prop="avg_src_call_durations" min-width="160"/>
        <el-table-column align="center" label="接听次数" prop="src_call_answer_count" min-width="180"/>
        <el-table-column align="center" label="接听率" prop="src_call_answer_rate_str" min-width="180"/>
        <el-table-column align="center" label="当日收益($)" prop="today_earned_str" min-width="120" sortable="custom"/>
        <el-table-column align="center" label="补贴时段内收益($)" prop="subsidy_time_earned_str" min-width="180" sortable="custom"/>
        <el-table-column align="center" label="补贴收益(宝石)" prop="subsidy_diamonds" min-width="160"/>
        <el-table-column align="center" label="是否已补贴" prop="is_subsidy_str" min-width="120" fixed="right">
          <template #default="scope">
            <el-tag type="success" v-if="scope.row.is_subsidy">是</el-tag>
            <el-tag type="info" v-else>否</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" min-width="180" fixed="right">
          <template #default="scope">
            <el-button :disabled="scope.row.is_subsidy" type="success" size="small" @click="showSubsidyBox(scope.row)">给予补贴</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            :total="total"
            @current-change="pageChange"
            @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {
  createAnchorSubsidy,
  deleteAnchorSubsidy,
  deleteAnchorSubsidyByIds,
  updateAnchorSubsidy,
  findAnchorSubsidy,
  getAnchorSubsidyList
} from '@/api/anchorSubsidy' //  此处请自行替换地址
import {createAnchorSubsidyDetail} from '@/api/anchorSubsidyDetail'
import infoList from '@/mixins/infoList'
import moment from "moment/moment";

export default {
  name: 'AnchorSubsidy',
  mixins: [infoList],
  data() {
    return {
      listApi: getAnchorSubsidyList,
    }
  },
  async created() {
    let yStr = moment().add(-1, 'day').format("YYYY-MM-DD")
    this.searchInfo.date_range = [yStr, yStr]
    this.pageSize = 100
    await this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    onReset() {
      this.pageSize = 100
      this.searchInfo = {}
    },
    handleDownload() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    tableSort(data) {
      let columnData = data.column
      let orderData = data.order
      let propData = data.prop
      if (propData !== null && orderData !== null) {
        let sortStr = orderData === 'ascending' ? "ASC" : "DESC"
        switch (propData) {
          case "online_durations_str":
            this.searchInfo.sort_prop = `online_durations ${sortStr}`
            break
          case "online_active_duration_str":
            this.searchInfo.sort_prop = `online_active_duration ${sortStr}`
            break
          case "subsidy_online_durations_str":
            this.searchInfo.sort_prop = `subsidy_online_durations ${sortStr}`
            break
          case "today_earned_str":
            this.searchInfo.sort_prop = `today_earned ${sortStr}`
            break
          case "subsidy_time_earned_str":
            this.searchInfo.sort_prop = `subsidy_time_earned ${sortStr}`
            break
          default:
            this.searchInfo.sort_prop = `${propData} ${sortStr}`
            break
        }
      }
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 100
      if (this.searchInfo.is_subsidy === "") {
        this.searchInfo.is_subsidy = null
      }
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    showSubsidyBox(row) {
      let inputNum = null
      if (row.is_subsidy) {
        inputNum = row.subsidy_diamonds
      }
      this.$prompt(
          "请输入要补贴的宝石数",
          "提示",
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            inputType: 'number',
            inputValue: inputNum,
            inputErrorMessage: '请输入正确的宝石数',
          }
      ).then(({value}) => {
        let diamondsFloat = parseFloat(value)
        if (diamondsFloat <= 0) {
          this.$message({
            type: 'error',
            message: '请输入大于0的补贴钻石数'
          })
          return
        }
        createAnchorSubsidyDetail({anchor_subsidy_id: row.id, subsidy_diamonds: parseFloat(value)}).then(res=>{
          if (res.code === 0) {
            this.$message({
              type: 'success',
              message: '成功'
            })
            this.getTableData(this.nFunc, this.tableDataFormat)
          }
        })
      })
    },
    formatDollar(v) {
      if (v === 0) {return 0}
      return parseFloat((v / 1000).toFixed(2))
    },
    formatRate(v) {
      let rate = parseFloat(v)
      if (rate === 0.0) {
        return '0'
      } else {
        return `${rate}%`
      }
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].date = this.formatDateYMD(item.date)
        data[i].union_id_str = this.formatUniosName(item.union_id)
        data[i].online_durations_str = this.formateSeconds(item.online_durations)
        data[i].online_active_duration_str = this.formateSeconds(item.online_active_duration)
        data[i].subsidy_online_durations_str = this.formateSeconds(item.subsidy_online_durations)
        data[i].today_earned_str = this.formatDollar(item.today_earned)
        data[i].src_call_answer_rate_str = this.formatRate(item.src_call_answer_rate)
        data[i].subsidy_time_earned_str = this.formatDollar(item.subsidy_time_earned)
        data[i].is_subsidy_str = item.is_subsidy ? "是": "否"

      })
    },
  },
}
</script>

<style>
</style>

