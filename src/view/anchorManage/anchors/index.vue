<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="用户ID">
          <el-input v-model="searchInfo.id" placeholder="用户ID"/>
        </el-form-item>
        <el-form-item label="昵称">
          <el-input v-model="searchInfo.nickname" placeholder="昵称"/>
        </el-form-item>
        <el-form-item label="国籍">
          <el-select v-model="searchInfo.countryCode" clearable filterable placeholder="国籍">
            <el-option
                v-for="item in countryCodeOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="认证状态">
          <el-select v-model.number="searchInfo.authState" clearable filterable placeholder="认证状态">
            <el-option
                v-for="item in authStateOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="封禁状态">
          <el-select v-model.number="searchInfo.recovery" clearable filterable placeholder="封禁状态">
            <el-option
                v-for="item in recoveryStateOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="主播类型">
          <el-select v-model.number="searchInfo.anchorFake" clearable filterable placeholder="主播类型">
            <el-option
                v-for="item in anchorFakeOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="类型">
          <el-select v-model.number="searchInfo.fake" clearable filterable placeholder="类型">
            <el-option
                v-for="item in fakeOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="工会">
          <el-select v-model="searchInfo.unionId" placeholder="请选择" clearable>
            <el-option
                v-for="item in uniosList"
                :key="item.id"
                :label="`${item.name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="主播等级">
          <el-select v-model="searchInfo.levels" clearable filterable placeholder="主播等级">
            <el-option
                v-for="item in levelsOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">Search</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">Reset</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
      </div>
      <el-table
          ref="multipleTable"
          style="width: 100%"
          tooltip-effect="dark"
          :data="tableData"
          row-key="id"
          max-height="590"
      >
        <el-table-column align="center" label="操作" width="240"
                         fixed="left">
          <template #default="scope">
            <el-popconfirm
                confirm-button-text="确定"
                cancel-button-text="取消"
                title="确定要删除吗?"
                @confirm="deleteUsers(scope.row)"
            >
              <template #reference>
                <el-button type="text" icon="el-icon-delete" size="mini">删除</el-button>
              </template>
            </el-popconfirm>
            <el-button type="text" icon="el-icon-view" size="small" @click="showAnchorDataDialog(scope.row.id)">查看数据</el-button>
            <el-button type="text" icon="el-icon-view" size="small" @click="showUserInfoUpdateDialog(scope.row.id)">
              变更
            </el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="用户ID" prop="id" width="120" fixed/>
        <el-table-column align="center" label="用户名" prop="username"
                         width="120"/>
        <el-table-column align="center" label="推荐标签" show-overflow-tooltip prop="userTag"
                         width="120"/>
        <el-table-column align="center" label="昵称" prop="nickname" min-width="160" show-overflow-tooltip/>
        <el-table-column align="center" label="目标" prop="user_like_str" min-width="160">
          <template #default="scope">
            {{formatUserLike(scope.row.user_like)}}
          </template>
        </el-table-column>
        <el-table-column align="center" label="头像" min-width="50" style="line-height: 50px">
          <template #default="scope">
            <el-image
                v-show="scope.row.avatar"
                style="width: 40px; height: 40px;line-height: 40px;overflow:visible"
                :src="scope.row.avatar"
                :lazy="true"
                :preview-src-list="[scope.row.avatar]"
            />
          </template>
        </el-table-column>

        <el-table-column align="center" label="性别" prop="status"
                         width="120">
          <template #default="scope">{{ formatSex(scope.row.gender) }}</template>
        </el-table-column>

        <el-table-column align="center" label="身高" prop="height"
                         width="120"/>

        <el-table-column align="center" label="体重" prop="weight"
                         width="120"/>

        <el-table-column align="center" label="创建时间" width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>

        <el-table-column align="center" label="身份" prop="role"
                         width="120">
          <template #default="scope">{{ formatRole(scope.row.role) }}</template>
        </el-table-column>

        <el-table-column align="center" label="工作状态" prop="role"
                         width="120">
          <template #default="scope">{{ formatWorkingCondition(scope.row.workingCondition) }}</template>
        </el-table-column>

        <el-table-column align="center" label="手机号" prop="phone"
                         width="120"/>
        <el-table-column align="center" label="所属工会" prop="unionId"
                         width="120"/>

        <el-table-column

            align="center"
            label="是否VIP"
            prop="vipExpireAt"
            width="120"
        >
          <template #default="scope">
            <el-tag type="success" v-if="Date.parse(scope.row.vipExpireAt) > new Date()">是</el-tag>
            <el-tag type="info" v-else>否</el-tag>
          </template>
        </el-table-column>

        <el-table-column align="center" label="VIP过期时间" width="180">
          <template #default="scope">{{ formatDate(scope.row.vipExpireAt) }}</template>
        </el-table-column>

        <el-table-column align="center" label="用户状态" width="180">
          <template #default="scope">{{ formatState(scope.row.state) }}</template>
        </el-table-column>

        <el-table-column

            align="center"
            label="最后登录IP"
            prop="lastLoginIp"
            width="120"
        />

        <el-table-column align="center" label="最后登录时间" width="180">
          <template #default="scope">{{ formatDate(scope.row.lastLoginAt) }}</template>
        </el-table-column>

        <el-table-column align="center" label="账号类型" width="120">
          <template #default="scope">{{ formatLoginType(scope.row.loginType) }}</template>
        </el-table-column>
        <el-table-column align="center" label="在线状态" width="120">
          <template #default="scope">{{ formatOnline(scope.row.online) }}</template>
        </el-table-column>
        <el-table-column align="center" label="国家" width="120">
          <template #default="scope">{{ formatCountryCode(scope.row.countryCode) }}</template>
        </el-table-column>
        <el-table-column align="center" label="主播类型" width="120">
          <template #default="scope">{{ formatAnchorFake(scope.row.anchorFake) }}</template>
        </el-table-column>
        <el-table-column
            align="center"
            label="用户余额"
            prop="diamonds"
            width="120"
        />
        <el-table-column align="center" label="渠道" prop="utmSource"
                         width="120"/>
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeListNormal"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>

    <anchor-data-dialog ref="anchorDataDialogRef" :dst_id="anchorDstId" ></anchor-data-dialog>
    <user-info-update-dialog v-if="showUserInfoDialog" @closeFunc="closeDialogUserInfo" ref="userInfoUpdateDialogRef" :user_id="userInfoUpdateId"></user-info-update-dialog>
  </div>
</template>

<script>
import {ElLoading} from 'element-plus'
import draggable from 'vuedraggable'
import {
  banUser,
  cancelAuditUsers,
  createUsers,
  deleteUsers,
  deleteUsersByIds,
  findOneUserWallet,
  findUsers,
  getUsersList,
  unsealUser,
  updateAuditUsers,
  updateUsers
} from '@/api/users' //  此处请自行替换地址
import {getPersonalityLabelsTree} from '@/api/personality_labels'
import infoList from '@/mixins/infoList'
import {banAnchor} from "@/api/banRecords";
import UserInfoUpdateDialog from "@/components/UserInfoUpdateDialog/index.vue";

const path = import.meta.env.VITE_BASE_API

const roleOptions = [
  {
    value: 2,
    label: '主播',
  },
]

export default {
  name: 'Users',
  components: {UserInfoUpdateDialog, draggable},
  mixins: [infoList],
  data() {
    return {
      showUserInfoDialog:false,

      banDialogFormVisible: false,
      formBanData: {},
      formBanDataRules: {
        ban_reason: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
      isDragging: false,
      roleOptions: roleOptions,
      previewImageVisible: false,
      previewImagePath: '',
      videoVisible: false,
      videoUrl: '',
      path: path,
      avatarFileList: [],
      photoAlbumsFileList: [],
      searchInfo: {role: 2, recovery: 0},
      listApi: getUsersList,
      anchorDstId: 0,
      userInfoUpdateId: 0,
      auditFormVisible: false,
      dialogFormVisible: false,
      type: '',
      dialogTitle: '',
      deleteVisible: false,
      multipleSelection: [],
      unionOptions: [],
      personalityLabelsOptions: [],
      formData: {
        albumVideo: [],
        callVideo: [],
        constellationVideo: [],
        photoAlbums: [],
        userTags: [],
        nickname: '',
        avatar: '',
        birthday: '',
        countryCode: '',
        anchorFake: 1,
        role: 2,
        avoid: 0,
      }
    }
  },
  async created() {
    // this.searchInfo.id = 4789
    await this.getTableData()
    await this.getPersonalityLabelsList()
  },
  methods: {
    closeDialogUserInfo() {
      this.showUserInfoDialog = false
    },
    showBanAnchorDialog(user_id) {
      findOneUserWallet({user_id}).then(res=>{
        if (res.code === 0) {
          this.formBanData = {
            record_type: 1,
            ban_id: user_id,
            deduction_ratio: 100,
            ban_days: 0,
            ban_reason: '',
            balance: res.data.un_diamonds,
          }
          this.banDialogFormVisible = true
        }
      })

    },
    closeBanDialog(){
      this.banDialogFormVisible = false
      this.$refs.formBanDataRef.resetFields()
    },
    async enterBanDialog() {
      this.$refs.formBanDataRef.validate(async (v) => {
        if (!v) {
          return
        }
        let res = await banAnchor(this.formBanData)
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功'
          })
          let userRes = await findUsers({id: this.formBanData.ban_id})
          if (userRes.code === 0) {
            this.formData = userRes.data.reusers
          }
          this.closeBanDialog()
        }
      })
    },
    showAnchorDataDialog(dst_id) {
      this.anchorDstId = dst_id
      this.$refs.anchorDataDialogRef.dialogVisible = true
    },
    showUserInfoUpdateDialog(dst_id) {
      console.log('showUserInfoUpdateDialog', dst_id)
      this.userInfoUpdateId = dst_id
      // this.$refs.userInfoUpdateDialogRef.dialogVisible = true
      this.showUserInfoDialog = true

    },
    banAnchor() {
      banUser({
        userId: this.formData.id,
        role:this.formData.role,
        app_id:this.formData.appId,
        isDeleteApply: false
      }).then(res=>{
        if (res.code === 0) {
          this.$message.success("成功")
          this.dialogFormVisible = false
        }
      })
    },
    unsealAnchor() {
      unsealUser({userId: this.formData.id}).then(res=>{
        if (res.code === 0) {
          this.$message.success("成功")
          this.dialogFormVisible = false
        }
      })
    },

    onChangeUserTags(e) {
      this.formData.userTags.forEach(function (value, index, array) {
        if (value.id === e.id) {
          array[index].checked = !e.checked
        }
      })
    },
    async updatePhotoAlbumsList(e) {
      const newIndex = e.newIndex// 新位置下标
      const oldIndex = e.oldIndex// 原始位置下标
    },
    async getPersonalityLabelsList() {
      const res = await getPersonalityLabelsTree()
      this.personalityLabelsOptions = res.data.list
    },
    handleImageRemove(file, fileList) {
      this.formData.avatar = ''
      this.avatarFileList = []
    },
    handleImageSuccess(res) {
      const that = this
      that.avatarFileList = []
      const {data} = res
      if (data.url) {
        that.formData.avatar = data.url
        that.avatarFileList.push(data)
      }
      this.progressLoading.close()
    },
    handlePhotoAlbumsRemove(file) {
      this.formData.photoAlbums = this.formData.photoAlbums.filter(function (age) {
        return age.url !== file.url
      })
    },
    handleProgressLoading() {
      this.progressLoading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
    },
    async handlePhotoAlbumsSuccess(response, file, fileList) {
      const that = this
      if (!that.formData.photoAlbums) {
        that.formData.photoAlbums = []
      }
      that.formData.photoAlbums.push(file.response.data)
      // 数据去重
      const temp = []
      that.formData.photoAlbums.forEach(function (a) {
        const check = temp.every(function (b) {
          return a.key !== b.key
        })
        check ? temp.push(a) : []
      })
      that.formData.photoAlbums = temp

      // 关闭loading
      this.progressLoading.close()

      this.$refs['handlePhotoAlbums'].clearFiles()
    },
    handleCallVideoSuccess(response, file, fileList) {
      const that = this
      if (!that.formData.callVideo) {
        that.formData.callVideo = []
      }
      that.formData.callVideo.push(file.response.data)

      // 数据去重
      const temp = []
      that.formData.callVideo.forEach(function (a) {
        const check = temp.every(function (b) {
          return a.url !== b.url
        })
        check ? temp.push(a) : []
      })
      that.formData.callVideo = temp

      // 关闭loading
      this.progressLoading.close()

      this.$refs['handleCallVideo'].clearFiles()
    },
    handleCallVideoRemove(file, fileList) {
      this.formData.callVideo = this.formData.callVideo.filter(function (age) {
        return age.url !== file.url
      })
    },
    handleConstellationVideoSuccess(response, file, fileList) {
      const that = this
      that.formData.constellationVideo = []
      fileList.forEach((item, i) => {
        if (item.response !== undefined) {
          that.formData.constellationVideo.push(item.response.data)
        } else {
          that.formData.constellationVideo.push(item)
        }
      })

      // 关闭loading
      this.progressLoading.close()

      this.$refs['handleConstellationVideo'].clearFiles()
    },
    handleConstellationVideoRemove(file, fileList) {
      this.formData.constellationVideo = []
      fileList.forEach((item, i) => {
        this.formData.constellationVideo.push(item)
      })
    },

    handleAlbumVideoSuccess(response, file, fileList) {
      const that = this
      if (!that.formData.albumVideo) {
        that.formData.albumVideo = []
      }
      that.formData.albumVideo.push(file.response.data)
      // 数据去重
      const temp = []
      that.formData.albumVideo.forEach(function (a) {
        const check = temp.every(function (b) {
          return a.url !== b.url
        })
        check ? temp.push(a) : []
      })
      that.formData.albumVideo = temp

      // 关闭loading
      this.progressLoading.close()

      this.$refs['handleAlbumVideo'].clearFiles()
    },
    handleAlbumVideoRemove(file, fileList) {
      this.formData.albumVideo = this.formData.albumVideo.filter(function (age) {
        return age.url !== file.url
      })
    },

    handleVideoVisible(file) {
      this.videoVisible = true
      this.videoUrl = file.video
    },

    handleImagePreview(file) {
      // 输出的file对象信息有一个tmp_path临时路径名称和一个url地址
      // 把url地址赋值到本地
      this.previewImagePath = file.url
      // 打开预览图片的对话框
      this.previewImageVisible = true
    },

    onReset() {
      this.searchInfo = {role: 2, recovery: 0}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
      this.multipleSelection.map(item => {
        ids.push(item.id)
      })
      const res = await deleteUsersByIds({ids})
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateUsers(row) {
      const that = this
      const res = await findUsers({id: row.id})
      that.type = 'update'
      that.dialogTitle = '修改信息'
      that.avatarFileList = []
      if (res.code === 0) {
        that.formData = res.data.reusers
        if (that.formData.avatar !== '') {
          that.avatarFileList.push({name: '', url: that.formData.avatar})
        } else {
          that.avatarFileList.push({name: '', url: that.formData.avatarVerify})
        }
        that.dialogFormVisible = true
      }
    },
    cancleAuth(formData) {
      this.handleProgressLoading()
      cancelAuditUsers({"userId": formData.id}).then(res => {
        // 关闭loading
        this.progressLoading.close()
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.closeDialog()

        } else {
          this.$message({
            type: 'danger',
            message: res.msg
          })
        }
        this.getTableData()
      })
    },
    closeDialog() {
      this.avatarFileList = []
      this.dialogFormVisible = false
      this.formData = {}
      this.getTableData()
    },
    closeAuditDialog() {
      this.avatarFileList = []
      this.auditFormVisible = false
      this.formData = {}
    },
    async deleteUsers(row) {
      const res = await deleteUsers({id: row.id})
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      const that = this
      let res
      switch (this.type) {
        case 'create':
          res = await createUsers(this.formData)
          break
        case 'update':
          res = await updateUsers(this.formData)
          break
        default:
          res = await createUsers(this.formData)
          break
      }
      if (res.code === 0) {
        that.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        that.closeDialog()
        that.getTableData()
      }
    },
    async enterAuditDialog() {
      const that = this
      let res
      switch (this.type) {
        case 'audit':
          res = await updateAuditUsers(this.formData)
          break
        default:
      }
      if (res.code === 0) {
        that.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        that.closeAuditDialog()
        that.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogTitle = '新增主播'
      this.dialogFormVisible = true
      this.photoAlbumsFileList = []
      this.formData = {
        albumVideo: [],
        callVideo: [],
        constellationVideo: [],
        photoAlbums: [],
        userTags: [],
        nickname: '',
        avatar: '',
        birthday: '',
        countryCode: '',
        anchorFake: 1,
        role: 2,
        avoid: 0,
        appId: 27,
      }
    }
  },
}
</script>

<style lang="scss">
.m-photo-box {
  display: flex;
  flex-direction: column;

  .el-form-item__content {
    display: flex !important;
  }
}

.m-item-box {
  display: flex;

  .m-item-box-left {
    width: 3%;
    margin-right: 5px
  }

  .m-item-box-right {
    width: 90%;
  }
}

.upload-container {
  width: 100%;
  display: flex;
  align-items: flex-end;

  .draggable-container {
    display: flex;

    .image-container {
      position: relative;
      width: 152px;
      height: 86px;
      margin-right: 17px;

      img {
        width: 100%;
        height: 100%;
      }

      .close {
        position: absolute;
        top: 4px;
        right: 4px;
        background: rgba(255, 255, 255, 1);
        border-radius: 50%;
        font-weight: 600;
      }
    }
  }

  .el-button {
    background: #EEF5FF;
    border: 1px solid #CFE3FD;
    color: #5E9FF8;
  }
}
</style>
