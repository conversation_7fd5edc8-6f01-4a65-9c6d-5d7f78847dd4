<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="时间范围">
          <el-date-picker
              v-model="searchInfo.date_range"
              type="datetimerange"
              :shortcuts="dateRangeShortcuts"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="工会长ID">
          <el-input v-model.number="searchInfo.union_manager"/>
        </el-form-item>
        <el-form-item label="工会">
          <el-select v-model="searchInfo.union_id" clearable filterable>
            <el-option
                v-for="item in uniosList"
                :label="`${item.name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="收益类型" prop="is_subsidy">
          <el-select v-model="searchInfo.trade_type" clearable placeholder="请选择收益类型">
            <el-option
                v-for="item in tradeTypeList"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否已提现" prop="is_subsidy">
          <el-select v-model="searchInfo.is_set" clearable>
            <el-option label="是" :value="1"/>
            <el-option label="否" :value="2"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box" style="margin: 10px 0; padding: 5px">
      <el-descriptions border :column="3">
        <el-descriptions-item :span="1" align="center" label="用户ID">{{
            anchorRecentData.user_id
          }}
        </el-descriptions-item>
        <el-descriptions-item :span="1" align="center" label="用户昵称">{{
            anchorRecentData.nickname
          }}
        </el-descriptions-item>
        <el-descriptions-item :span="1" align="center" label="工会">{{ anchorRecentData.union_id }}</el-descriptions-item>
        <el-descriptions-item :span="1" align="center" label="工会钱包余额(宝石)">{{
            anchorRecentData.balance
          }}
        </el-descriptions-item>
        <el-descriptions-item :span="1" align="center" label="工会本周收入(宝石)">{{
            anchorRecentData.week_income
          }}
        </el-descriptions-item>
        <el-descriptions-item :span="1" align="center" label="工会可结算收入(宝石)">{{
            anchorRecentData.settleable_income
          }}
        </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="gva-table-box">
      <el-table
          border
          ref="tableData"
          style="width: 100%"
          tooltip-effect="dark"
          :data="tableData"
          row-key="id"
      >
        <el-table-column align="center" label="日期" prop="created_at_ymd" min-width="120"/>
        <el-table-column align="center" label="时间" prop="created_at_hms" min-width="120"/>
        <el-table-column align="center" label="宝石" prop="withdrawal_count" min-width="120"/>
        <el-table-column align="center" label="收益类型" prop="trade_type_str" min-width="120"/>
        <el-table-column align="center" label="余额" prop="manager_balance" min-width="120"/>
        <el-table-column align="center" label="来源用户ID" prop="user_id" min-width="140"/>
        <el-table-column align="center" label="是否已提现" min-width="140">
          <template #default="scope">
            <el-tag :type="scope.row.period !== '' ? 'success':'info'">{{ scope.row.period !== '' ? '是' : '否' }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            :total="total"
            @current-change="pageChange"
            @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {getPageUnionManager} from "@/api/manager_withdrawal_profits";
import infoList from '@/mixins/infoList'

export default {
  mixins: [infoList],
  data() {
    return {
      listApi: getPageUnionManager,
      formData: {},
      anchorRecentData: {},
      searchInfo: {
        dst_id: null
      }
    }
  },
  async created() {
    let queryData = this.$route.query;
    if (queryData.userId) {
      this.searchInfo.union_manager = queryData.userId
    }
    if (queryData.startTime && queryData.endTime) {
      this.searchInfo.date_range = [queryData.startTime, queryData.endTime]
    }
    if (queryData.userId) {
      this.getTableData(this.nFunc, this.tableDataFormat)
      this.getManagerRecentData()
    }
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    handleDownload() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.getTableData(this.nFunc, this.tableDataFormat)
      this.getManagerRecentData()
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_ymd = this.formatDateYMD(item.created_at)
        data[i].created_at_hms = this.formatDateHMS(item.created_at)
        data[i].trade_type_str = this.formatTradeType(item.trade_type)
      })
    },
    getManagerRecentData() {

    },
  },
}
</script>

<style>
</style>

