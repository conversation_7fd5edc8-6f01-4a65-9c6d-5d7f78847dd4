<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchInfo.date_range"
            type="datetimerange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="用户ID">
          <el-input v-model="searchInfo.anchor_id" placeholder="请输入主播ID" />
        </el-form-item>
        <el-form-item label="收益类型" prop="is_subsidy">
          <el-select v-model="searchInfo.sub_type" clearable placeholder="请选择收益类型">
            <el-option v-for="item in subTypeMapList" :key="item.value" :label="`${item.label_zh}(${item.value})`" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="handleDownload">导出 </el-button>
          <el-button size="mini" type="success" icon="el-icon-add" @click="onAdd">增加</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box" style="margin: 10px 0; padding: 5px">
      <el-descriptions border :column="4">
        <el-descriptions-item :span="1" align="center" label="用户ID">{{ anchorRecentData.user_id }} </el-descriptions-item>
        <el-descriptions-item :span="1" align="center" label="用户昵称">{{ anchorRecentData.nickname }} </el-descriptions-item>
        <el-descriptions-item :span="1" align="center" label="工会">{{ formatUniosName(anchorRecentData.union_id) }} </el-descriptions-item>
        <el-descriptions-item :span="1" align="center" label="主播等级">{{ anchorRecentData.levels }} </el-descriptions-item>
        <el-descriptions-item :span="1" align="center" label="主播不可提现余额(宝石)">{{ anchorRecentData.un_diamonds }} </el-descriptions-item>
        <el-descriptions-item :span="1" align="center" label="主播本周收入(宝石)">{{ anchorRecentData.week_income }} </el-descriptions-item>
        <el-descriptions-item :span="1" align="center" label="主播可提现余额(宝石)">{{ anchorRecentData.diamonds }} </el-descriptions-item>
      </el-descriptions>
    </div>
    <div class="gva-table-box">
      <el-table border ref="tableData" style="width: 100%" tooltip-effect="dark" :data="tableData" row-key="id">
        <el-table-column align="center" label="日期" prop="created_at_str" width="180" />
        <el-table-column align="center" label="用户ID" prop="user_id" width="140" />
        <el-table-column align="center" label="主播ID" prop="anchor_id" width="140" />
        <el-table-column align="center" label="基础收益" prop="income" width="140" />
        <el-table-column align="center" label="奖励" prop="reward" width="140" />
        <el-table-column align="center" label="工会分成" prop="union_income" width="140" />
        <el-table-column align="center" label="备注信息" prop="extra_desc" show-overflow-tooltip min-width="140" />
        <el-table-column align="center" label="操作人" prop="sys_user_id" width="140">
          <template #default="scope">
            {{ scope.row.sys_user_id===0?'':userNameObj[scope.row.sys_user_id] }}
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>

    <el-dialog v-model="dialogFormVisible" top="5px" :before-close="closeDialog" title="增加流水">
      <el-form ref="formAddDataRef" :model="formAddData" :rules="formAddDataRules" label-position="right" label-width="120px">
        <el-form-item label="主播ID:" prop="dst_id">
          <el-input-number class="w100" v-model="formAddData.dst_id" />
        </el-form-item>
        <el-form-item label="收入:" prop="income">
          <el-input-number class="w100" v-model="formAddData.income" />
        </el-form-item>
        <el-form-item label="描述:" prop="extra_desc">
          <el-input v-model="formAddData.extra_desc" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button :loading="formAddDataLoading" size="small" type="primary" @click="enterAddDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { getAnchorTradeRecordsList } from '@/api/tradeRecords'
import { getAnchorRecentData, getAnchorRecentDataV2, addDiamonds } from '@/api/users'
import { getAnchorRewardLogsList } from '@/api/anchorRewardLogs'
import {  getUserNameList } from '@/api/userStrategyRd'
import {
  findIncomeRecords,
  getIncomeRecordsList,
  getSubTypeMapList,
  getStateMapList,
  getIncomeTypeMapList,
  createIncomeRecords,
  deleteIncomeRecords,
  deleteIncomeRecordsByIds,
  updateIncomeRecords,
} from '@/api/incomeRecords'
import infoList from '@/mixins/infoList'

export default {
  mixins: [infoList],
  data() {
    return {
      listApi: getAnchorRewardLogsList,
      formData: {},
      formAddData: {},
      dialogFormVisible: false,
      formAddDataLoading: false,
      formAddDataRules: {
        dst_id: [{ required: true, message: '请完善该字段.', trigger: 'blur' }],
        income: [{ required: true, message: '请完善该字段.', trigger: 'blur' }],
        extra_desc: [{ required: true, message: '请完善该字段.', trigger: 'blur' }],
      },
      anchorRecentData: {},
      subTypeMapList: [],
      stateMapList: [],
      subTypeMapDict: {},
      incomeTypeMapList: [],
      searchInfo: {
        anchor_id: null,
      },
      userNameObj: {}
    }
  },
  async created() {
    // 查询用户列表
    await this.getUserNameList()
    await this.pageInit()
  },

  methods: {
    // 查询用户列表
    getUserNameList() {
      getUserNameList({}).then((res) => {
        if (res.code === 0) {
          const obj = {}
          res.data.forEach((item) => {
            obj[item.id] = item.nick_name
          })
          this.userNameObj = obj
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formAddData = {}
    },
    enterAddDialog() {
      this.$refs.formAddDataRef.validate(async (valid) => {
        if (valid) {
          let tm13 = new Date().valueOf()
          this.formAddData = {
            ...this.formAddData,
            income_type: 1,
            sub_type: 5,
            dst_app_id: 27,
            state: 2,
            original_id: tm13,
          }
          this.formAddDataLoading = true
          let res = await addDiamonds(this.formAddData)
          this.formAddDataLoading = false
          if (res.code === 0) {
            this.$message.success('添加消费队列信息成功,具体是否成功请查询主播消费明细')
            this.closeDialog()
          }
        }
      })
    },
    onAdd() {
      this.dialogFormVisible = true
    },
    formatSubType(v) {
      return this.subTypeMapDict[v]
    },
    formatRecordState(v) {
      let i = this.stateMapList.find((item) => {
        return item.value === v
      })
      return i === undefined ? '' : i.label
    },
    formatIncomeType(v) {
      let i = this.incomeTypeMapList.find((item) => {
        return item.value === v
      })
      return i === undefined ? '' : i.label
    },
    async initStateMapList() {
      let res
      res = await getStateMapList()
      if (res.code === 0) {
        this.stateMapList = res.data || []
      }
    },
    async initSubTypeMapList() {
      let res
      res = await getSubTypeMapList()
      if (res.code === 0) {
        let resData = res.data || []
        let resDict = {}
        resData.forEach((item) => {
          resDict[item.value] = item.label_zh
        })
        this.subTypeMapList = resData
        this.subTypeMapDict = resDict
      }
    },
    async initIncomeTypeMapList() {
      let res
      res = await getIncomeTypeMapList()
      if (res.code === 0) {
        this.incomeTypeMapList = res.data || []
      }
    },
    async pageInit() {
      await this.initStateMapList()
      await this.initSubTypeMapList()
      await this.initIncomeTypeMapList()
      let queryData = this.$route.query
      if (queryData.userId) {
        this.searchInfo.anchor_id = queryData.userId
      }
      if (queryData.startTime && queryData.endTime) {
        this.searchInfo.date_range = [queryData.startTime, queryData.endTime]
      }
      if (queryData.userId) {
        this.getTableData(this.nFunc, this.tableDataFormat)
        this.getAnchorRecentData()
      }
    },
    onReset() {
      this.searchInfo = {}
    },
    handleDownload() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    // 条件搜索前端看此方法
    onSubmit() {
      if (!this.searchInfo.anchor_id) {
        this.$message.error('请输入主播ID')
        return
      }
      this.page = 1
      this.getTableData(this.nFunc, this.tableDataFormat)
      this.getAnchorRecentData()
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].sub_type_str = this.formatSubType(item.sub_type)
      })
    },
    getAnchorRecentData() {
      getAnchorRecentDataV2(this.searchInfo.anchor_id).then((res) => {
        if (res.code === 0) {
          this.anchorRecentData = res.data
        }
      })
    },
  },
}
</script>

<style></style>
