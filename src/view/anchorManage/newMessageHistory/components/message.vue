<template>
  <div>
    <div class="c-box-item" :class="{'c-box-left': direction === 'left','c-box-right': direction !== 'left'}">
      <div v-if="direction === 'left'" style="width: 40px; margin-right: 4px">
        <el-avatar :size="40" :src="userAvatar"/>
      </div>
      <div class="chat-content d-f">
        <div v-if="messageType === 1" class="chat-box">
          <div class="chat-msg">{{ msgContent }}</div>
          <div v-if="messageType === 11" class="chat-msg">
            <div class="flex-box a-c">
              <div class="flex-box f-c">
                <p class="program-title">{{originData.payload.program_content.program_name}}</p>
                <div class="flex-box a-c">
                  <img src="/src/assets/ic_coin.svg" style="width: 16px;height: 16px;">
                  <p class="program-price">{{originData.payload.program_content.program_price}}</p>
                </div>
              </div>
              <div style="margin-left: 13px">
                <img src="/src/assets/ic_program.svg" alt="" style="width: 30px;height: 30px;">
              </div>
            </div>
          </div>
        </div>
        <div v-if="messageType === 2" class="chat-img-box">
          <el-image style="width: 100px; height: 150px; border-radius: 8px" :src="originData.payload.media_url" fit="cover" :preview-src-list="[originData.payload.media_url]" />
        </div>
        <div v-if="messageType === 3">--语音消息--</div>
        <div v-if="messageType === 4" class="chat-box">--礼物消息--</div>
        <div v-if="messageType === 5">
          <img @click="playVideo" src="@/assets/video_preview.svg" alt="" style="width: 100px; height: 150px; cursor: pointer">
        </div>
        <div v-if="messageType === 6">--MissVideoCall--</div>
        <div v-if="messageType === 7">--带跳转链接--</div>
        <div v-if="messageType === 8">--带跳转链接的图文--</div>


        <div v-if="messageType === 11" class="chat-box">
          <div class="flex-box a-c">
            <div class="flex-box f-c">
              <p class="program-title">{{originData.payload.program_content.program_name}}</p>
              <div class="flex-box a-c">
                <img src="/src/assets/ic_coin.svg" style="width: 16px;height: 16px;">
                <p class="program-price">{{originData.payload.program_content.program_price}}</p>
              </div>
            </div>
            <div style="margin-left: 13px">
              <img src="/src/assets/ic_program.svg" alt="" style="width: 30px;height: 30px;">
            </div>
          </div>
        </div>
        <div class="send-time">{{ sendStr }}</div>
      </div>
      <div v-if="direction !== 'left'" style="width: 40px; margin-left: 4px">
        <el-avatar :size="40" :src="anchorAvatar"/>
      </div>
    </div>
  </div>
</template>

<script>
import moment from 'moment'

export default {
  name: "messageBox",
  props: {
    // 方向
    direction: {
      type: String,
      default: 'right'
    },
    // 消息类型 1、文字 2、图片 3、语音 4、礼物 5、视频 6、miss video 1000、更新income(不插入数据库) 8、Toast类信息
    messageType: {
      type: Number,
      default: 1
    },
    // 是否已读
    readType: {
      type: Number,
      default: 1
    },
    // 头像
    avatar: {
      type: String,
      default: 'https://s3.oklove.top/image/1659091869163963858.png'
    },
    msgContent: {
      type: String,
      // default: 'Where are you from? You are vrey beautiful! Received Pronunciation;',
      default: 'Where are you from? ',
    },
    sendTime: {
      type: Number,
      default: 1318781876406
    },
    originData: {
      type: Object,
      default: {}
    },
  },
  data() {
    return {
      sendStr: '',
      userAvatar: 'https://cube.elemecdn.com/3/7c/********************************.png',
      anchorAvatar: '',
    }
  },
  created() {

  },
  beforeMount() {
    this.sendStr = moment(this.sendTime).format("YYYY-MM-DD HH:mm:ss")
  },
  methods: {
    playVideo() {
      console.log(this.originData.payload.media_url)
    }
  }
}
</script>

<style lang="less" scoped>
.program-title{
  font-size: 16px;
  font-weight: 400;
  color: #333333;
  line-height: 18px;
  margin-bottom: 6px;
}
.program-price {
  font-size: 16px;
  font-weight: 400;
  color: #999999;
  line-height: 18px;
  margin-left: 6px;
}
</style>
