<template>
  <div>
    <el-button @click="onSubMit">刷新</el-button>
    <el-scrollbar height="700px" style="width: 450px; background-color: #F6F7F9;">
      <div style="display: flex; justify-content: center" v-if="msgList.length < total">
        <el-button @click="loadMoreMsg" type="text">查看更多</el-button>
      </div>
      <message-box
          v-for="(item, i) in msgList"
          :key="i"
          :direction="'left'"
          :message-type="item.payload.message_type"
          :msg-content="item.payload.message_content"
          :origin-data="item"
          :send-time="item.payload.time_stamp"
          avatar=""
          :read-type="item.read_type"
      ></message-box>
    </el-scrollbar>
    <!--<div style="width: 450px;height: 700px; background-color: #F6F7F9;"></div>-->
  </div>
</template>

<script>
import messageBox from "./components/message.vue";
import {getNewEsDetailMsgs} from "@/api/esMonitorMsgs";
import {getUserDetail} from "@/api/users";

export default {
  name: "index",
  components: {
    messageBox,
  },
  data() {
    return {
      msgList: [],
      anchorInfo: null,
      userInfo: null,
      id: 1232,
      page: 1,
      pageSize: 10,
      total: 0,
    }
  },
  beforeMount() {
    this.onSubMit()
  },

  methods: {
    loadMoreMsg() {
      this.page++
      this.onSearch()
    },
    onSubMit() {
      this.page = 1
      this.msgList = []
      this.onSearch()
    },
    onSearch() {
      getNewEsDetailMsgs({id: this.id, page: this.page, pageSize: this.pageSize}).then(res => {
        if (res.code === 0) {
          let resDataList = res.data.list || []
          this.msgList = resDataList.concat(this.msgList)
          this.total = res.data.total || 0
          if (this.msgList.length > 0) {
            // 查询两个用户的角色
            let srcId = this.msgList[0].src
            let dstId = this.msgList[0].dst
            if (this.userInfo === null || this.anchorInfo === null) {
              // 获取用户信息
              getUserDetail({id: srcId}).then(res=>{
                if (res.code === 0) {
                  let uInfo = res.data
                  if (uInfo.role === 1) {
                    this.userInfo = uInfo
                  } else if (uInfo.role === 2) {
                    this.anchorInfo = uInfo
                  }
                }
              })
              getUserDetail({id: dstId}).then(res=>{
                if (res.code === 0) {
                  let uInfo = res.data
                  if (uInfo.role === 1) {
                    this.userInfo = uInfo
                  } else if (uInfo.role === 2) {
                    this.anchorInfo = uInfo
                  }
                }
              })
            }

          }

        }
      })
    },
  },
}
</script>

<style scoped>

</style>
