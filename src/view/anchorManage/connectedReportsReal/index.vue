<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item v-if="unionIdBool" label="产品名称">
          <el-select v-model="searchInfo.app_id" filterable clearable>
            <el-option
                v-for="item in appList"
                :key="item.id"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="主播ID">
          <el-input v-model.number="searchInfo.dst_id" clearable placeholder="请输入主播ID"></el-input>
        </el-form-item>
        <el-form-item label="用户ID">
          <el-input v-model.number="searchInfo.src_id" clearable placeholder="请输入用户ID"></el-input>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="通话频道">
          <el-input v-model="searchInfo.project" clearable placeholder="请输入通话频道"></el-input>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="工会">
          <el-select v-model="searchInfo.dst_union_id" clearable filterable>
            <el-option
                v-for="item in uniosList"
                :key="item.id"
                :label="`${item.name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="通话类型">
          <el-select collapse-tags v-model="searchInfo.src_free" placeholder="通话类型">
            <el-option label="全部" :value="null"/>
            <el-option label="免费通话" :value="true"/>
            <el-option label="付费通话" :value="false"/>
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="呼叫类型">
          <el-select v-model="searchInfo.call_type" placeholder="通话类型">
            <el-option label="全部" :value="null"/>
            <el-option label="用户主叫" :value="1"/>
            <el-option label="主播主叫" :value="2"/>
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="时长">
          <el-select v-model="searchInfo.duration_range" placeholder="时长" clearable>
            <el-option label="10秒以下" :value="[0, 10]"/>
            <el-option label="10 - 30" :value="[10, 30]"/>
            <el-option label="30 - 60" :value="[30, 60]"/>
            <el-option label="60 - 120" :value="[60, 120]"/>
            <el-option label="120 - 300" :value="[120, 300]"/>
            <el-option label="300秒以上" :value="[300, 99999999]"/>
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="时间范围">
          <el-date-picker
              v-model="searchInfo.date_range"
              type="datetimerange"
              :shortcuts="dateRangeShortcuts"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DDTHH:mm:ssZ"
              clearable
              :default-time="[new Date(2022, 1, 1, 0, 0, 0),new Date(2022, 1, 1, 23, 59, 59)]"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="封禁状态">
          <el-select v-model.number="searchInfo.recovery" clearable filterable placeholder="封禁状态">
            <el-option
                v-for="item in recoveryStateOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="国籍">
          <el-select v-model="searchInfo.country_code" clearable filterable placeholder="国籍">
            <el-option
                v-for="item in countryCodeOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="视频等级">
          <el-select v-model="searchInfo.video_level" clearable filterable placeholder="等级">
            <el-option
                v-for="item in videoLevelList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="text" :icon="queryTxtIcon" @click="queryTxtChange">{{ queryTxt }}</el-button>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-row style="padding: 0" :gutter="10">
        <el-col
            style="margin-bottom: 15px"
            v-for="item in tableData"
            :key="item.id"
            :xs="24"
            :sm="24"
            :md="12"
            :lg="8"
            :xl="8"
        >
          <el-card :body-style="{padding: '10px'}">
            <vue3VideoPlay
                width="100%"
                height="300px"
                :src="item.video_url"
                type="m3u8"
                :autoPlay="false"
                :volume="1"
                :controlBtns="['audioTrack', 'volume', 'pageFullScreen', 'fullScreen']"
            />
            <el-descriptions
                style="margin-top: 5px"
                size="small"
                :column="3"
                border
            >
              <el-descriptions-item align="center" label="用户ID">
                 {{ item.src_id }}
              </el-descriptions-item>
              <el-descriptions-item align="center" label="主播ID">
                <span style="color: #0d84ff;cursor: pointer;" @click="toEditPage(item.dst_id)">{{ item.dst_id }}</span>
              </el-descriptions-item>
              <el-descriptions-item align="center" label="挂断方">{{ item.close_id }}</el-descriptions-item>
              <el-descriptions-item align="center" label="呼叫类型">
                <span v-if="item.call_type === 1">用户主叫</span>
                <span v-else-if="item.call_type === 2">主播主叫</span>
                <span v-else type="danger">未知</span>
              </el-descriptions-item>
              <el-descriptions-item align="center" label="用户昵称">{{ item.src_nick_name }}</el-descriptions-item>
              <el-descriptions-item align="center" label="主播昵称">{{ item.dst_nick_name }}</el-descriptions-item>
              <el-descriptions-item align="center" label="时长">{{ item.duration }}</el-descriptions-item>
              <el-descriptions-item align="center" label="通话类型">
                <span v-if="item.src_free">免费</span>
                <span v-else>付费</span>
              </el-descriptions-item>
              <!--
              <el-descriptions-item align="center" label="30天次均">
                {{ item.anchor_accept_30days_second }}
              </el-descriptions-item>
              -->
              <el-descriptions-item align="center" label="数据">
                <el-button @click="showAnchorDataDialog(item.dst_id)" type="primary">查看数据</el-button>
              </el-descriptions-item>
              <el-descriptions-item align="center" label="发起时间">
                {{ formatDate(item.begin_ts) }}
              </el-descriptions-item>
              <el-descriptions-item align="center" label="通知记录">
                <span style="color: #0d84ff;cursor: pointer;" @click="beforeOpen(2,item.dst_id)">{{ item.anchor_warnings }}</span>
              </el-descriptions-item>
              <el-descriptions-item align="center" label="通知">
                <el-button type="primary" size="small" @click="beforeOpen(1,item.dst_id)">发送</el-button>
              </el-descriptions-item>

            </el-descriptions>
          </el-card>
        </el-col>
      </el-row>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="[6, 12, 24, 48]"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- <el-dialog
        v-model="anchorDataDialogVisible"
        :title="`主播ID:${anchorId}`"
        width="60%"
    >
      <el-row :gutter="10">
        <el-col :span="4"><h1 class="title-2">昨日</h1></el-col>
        <el-col :span="4">
          <div class="d-f">
            <p>活跃数据</p>
            <p>在线时长：{{formateSeconds(anchorRecentData.lastDayData.online_durations)}}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="d-f">
            <p>通话数据-用户直呼</p>
            <p>呼叫次数：{{anchorRecentData.lastDayData.c1_calls}}</p>
            <p>接听次数：{{anchorRecentData.lastDayData.c1_answer}}</p>
            <p>呼叫用户数：{{anchorRecentData.lastDayData.c1_call_users}}</p>
            <p>接听用户数：{{anchorRecentData.lastDayData.c1_call_answer_users}}</p>
            <p>通话时长：{{anchorRecentData.lastDayData.c1_call_duration}}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="d-f">
            <p>通话数据-主播直呼</p>
            <p>呼叫次数：{{anchorRecentData.lastDayData.c2_calls}}</p>
            <p>接听次数：{{anchorRecentData.lastDayData.c2_answer}}</p>
            <p>呼叫用户数：{{anchorRecentData.lastDayData.c2_call_users}}</p>
            <p>接听用户数：{{anchorRecentData.lastDayData.c2_call_answer_users}}</p>
            <p>通话时长：{{anchorRecentData.lastDayData.c2_call_duration}}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="d-f">
            <p>礼物</p>
            <p>收礼次数：{{anchorRecentData.lastDayData.gift_count}}</p>
            <p>收礼人数：{{anchorRecentData.lastDayData.gift_user_count}}</p>
            <p>礼物宝石收益：{{anchorRecentData.lastDayData.gifts_earned}}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="d-f">
            <p>聊天收益</p>
            <p>收益聊天次数：{{anchorRecentData.lastDayData.gift_msg_count}}</p>
            <p>收益聊天用户数：{{anchorRecentData.lastDayData.gift_msg_user_count}}</p>
            <p>聊天宝石收益：{{anchorRecentData.lastDayData.message_earned}}</p>
          </div>
        </el-col>

      </el-row>
      <el-row :gutter="10">
        <el-col :span="4"><h1 class="title-2">过去7日</h1></el-col>
        <el-col :span="4">
          <div class="d-f">
            <p>活跃数据</p>
            <p>在线时长：{{formateSeconds(anchorRecentData.last7DayData.online_durations)}}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="d-f">
            <p>通话数据-用户直呼</p>
            <p>呼叫次数：{{anchorRecentData.last7DayData.c1_calls}}</p>
            <p>接听次数：{{anchorRecentData.last7DayData.c1_answer}}</p>
            <p>呼叫用户数：{{anchorRecentData.last7DayData.c1_call_users}}</p>
            <p>接听用户数：{{anchorRecentData.last7DayData.c1_call_answer_users}}</p>
            <p>通话时长：{{anchorRecentData.last7DayData.c1_call_duration}}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="d-f">
            <p>通话数据-主播直呼</p>
            <p>呼叫次数：{{anchorRecentData.last7DayData.c2_calls}}</p>
            <p>接听次数：{{anchorRecentData.last7DayData.c2_answer}}</p>
            <p>呼叫用户数：{{anchorRecentData.last7DayData.c2_call_users}}</p>
            <p>接听用户数：{{anchorRecentData.last7DayData.c2_call_answer_users}}</p>
            <p>通话时长：{{anchorRecentData.last7DayData.c2_call_duration}}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="d-f">
            <p>礼物</p>
            <p>收礼次数：{{anchorRecentData.last7DayData.gift_count}}</p>
            <p>收礼人数：{{anchorRecentData.last7DayData.gift_user_count}}</p>
            <p>礼物宝石收益：{{anchorRecentData.last7DayData.gifts_earned}}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="d-f">
            <p>聊天收益</p>
            <p>收益聊天次数：{{anchorRecentData.last7DayData.gift_msg_count}}</p>
            <p>收益聊天用户数：{{anchorRecentData.last7DayData.gift_msg_user_count}}</p>
            <p>聊天宝石收益：{{anchorRecentData.last7DayData.message_earned}}</p>
          </div>
        </el-col>

      </el-row>
      <el-row :gutter="10">
        <el-col :span="4"><h1 class="title-2">过去30日</h1></el-col>
        <el-col :span="4">
          <div class="d-f">
            <p>活跃数据</p>
            <p>在线时长：{{formateSeconds(anchorRecentData.last30DayData.online_durations)}}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="d-f">
            <p>通话数据-用户直呼</p>
            <p>呼叫次数：{{anchorRecentData.last30DayData.c1_calls}}</p>
            <p>接听次数：{{anchorRecentData.last30DayData.c1_answer}}</p>
            <p>呼叫用户数：{{anchorRecentData.last30DayData.c1_call_users}}</p>
            <p>接听用户数：{{anchorRecentData.last30DayData.c1_call_answer_users}}</p>
            <p>通话时长：{{anchorRecentData.last30DayData.c1_call_duration}}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="d-f">
            <p>通话数据-主播直呼</p>
            <p>呼叫次数：{{anchorRecentData.last30DayData.c2_calls}}</p>
            <p>接听次数：{{anchorRecentData.last30DayData.c2_answer}}</p>
            <p>呼叫用户数：{{anchorRecentData.last30DayData.c2_call_users}}</p>
            <p>接听用户数：{{anchorRecentData.last30DayData.c2_call_answer_users}}</p>
            <p>通话时长：{{anchorRecentData.last30DayData.c2_call_duration}}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="d-f">
            <p>礼物</p>
            <p>收礼次数：{{anchorRecentData.last30DayData.gift_count}}</p>
            <p>收礼人数：{{anchorRecentData.last30DayData.gift_user_count}}</p>
            <p>礼物宝石收益：{{anchorRecentData.last30DayData.gifts_earned}}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="d-f">
            <p>聊天收益</p>
            <p>收益聊天次数：{{anchorRecentData.last30DayData.gift_msg_count}}</p>
            <p>收益聊天用户数：{{anchorRecentData.last30DayData.gift_msg_user_count}}</p>
            <p>聊天宝石收益：{{anchorRecentData.last30DayData.message_earned}}</p>
          </div>
        </el-col>
      </el-row>
    </el-dialog> -->

    <!-- 新增通知抽屉 -->
    <el-dialog
      v-model="warnDrawer"
      title="通知信息"
    >

    <el-form label-width="120px">
        <el-form-item label="主播ID">
          <el-input v-model.number="warnInfo.anchor_id" clearable placeholder="请输入主播ID"></el-input>
        </el-form-item>
        <el-form-item label="奖罚金额">
          <el-input v-model.number="warnInfo.income" clearable placeholder="请输入扣减金额"></el-input>
        </el-form-item>
        <el-form-item label="原因">
          <el-input v-model="warnInfo.Reason" clearable placeholder="请输入原因" :rows="5" type="textarea"></el-input>
        </el-form-item>
      </el-form>

      <template #footer>
      <span class="dialog-footer">
        <el-button type="primary" @click="addWarnItem">
        确认
        </el-button>
      </span>
    </template>
    </el-dialog>

    <!-- 通知记录抽屉 -->
    <el-dialog
      v-model="warnLogDrawer"
      title="通知记录"
    >
    <el-table :data="warnLogtableData" style="width: 100%">
        <el-table-column label="日期" width="220" show-overflow-tooltip>
          <template #default="{row}">
            {{moment(row.created_at).format("YYYY-MM-DD hh:mm:ss")}}
          </template>
        </el-table-column>
        <el-table-column prop="anchor_id" label="主播ID" width="180" />
        <el-table-column prop="income" label="奖罚金额" width="100"/>
        <el-table-column prop="Reason" label="原因" />
      </el-table>
    </el-dialog>

    <!-- 个人信息编辑页面 -->
    <user-info-update-dialog ref="userInfoUpdateDialogRef" :user_id="userInfoUpdateId"></user-info-update-dialog>

    <!-- 数据详情页 -->
    <anchor-data-dialog ref="anchorDataDialogRef" :dst_id="anchorDstId" ></anchor-data-dialog>
  </div>
</template>

<script>
import {addWarningInfos, getRealList, getWarningInfos} from '@/api/connectedReports' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import moment from 'moment'
import {ElMessage} from 'element-plus'


export default {
  mixins: [infoList],
  data() {
    return {
      unionIdBool: null,
      moment:moment,
      searchInfo: {
        recovery:0
      },
      listApi: getRealList,
      warnDrawer:false,
      warnLogDrawer:false,
      anchorDataDialogVisible: false,
      anchorId: null,
      anchorRecentData: {},
      warnLogtableData:[],
      userInfoUpdateId: 0,
      anchorDstId:0,
      warnInfo:{
        anchor_id:'',
        income:0,
        Reason:''
      },
      videoLevelList:[
        {label:'1,2级',value:1},
        {label:'4级',value:2},
      ]
    }
  },
  async created() {
    this.unionIdBool = this.userInfo.union_id === 0
    this.pageSize = 6
    await this.getTableData()
  },
  methods: {
    //添加通知信息
    addWarnItem(){
      addWarningInfos({
        "anchor_id":this.warnInfo.anchor_id,
        "income":this.warnInfo.income,
        "reason":this.warnInfo.Reason
      }).then(res => {
        if(res.code === 0){
          ElMessage.success('已添加通知')
          this.warnDrawer = false
          this.getTableData()
        }

      })
    },
    toEditPage(id){
      this.userInfoUpdateId = id
      this.$refs.userInfoUpdateDialogRef.dialogVisible = true
    },
    handleClose(num){
      if(num === 1){
        this.warnDrawer = false
      }else{
        this.warnLogDrawer = false
      }
    },
    beforeOpen(num,id){
      if(num === 1){
        this.warnInfo = {
          anchor_id:'',
          income:0,
          Reason:''
        }
        this.warnInfo.anchor_id = id
        this.warnDrawer = true
      }else{
        this.warnLogDrawer = true
        getWarningInfos({
          anchor_id:id
        }).then(res => {
          if(res.code === 0){
           this.warnLogtableData = res.data
          }
        })

      }
    },
    showAnchorDataDialog(dst_id) {
      this.anchorDstId = dst_id
      this.$refs.anchorDataDialogRef.dialogVisible = true
      // getRecentAnchorStaticData({id: dst_id}).then(res=>{
      //   if (res.code === 0) {
      //     this.anchorId = dst_id
      //     this.anchorDataDialogVisible = true
      //     this.anchorRecentData = res.data
      //     console.log(this.anchorRecentData)
      //   }
      // })
    },

    onReset() {
      this.searchInfo = {}
      this.pageSize = 6
      this.getTableData()
    },
    onSubmit() {
      this.page = 1
      this.pageSize = 6
      this.getTableData()
    },
  },
}
</script>

<style lang="scss">
.d-f > p {
  font-size: 16px;
  margin-bottom: 4px;
}

.el-descriptions__cell {
  padding: 0 !important;
}

.el-descriptions__label {
  width: 70px !important;
}

.el-descriptions__content {
  border-top: none !important;
  border-bottom: none !important;
}
</style>
