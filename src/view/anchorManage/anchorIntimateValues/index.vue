<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="用户ID">
          <el-input v-model="searchInfo.src_id" placeholder="请输入用户ID"/>
        </el-form-item>
        <el-form-item label="主播ID">
          <el-input v-model="searchInfo.dst_id" placeholder="请输入主播ID"/>
        </el-form-item>
        <el-form-item label="主播等级">
          <el-select v-model="searchInfo.levels" clearable filterable placeholder="主播等级">
            <el-option
                v-for="item in levelsOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="亲密值等级">
          <el-select v-model="searchInfo.intimate_levels" placeholder="亲密值等级">
            <el-option
                v-for="item in intimateLevelsOptions"
                :key="item.value"
                :label="item.value !== -1 ? `${item.label}(${item.value})`: item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工会">
          <el-select v-model="searchInfo.union_id" clearable filterable placeholder="工会">
            <el-option
                v-for="item in uniosList"
                :label="`${item.name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否悬赏">
          <el-select v-model="searchInfo.is_state" clearable>
            <el-option label="已悬赏" :value="1"/>
            <el-option label="未悬赏" :value="2"/>
          </el-select>
        </el-form-item>
        <el-form-item label="悬赏状态">
          <el-select v-model="searchInfo.state" clearable>
            <el-option label="未领取" :value="200"/>
            <el-option label="进行中" :value="3"/>
            <el-option label="已完成" :value="4"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出
          </el-button>
          <!--<el-button size="mini" type="success" icon="el-icon-plus" @click="openDialog">新增</el-button>-->
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table border ref="tableData" style="width: 100%" :data="tableData" @sort-change="tableSort" row-key="id" :max-height="590">
        <el-table-column align="center" label="主播ID" prop="dst_id" min-width="120"/>
        <el-table-column align="center" label="主播等级" prop="levels" min-width="120" sortable="custom"/>
        <el-table-column align="center" label="用户ID" prop="src_id" min-width="120"/>
        <el-table-column align="center" label="总收益" prop="total_income" min-width="120"/>
        <!--<el-table-column align="center" label="总消耗" prop="all_consume" min-width="120"/>-->
        <el-table-column align="center" label="亲密值" prop="total_income" min-width="120"/>
        <el-table-column align="center" label="亲密值等级" prop="intimate_levels" min-width="120"/>
        <el-table-column align="center" label="累计亲密值奖励" prop="intimate_reward" min-width="120"/>
        <el-table-column align="center" label="是否已悬赏" prop="state_b_str" min-width="160">
          <template #default="scope">
            <el-tag v-if="scope.row.state" type="success">已悬赏</el-tag>
            <el-tag v-else type="danger">未悬赏</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="悬赏状态" prop="state_str" min-width="160"></el-table-column>
        <el-table-column align="center" label="开始时间" prop="start_time_str" min-width="160"/>
        <el-table-column align="center" label="最后时间" prop="last_time_str" min-width="160"/>
        <el-table-column align="center" label="断联天数" prop="lost_day" min-width="120"/>
        <el-table-column align="center" label="消息收益" prop="message_income" min-width="120" sortable="custom"/>
        <el-table-column align="center" label="用户消息条数" prop="src_msg_send_count" min-width="120"/>
        <el-table-column align="center" label="视频收益" prop="call_income" min-width="120" sortable="custom"/>
        <!--<el-table-column align="center" label="≥30秒通话次数" prop="call_count" min-width="120"/>-->
        <el-table-column align="center" label="通话次数" prop="call_count" min-width="120" sortable="custom"/>
        <el-table-column align="center" label="礼物收益" prop="gift_income" min-width="120" sortable="custom"/>
        <el-table-column align="center" label="礼物次数" prop="gift_count" min-width="120" sortable="custom"/>
        <el-table-column align="center" label="节目收益" prop="program_income" min-width="120" sortable="custom"/>
        <el-table-column align="center" label="节目次数" prop="program_count" min-width="120" sortable="custom"/>
        <el-table-column align="center" label="操作" fixed="right" min-width="180">
          <template #default="scope">
            <el-button :disabled="scope.row.state !== null" type="text" icon="el-icon-s-goods" size="small" @click="addRecommend(scope.row)">悬赏</el-button>
            <el-button type="text" icon="el-icon-edit" size="small" @click="updateAnchorIntimateValues(scope.row)">修改</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            :total="total"
            @current-change="pageChange"
            @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" top="5px" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" :rules="formDataRules" label-position="right" label-width="120px">
        <el-form-item label="用户ID:" prop="src_id">
          <el-input-number style="width: 100%;" v-model="formData.src_id" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="主播ID:" prop="dst_id">
          <el-input-number style="width: 100%;" v-model="formData.dst_id" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="消息收入:" prop="message_income">
          <el-input-number style="width: 100%;" v-model="formData.message_income" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="消息次数:" prop="message_count">
          <el-input-number disabled style="width: 100%;" v-model="formData.message_count" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="通话收入:" prop="call_income">
          <el-input-number style="width: 100%;" v-model="formData.call_income" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="通话次数:" prop="call_count">
          <el-input-number disabled style="width: 100%;" v-model="formData.call_count" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="礼物收入:" prop="gift_income">
          <el-input-number style="width: 100%;" v-model="formData.gift_income" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="礼物次数:" prop="gift_count">
          <el-input-number disabled style="width: 100%;" v-model="formData.gift_count" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="亲密值等级:" prop="intimate_levels">
          <el-input v-model.number="formData.intimate_levels" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="节目单收入:" prop="program_income">
          <el-input-number style="width: 100%;" v-model="formData.program_income" clearable placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="节目单次数:" prop="program_count">
          <el-input-number disabled style="width: 100%;" v-model="formData.program_count" clearable placeholder="请输入"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import {
  createAnchorIntimateValues,
  deleteAnchorIntimateValues,
  deleteAnchorIntimateValuesByIds,
  updateAnchorIntimateValues,
  findAnchorIntimateValues,
  getAnchorIntimateValuesList,
  getAnchorIntimateValuesListData
} from '@/api/anchorIntimateValues' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import {createRicherRecommends} from "@/api/richer_recommends";

export default {
  name: 'AnchorIntimateValues',
  mixins: [infoList],
  data() {
    return {
      listApi: getAnchorIntimateValuesListData,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        src_id: null,
        dst_id: null,
        message_count: null,
        call_count: null,
        start_time: null,
        last_time: null,
        interval_time: null,
        app_id: null,
        created_time: null,
        updated_time: null,
        intimate_levels: null,
      },
      formDataRules: {
        src_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        dst_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
    }
  },
  created() {
    this.searchInfo.intimate_levels = -1
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    tableSort({column, prop, order}) {
      if (prop !== null && order !== null) {
        if (prop === "levels") {
          prop = "u.levels"
        } else {
          prop = `a.${prop}`
        }

        if (order === 'descending') {
          this.searchInfo.sort_str = prop + " DESC"
        } else {
          this.searchInfo.sort_str = prop + " ASC"
        }
      } else {
        this.searchInfo.sort_str = null
      }
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    onReset() {
      this.searchInfo = {}
      this.searchInfo.intimate_levels = -1
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteAnchorIntimateValues(row)
      })
    },
    addRecommend(row) {
      this.$confirm('确定要悬赏吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        createRicherRecommends({
          intimate_id: row.id,
          anchor_id: row.dst_id,
          user_id: row.src_id,
          state: 1,
        }).then(res=>{
          if (res.code === 0) {
            this.$message({
              type: 'success',
              message: '成功'
            })
            this.getTableData(this.nFunc, this.tableDataFormat)
          }
        })
      })
    },
    async updateAnchorIntimateValues(row) {
      const res = await findAnchorIntimateValues({id: row.id})
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data
        this.formData.message_income = parseFloat(this.formData.message_income)
        this.formData.call_income = parseFloat(this.formData.call_income)
        this.formData.gift_income = parseFloat(this.formData.gift_income)
        this.formData.program_income = parseFloat(this.formData.program_income)

        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {}
    },
    async deleteAnchorIntimateValues(row) {
      const res = await deleteAnchorIntimateValues({id: row.id})
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createAnchorIntimateValues(this.formData)
          break
        case 'update':
          res = await updateAnchorIntimateValues(this.formData)
          break
        default:
          res = await createAnchorIntimateValues(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '成功'
        })
        this.closeDialog()
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      let nowUnix = moment().unix()
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].start_time_str = this.formatTime10Stamp(item.start_time)
        data[i].last_time_str = this.formatTime10Stamp(item.last_time)
        if (item.state) {
          data[i].state_b_str = "已悬赏"
          let stateStr = ""
          switch (item.state) {
            case 1:
              stateStr = "未领取"
              break
            case 2:
              stateStr = "未领取"
              break
            case 3:
              stateStr = "进行中"
              break
            case 4:
              stateStr = "已完成"
              break
            default:
              stateStr="未知"
          }
          data[i].state_str = stateStr
        } else {
          data[i].state_b_str = "未悬赏"
        }
        if (item.last_time === 0) {
          data[i].lost_day = "-"
        } else {
          let res = Math.floor((nowUnix - item.last_time) / 86400)
          if (res !== 0) {
            data[i].lost_day = res
          }
        }
      })
    },
  },
}
</script>

<style>
</style>
