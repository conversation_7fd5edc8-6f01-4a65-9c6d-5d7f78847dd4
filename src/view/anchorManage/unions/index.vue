<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="工会ID">
          <el-input v-model.number="searchInfo.id" placeholder="工会ID"/>
        </el-form-item>
        <el-form-item label="工会长ID">
          <el-input v-model.number="searchInfo.manager_id" placeholder="工会长ID"/>
        </el-form-item>
        <el-form-item label="工会名称">
          <el-input v-model="searchInfo.name" placeholder="工会名称"/>
        </el-form-item>

        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
      >

        <el-table-column align="center" label="主键ID" prop="id" min-width="100" fixed="left" :sortable="true"/>
        <el-table-column align="center" label="主播数" prop="total" min-width="100" :sortable="true"/>
        <el-table-column align="center" label="在线数" prop="online" min-width="100" :sortable="true"/>
        <el-table-column align="center" label="本周新增" prop="week_new" min-width="100" :sortable="true"/>
        <el-table-column align="center" label="上周新增" prop="last_week_new" min-width="100" :sortable="true"/>
        <el-table-column align="center" label="日期" min-width="160">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="工会名称" prop="name" min-width="180" show-overflow-tooltip/>
        <el-table-column align="center" label="所属工会ID" prop="parent_union_id" min-width="120" show-overflow-tooltip/>
        <el-table-column align="center" label="工会长ID" prop="manager_id" min-width="100" show-overflow-tooltip/>
        <el-table-column align="center" label="工会长手机号" prop="phone_no" min-width="150" show-overflow-tooltip/>
        <el-table-column align="center" label="管理员ID" prop="operate_id" min-width="100" show-overflow-tooltip/>
        <el-table-column align="center" label="自主提现" prop="user_withdrawal" min-width="100">
          <template #default="scope">
            <el-tag :type="scope.row.user_withdrawal === 1 ? 'success': 'info'">{{scope.row.user_withdrawal === 1 ? '开': '关'}}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="渠道显示" prop="channel_display" min-width="100">
          <template #default="scope">
            <el-tag :type="scope.row.channel_display === 1 ? 'success': 'info'">{{scope.row.channel_display === 1 ? '开': '关'}}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" prop="state" min-width="100" :sortable="true">
          <template #default="scope">
            <el-switch
              v-model="scope.row.state"
              :active-value="1"
              :inactive-value="2"
              @change="stateChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="邀请码" prop="invitation_code" min-width="120"/>
        <el-table-column align="center" label="操作" min-width="180" fixed="right">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button"
                       @click="updateUnions(scope.row)">修改
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog top="5px" v-model="dialogFormVisible" :before-close="closeDialog" :title="dialogTitle">
      <el-form :model="formData" label-position="right" label-width="80px">
        <el-form-item label="工会名称:">
          <el-input v-model="formData.name" clearable placeholder="请输入工会名称"/>
        </el-form-item>
        <el-form-item label="工会长ID">
          <el-row v-show="type === 'update'">
            <el-col :span="20">
              <el-input :disabled="managerInputDis" @clear="managerIdClear" v-model.number="formData.manager_id"
                        clearable placeholder="请输入用户ID"/>
            </el-col>
            <el-col :span="4">
              <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="managerIdClear">
                删除并修改
              </el-button>
            </el-col>
          </el-row>
          <el-input-number v-show="type === 'create'" style="width: 100%;" v-model="formData.manager_id" clearable
                           placeholder="请输入用户ID"/>

        </el-form-item>
        <el-form-item label="管理员ID">
          <el-input v-model.number="formData.operate_id" clearable placeholder="请输入管理员ID"/>
        </el-form-item>
        <el-form-item label="状态" style="width:50%">
          <el-select v-model.number="formData.state" clearable filterable placeholder="状态">
            <el-option
              v-for="item in stateOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="邀请码">
          <el-input v-model="formData.invitation_code" maxlength="6" show-word-limit clearable
                    placeholder="请输入邀请码"/>
        </el-form-item>
        <el-form-item label="是否主播">
          <el-switch v-model="formData.is_anchor" :active-value="1" :inactive-value="0" />
        </el-form-item>
        <el-form-item label="工会余额">
          <el-input-number v-model="formData.balance" />
        </el-form-item>
        <el-form-item label="提成比例">
          <el-input-number v-model="formData.ratio" />
        </el-form-item>
        <el-form-item label="子工会分成比例" label-width="120px">
          <el-input-number v-model="formData.child_ratio" />
        </el-form-item>
        <el-form-item label="渠道显示">
          <el-switch v-model="formData.channel_display" :active-value="1" :inactive-value="-1" />
        </el-form-item>
        <el-form-item label="自主提现">
          <el-switch v-model="formData.user_withdrawal" :active-value="1" :inactive-value="-1" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createUnions,
  deleteUnions,
  deleteUnionsByIds,
  deleteUnionManagerId,
  updateUnions,
  findUnions,
  getUnionsList
} from '@/api/unions' //  此处请自行替换地址
import infoList from '@/mixins/infoList'

const stateOptions = [
  {
    value: 1,
    label: '正常',
  },
  {
    value: 2,
    label: '封禁',
  },
]

export default {
  name: 'Unions',
  mixins: [infoList],
  data() {
    return {
      listApi: getUnionsList,
      stateOptions: stateOptions,
      dialogFormVisible: false,
      dialogTitle: '',
      managerInputDis: true,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        name: '',
        operate_id: null,
        manager_id: null,
        state: null,
        invitation_code: '',
      }
    }
  },
  async created() {
    this.page = 1
    this.pageSize = 50
    await this.getTableData()
  },
  methods: {
    async stateChange(row) {
      await updateUnions(row)
    },
    async userWithdrawalChange(row) {
      await updateUnions(row)
    },
    async channelDisplayChange(row) {
      await updateUnions(row)
    },
    managerIdClear() {
      deleteUnionManagerId({id: this.formData.id}).then(res => {
        if (res.code === 0) {
          this.$message.success("删除工会长成功")
          this.managerInputDis = false
          this.formData.manager_id = null
          // this.dialogFormVisible = false
          // this.getTableData()
        }
      })
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 1000
      this.getTableData()
    },
    async updateUnions(row) {
      const res = await findUnions({id: row.id})
      this.type = 'update'
      this.dialogTitle = '更新工会'
      if (res.code === 0) {
        this.formData = res.data.reunions
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.getTableData()
      this.dialogFormVisible = false
      this.managerInputDis = true
      this.formData = {
        name: '',
        operate_id: null,
        manager_id: null,
        state: null,
        invitation_code: '',
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createUnions(this.formData)
          break
        case 'update':
          res = await updateUnions(this.formData)
          break
        default:
          res = await createUnions(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogTitle = '创建工会'
      this.formData = {
        name: '',
        operate_id: null,
        manager_id: null,
        state: null,
        invitation_code: '',
      }
      this.dialogFormVisible = true
    }
  },
}
</script>

<style>
</style>
