<template>
  <div>
    <div class="gva-table-box" style="margin: 30px 0;padding: 40px;">
      <el-form :model="formData" :rules="rules" label-width="80px" label-position="left">
        <el-row :gutter="24">
          <div class="col-item" style="width: 80px;color: #606266;">发送给</div>
          <el-col :xs="12" :sm="12" :md="12" :lg="4" :xl="4" class="col-item">
            <el-select v-model="formData.type">
              <el-option v-for="item in achortList" :key="item.id" :label="item.name" :value="item.id"></el-option>
            </el-select>
          </el-col>
          <el-col :xs="12" :sm="12" :md="12" :lg="12" :xl="12" class="col-item" v-if="formData.type == 1">
            <el-tooltip class="box-item" effect="dark" content="多个主播请使用英文,隔开" placement="top">
              <i class="el-icon-warning" style="color: #4D70FF;margin-right: 6px;" />
            </el-tooltip>
            <el-input type="textarea" v-model="formData.notify_ids" placeholder="多个主播请使用英文,隔开" :rows="1"></el-input>
          </el-col>
        </el-row>

        <el-form-item label="标题" style="width: 70%;">
          <el-input v-model="formData.title" placeholder="请输入标题内容" />
        </el-form-item>

        <el-form-item label="正文" style="width: 70%;">
          <el-input v-model="formData.content" type="textarea" placeholder="请输入正文内容" :rows="4" />
        </el-form-item>

        <el-form-item label="图片" style="width: 70%;">
          <el-upload
            ref="handleImage"
            list-type="picture-card"
            :action="`${path}/files/createFiles`"
            :headers="{ 'x-token': token }"
            v-model:fileList="fileList"
            :on-success="onSuccess"
            :on-preview="handleImagePreview"
            :on-exceed="onExceed"
            :limit="1"
            accept=".jpg, .png"
            :on-remove="handleImageRemove"
          >
            <i class="el-icon-plus" />

            <template #tip>
              <div class="el-upload__tip text-red">
                请上传 jpg/png 格式
              </div>
            </template>
          </el-upload>
        </el-form-item>

        <el-form-item style="margin-top: 40px;">
          <el-button type="primary" @click="submit">
            确认
          </el-button>
        </el-form-item>
      </el-form>

      <el-dialog v-model="previewImageVisible">
        <img style="width: 100%;" :src="previewImagePath" alt="Preview Image" />
      </el-dialog>
    </div>
  </div>
</template>

<script>
import { sendOfficialNotice } from '@/api/officialNotice'
import { debounce } from 'lodash-es'
import { mapGetters } from 'vuex'

const path = import.meta.env.VITE_BASE_API

export default {
  name: 'OfficialNotice',

  computed: {
    ...mapGetters('user', ['token']),
  },

  data() {
    return {
      fileList: [],
      type: '',
      multipleSelection: [],
      formData: {
        type: 1,
        notify_ids: '',
        title: '',
        content: '',
        img_url: '',
      },
      previewImageVisible: false,
      previewImagePath: '',
      path,
      rules: {
        title: [{ required: true, message: '请输入标题', trigger: 'blur' }],
        content: [{ required: true, message: '请输入正文', trigger: 'blur' }],
      },

      achortList: [
        {
          id: 1,
          name: '指定主播',
        },
        {
          id: 2,
          name: '全部主播',
        },
      ],
    }
  },
  created() {},
  methods: {
    async onSuccess(response, file, fileList) {
      this.formData.img_url = response.data.key
    },

    onExceed() {
      this.$message.error('最多只能上传一张图片')
    },
    handleImagePreview(file) {
      this.previewImagePath = file.key
      this.previewImageVisible = true
    },
    handleImageChange(val) {
      console.log(val)
      this.formData.fileList = [val]
    },
    handleImageSuccess(val) {
      console.log(val)
    },
    handleImageRemove(val) {
      this.formData.img_url = ''
    },

    resetForm() {
      this.formData = {
        type: 1,
        notify_ids: '',
        title: '',
        content: '',
        img_url: '',
      }

      this.fileList = []
    },

    submit: debounce(async function() {
      let notify_ids = []
      // 使用正则表达式验证 notify_ids 格式
      if (this.formData.type == 1) {
        const notifyIdsPattern = /^(\d+,)*\d+$/
        if (!notifyIdsPattern.test(this.formData.notify_ids)) {
          this.$message.error('请输入正确的主播id')
          return
        }

        notify_ids = this.formData.notify_ids.split(',').map(Number)

        //去重
        notify_ids = [...new Set(notify_ids)]
      }

      console.log(notify_ids);

      if (!this.formData.title && !this.formData.content && !this.formData.img_url) {
        this.$message.error('请输入标题或正文或图片')
        return
      }

      let param = {
        notify_ids,
        img_url: this.formData.img_url,
        title: this.formData.title,
        content: this.formData.content,
      }


      const table = await sendOfficialNotice(param)
      if (table.code === 0) {
        this.resetForm()
        this.$message({
          type: 'success',
          message: '发送成功',
        })
      }
    }, 500),
  },
}
</script>

<style scoped lang="scss">
.col-item {
  display: flex;
  align-items: center;
}

.text-red {
  color: red;
}
</style>
