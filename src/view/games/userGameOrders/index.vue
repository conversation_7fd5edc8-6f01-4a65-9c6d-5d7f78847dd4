<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="时间">
          <el-date-picker
            v-model="searchInfo.date_range"
            type="daterange"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            :shortcuts="dateRangeShortcuts"
          />
        </el-form-item>
        <el-form-item label="用户id">
          <el-input v-model="searchInfo.user_id" placeholder="请输入用户id" clearable />
        </el-form-item>
        <el-form-item label="消耗类型">
          <el-select v-model="searchInfo.coins_type" placeholder="请选择" clearable>
            <el-option
              v-for="item in coinsTypeOptions"
              :key="item"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="游戏">
          <el-select v-model="searchInfo.game_id" placeholder="请选择" clearable>
            <el-option
              v-for="item in gameOptions"
              :key="item"
              :label="`${item.name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <!--<el-button size="mini" type="success" icon="el-icon-plus" @click="openDialog">新增</el-button>-->
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table border ref="tableData" style="width: 100%" :data="tableData" row-key="id" :max-height="590">
        <el-table-column align="center" label="日期" prop="created_at_str" min-width="180"/>
        <!--<el-table-column align="center" label="app_id" prop="app_id" min-width="120" />-->
        <el-table-column align="center" label="用户id" prop="user_id" min-width="120" />
        <!--<el-table-column align="center" label="用户token" prop="token" min-width="120" />-->
        <!--<el-table-column align="center" label="交易id" prop="transaction_id" min-width="120" />-->
        <el-table-column align="center" label="游戏" prop="game_id" min-width="120">
            <template #default="scope">
              <div>{{formatGameName(scope.row.game_id)}}</div>
            </template>
        </el-table-column>
        <el-table-column align="center" label="消耗类型" prop="coins_type_str" min-width="120" />
        <el-table-column align="center" label="金币数量" prop="coins" min-width="120" />
        <!--<el-table-column align="center" label="房间id" prop="room_id" min-width="120" />-->
        <!--<el-table-column align="center" label="操作" min-width="180">-->
        <!--  <template #default="scope">-->
        <!--    <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateUserGameOrders(scope.row)">修改</el-button>-->
        <!--    <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>-->
        <!--  </template>-->
        <!--</el-table-column>-->
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" top="5px" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" :rules="formDataRules" label-position="right" label-width="120px">
        <el-form-item label="app_id:" prop="app_id">
          <el-input v-model.number="formData.app_id" clearable/>
        </el-form-item>
        <el-form-item label="用户id:" prop="user_id">
          <el-input v-model.number="formData.user_id" clearable/>
        </el-form-item>
        <el-form-item label="用户token:" prop="token">
          <el-input v-model="formData.token" clearable />
        </el-form-item>
        <el-form-item label="交易id:" prop="transaction_id">
          <el-input v-model="formData.transaction_id" clearable />
        </el-form-item>
        <el-form-item label="金币变化额:" prop="coins">
          <el-input v-model.number="formData.coins" clearable/>
        </el-form-item>
        <el-form-item label="1: 减金币 2：加金币:" prop="coins_type">
          <el-input v-model.number="formData.coins_type" clearable/>
        </el-form-item>
        <el-form-item label="房间id:" prop="room_id">
          <el-input v-model="formData.room_id" clearable />
        </el-form-item>
        <el-form-item label="创建时间:" prop="created_time">
          <el-input v-model.number="formData.created_time" clearable/>
        </el-form-item>
        <el-form-item label="更新时间:" prop="updated_time">
          <el-input v-model.number="formData.updated_time" clearable/>
        </el-form-item>
        <el-form-item label="游戏id:" prop="game_id">
          <el-input v-model.number="formData.game_id" clearable/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { createUserGameOrders, deleteUserGameOrders, deleteUserGameOrdersByIds, updateUserGameOrders, findUserGameOrders, getUserGameOrdersList} from '@/api/userGameOrders' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import {findAllGameLists} from "@/api/gameLists";
export default {
  name: 'UserGameOrders',
  mixins: [infoList],
  data() {
    return {
      listApi: getUserGameOrdersList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      coinsTypeOptions: [
        {label:'支出',value: 1},
        {label:'收入',value: 2},
      ],
      gameOptions: [],
      gameDict: [],
      coinsTypeDict: {1:'支出',2:'收入'},
      formData: {
        app_id: null,
        user_id: null,
        token: null,
        transaction_id: null,
        coins: null,
        coins_type: null,
        room_id: null,
        created_time: null,
        updated_time: null,
        game_id: null,
      },
      formDataRules: {
        app_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        user_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        token: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        transaction_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        coins: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        coins_type: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        room_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        created_time: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        updated_time: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        game_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
    }
  },
  created() {
    this.initAllGameLists()
    this.pageSize = 100
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    formatGameName(v) {
      let gInfo = this.gameDict[v]
      if (gInfo === undefined) {
        return `unKnown(${v})`
      } else {
        return `${gInfo.name}(${v})`
      }
    },
    initAllGameLists() {
      findAllGameLists().then(res=>{
        if (res.code === 0) {
          let resData = res.data.list || []
          let resDict = {}
          resData.forEach(item=>{
            resDict[item.id] = item
          })
          this.gameOptions = resData
          this.gameDict = resDict
        }
      })
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteUserGameOrders(row)
      })
    },
    async updateUserGameOrders(row) {
      const res = await findUserGameOrders({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.reuserGameOrders
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {}
    },
    async deleteUserGameOrders(row) {
      const res = await deleteUserGameOrders({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createUserGameOrders(this.formData)
          break
        case 'update':
          res = await updateUserGameOrders(this.formData)
          break
        default:
          res = await createUserGameOrders(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '成功'
        })
        this.closeDialog()
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].coins_type_str = this.coinsTypeDict[item.coins_type]
      })
    },
  },
}
</script>

<style>
</style>
