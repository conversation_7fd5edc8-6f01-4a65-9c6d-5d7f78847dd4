<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="时间">
          <el-date-picker
            v-model="searchInfo.date_range"
            type="daterange"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            :shortcuts="dateRangeShortcuts"
          />
        </el-form-item>
        <!--
        <el-form-item label="游戏">
          <el-select v-model="searchInfo.game_id" placeholder="请选择" filterable clearable>
            <el-option
              v-for="item in gameOptions"
              :key="item"
              :label="`${item.name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        -->
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table show-summary :summary-method="tableSummary" border ref="tableData" style="width: 100%" :data="tableData" row-key="id" :max-height="590">
        <el-table-column align="center" label="日期" prop="date" min-width="120" />
        <!--
        <el-table-column v-if="searchInfo.game_id !== 999999" align="center" label="游戏" prop="game_id" min-width="120">
          <template #default="scope">
            <div>{{formatGameName(scope.row.game_id)}}</div>
          </template>
        </el-table-column>
        -->
        <el-table-column align="center" label="消耗金币" prop="consumed_coins" min-width="120" />
        <el-table-column align="center" label="奖励金币" prop="reward_coins" min-width="120" />
        <el-table-column align="center" label="今日利润" prop="profit" min-width="120" />
        <el-table-column align="center" label="参与人数" prop="num_participants" min-width="120" />
        <el-table-column align="center" label="下注次数" prop="bet_count" min-width="120" />

        <el-table-column align="center" label="操作" min-width="180">
          <template #default="scope">
            <el-button type="text" icon="el-icon-view" size="small" @click="viewDayDetail(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" top="5px" title="游戏详情">
      <el-table border ref="tableData" style="width: 100%" :data="tableGameData" row-key="id" :max-height="590">
        <el-table-column align="center" label="日期" prop="date" min-width="120" />
        <el-table-column align="center" label="游戏" prop="game_id" min-width="120">
          <template #default="scope">
            <div>{{formatGameName(scope.row.game_id)}}</div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="消耗金币" prop="consumed_coins" min-width="120" />
        <el-table-column align="center" label="奖励金币" prop="reward_coins" min-width="120" />
        <el-table-column align="center" label="利润" prop="profit" min-width="120" />
        <el-table-column align="center" label="参与人数" prop="num_participants" min-width="120" />
        <el-table-column align="center" label="下注次数" prop="bet_count" min-width="120" />
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import {
  createUserGameSta,
  deleteUserGameSta,
  deleteUserGameStaByIds,
  updateUserGameSta,
  findUserGameSta,
  getUserGameStaList,
  getUserGameStaSum,
  getDayAll
} from '@/api/userGameSta' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import {findAllGameLists} from "@/api/gameLists";
import dayjs from "dayjs";
export default {
  name: 'UserGameSta',
  mixins: [infoList],
  data() {
    return {
      listApi: getUserGameStaList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      sumResData: {},
      tableGameData: [],
      multipleSelection: [],
      gameOptions: [],
      gameDict: {},
      formData: {
        date: null,
        game_id: null,
        num_participants: null,
        bet_count: null,
        consumed_coins: null,
        reward_coins: null,
      },
      formDataRules: {
        date: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        game_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        num_participants: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        bet_count: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        consumed_coins: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        reward_coins: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
    }
  },
  created() {
    this.initAllGameLists()
    this.searchInfo.game_id = 999999
    this.pageSize = 100
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    viewDayDetail(row) {
      getDayAll({date: row.date}).then(res=>{
        if (res.code === 0) {
          let resData = res.data || []
          this.tableGameData = resData
          this.tableGameData.map((item, i, data) => {
            data[i].created_at_str = this.formatDate(item.created_at)
            data[i].profit = item.consumed_coins - item.reward_coins
          })
          this.dialogFormVisible = true
        }
      })
    },
    tableSummary({columns, data})  {
      let sumList = []
      columns.forEach((item, index) => {
        if (index === 0) {
          sumList.push('Sum(All)')
        } else {
          let itemProperty = item.property
          let valueElement = this.sumResData[itemProperty]
          if (valueElement === undefined || valueElement === null) {
            sumList.push('N/A')
          } else {
            if (valueElement === 0) {
              sumList.push('-')
            } else {
              sumList.push(valueElement)
            }
          }
        }
      })
      return sumList
    },

    formatGameName(v) {
      let gInfo = this.gameDict[v]
      if (gInfo === undefined) {
        return `unKnown(${v})`
      } else {
        return `${gInfo.name}(${v})`
      }
    },
    initAllGameLists() {
      findAllGameLists().then(res=>{
        if (res.code === 0) {
          let resData = res.data.list || []
          let resDict = {}
          resData.forEach(item=>{
            resDict[item.id] = item
          })
          this.gameOptions = resData
          this.gameDict = resDict
        }
      })
    },

    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.tableGameData = []
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].profit = item.consumed_coins - item.reward_coins
      })
      getUserGameStaSum(this.searchInfo).then(res=>{
        if (res.code === 0) {
          this.sumResData = res.data || {}
          this.sumResData.profit = this.sumResData.consumed_coins - this.sumResData.reward_coins
        }
      })
    },
  },
}
</script>

<style>
</style>
