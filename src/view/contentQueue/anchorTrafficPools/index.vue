<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" ref="searchInfoRef" :model="searchInfo">
        <el-form-item label="日期">
          <el-date-picker
              v-model="searchInfo.date"
              type="date"
              value-format="YYYY-MM-DD"
              clearable
              placeholder="请选择日期"
          />
        </el-form-item>
        <el-form-item label="主播ID" prop="user_id">
          <el-input v-model.number="searchInfo.user_id" @clear="searchInfo.user_id = null" clearable
                    placeholder="主播ID"></el-input>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="主播等级">
          <el-select @clear="searchInfo.levels = null" v-model="searchInfo.levels" clearable filterable
                     placeholder="主播等级">
            <el-option
                v-for="item in levelsOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button size="mini" type="text" :icon="queryTxtIcon" @click="queryTxtChange">{{ queryTxt }}</el-button>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
          ref="tableData"
          style="width: 100%"
          :data="tableData"
          row-key="id"
          max-height="590"
      >
        <el-table-column align="center" label="日期" prop="date" min-width="120" />
        <el-table-column align="center" label="主播ID" prop="user_id" min-width="120" />
        <el-table-column align="center" label="主播ID-素材编号" prop="source_material" min-width="160" show-overflow-tooltip/>
        <el-table-column align="center" label="等级" prop="levels" min-width="120" />
        <el-table-column align="center" label="标签" prop="label" min-width="120" show-overflow-tooltip/>
        <el-table-column align="center" label="主播类型" prop="anchor_type_str" min-width="120" />
        <el-table-column align="center" label="推送次数" prop="push_count" min-width="120" />
        <el-table-column align="center" label="浏览次数" prop="views_count" min-width="120" />
        <el-table-column align="center" label="喜欢次数" prop="like_count" min-width="120" />
        <el-table-column align="center" label="拨打次数" prop="call_count" min-width="120" />
        <el-table-column align="center" label="剧本互动次数" prop="qa_count" min-width="120" />
        <el-table-column align="center" label="付费次数" prop="pay_count" min-width="120" />
        <el-table-column align="center" label="CTR" prop="ctr" min-width="120" />
        <el-table-column align="center" label="CVR" prop="cvr" min-width="120" />
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            v-show="total > 0"
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            :total="total"
            @current-change="pageChange"
            @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {getAnchorTrafficPoolsList} from '@/api/anchorTrafficPools' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import moment from "moment";
export default {
  mixins: [infoList],
  data() {
    return {
      searchInfo: {},
      searchInfoRules: {},
      listApi: getAnchorTrafficPoolsList,
    }
  },
  async created() {
    this.pageSize = 100
    this.searchInfo.date = moment().format('YYYY-MM-DD')
    await this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    handleDownload() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    formatAnchorType(v) {
      switch (v) {
        case 0:
          return "真实"
        case 1:
          return "虚拟"
        default:
          return "未知"
      }
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].anchor_type_str = this.formatAnchorType(item.anchor_type)

      })
    },
    onReset() {
      this.searchInfo = {}
    },
    onSubmit() {
      this.page = 1
      this.pageSize = 100
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
  },
}
</script>

<style lang="scss" scoped>

</style>
