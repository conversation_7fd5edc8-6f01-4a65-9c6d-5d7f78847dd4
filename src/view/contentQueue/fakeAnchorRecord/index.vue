<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="日期">
          <el-date-picker
              v-model="searchInfo.date_range"
              type="daterange"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :shortcuts="dateRangeShortcuts"
          />
        </el-form-item>
        <el-form-item label="主播ID">
          <el-input v-model="searchInfo.dst_id" placeholder="请输入主播ID"/>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
          ref="tableData"
          style="width: 100%"
          :data="tableData"
          @sort-change="tableSort"
          row-key="id"
          :max-height="590">
        <el-table-column align="center" label="日期" prop="date" sortable="custom" min-width="120"/>
        <el-table-column align="center" label="主播ID" prop="dst_id" sortable="custom" min-width="120"/>
        <el-table-column align="center" label="今日访问人数" sortable="custom" prop="profile_visit_count"
                         min-width="120"/>
        <el-table-column align="center" label="今日点赞人数" sortable="custom" prop="like_count" min-width="120"/>
        <el-table-column align="center" label="点赞率" prop="like_rate" min-width="120"/>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            :total="total"
            @current-change="pageChange"
            @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" top="5px" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" :rules="formDataRules" label-position="right" label-width="120px">
        <el-form-item label="日期:" prop="date">
          <el-input v-model="formData.date" clearable/>
        </el-form-item>
        <el-form-item label="主播ID:" prop="dst_id">
          <el-input v-model.number="formData.dst_id" clearable/>
        </el-form-item>
        <el-form-item label="今日访问人数:" prop="profile_visit_count">
          <el-input v-model.number="formData.profile_visit_count" clearable/>
        </el-form-item>
        <el-form-item label="今日点赞人数:" prop="like_count">
          <el-input v-model.number="formData.like_count" clearable/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createFakeAnchorRecord,
  deleteFakeAnchorRecord,
  deleteFakeAnchorRecordByIds,
  updateFakeAnchorRecord,
  findFakeAnchorRecord,
  getFakeAnchorRecordList
} from '@/api/fakeAnchorRecord' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import moment from "moment/moment";

export default {
  name: 'FakeAnchorRecord',
  mixins: [infoList],
  data() {
    return {
      listApi: getFakeAnchorRecordList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        date: null,
        dst_id: null,
        profile_visit_count: null,
        like_count: null,
      },
      formDataRules: {
        date: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        dst_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        profile_visit_count: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        like_count: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
    }
  },
  created() {
    const lastDayStr = moment().add(-1, 'days').format("YYYY-MM-DD")
    this.searchInfo.date_range = [lastDayStr, lastDayStr]
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    tableSort(data) {
      let orderData = data.order
      let propData = data.prop
      if (propData !== null && orderData !== null) {
        this.searchInfo.sort_prop = propData
        this.searchInfo.sort_desc = orderData !== 'ascending'
      } else {
        this.searchInfo.sort_prop = null
        this.searchInfo.sort_desc = false
      }
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteFakeAnchorRecord(row)
      })
    },
    async updateFakeAnchorRecord(row) {
      const res = await findFakeAnchorRecord({id: row.id})
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.refakeAnchorRecord
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {}
    },
    async deleteFakeAnchorRecord(row) {
      const res = await deleteFakeAnchorRecord({id: row.id})
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createFakeAnchorRecord(this.formData)
          break
        case 'update':
          res = await updateFakeAnchorRecord(this.formData)
          break
        default:
          res = await createFakeAnchorRecord(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '成功'
        })
        this.closeDialog()
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].like_rate = this.percent2(item.like_count, item.profile_visit_count)
      })
    },
  },
}
</script>

<style>
</style>
