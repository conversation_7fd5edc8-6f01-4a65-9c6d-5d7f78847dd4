<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" ref="searchInfoRef" :model="searchInfo" :rules="searchInfoRules" class="demo-form-inline">
        <el-form-item label="日期">
          <el-date-picker
              v-model="searchInfo.date"
              type="date"
              value-format="YYYY-MM-DD"
              clearable
              placeholder="请选择日期"
          />
        </el-form-item>
        <el-form-item label="主播ID" prop="user_id">
          <el-input v-model.number="searchInfo.user_id" @clear="searchInfo.user_id = null" clearable
                    placeholder="主播ID"></el-input>
        </el-form-item>
        <el-form-item label="权重最小" prop="score_min">
          <el-input v-model="searchInfo.score_min" placeholder="权重最小值" clearable
                    @clear="searchInfo.score_min = null"></el-input>
        </el-form-item>
        <el-form-item label="权重最大" prop="score_max">
          <el-input v-model="searchInfo.score_max" placeholder="权重最大值" clearable
                    @clear="searchInfo.score_max = null"></el-input>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="主播等级">
          <el-select @clear="searchInfo.levels = null" v-model="searchInfo.levels" clearable filterable
                     placeholder="主播等级">
            <el-option
                v-for="item in levelsOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="在线状态">
          <el-select @clear="searchInfo.online = null" v-model="searchInfo.online" clearable filterable
                     placeholder="在线状态">
            <el-option
                v-for="item in onlineOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="text" :icon="queryTxtIcon" @click="queryTxtChange">{{ queryTxt }}</el-button>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="header-avatar">
        <div style="font-size: 16px;">{{ onlineNotice }}</div>
      </div>
      <el-table
          ref="tableData"
          style="width: 100%"
          :data="tableData"
          row-key="id"
          border
          max-height="590"
      >
        <el-table-column align="center" label="日期" prop="date" min-width="120"/>
        <el-table-column align="center" label="主播ID" prop="user_id" min-width="120" sortable/>
        <!--<el-table-column align="center" label="工会" min-width="120" />-->
        <el-table-column align="center" label="在线状态" prop="onlineStr" width="140" sortable>
          <template #default="scope">
            <el-tag :type="scope.row.onlineType">{{ scope.row.onlineStr }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="主播等级" prop="levels" width="120" sortable/>
        <el-table-column align="center" label="转接次数" prop="push_calls" width="120" sortable/>
        <el-table-column align="center" label="接听转接" prop="accept_calls" width="120" sortable/>
        <el-table-column align="center" label="用户直呼" prop="call_direct_count" min-width="140" sortable/>
        <el-table-column align="center" label="接听直呼" prop="call_accept_direct_count" min-width="140" sortable/>
        <el-table-column align="center" label="主播直呼" prop="anchor_call_direct_count" min-width="140" sortable/>
        <el-table-column align="center" label="主播直呼接听次数" prop="anchor_call_accept_direct_count" min-width="180"
                         sortable/>
        <el-table-column align="center" label="接听时长" prop="calls_duration_str" width="120" sortable/>
        <el-table-column align="center" label="接听率" prop="answer_rate_str" width="120"/>
        <el-table-column align="center" label="人均收益(月)" prop="month_avg_income" width="120" sortable/>
        <el-table-column align="center" label="百人收益" prop="hundred_income" width="120" sortable/>
        <el-table-column align="center" label="百人亲密值" prop="hundred_intimate" width="120" sortable/>
<!--        <el-table-column align="center" label="总通话时长" prop="" width="120"/>-->
<!--        <el-table-column align="center" label="总通话次数" prop="" width="120"/>-->
        <el-table-column align="center" label="今日总次均时长(s)" prop="call_time_average" min-width="160" sortable/>
        <el-table-column align="center" label="近30天总次均时长(s)" prop="anchor_accept_30days_second" min-width="180"
                         sortable/>
<!--        <el-table-column align="center" label="今日转接次均时长(s)" prop="" min-width="180"/>-->
        <el-table-column align="center" label="主播标签" prop="tags" min-width="120" show-overflow-tooltip/>
        <el-table-column align="center" label="当前权重" prop="score" show-overflow-tooltip width="120" sortable/>
        <el-table-column align="center" label="收益" prop="income" min-width="120"/>
        <el-table-column align="center" label="运营权重" prop="TPCalculateRatio" width="160" sortable>
          <template #default="scope">
            <div class="flex-box">
              <p style="margin-right: 8px">{{ scope.row.TPCalculateRatio }}</p>
              <el-button type="text" size="mini" icon="el-icon-edit" @click="CalculateRatioEdit(scope.row)">修改</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="消息收入" prop="msg_income" width="120" sortable/>
        <el-table-column align="center" label="收到消息条数" prop="msg_receive_count" width="120" sortable/>
        <el-table-column align="center" label="回复消息条数" prop="msg_reply_count" width="120" sortable/>
        <el-table-column align="center" label="主动赠送礼物收入" prop="src_send_gift_income" width="120" sortable/>
        <el-table-column align="center" label="收到主动赠送礼物数量" prop="receive_src_send_gift_count" width="120"
                         sortable/>
        <el-table-column align="center" label="领取主动赠送礼物数量" prop="draw_src_send_gift_count" width="120"
                         sortable/>
        <el-table-column align="center" label="索要礼物收入" prop="ask_gift_income" width="120" sortable/>
        <el-table-column align="center" label="发送索要礼物次数" prop="send_ask_gift_count" width="120" sortable/>
        <el-table-column align="center" label="收到索要类礼物数" prop="receive_ask_gift_count" width="120" sortable/>
        <el-table-column align="center" label="领取索要类礼物数" prop="draw_ask_gift_count" width="120" sortable/>
        <el-table-column align="center" label="今日推送人数" prop="push_count" width="120" sortable/>
        <el-table-column align="center" label="今日亲密对数" prop="intimate_count" width="120" sortable/>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            v-show="total > 0"
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            :total="total"
            @current-change="pageChange"
            @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {ElLoading} from 'element-plus'
import {getTransferPoolsList, updateTransferPools} from '@/api/transferPools' //  此处请自行替换地址
import {addWhiteList, anchorOnlineCount} from '@/api/users' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import moment from "moment";
import {updateAnchorWeight} from "@/api/anchorWeight";

const sMin = 0
export default {
  mixins: [infoList],
  data() {
    const checkScoreRange = (rule, value, callback) => {
      let vInt = parseFloat(value)
      if (vInt < sMin) {
        return callback(new Error(`最小值${sMin}`))
      }
      callback()
    }
    return {
      searchInfo: {
        levels: null,
        date: "",
        score_min: sMin,
        score_max: 100,
      },
      searchInfoRules: {
        user_id: [
          {type: 'number', required: false, message: '请输入数字', trigger: 'blur'},
        ],
        score_min: [
          {pattern: /^(\-|\+)?\d+(\.\d+)?$/, message: '请输入正确的数字', trigger: ['blur', 'change']},
          {validator: checkScoreRange, trigger: 'blur'},
        ],
        score_max: [
          {pattern: /^(\-|\+)?\d+(\.\d+)?$/, message: '请输入正确的数字', trigger: ['blur', 'change']},
          {validator: checkScoreRange, trigger: 'blur'},
        ],
      },
      onlineNotice: "",
      listApi: getTransferPoolsList,
    }
  },
  async created() {
    this.pageSize = 100
    this.searchInfo.date = moment().format('YYYY-MM-DD')
    await this.getTableData(this.nFunc, this.tableDataFormat)
    await this.getAnchorOnlineCount()
  },
  methods: {
    CalculateRatioEdit(row) {
      this.$messageBox.prompt("请输入运营权重百分比", `修改${row.user_id}的权重`, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputPattern: /^\d{1,10}$/,
        inputErrorMessage: '请输入运营权重(数字)',
      }).then(({value}) => {
        let calculate_ratio = (parseInt(value) / 100).toFixed(2)
        updateTransferPools({id: row.id, calculate_ratio}).then(res => {
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.getTableData(this.nFunc, this.tableDataFormat)
          }
        })
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    handleDownload() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    formateRate(v) {
      return (v * 100).toFixed(2) + '%'
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].onlineStr = this.formatOnline(item.online)
        data[i].onlineType = this.formatOnlineType(item.online)
        data[i].calls_duration_str = this.formateSeconds(item.calls_duration)
        data[i].answer_rate_str = this.formateRate(item.answer_rate)
        // data[i].income = this.formateTPIncome(item)
        data[i].TPCalculateRatio = this.formateTPCalculateRatio(item)
      })
    },
    handleProgressLoading() {
      this.progressLoading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
    },
    onReset() {
      this.searchInfo = {
        levels: null,
        date: moment().format('YYYY-MM-DD'),
        score_min: sMin,
        score_max: 100,
      }
    },
    getAnchorOnlineCount() {
      anchorOnlineCount().then(res => {
        let onlineStrList = []
        res.data.forEach(item => {
          onlineStrList.push(`lv${item.levels}:${item.total}`)
        })
        let notice = onlineStrList.join(" -- ")
        this.onlineNotice = "当前主播在线 " + notice
      })
    },
    onSubmit() {
      this.page = 1
      this.pageSize = 100
      this.$refs['searchInfoRef'].validate(async valid => {
        if (!valid) return
        if (this.searchInfo.score_min !== null && this.searchInfo.score_max !== null) {
          if (this.searchInfo.score_min > this.searchInfo.score_max) {
            this.$message.error("权重最小值不能大于权重最大值")
            return
          }
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      })

    },
  },
}
</script>

<style lang="scss" scoped>

</style>
