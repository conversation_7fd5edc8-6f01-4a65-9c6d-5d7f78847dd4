<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="日期">
          <el-date-picker
              v-model="searchInfo.date_range"
              type="daterange"
              value-format="YYYY-MM-DD"
              format="YYYY-MM-DD"
              :shortcuts="dateRangeShortcuts"
              start-placeholder="Start Date"
              end-placeholder="End Date"
          />
        </el-form-item>
        <el-form-item label="主播ID">
          <el-input v-model="searchInfo.dst_id" placeholder="请输入主播ID" clearable/>
        </el-form-item>
        <el-form-item label="主播等级">
          <el-select v-model="searchInfo.levels" clearable placeholder="主播等级">
            <el-option
                v-for="item in levelsOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
          border
          ref="tableData"
          style="width: 100%"
          :data="tableData"
          @sort-change="tableSort"
          row-key="id"
          :max-height="590">
        <el-table-column align="center" label="更新时间" prop="updated_at_str" min-width="120"/>
        <el-table-column align="center" label="日期" prop="date" min-width="120"/>
        <el-table-column align="center" label="主播ID" prop="dst_id" min-width="120"/>
        <el-table-column align="center" label="主播等级" prop="levels" sortable="custom" min-width="120"/>
        <el-table-column align="center" label="亲密关系对" prop="intimate_count" sortable="custom" min-width="120"/>
        <el-table-column align="center" label="累计亲密值" prop="cumulative_intimate" sortable="custom"
                         min-width="120"/>
        <el-table-column align="center" label="总亲密价" prop="intimate_value" sortable="custom" min-width="120"/>
        <el-table-column align="center" label="总消费钻石" prop="all_diamonds" sortable min-width="120"/>
        <el-table-column align="center" label="视频钻石" prop="call_diamond" sortable="custom" min-width="120"/>
        <el-table-column align="center" label="消息钻石" prop="msg_diamond" sortable="custom" min-width="120"/>
        <el-table-column align="center" label="礼物钻石" prop="gift_diamond" sortable="custom" min-width="120"/>
        <el-table-column align="center" label="节目钻石" prop="program_diamond" sortable="custom" min-width="120"/>
        <el-table-column align="center" label="100次价值" prop="hundred_value" sortable min-width="120"/>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            :total="total"
            @current-change="pageChange"
            @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {getAnchorValueList} from '@/api/anchorValue' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import moment from "moment";

export default {
  name: 'AnchorValue',
  mixins: [infoList],
  data() {
    return {
      listApi: getAnchorValueList,
    }
  },
  created() {
    const lastDayStr = moment().add(-1, 'days').format("YYYY-MM-DD")
    this.searchInfo.date_range = [lastDayStr, lastDayStr]
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    tableSort(data) {
      let orderData = data.order
      let propData = data.prop
      if (propData !== null && orderData !== null) {
        this.searchInfo.sort_prop = propData
        this.searchInfo.sort_desc = orderData !== 'ascending'
      } else {
        this.searchInfo.sort_prop = null
        this.searchInfo.sort_desc = false
      }
      if (propData === 'all_diamonds' || propData === 'hundred_value') {
        this.$message({
          type: 'info',
          message: '仅排序当前页数据'
        })
        this.searchInfo.sort_prop = null
        this.searchInfo.sort_desc = false
      } else {
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    onReset() {
      this.searchInfo = {}
    },
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].updated_at_str = this.formatDate(item.updated_at)
        let a_diamond = item.call_diamond + item.msg_diamond + item.gift_diamond + item.program_diamond
        data[i].all_diamonds = a_diamond
        data[i].hundred_value = this.div2(this.div(a_diamond, item.intimate_count), 100)
      })
    },
  },
}
</script>

<style>
</style>
