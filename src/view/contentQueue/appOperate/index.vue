<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="时间范围">
          <el-date-picker
              v-model="searchInfo.date_range"
              type="daterange"
              :shortcuts="dateRangeShortcuts"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="包名">
          <el-select filterable v-model="searchInfo.app_id" clearable>
            <el-option
                v-for="item in appList"
                :key="item.id"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table ref="tableData" show-summary border style="width: 100%" :data="tableData" row-key="id"
                :max-height="590">
        <el-table-column align="center" label="日期" prop="date" min-width="120"/>
        <el-table-column align="center" label="包名" prop="app_name" min-width="120"/>
        <el-table-column align="center" label="充值金额" prop="charge_amount" min-width="120"/>
        <el-table-column align="center" label="自然用户" prop="origin_login" min-width="120"/>
        <el-table-column align="center" label="非自然用户" prop="not_origin_login" min-width="120"/>
        <el-table-column align="center" label="审核用户" prop="audit_user" min-width="120"/>
        <el-table-column align="center" label="付费DAU" prop="paid_user_total" min-width="120"/>
        <el-table-column align="center" label="付费用户" prop="paid_login" min-width="120"/>
        <el-table-column align="center" label="匹配数" prop="match_success" min-width="120"/>
        <el-table-column align="center" label="电话数" prop="call_success" min-width="120"/>
        <el-table-column align="center" label="付费消息" prop="paid_user_send_msg" min-width="120"/>
        <el-table-column align="center" label="免费消息" prop="no_paid_user_send_msg" min-width="120"/>
        <el-table-column align="center" label="礼物个数" prop="send_gift_count" min-width="120"/>
        <el-table-column align="center" label="节目数" prop="program_count" min-width="120"/>
        <el-table-column align="center" label="累计钻石余额" prop="diamond_balance" min-width="120"/>
        <el-table-column align="center" label="签到钻石" prop="sign_diamond" min-width="120"/>
        <el-table-column align="center" label="签到次数" prop="sign_count" min-width="120"/>
        <el-table-column align="center" label="签到人数" prop="sign_user_count" min-width="120"/>
        <el-table-column align="center" label="通话退回钻石" prop="call_cancel_diamond" min-width="150"/>
        <el-table-column align="center" label="亲密等级奖励" prop="intimate_diamonds" min-width="150"/>
        <el-table-column align="center" label="充值钻石" prop="charge_diamond" min-width="120"/>
        <el-table-column align="center" label="今日消耗钻石" prop="consume_diamond" min-width="120"/>
        <el-table-column align="center" label="消耗占比" prop="consume_rate" min-width="120"/>
        <el-table-column align="center" label="视频消耗" prop="video_consume_diamond" min-width="120"/>
        <el-table-column align="center" label="免費消息消耗" prop="free_msg_consume_diamond" min-width="120"/>
        <el-table-column align="center" label="付費消息消耗" prop="paid_msg_consume_diamond" min-width="120"/>
        <el-table-column align="center" label="礼物消耗" prop="gift_consume_diamond" min-width="120"/>
        <el-table-column align="center" label="节目消耗" prop="program_consume_diamond" min-width="120"/>
        <el-table-column align="center" label="相册解锁" prop="album_unlock" min-width="120"/>
        <el-table-column align="center" label="卡片余额" prop="card_balance" min-width="120"/>
        <el-table-column align="center" label="今日付费卡片" prop="paid_card" min-width="120"/>
        <el-table-column align="center" label="今日广告卡片" prop="advert_card" min-width="120"/>
        <el-table-column align="center" label="消耗卡片(总)" prop="consume_card" min-width="120"/>
        <el-table-column align="center" label="视频消耗卡片" prop="video_consume_card" min-width="120"/>
        <el-table-column align="center" label="图片消耗卡片" prop="photo_consume_card" min-width="120"/>
        <el-table-column align="center" label="消息消耗卡片" prop="msg_consume_card" min-width="120"/>
        <el-table-column align="center" label="匹配消耗卡片" prop="match_consume_card" min-width="120"/>
        <el-table-column align="center" label="有视频的人" prop="call_effective_user" min-width="120"/>
        <el-table-column align="center" label="免费消息发送人数" prop="send_free_msg_user" min-width="140"/>
        <el-table-column align="center" label="付费消息发送人数" prop="send_paid_msg_user" min-width="140"/>
        <el-table-column align="center" label="送礼人数" prop="send_gift_user" min-width="120"/>
        <el-table-column align="center" label="发送节目人数" prop="send_program_user" min-width="120"/>
        <el-table-column align="center" label="发起匹配人数" prop="send_match_user" min-width="120"/>
        <el-table-column align="center" label="使用卡片人数" prop="use_card_user" min-width="120"/>
        <el-table-column align="center" label="亲密0级" prop="intimate_lv_zero" min-width="120"/>
        <el-table-column align="center" label="亲密1级" prop="intimate_lv_one" min-width="120"/>
        <el-table-column align="center" label="亲密2级" prop="intimate_lv_two" min-width="120"/>
        <el-table-column align="center" label="亲密3级" prop="intimate_lv_three" min-width="120"/>
        <el-table-column align="center" label="亲密4级" prop="intimate_lv_four" min-width="120"/>
        <el-table-column align="center" label="更新时间" prop="updated_at_str" min-width="180"/>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            :total="total"
            @current-change="pageChange"
            @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {calcPercent, div2, numberTo3} from "@/utils/number"
import {getAppOperateList} from '@/api/appOperate' //  此处请自行替换地址
import infoList from '@/mixins/infoList'

export default {
  name: 'AppOperate',
  mixins: [infoList],
  data() {
    return {
      listApi: getAppOperateList,
      pageSize: 100,
    }
  },
  created() {
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 100
      this.getTableData(this.nFunc, this.tableDataFormat)
    },

    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].app_name = this.formatAppName(item.app_id)
        data[i].consume_rate = calcPercent(item.consume_diamond, (item.sign_diamond + item.charge_diamond))[0]
        // data[i].paid_dau =
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].updated_at_str = this.formatDate(item.updated_at)
      })
    },
  },
}
</script>

<style>
</style>
