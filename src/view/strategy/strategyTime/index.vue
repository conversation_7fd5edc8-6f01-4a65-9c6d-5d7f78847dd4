<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="AppId">
          <el-select v-model="searchInfo.app_id" clearable filterable placeholder="AppId">
            <el-option
                v-for="item in appList"
                :key="item"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="版本号">
          <el-input v-model="searchInfo.version" placeholder="版本号"/>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
        <el-popover v-model:visible="deleteVisible" placement="top" width="160">
          <p>确定要删除吗？</p>
          <div style="text-align: right; margin-top: 8px;">
            <el-button size="mini" type="text" @click="deleteVisible = false">取消</el-button>
            <el-button size="mini" type="primary" @click="onDelete">确定</el-button>
          </div>
          <template #reference>
            <el-button icon="el-icon-delete" size="mini" style="margin-left: 10px;"
                       :disabled="!multipleSelection.length">删除
            </el-button>
          </template>
        </el-popover>
      </div>
      <el-table
          ref="multipleTable"
          style="width: 100%"
          tooltip-effect="dark"
          :data="tableData"
          row-key="id"
          max-height="590"
          @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55"/>
        <el-table-column align="center" label="日期" width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="开始时间" prop="start_time" width="120"/>
        <el-table-column align="center" label="结束时间" prop="end_time" width="120"/>
        <el-table-column align="center" label="AppId" prop="app_id" width="120">
          <template #default="scope">{{ formaAppList(scope.row.app_id) }}</template>
        </el-table-column>
        <el-table-column align="center" label="版本号" prop="version" width="120"/>
        <el-table-column align="center" label="按钮组">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button"
                       @click="updateStrategyTime(scope.row)">变更
            </el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作" :width="500">
      <el-form :model="formData" label-position="right" label-width="80px">
        <el-form-item label="时间:">
          <el-time-picker
              v-model="defaultTime"
              is-range
              range-separator="To"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              value-format="HH:mm:ss"
              :default-value="defaultTime"
          />
        </el-form-item>
        <el-form-item label="AppId:" prop="app_id">
          <el-select v-model="formData.app_id" clearable filterable placeholder="AppId">
            <el-option
                v-for="item in appList"
                :key="item"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="版本号:" prop="version">
          <el-input v-model="formData.version" clearable placeholder="请输入版本号（1.0.0）"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createStrategyTime,
  deleteStrategyTime,
  deleteStrategyTimeByIds,
  updateStrategyTime,
  findStrategyTime,
  getStrategyTimeList
} from '@/api/strategyTime' //  此处请自行替换地址
import {getAppXesList} from '@/api/appXes'
import infoList from '@/mixins/infoList'

export default {
  name: 'StrategyTime',
  mixins: [infoList],
  data() {
    return {
      listApi: getStrategyTimeList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      appListOptions: [],
      defaultTime: ['00:00:00', '23:59:59'],
      formData: {
        start_time: '',
        end_time: '',
        app_id: 4,
        version: '',
      }
    }
  },
  async created() {
    await this.getTableData()
    await this.getAppList()
  },
  methods: {
    formaAppList: function (bool) {
      let text
      this.appListOptions.forEach(function (element) {
        if (bool === element.id) {
          text = element.appName
          return text
        }
      })
      return text
    },
    async getAppList() {
      const res = await getAppXesList()
      this.appListOptions = res.data.list
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteStrategyTime(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
      this.multipleSelection.map(item => {
        ids.push(item.id)
      })
      const res = await deleteStrategyTimeByIds({ids})
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateStrategyTime(row) {
      const res = await findStrategyTime({id: row.id})
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.restrategyTime
        this.dialogFormVisible = true
        this.defaultTime[0] = this.formData.start_time
        this.defaultTime[1] = this.formData.end_time
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        start_time: '',
        end_time: '',
      }
    },
    async deleteStrategyTime(row) {
      const res = await deleteStrategyTime({id: row.id})
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      this.formData.start_time = this.defaultTime[0]
      this.formData.end_time = this.defaultTime[1]
      switch (this.type) {
        case 'create':
          res = await createStrategyTime(this.formData)
          break
        case 'update':
          res = await updateStrategyTime(this.formData)
          break
        default:
          res = await createStrategyTime(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.defaultTime = ['00:00:00', '23:59:59']
      this.dialogFormVisible = true
    }
  },
}
</script>

<style>
</style>

