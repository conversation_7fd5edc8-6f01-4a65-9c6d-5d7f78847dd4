<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="主题ID">
          <el-input v-model.number="searchInfo.id" clearable></el-input>
        </el-form-item>
        <el-form-item label="标签">
          <el-select v-model="searchInfo.group_id" filterable clearable>
            <el-option
              v-for="item in quickReplyGroupList"
              :key="item"
              :label="`${item.title}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchInfo.online" clearable>
            <el-option label="上线" :value="1"/>
            <el-option label="下线" :value="-1"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" type="success" icon="el-icon-plus" @click="openDialog">新增</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table border ref="tableData" style="width: 100%" :data="tableData" row-key="id" :max-height="590">
        <el-table-column align="center" label="主题ID" prop="id" width="180"/>
        <el-table-column align="center" label="标签" prop="group_str" min-width="120" />
        <el-table-column align="center" label="内容" prop="content" min-width="120" />
        <el-table-column align="center" label="状态" prop="online_str" width="120" />
        <el-table-column align="center" label="操作" width="180">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" @click="updateQuickReplyNew(scope.row)">修改</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" top="5px" :before-close="closeDialog" title="弹窗操作">
      <el-form ref="formDataRef" :model="formData" :rules="formDataRules" label-position="right" label-width="120px">
        <el-form-item label="标签:" prop="group_id">
          <el-select style="width: 100%;" v-model="formData.group_id" filterable clearable>
            <el-option
              v-for="item in quickReplyGroupList"
              :key="item"
              :label="`${item.title}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="内容:" prop="content">
          <el-input type="textarea" v-model="formData.content" :autosize="{ minRows: 5, maxRows: 10 }" />
        </el-form-item>
        <el-form-item label="是否上线:" prop="online">
          <el-switch v-model="formData.online" :active-value="1" :inactive-value="-1"></el-switch>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { createQuickReplyNew, deleteQuickReplyNew, deleteQuickReplyNewByIds, updateQuickReplyNew, findQuickReplyNew, getQuickReplyNewList} from '@/api/quickReplyNew' //  此处请自行替换地址
import { findAllQuickReplyGroupNew } from '@/api/quickReplyGroupNew' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
export default {
  name: 'QuickReplyNew',
  mixins: [infoList],
  data() {
    return {
      listApi: getQuickReplyNewList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      quickReplyGroupList: [],
      quickReplyGroupDict: {},
      formData: {
        group_id: null,
        content: null,
        online: 1,
      },
      formDataRules: {
        group_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        content: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
    }
  },
  created() {
    this.getTableData(this.nFunc, this.tableDataFormat)
    this.initQuickReplyGroupList()
  },
  methods: {
    initQuickReplyGroupList() {
      findAllQuickReplyGroupNew().then(res=>{
        if (res.code === 0) {
          let resData = res.data || []
          let resDict = {}
          resData.forEach(item=>{
            resDict[item.id] = item.title
          })
          this.quickReplyGroupList = resData
          this.quickReplyGroupDict = resDict
          this.formData.group_id = this.quickReplyGroupList.length > 0 ? this.quickReplyGroupList[0].id : null
        }
      })
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteQuickReplyNew(row)
      })
    },
    async updateQuickReplyNew(row) {
      const res = await findQuickReplyNew({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.$refs.formDataRef.resetFields()
    },
    async deleteQuickReplyNew(row) {
      const res = await deleteQuickReplyNew({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    async enterDialog() {
      this.$refs.formDataRef.validate(async v=>{
        if (!v) {
          return
        }
        let res
        switch (this.type) {
          case 'create':
            res = await createQuickReplyNew(this.formData)
            break
          case 'update':
            res = await updateQuickReplyNew(this.formData)
            break
          default:
            res = await createQuickReplyNew(this.formData)
            break
        }
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功'
          })
          this.closeDialog()
          this.getTableData(this.nFunc, this.tableDataFormat)
        }
      })

    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].group_str = this.quickReplyGroupDict[item.group_id]
        data[i].online_str = item.online === 1 ? '上线' : '下线'
      })
    },
  },
}
</script>

<style>
</style>

