<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="剧本ID">
          <el-input v-model="searchInfo.id" clearable/>
        </el-form-item>
        <el-form-item label="名称">
          <el-input v-model="searchInfo.name" clearable/>
        </el-form-item>
        <el-form-item label="场景值">
          <el-input v-model="searchInfo.scene_code" clearable/>
        </el-form-item>
        <el-form-item label="分组">
          <el-input v-model="searchInfo.group_str" clearable/>
        </el-form-item>
        <el-form-item label="主播ID">
          <el-input v-model="searchInfo.dst_id" clearable/>
        </el-form-item>
        <el-form-item label="语种">
          <el-select v-model="searchInfo.language_range" clearable filterable allow-create default-first-option multiple
                     collapse-tags>
            <el-option
                v-for="item in languageOptions"
                :key="item"
                :label="item.code"
                :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchInfo.state" clearable>
            <el-option label="上线" :value="1"/>
            <el-option label="下线" :value="2"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" type="success" icon="el-icon-plus" @click="openDialog">新增</el-button>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="warning" icon="el-icon-download" @click="downloadExcelTemplate">下载模板
          </el-button>
        </el-form-item>
        <el-form-item>
          <el-upload
              :action="`${path}/strategyInfo/importStrategy`"
              :headers="{'x-token':token}"
              :on-success="uploadStrategySuccess"
              :on-progress="uploadStrategyProgress"
              :show-file-list="false"
          >
            <el-button size="mini" type="danger" icon="el-icon-folder-opened">导入</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table ref="tableData" style="width: 100%" :data="tableData" row-key="id" :max-height="590" border>
        <el-table-column align="center" label="策略信ID" prop="id" width="100"/>
        <el-table-column align="center" label="名称" prop="name" show-overflow-tooltip min-width="120"/>
        <el-table-column align="center" label="语种" prop="language" show-overflow-tooltip min-width="120"/>
        <el-table-column align="center" label="场景值" prop="scene_code" show-overflow-tooltip min-width="120"/>
        <el-table-column align="center" label="主播ID" prop="dst_id" show-overflow-tooltip min-width="120"/>
        <el-table-column align="center" label="分组" prop="group_ids" show-overflow-tooltip min-width="120"/>
        <el-table-column align="center" label="状态" prop="state" width="160">
          <template #default="scope">
            <el-tag type="success" v-if="scope.row.state === 1">上线</el-tag>
            <el-tag type="info" v-else-if="scope.row.state === 2">下线</el-tag>
            <el-tag type="danger" v-else>未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="210">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" @click="updateObj(scope.row)">修改</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeListNormal"
            :total="total"
            @current-change="pageChange"
            @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog width="30%" v-model="dialogFormVisible" top="5px" :before-close="closeDialog" :title="dialogTitle">
      <el-form ref="formData" :model="formData" :rules="formDataRules" label-position="right" label-width="120px">
        <el-form-item label="状态" prop="state">
          <el-select v-model="formData.state" style="width: 100%;">
            <el-option label="上线" :value="1"/>
            <el-option label="下线" :value="2"/>
          </el-select>
        </el-form-item>
        <el-form-item label="名称" prop="name">
          <el-input v-model="formData.name" maxlength="64" show-word-limit clearable/>
        </el-form-item>
        <el-form-item label="语种" prop="language">
          <el-select v-model="formData.language" allow-create default-first-option filterable style="width: 100%;">
            <el-option
                v-for="item in languageOptions"
                :key="item"
                :label="`${item.en_name}(${item.code})`"
                :value="item.code"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="主播ID" prop="dst_id">
          <el-input-number style="width: 100%;" v-model="formData.dst_id" clearable/>
        </el-form-item>
        <el-form-item label="分组ID" prop="group_ids">
          <el-input v-model="formData.group_ids">
            <template #suffix>
              <el-tooltip effect="dark"
                          content="1.用大写字母分组.2.允许输入多个分组，用英文逗号隔开(方便AB实验时,只调整部分策略信)"
                          placement="bottom">
                <i class="el-icon-question el-q"></i>
              </el-tooltip>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="场景码" prop="scene_code">
          <el-input v-model="formData.scene_code" :rows="2" type="textarea" clearable/>
        </el-form-item>

        <div v-for="(item, i) in formData.content_list" :key="i">
          <el-form-item :label="`发送类型${i}`">
            <el-row style="width: 100%; margin: 0; padding: 0" :gutter="20">
              <el-col :span="20">
                <el-select v-model="item.send_type" filterable style="width: 100%;">
                  <el-option
                      v-for="item in sendTypeOptions"
                      :key="item"
                      :label="`${item.label}(${item.value})`"
                      :value="item.value"
                  />
                </el-select>
              </el-col>
              <el-col :span="4" v-show="i !== 0">
                <el-button size="small" type="danger" @click="removeItem(i)">删除</el-button>
              </el-col>
            </el-row>

          </el-form-item>
          <el-form-item :label="`发送内容${i}`">

            <div style="display: flex;" v-if="item.send_type === 2 || item.send_type === 3">
              <el-input v-model="item.content" :rows="3" type="textarea" clearable/>
              <el-button size="small" type="success" @click="uploadItem(item)">上传</el-button>
            </div>
            <div v-else>
              <el-input v-model="item.content" :rows="3" type="textarea" clearable/>
            </div>
          </el-form-item>
        </div>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" type="success" @click="addItem">添加一条消息</el-button>
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createStrategyInfo,
  updateStrategyInfo,
  findStrategyInfo,
  getStrategyInfoList,
  deleteStrategyInfo, downloadTemplate
} from '@/api/strategy_info' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import {findLanguagesAll} from "@/api/languages";
import {createFilesFromUrl} from "@/api/sys_file";
import {getStrategySendTypeList} from "@/api/strategy_send_content";

const path = import.meta.env.VITE_BASE_API
export default {
  mixins: [infoList],
  data() {
    return {
      listApi: getStrategyInfoList,
      dialogFormVisible: false,
      path: path,
      type: '',
      dialogTitle: '',
      deleteVisible: false,
      loading: false,
      languageOptions: [
        {code: 'en'},
        {code: 'es'},
        {code: 'ar'},
        {code: 'hi'},
        {code: 'in'},
        {code: 'tr'},
        {code: 'br'},
        {code: 'pk'},
      ],
      sendTypeOptions: [],
      anchorOptions: [],
      formData: {
        content_list: [],
      },
      formDataRules: {
        state: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        name: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        // dst_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        group_ids: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        language: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        scene_code: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
    }
  },
  created() {
    this.getSendTypeList()
    this.pageSize = 100
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 100
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    downloadExcelTemplate() {
      downloadTemplate()
    },
    uploadStrategySuccess(response) {
      this.progressLoading.close()
      if (response.code != 0) {
        this.$message.error(response.msg)
        return
      }
      this.$message.success("成功")
      this.page = 1
      this.pageSize = 100
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    uploadStrategyProgress(evt) {
      this.handleProgressLoading()
    },
    addItem() {
      this.formData.content_list.push({})
    },
    uploadItem(item) {
      if (item.content === "") {
        this.$message.error("请输入内容")
        return
      }
      createFilesFromUrl({file_url: item.content, file_type: item.send_type}).then(res => {
        if (res.code === 0) {
          item.content = res.data.url
          this.$message.success("已上传成功并替换")
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    removeItem(index) {
      this.formData.content_list.splice(index, 1)
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteObj(row)
      })
    },
    async updateObj(row) {
      const res = await findStrategyInfo({id: row.id})
      this.type = 'update'
      this.dialogTitle = '更新'
      if (res.code === 0) {
        this.formData = res.data
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        content_list: [],
      }
    },
    async deleteObj(row) {
      const res = await deleteStrategyInfo({id: row.id})
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    async getSendTypeList() {
      const res = await getStrategySendTypeList()
      if (res.code === 0) {
        this.sendTypeOptions = res.data || []
      }
    },
    async enterDialog() {
      this.$refs['formData'].validate(async valid => {
        if (!valid) return
        let res
        switch (this.type) {
          case 'create':
            res = await createStrategyInfo(this.formData)
            break
          case 'update':
            res = await updateStrategyInfo(this.formData)
            break
          default:
            res = await createStrategyInfo(this.formData)
            break
        }
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功'
          })
          this.closeDialog()
          this.getTableData(this.nFunc, this.tableDataFormat)
        }
      })
    },
    openDialog() {
      this.type = 'create'
      this.dialogTitle = '创建'
      this.formData.content_list.push({send_type: 1, content: ''})
      this.dialogFormVisible = true
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
      })
    },
  },
}
</script>

<style lang="scss">
.el-q {
  cursor: pointer;
}
</style>
