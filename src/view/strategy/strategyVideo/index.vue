<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="ID">
          <el-input v-model.number="searchInfo.id" placeholder="请输入ID"/>
        </el-form-item>
        <el-form-item label="主播ID">
          <el-input v-model.number="searchInfo.user_id" placeholder="请输入主播ID"/>
        </el-form-item>
        <el-form-item label="视频状态">
          <el-select v-model="searchInfo.video_state">
            <el-option label="全部" :value="0"/>
            <el-option label="正常" :value="1"/>
            <el-option label="暂停" :value="2"/>
          </el-select>
        </el-form-item>
        <el-form-item label="视频类型">
          <el-select v-model="searchInfo.video_type">
            <el-option label="全部" :value="0"/>
            <el-option label="白天" :value="1"/>
            <el-option label="夜晚" :value="2"/>
          </el-select>
        </el-form-item>
        <el-form-item label="付费类型">
          <el-select v-model="searchInfo.pay_type">
            <el-option label="全部" :value="0"/>
            <el-option label="付费" :value="1"/>
            <el-option label="免费" :value="2"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" type="success" icon="el-icon-plus" @click="openDialog">新增</el-button>
          <!--<el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>-->
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table ref="tableData" style="width: 100%" :data="tableData" row-key="id" :max-height="590" border>
        <el-table-column align="center" label="ID" prop="id" min-width="80"/>
        <el-table-column align="center" label="视频状态" prop="video_state_str" min-width="120"/>
        <el-table-column align="center" label="类型" prop="video_type_str" min-width="120"/>
        <el-table-column align="center" label="付费类型" prop="pay_type_str" min-width="120"/>
        <el-table-column align="center" label="主播ID" prop="user_id" min-width="120" show-overflow-tooltip/>
        <el-table-column align="center" label="视频权重" prop="video_weight" min-width="120"/>
        <el-table-column align="center" label="视频链接" prop="url" min-width="120" show-overflow-tooltip/>
        <el-table-column align="center" label="创建时间" prop="created_at_str" min-width="180"/>
        <el-table-column align="center" label="操作" min-width="180">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button"
                       @click="updateStrategyVideo(scope.row)">修改
            </el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeListNormal"
            :total="total"
            @current-change="pageChange"
            @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog width="30%" v-model="dialogFormVisible" top="5px" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" :rules="formDataRules" label-position="right" label-width="120px">
        <el-form-item label="视频状态:" prop="video_state">
          <el-select v-model="formData.video_state" style="width: 100%;">
            <el-option label="正常" :value="1"/>
            <el-option label="暂停" :value="2"/>
          </el-select>
        </el-form-item>

        <el-form-item label="视频类型:" prop="video_type">
          <el-select v-model="formData.video_type" style="width: 100%;">
            <el-option label="白天-UTC+0(6:00-18:00)" :value="1"/>
            <el-option label="夜晚-UTC+0(18:00-6:00)" :value="2"/>
          </el-select>
        </el-form-item>

        <el-form-item label="付费类型:" prop="pay_type">
          <el-select v-model="formData.pay_type" style="width: 100%;">
            <el-option label="付费策略" :value="1"/>
            <el-option label="免费策略" :value="2"/>
          </el-select>
        </el-form-item>

        <el-form-item label="视频权重:" prop="video_weight">
          <el-slider v-model="formData.video_weight"/>
        </el-form-item>

        <el-form-item label="主播ID:" prop="user_id">
          <el-input v-model.number="formData.user_id" clearable/>
        </el-form-item>

        <el-form-item label="地址:" prop="url">
          <div style="display: flex;">
            <el-input v-model="formData.url" clearable/>
            <el-button type="success" @click="uploadItem(formData.url)">上 传</el-button>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createStrategyVideo,
  createStrategyVideoDetail,
  updateStrategyVideo,
  updateStrategyVideoDetail,
  findStrategyVideo,
  findStrategyVideoDetail,
  getStrategyVideoList,
  deleteStrategyVideo,
  deleteStrategyVideoDetail,
} from '@/api/strategyVideo' //  此处请自行替换地址
import {getUserDetail} from '@/api/users' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import {createFilesFromUrl} from "@/api/sys_file";

export default {
  name: 'StrategyVideo',
  mixins: [infoList],
  data() {
    return {
      listApi: getStrategyVideoList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      loading: false,
      multipleSelection: [],
      anchorOptions: [],
      formData: {
        url: '',
        video_state: 1,
        video_type: 1,
        video_weight: 88,
        pay_type: 1,
        user_id: 0,
      },
      formDataRules: {
        url: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        video_state: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        video_type: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        video_weight: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        pay_type: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
    }
  },
  created() {
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    uploadItem(fileUrl) {
      if (fileUrl === "") {
        this.$message.error("请输入内容")
        return
      }
      createFilesFromUrl({file_url: fileUrl, file_type: 3}).then(res => {
        if (res.code === 0) {
          this.formData.url = res.data.url
          this.$message.success("已上传成功并替换")
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    remoteSearchMethod(query) {
      if (query) {
        let nQuery = parseInt(query)
        if (isNaN(nQuery)) {
          return
        }
        this.loading = true
        getUserDetail({id: nQuery, role: 2}).then(res => {
          this.loading = false
          if (res.code === 0) {
            let nList = []
            let rData = res.data
            nList.push({
              id: nQuery,
              nickname: rData.nickname
            })
            this.anchorOptions = nList
            console.log(this.anchorOptions)
          }
        })
      } else {
        this.anchorOptions = []
      }
    },

    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteStrategyVideo(row)
      })
    },
    async updateStrategyVideo(row) {
      const res = await findStrategyVideo({id: row.id})
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        url: '',
        video_state: 1,
        video_type: 1,
        video_weight: 88,
        pay_type: 1,
        user_id: 0,
      }
    },
    async deleteStrategyVideo(row) {
      const res = await deleteStrategyVideo({id: row.id})
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createStrategyVideo(this.formData)
          break
        case 'update':
          res = await updateStrategyVideo(this.formData)
          break
        default:
          res = await createStrategyVideo(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '成功'
        })
        this.closeDialog()
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    formatVideoState(v) {
      let s = ""
      switch (v) {
        case 1:
          s = "正常"
          break
        case 2:
          s = "暂停"
          break
        default:
          s = "未知"
      }
      return s
    },
    formatVideoType(v) {
      let s = ""
      switch (v) {
        case 1:
          s = "白天"
          break
        case 2:
          s = "夜晚"
          break
        default:
          s = "未知"
      }
      return s
    },
    formatVideoPayType(v) {
      let s = ""
      switch (v) {
        case 1:
          s = "付费"
          break
        case 2:
          s = "免费"
          break
        default:
          s = "未知"
      }
      return s
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].video_state_str = this.formatVideoState(item.video_state)
        data[i].video_type_str = this.formatVideoType(item.video_type)
        data[i].pay_type_str = this.formatVideoPayType(item.pay_type)
      })
    },
  },
}
</script>

<style lang="scss">

</style>
