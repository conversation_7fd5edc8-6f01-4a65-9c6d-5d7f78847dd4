<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="剧本ID:">
          <el-select v-model.number="searchInfo.drama_id" clearable filterable placeholder="请输入">
            <el-option
              v-for="item in dramaOptions"
              :key="item.id"
              :label="`${item.title}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态:">
          <el-select v-model.number="searchInfo.state" clearable filterable placeholder="请输入">
            <el-option
              v-for="item in dramaTacticStateOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="语言:">
          <el-select v-model.number="searchInfo.language" clearable filterable placeholder="请选择">
            <el-option
                v-for="item in dramaTacticLanguageOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
        <el-popover v-model:visible="deleteVisible" placement="top" width="160">
          <p>确定要删除吗？</p>
          <div style="text-align: right; margin-top: 8px;">
            <el-button size="mini" type="text" @click="deleteVisible = false">取消</el-button>
            <el-button size="mini" type="primary" @click="onDelete">确定</el-button>
          </div>
          <template #reference>
            <el-button
              icon="el-icon-delete"
              size="mini"
              style="margin-left: 10px;"
              :disabled="!multipleSelection.length"
            >删除
            </el-button>
          </template>
        </el-popover>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="center" label="创建日期" width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="内容" prop="content" width="120" />
        <el-table-column align="center" label="语言" prop="language" width="160">
          <template #default="scope">{{ formaDramaTacticLanguage(scope.row.language) }}</template>
        </el-table-column>
        <el-table-column align="center" label="剧本ID" prop="drama_id" width="120" />
        <el-table-column align="center" label="发送时间" prop="second" width="120" />
        <el-table-column align="center" label="发送时机" prop="send_moment" width="160">
          <template #default="scope">{{ formaDramaTacticSendMomentPartner(scope.row.send_moment) }}</template>
        </el-table-column>
        <el-table-column align="center" label="主播ID" prop="anchor_id" width="120" />
        <el-table-column align="center" label="状态" prop="state" width="120">
          <template #default="scope">{{ formaDramaTacticStatePartner(scope.row.state) }}</template>
        </el-table-column>
        <el-table-column align="center" label="第几次触发" prop="number" width="120" />
        <el-table-column align="center" label="触发概率(%)" prop="chance" width="120" />
        <el-table-column align="center" label="按钮组" width="160">
          <template #default="scope">
            <el-button
              type="text"
              icon="el-icon-edit"
              size="small"
              class="table-button"
              @click="updateDramaTactic(scope.row)"
            >变更
            </el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作" top="1%">
      <el-form :model="formData" label-position="right" label-width="100px" style="max-height: 80vh;overflow-y: auto;">
        <el-form-item label="发送时机" style="width:30%">
          <el-select
            v-model="formData.send_moment"
            clearable
            filterable
            placeholder="请输入"
            :disabled="type === 'update' ? true : false"
            @change="dramaSendMomentChange"
          >
            <el-option
              v-for="item in dramaTacticSendMomentOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="发送时间:" style="width:30%">
          <el-input v-model.number="formData.second" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="剧本ID:">
          <el-select v-model.number="formData.drama_id" clearable filterable placeholder="请输入">
            <el-option
              v-for="item in dramaOptions"
              :key="item.id"
              :label="`${item.title}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="anchorIdIsShow" label="主播ID:" style="width:30%">
          <el-input v-model.number="formData.anchor_id" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item v-if="numberIsShow" label="第几次触发:" style="width:30%">
          <el-input v-model.number="formData.number" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item v-if="chanceIsShow" label="触发概率(%):" style="width:30%">
          <el-input-number v-model.number="formData.chance" :min="1" :max="100" clearable placeholder="请输入" />
        </el-form-item>

        <el-form-item label="语言:" style="width:75%">
          <el-select v-model="formData.language" clearable filterable placeholder="请输入">
            <el-option
                v-for="item in dramaTacticLanguageOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="状态:" style="width:30%">
          <el-select v-model="formData.state" clearable filterable placeholder="请输入">
            <el-option
              v-for="item in dramaTacticStateOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="contentIsShow" label="内容:">
          <el-input v-model="formData.content" clearable placeholder="请输入" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createDramaTactic,
  deleteDramaTactic,
  deleteDramaTacticByIds,
  updateDramaTactic,
  findDramaTactic,
  getDramaTacticList
} from '@/api/dramaTactic' //  此处请自行替换地址
import {getDramaList} from '@/api/drama' //  此处请自行替换地址
import {findLanguagesAll} from '@/api/languages'
import infoList from '@/mixins/infoList'

const dramaTacticStateOptions = [
  {
    value: 1,
    label: '上线',
  },
  {
    value: 2,
    label: '下线',
  },
]

const dramaTacticSendMomentOptions = [
  {
    value: 1,
    label: 'SayHi后',
  },
  {
    value: 2,
    label: '喜欢主播后',
  },
  {
    value: 3,
    label: '详情页切换主播照片后',
  },
  {
    value: 4,
    label: '拨打视频后',
  },
  {
    value: 5,
    label: '发送私信后',
  },
  {
    value: 6,
    label: '注册成功后',
  },
  {
    value: 7,
    label: '第二天访问后',
  },
  {
    value: 8,
    label: '第三天访问后',
  },
]
export default {
  name: 'DramaTactic',
  mixins: [infoList],
  data() {
    return {
      dramaOptions: [],
      dramaTacticSendMomentOptions: dramaTacticSendMomentOptions,
      dramaTacticStateOptions: dramaTacticStateOptions,
      dramaTacticLanguageOptions: [],
      listApi: getDramaTacticList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      contentIsShow: true,
      anchorIdIsShow: true,
      numberIsShow: true,
      chanceIsShow: true,
      multipleSelection: [],
      formData: {
        content: '',
        drama_id: 0,
        category: 0,
        second: 1,
        send_moment: 0,
        anchor_id: 0,
        state: 1,
        number: 0,
        chance: 0,
        language:'en',
      }
    }
  },
  async created() {
    await this.getLanguagesAllList()
    await this.getDramaList()
    await this.getTableData()
  },
  methods: {
    checkEnterSendMoment: function() {
      if (this.type === 'update') {
        return true
      } else {
        return false
      }
    },
    dramaSendMomentChange: function() {
      switch (this.formData.send_moment) {
        case 1:
          this.contentIsShow = true
          this.anchorIdIsShow = true
          this.numberIsShow = false
          this.chanceIsShow = false
          break
        case 2:
          this.contentIsShow = false
          this.anchorIdIsShow = false
          this.numberIsShow = true
          this.chanceIsShow = true
          break
        case 3:
          this.contentIsShow = false
          this.anchorIdIsShow = false
          this.numberIsShow = true
          this.chanceIsShow = true
          break
        case 4:
          this.contentIsShow = false
          this.anchorIdIsShow = false
          this.numberIsShow = true
          this.chanceIsShow = true
          break
        case 5:
          this.contentIsShow = false
          this.anchorIdIsShow = false
          this.numberIsShow = true
          this.chanceIsShow = true
          break
        case 6:
          this.contentIsShow = false
          this.anchorIdIsShow = true
          this.numberIsShow = false
          this.chanceIsShow = false
          break
        case 7:
          this.contentIsShow = false
          this.anchorIdIsShow = true
          this.numberIsShow = false
          this.chanceIsShow = false
          break
        case 8:
          this.contentIsShow = false
          this.anchorIdIsShow = true
          this.numberIsShow = false
          this.chanceIsShow = false
          break
        default:
      }
    },
    formaDramaTacticLanguage:function (code) {
      let lang
      this.dramaTacticLanguageOptions.forEach(function(element) {
        if (code === element.value) {
          lang = element.label
          return lang
        }
      })
      return lang
    },
    formaDramaList: function(bool) {
      let text
      this.dramaOptions.forEach(function(element) {
        if (bool === element.id) {
          text = element.appName
          return text
        }
      })
      return text
    },
    async getDramaList() {
      const res = await getDramaList()
      this.dramaOptions = res.data.list
    },
    async getLanguagesAllList() {
      const res = await findLanguagesAll()
      let resList = res.data || []
      let lList = []
      resList.forEach(item=>{
        lList.push({
          value: item.code,
          label: item.en_name,
        })
      })
      this.dramaTacticLanguageOptions = lList
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteDramaTactic(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
        this.multipleSelection.map(item => {
          ids.push(item.id)
        })
      const res = await deleteDramaTacticByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateDramaTactic(row) {
      const res = await findDramaTactic({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.redramaTactic
        this.dialogFormVisible = true
        this.dramaSendMomentChange()
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        content: '',
        drama_id: 0,
        category: 0,
        second: 1,
        send_moment: 0,
        anchor_id: 0,
        state: 1,
        number: 0,
      }
    },
    async deleteDramaTactic(row) {
      const res = await deleteDramaTactic({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createDramaTactic(this.formData)
          break
        case 'update':
          res = await updateDramaTactic(this.formData)
          break
        default:
          res = await createDramaTactic(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    }
  },
}
</script>

<style>
</style>

