<template>
  <div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
      </div>

      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" label="ID" prop="id" width="250" />
        <el-table-column align="center" label="日期" width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="剧本ID" prop="drama_id" width="120" />
        <el-table-column align="center" label="父级ID" prop="parent_id" width="120">
          <template #default="scope">{{ formaDramaDetailOptions(scope.row.parent_id) }}</template>
        </el-table-column>
        <el-table-column align="center" label="类型" prop="type" width="120">
          <template #default="scope">{{ formaDramaTypeOptionsPartner(scope.row.type) }}</template>
        </el-table-column>
        <el-table-column align="center" label="剧本内容" prop="content" width="120" />
        <el-table-column align="center" label="时间间隔" prop="interval" width="120" />
        <el-table-column align="center" label="排序" prop="order_id" width="120" />
        <el-table-column align="center" label="按钮组">
          <template #default="scope">
            <el-button
              v-if="scope.row.type === 5||scope.row.type === 6"
              type="text"
              icon="el-icon-plus"
              size="small"
              class="table-button"
              @click="addDramaDetail(scope.row)"
            >添加子节点
            </el-button>
            <el-button
              type="text"
              icon="el-icon-edit"
              size="small"
              class="table-button"
              @click="updateDramaDetail(scope.row)"
            >变更
            </el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" label-position="right" label-width="80px">
        <el-form-item label="剧本ID:">
          <el-input v-model.number="formData.drama_id" disabled placeholder="请输入" />
        </el-form-item>
        <el-form-item label="父节点ID" style="width:30%">
          <el-cascader
            v-model="formData.parent_id"
            style="width:100%"
            :options="dramaDetailTreeOptions"
            :props="{ checkStrictly: true,label:'content',value:'id',disabled:'disabled',emitPath:false}"
            :show-all-levels="false"
            filterable
          />
        </el-form-item>
        <el-form-item label="类型" style="width:30%">
          <el-select v-model="formData.type" clearable filterable placeholder="类型" @change="dramaTypeChange">
            <el-option
              v-for="item in dramaTypeOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时间间隔:">
          <el-input-number v-model.number="formData.interval" :min="1" :max="100000" clearable placeholder="请输入" />
        </el-form-item>

        <el-form-item label="排序:">
          <el-input-number v-model.number="formData.order_id" :min="1" :max="100000" clearable placeholder="请输入" />
        </el-form-item>

        <el-form-item label="剧本内容:">
          <el-input v-model="formData.content" clearable placeholder="请输入" :disabled="contentDisabled" type="textarea" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createDramaDetail,
  deleteDramaDetail,
  deleteDramaDetailByIds,
  updateDramaDetail,
  findDramaDetail,
  getDramaDetailTree,
  getDramaDetailFindAll
} from '@/api/dramaDetail' //  此处请自行替换地址
import infoList from '@/mixins/infoList'

const dramaTypeOptions = [
  {
    value: 1,
    label: '文本',
  },
  {
    value: 2,
    label: '图片',
  },
  {
    value: 3,
    label: '视频',
  },
  {
    value: 4,
    label: '呼叫',
  },
  {
    value: 5,
    label: '问题',
  },
  {
    value: 6,
    label: '回答',
  },
]

export default {
  name: 'DramaDetail',
  mixins: [infoList],
  data() {
    return {
      dramaTypeOptions: dramaTypeOptions,
      listApi: getDramaDetailFindAll,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      params: [],
      dramaDetailTreeOptions: [],
      contentDisabled: false,
      formData: {
        drama_id: 0,
        type: 0,
        content: '',
        interval: 0,
        order_id: 0,
        parent_id: 0,
      }
    }
  },
  async created() {
    await this.getDramaDetailOptions()
    this.searchInfo.drama_id = Number(this.$route.params.drama_id)
    this.formData.drama_id = Number(this.$route.params.drama_id)
    await this.getTableData()
  },
  methods: {
    formaDramaDetailOptions: function(bool) {
      let text
      this.dramaDetailTreeOptions.forEach(function(element) {
        if (bool === element.id) {
          text = element.content
          return text
        }
      })
      return text
    },
    async getDramaDetailOptions() {
      const res = await getDramaDetailTree({ 'drama_id': this.$route.params.drama_id })
      this.dramaDetailTreeOptions = res.data.list
      this.dramaDetailTreeOptions.unshift({ id: 0, content: '顶级' })
    },
    dramaTypeChange() {
      switch (this.formData.type) {
        case 2:
          this.formData.content = ''
          this.contentDisabled = true
          break
        case 3:
          this.formData.content = ''
          this.contentDisabled = true
          break
        case 4:
          this.formData.content = ''
          this.contentDisabled = true
          break
        case 6:
          this.formData.content = ''
          this.contentDisabled = false
          break
        default:
          this.formData.content = ''
          this.contentDisabled = false
      }
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteDramaDetail(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
        this.multipleSelection.map(item => {
          ids.push(item.id)
        })
      const res = await deleteDramaDetailByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        await this.getTableData()
        await this.getDramaDetailOptions()
      }
    },
    async addDramaDetail(row) {
      this.type = 'create'
      this.dialogFormVisible = true
      this.formData.parent_id = row.id
      this.formData.drama_id = this.$route.params.drama_id
    },
    async updateDramaDetail(row) {
      const res = await findDramaDetail({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.redramaDetail
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        drama_id: 0,
        type: 0,
        content: '',
        interval: 0,
        order_id: 0,
        parent_id: 0,
      }
    },
    async deleteDramaDetail(row) {
      const res = await deleteDramaDetail({
        id: row.id,
        type: row.type,
        parent_id: row.parent_id,
        drama_id: row.drama_id
      })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        await this.getTableData()
        await this.getDramaDetailOptions()
      }
    },
    async enterDialog() {
      this.formData.drama_id = Number(this.$route.params.drama_id)
      let res
      switch (this.type) {
        case 'create':
          res = await createDramaDetail(this.formData)
          break
        case 'update':
          res = await updateDramaDetail(this.formData)
          break
        default:
          res = await createDramaDetail(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        await this.getTableData()
        await this.getDramaDetailOptions()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    }
  },
}
</script>

<style>
</style>

