<template>
  <div>
    <el-row :gutter="15">
      <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="100px">
        <el-col :span="24">
          <el-form-item label="场景" prop="scenes">
            <el-select
              v-model="formData.scenes"
              placeholder="请选择场景"
              clearable
              :style="{width: '100%'}"
              @change="getAnchorWeight()"
            >
              <el-option
                v-for="(item, index) in scenesOptions"
                :key="index"
                :label="item.label"
                :value="item.value"
                :disabled="item.disabled"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="在线" prop="online">
            <el-input-number v-model="formData.online" placeholder="在线" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="忙线" prop="busy">
            <el-input-number v-model="formData.busy" placeholder="忙线" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="离线" prop="offline">
            <el-input-number v-model="formData.offline" placeholder="离线" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="印度" prop="in">
            <el-input-number v-model="formData.in" placeholder="印度" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="菲律宾" prop="ph">
            <el-input-number v-model="formData.ph" placeholder="菲律宾" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="越南" prop="vn">
            <el-input-number v-model="formData.vn" placeholder="越南" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="LV1" prop="lv1">
            <el-input-number v-model="formData.lv1" placeholder="LV1" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="LV2" prop="lv2">
            <el-input-number v-model="formData.lv2" placeholder="LV2" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="LV3" prop="lv3">
            <el-input-number v-model="formData.lv3" placeholder="LV3" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="LV4" prop="lv4">
            <el-input-number v-model="formData.lv4" placeholder="LV4" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="真主播" prop="real">
            <el-input-number v-model="formData.real" placeholder="真主播" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="假主播" prop="fake">
            <el-input-number v-model="formData.fake" placeholder="假主播" :step="1" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item size="large">
            <el-button type="primary" @click="submitForm">提交</el-button>
          </el-form-item>
        </el-col>
      </el-form>
    </el-row>
    <el-row>
      <el-col :span="2">
        <el-button type="primary" size="medium" @click="refresh"> 刷新列表</el-button>
      </el-col>
      <el-col :span="20">
        <el-table :data="tableData" style="width: 100%">
          <el-table-column
            prop="anchor_id"
            label="主播ID"
            width="180"
          />
          <el-table-column
            label="头像"
            width="180"
          >
            <template #default="scope">
              <el-avatar :src="scope.row.avatar" />
            </template>
          </el-table-column>
          <el-table-column
            prop="score"
            label="分数"
            width="180"
          />
          <el-table-column
            label="权重"
            width="180"
          >
            <template #default="scope">
              <el-input-number v-model="scope.row.weight" />
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            width="180"
          >
            <template #default="scope">
              <el-button @click="updateAnchorRoleWeight(scope.row)">保存</el-button>
            </template>
          </el-table-column>
        </el-table>
        <el-pagination v-show="page.total > 0"
                       background
                       :current-page.sync="page.page"
                       :page-sizes="[10, 50, 100, 1000, 2000, 5000]"
                       :page-size="page.pageSize"
                       layout="sizes, prev, pager, next"
                       :total="page.total"
                       @size-change="handleSizeChange"
                       @current-change="handleCurrentChange"
        />
      </el-col>
    </el-row>
  </div>
</template>
<script>
import {
  updateAnchorWeight,
  getAnchorWeight,
  getAnchorWeightList
} from '@/api/anchorWeight'
import infoList from '@/mixins/infoList'
import { updateAnchorRoleWeight } from '../../../api/anchorWeight' //  此处请自行替换地址

export default {
  mixins: [infoList],
  props: [],
  data() {
    return {
      page: {
        page: 1,
        pageSize: 10,
        total: 0,
      },
      formData: {
        scenes: 1,
        online: 0,
        busy: 0,
        offline: 0,
        in: 0,
        ph: 0,
        vn: 0,
        lv1: 0,
        lv2: 0,
        lv3: 0,
        lv4: 0,
        real: 0,
        fake: 0,
      },
      tableData: [],
      rules: {
        scenes: [{
          required: true,
          message: '请选择场景',
          trigger: 'change'
        }],
        online: [{
          required: true,
          message: '在线',
          trigger: 'blur'
        }],
        busy: [{
          required: true,
          message: '忙线',
          trigger: 'blur'
        }],
        offline: [{
          required: true,
          message: '离线',
          trigger: 'blur'
        }],
        in: [{
          required: true,
          message: '印度',
          trigger: 'blur'
        }],
        ph: [{
          required: true,
          message: '菲律宾',
          trigger: 'blur'
        }],
        vn: [{
          required: true,
          message: '越南',
          trigger: 'blur'
        }],
        lv1: [{
          required: true,
          message: 'LV1',
          trigger: 'blur'
        }],
        lv2: [{
          required: true,
          message: 'LV2',
          trigger: 'blur'
        }],
        lv3: [{
          required: true,
          message: 'LV3',
          trigger: 'blur'
        }],
        lv4: [{
          required: true,
          message: 'LV4',
          trigger: 'blur'
        }],
        real: [{
          required: true,
          message: '真主播',
          trigger: 'blur'
        }],
        fake: [{
          required: true,
          message: '假主播',
          trigger: 'blur'
        }],
      },
      scenesOptions: [{
        'label': '印度用户付费前',
        'value': 1
      }, {
        'label': '印度用户付费后',
        'value': 2
      }, {
        'label': '非印度用户付费前',
        'value': 3
      }, {
        'label': '非印度用户付费后',
        'value': 4
      }],
    }
  },
  computed: {},
  watch: {},
  async created() {
    await this.getAnchorWeight()
  },
  mounted() {
  },
  methods: {
    async submitForm() {
      this.$refs['elForm'].validate(async valid => {
        if (!valid) return
        await updateAnchorWeight(this.formData).then(() => {
          this.$message({
            message: '更新数据成功!',
            type: 'success'
          })
        })
      })
    },
    async updateAnchorRoleWeight(info) {
      await updateAnchorRoleWeight({ scenes: this.formData.scenes, user_id: info.anchor_id, weight: info.weight }).then(() => {
        this.$message({
          message: '更新主播数据成功!',
          type: 'success'
        })
      })
    },
    resetForm() {
      this.$refs['elForm'].resetFields()
    },
    async getAnchorWeight() {
      await getAnchorWeight({ scenes: this.formData.scenes }).then((resp) => {
        this.formData = resp.data.awInfo
      })
      this.refresh()
    },
    refresh() {
      this.page.page = 1
      this.getAnchorWeightList()
    },
    async getAnchorWeightList() {
      this.page.scenes = this.formData.scenes
      await getAnchorWeightList(this.page).then((resp) => {
        this.tableData = resp.data.list
        this.page.total = resp.data.total
      })
    },
    handleSizeChange(val) {
      this.page.pageSize = val
      this.getAnchorWeightList()
    },
    handleCurrentChange(val) {
      this.page.page = val
      this.getAnchorWeightList()
    }
  }
}

</script>
<style>
</style>
