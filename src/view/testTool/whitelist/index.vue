<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="用户ID">
          <el-input v-model="searchInfo.user_id"></el-input>
        </el-form-item>

        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" type="success" icon="el-icon-plus" @click="addWhite">增加</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        style="width: 100%"
        :data="tableData"
        max-height="590"
        row-key="id">
        <el-table-column align="center" label="用户ID" prop="user_id" width="180"/>
        <el-table-column align="center" label="用户昵称" prop="nickname" min-width="120" show-overflow-tooltip/>
        <el-table-column align="center" label="时间" prop="date" min-width="120"/>
        <el-table-column align="center" label="操作" min-width="140">
          <template #default="scope">
            <el-popconfirm
              confirm-button-text="确定"
              cancel-button-text="取消"
              @confirm="deleteWhiteList(scope.row)"
              title="确定要删除吗?"
            >
              <template #reference>
                <el-button type="text" icon="el-icon-delete" size="small">删除</el-button>
              </template>
            </el-popconfirm>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeListNormal"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import {getWhiteList, addWhiteList, delWhiteList} from '@/api/users' //  此处请自行替换地址
  import infoList from '@/mixins/infoList'

  export default {
    mixins: [infoList],
    data() {
      return {
        searchInfo: {},
        listApi: getWhiteList,
      }
    },
    async created() {
      await this.getTableData()
    },
    methods: {
      deleteWhiteList(row) {
        delWhiteList({user_id: row.user_id}).then(res => {
          if (res.code === 0) {
            this.$message.success(res.msg)
            this.getTableData()
          }
        })
      },
      onReset() {
        this.searchInfo = {}
        this.getTableData()
      },
      addWhite() {
        this.$messageBox.prompt("请输入用户ID", "提示", {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputPattern: /^\d{1,10}$/,
          inputErrorMessage: '请输入用户ID(数字,长度1-10)',
        }).then(({value}) => {
          addWhiteList({user_id: parseInt(value)}).then(res => {
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.getTableData()
            }
          })
        })
      },
      onSubmit() {
        this.page = 1
        this.getTableData()
      },
      getSummaries({columns, data}) {
        // 获取表头和数据
        // :show-summary="this.searchInfo.group_range.length > 0"
        // :summary-method="getSummaries"
        let labelList = []
        let vList = []
        columns.forEach((column, i) => {
          labelList.push(column.label)
          vList.push(column.property)
        })
        console.log(JSON.stringify(labelList))
        console.log(JSON.stringify(vList))
        return []
      },
    },
  }
</script>

<style lang="scss" scoped>

</style>
