<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="UID">
          <el-input v-model="searchInfo.uid" placeholder="UID" />
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">Submit</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">Reset</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">add</el-button>
        <el-popover v-model:visible="deleteVisible" placement="top" width="160">
          <p>确定要删除吗？</p>
          <div style="text-align: right; margin-top: 8px;">
            <el-button size="mini" type="text" @click="deleteVisible = false">取消</el-button>
            <el-button size="mini" type="primary" @click="onDelete">确定</el-button>
          </div>
          <template #reference>
            <el-button icon="el-icon-delete" size="mini" style="margin-left: 10px;" :disabled="!multipleSelection.length">del</el-button>
          </template>
        </el-popover>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="center" label="Date" width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="APPID" prop="app_id" width="120" />
        <el-table-column align="center" label="uid" prop="uid" width="120" />
        <el-table-column align="center" label="nickname" prop="nickname" width="120" />
        <el-table-column align="center" label="operate">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updatePersonalProfile(scope.row)">change</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">delete</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="dialog">
      <el-form :model="formData" label-position="right" label-width="80px">
        <el-form-item label="APPID:">
          <el-input v-model.number="formData.app_id" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="uid:">
          <el-input v-model.number="formData.uid" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="image:" style="width:100%">
          <el-upload
            :action="`${path}/files/createFiles`"
            :headers="{ 'x-token': token }"
            list-type="picture-card"
            :file-list="formData.image"
            :on-success="handlePhotoAlbumsSuccess"
            :multiple="false"
            accept=".jpg, .jpeg, .png, .gif"
            :on-remove="handlePhotoAlbumsRemove"
          >
            <i class="el-icon-plus" />
          </el-upload>
        </el-form-item>

        <el-form-item label="video:" style="width:100%">
          <el-upload
            :action="`${path}/files/createVideo`"
            :headers="{ 'x-token': token }"
            list-type="picture-card"
            :file-list="formData.video"
            :on-success="handleAlbumVideoSuccess"
            :on-preview="handleVideoVisible"
            :multiple="false"
            accept=".mp4"
            :on-remove="handleAlbumVideoRemove"
          >
            <i class="el-icon-plus" />
          </el-upload>
        </el-form-item>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">close</el-button>
          <el-button size="small" type="primary" @click="enterDialog">confirm</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog
      v-model="videoVisible"
      title="Notice"
      width="397px"
      top="0"
      destroy-on-close
      center
    >
      <video
        :src="videoUrl"
        class="avatar video-avatar"
        controls="controls"
      >
        您的浏览器不支持视频播放
      </video>
    </el-dialog>
  </div>
</template>

<script>
import {
  createPersonalProfile,
  deletePersonalProfile,
  deletePersonalProfileByIds,
  updatePersonalProfile,
  findPersonalProfile,
  getPersonalProfileList
} from '@/api/personalProfile' //  此处请自行替换地址
const path = import.meta.env.VITE_BASE_API
import infoList from '@/mixins/infoList'
import { mapGetters } from 'vuex'
export default {
  name: 'PersonalProfile',
  mixins: [infoList],
  data() {
    return {
      videoVisible: false,
      videoUrl: '',
      path: path,
      avatarFileList: [],
      photoAlbumsFileList: [],
      listApi: getPersonalProfileList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        app_id: 0,
        uid: 0,
        image: [],
        video: [],
      }
    }
  },
  async created() {
    await this.getTableData()
  },
  methods: {
    handleAlbumVideoRemove(file, fileList) {
      this.formData.video = []
      fileList.forEach((item, i) => {
        this.formData.video.push(item)
      })
    },
    handleAlbumVideoSuccess(response, file, fileList) {
      this.formData.video = []
      fileList.forEach((item, i) => {
        if (item.response) {
          this.formData.video.push(item.response.data)
        } else {
          this.formData.video.push(item)
        }
      })
    },
    async handlePhotoAlbumsSuccess(response, file, fileList) {
      this.formData.image = []
      fileList.forEach((item, i) => {
        if (item.response) {
          this.formData.image.push(item.response.data)
        } else {
          this.formData.image.push(item)
        }
      })
    },
    handlePhotoAlbumsRemove(file, fileList) {
      this.formData.image = []
      fileList.forEach((item, i) => {
        this.formData.image.push(item)
      })
    },
    handleVideoVisible(file) {
      this.videoVisible = true
      this.videoUrl = file.video
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deletePersonalProfile(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
        this.multipleSelection.map(item => {
          ids.push(item.id)
        })
      const res = await deletePersonalProfileByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updatePersonalProfile(row) {
      const res = await findPersonalProfile({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.repersonalProfile
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        app_id: 0,
        uid: 0,
        image: [],
        video: [],
      }
    },
    async deletePersonalProfile(row) {
      const res = await deletePersonalProfile({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createPersonalProfile(this.formData)
          break
        case 'update':
          res = await updatePersonalProfile(this.formData)
          break
        default:
          res = await createPersonalProfile(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    }
  },
}
</script>

<style>
</style>

