<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="产品名称">
          <el-select filterable multiple collapse-tags v-model="searchInfo.app_id_range" clearable>
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="`${item.app_name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="searchInfo.info_type">
            <el-option
              v-for="item in infoTypeOptions"
              :key="item"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="支付方式">
          <el-select v-model="searchInfo.payment" clearable>
            <el-option
              v-for="item in payWaysList"
              :key="item"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="状态">
          <el-select v-model="searchInfo.state" clearable>
            <el-option
              v-for="item in stateOptions"
              :key="item"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="用户ID">
          <el-input v-model="searchInfo.user_id" placeholder="请输入用户ID"></el-input>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="订单号">
          <el-input v-model="searchInfo.order_no" placeholder="请输入订单号"></el-input>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchInfo.date_range"
            :shortcuts="dateRangeShortcuts"
            type="datetimerange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="text" :icon="queryTxtIcon" @click="queryTxtChange">{{ queryTxt }}</el-button>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="tableData"
        style="width: 100%"
        :data="tableData"
        row-key="id"
        max-height="590"
      >
        <el-table-column align="center" label="日期" prop="created_at" width="180"/>
        <el-table-column align="center" label="类型" prop="info_type" width="120" show-overflow-tooltip/>
        <el-table-column align="center" label="应用" prop="app_name" width="120"/>
        <el-table-column align="center" label="用户ID" prop="user_id" width="120"/>
        <el-table-column align="center" label="版本号" prop="version" width="120"/>
        <el-table-column align="center" label="商品名称" prop="product_name" width="120"/>
        <el-table-column align="center" label="支付方式" prop="payment" width="120"/>
        <el-table-column align="center" label="状态" prop="state" width="120"/>
        <el-table-column align="center" label="支付金额" prop="payment_amount" width="120"/>
        <el-table-column align="center" label="货币种类" prop="currency_type" width="120"/>
        <el-table-column align="center" label="订单号" prop="order_no" min-width="270"/>
        <el-table-column align="center" label="完成支付时间" prop="payment_at" min-width="180"/>
        <el-table-column align="center" label="回传时间" prop="call_at" min-width="180"/>
        <el-table-column align="center" label="回传请求数据" prop="call_req" min-width="180" show-overflow-tooltip/>
        <el-table-column align="center" label="回传结果" prop="call_data" width="120" show-overflow-tooltip/>
        <el-table-column align="center" label="AdID" prop="ad_id" min-width="320" show-overflow-tooltip/>
        <el-table-column align="center" label="AfInfo" prop="af_info" min-width="180" show-overflow-tooltip/>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import {getOrderRecordsList} from '@/api/orderRecords' //  此处请自行替换地址
  import infoList from '@/mixins/infoList'
  import orderRecordsMixins from '@/mixins/orderRecords'

  export default {
    mixins: [infoList, orderRecordsMixins],
    data() {
      return {
        searchInfo: {
          info_type: -1,
          app_id_range: [],
          date_range: [],
          payment: null,
          state: null,
          user_id: null,
        },
        listApi: getOrderRecordsList,
      }
    },
    async created() {
      await this.getTableData(this.nFunc, this.tableDataFormat)
    },
    methods: {
      onReset() {
        this.searchInfo = {
          info_type: -1,
          app_id_range: [],
          date_range: [],
          payment: null,
          state: null,
          user_id: null,
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      onSubmit() {
        this.page = 1
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      getSummaries({columns, data}) {
        // 获取表头和数据
        // :show-summary="this.searchInfo.group_range.length > 0"
        // :summary-method="getSummaries"
        let labelList = []
        let vList = []
        columns.forEach((column, i) => {
          labelList.push(column.label)
          vList.push(column.property)
        })
        console.log(JSON.stringify(labelList))
        console.log(JSON.stringify(vList))
        return []
      },
      tableDataFormat() {
        this.tableData.map((item, i, data) => {
          data[i].created_at = this.formatDate(item.created_at)
          data[i].payment_at = this.formatDate(item.payment_at)
          data[i].payment = this.formatPayment(item.payment)
          data[i].call_at = this.formatDate(item.call_at)
          data[i].app_name = this.formatAppName(item.app_id)
          data[i].info_type = this.formatInfoType(item.info_type)
          data[i].state = this.formatStateOptions(item.state)
        })
      },
      handleDownload() {
        let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
        let res = this.getTableHeaderProp(tableColumns)
        let tHeader = res[0]
        let filterV = res[1]
        this.handleProgressLoading()
        import('@/utils/excel').then((excel) => {
          const data = this.formatJson(filterV, this.tableData)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.filename,
            autoWidth: this.autoWidth,
            bookType: this.bookType,
          })
          this.progressLoading.close()
        })
      },
      sizeChange(v) {
        this.pageSize = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      pageChange(v) {
        this.page = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
    },
  }
</script>

<style lang="scss" scoped>

</style>
