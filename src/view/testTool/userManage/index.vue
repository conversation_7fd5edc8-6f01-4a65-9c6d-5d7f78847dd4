<template>
  <div>
    <el-row class="gva-table-box">
      <el-col :span="12" :offset="1">
        <div>
          <el-row :gutter="5" type="flex" style="align-items: center">
            <el-col :span="7">
              <el-input v-model.number="formData.user_id" clearable placeholder="请输入用户ID" />
            </el-col>
            <el-col :span="3">
              <el-popconfirm
                confirm-button-text="确定"
                cancel-button-text="取消"
                title="确定要删除吗?"
                @confirm="create"
              >
                <template #reference>
                  <el-button size="small" type="danger">删除</el-button>
                </template>
              </el-popconfirm>
            </el-col>
            <el-col :span="3">
              <el-popconfirm
                confirm-button-text="确定"
                cancel-button-text="取消"
                title="确定要删除这个审核用户吗?"
                @confirm="execDeleteAuditUser"
              >
                <template #reference>
                  <el-button size="small" type="primary">审核删除</el-button>
                </template>
              </el-popconfirm>
            </el-col>
            <el-col :span="3">
              <el-popconfirm
                confirm-button-text="确定"
                cancel-button-text="取消"
                title="确定要删除这个用户所属设备的语言设置吗?"
                @confirm="execDeleteDeviceLanguage"
              >
                <template #reference>
                  <el-button size="small" type="primary">设备语言删除</el-button>
                </template>
              </el-popconfirm>

            </el-col>
          </el-row>
          <el-table
            ref="multipleTable"
            style="width: 100%"
            tooltip-effect="dark"
            :data="tableData"
            row-key="id"
            max-height="590"
            stripe
            fit
            @selection-change="handleSelectionChange"
          >
            <el-table-column align="center" label="用户ID" prop="user_id" />
            <el-table-column align="center" label="用户昵称" prop="nickname" />
            <el-table-column align="center" label="用户头像" min-width="50" style="line-height: 50px" prop="avatar">
              <template #default="scope">
                <el-image
                  v-show="scope.row.avatar"
                  style="width: 40px; height: 40px;line-height: 40px;overflow:visible"
                  :src="scope.row.avatar"
                  :lazy="true"
                  :preview-src-list="[scope.row.avatar]"
                />
              </template>
            </el-table-column>

          </el-table>
          <div class="gva-pagination">
            <el-pagination
              v-show="total > 0"
              layout="total, sizes, prev, pager, next, jumper"
              :current-page="page"
              :page-size="pageSize"
              :page-sizes="pageSizeList"
              :total="total"
              @current-change="handleCurrentChange"
              @size-change="handleSizeChange"
            />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import {
  createUserdRecords,
  deleteUserdRecords,
  deleteUserdRecordsByIds,
  updateUserdRecords,
  findUserdRecords,
  getUserdRecordsList
} from '@/api/userd_records' //  此处请自行替换地址
import { deleteAuditUser, deleteDeviceLanguage } from '@/api/users'
import infoList from '@/mixins/infoList'

export default {
  name: 'UserdRecords',
  mixins: [infoList],
  data() {
    return {
      listApi: getUserdRecordsList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        user_id: null,
        nickname: '',
        avatar: '',
      }
    }
  },
  async created() {
    await this.getTableData()
  },
  methods: {
    execDeleteAuditUser() {
      deleteAuditUser({ user_id: this.formData.user_id + '' }).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg)
        }
      })
    },
    execDeleteDeviceLanguage() {
      deleteDeviceLanguage({ id: this.formData.user_id }).then(res => {
        if (res.code === 0) {
          this.$message.success(res.msg)
        }
      })
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteUserdRecords(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
      this.multipleSelection.map(item => {
        ids.push(item.id)
      })
      const res = await deleteUserdRecordsByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateUserdRecords(row) {
      const res = await findUserdRecords({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.reuserdRecords
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        user_id: null,
        nickname: '',
        avatar: '',
      }
    },
    async deleteUserdRecords(row) {
      const res = await deleteUserdRecords({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async create() {
      if (this.formData.user_id === null || this.formData.user_id <= 0) {
        this.$message({
          type: 'warning',
          message: '请输入用户ID'
        })
        return
      }
      let res
      res = await createUserdRecords(this.formData)
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createUserdRecords(this.formData)
          break
        case 'update':
          res = await updateUserdRecords(this.formData)
          break
        default:
          res = await createUserdRecords(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    }
  },
}
</script>

<style>
</style>

