<template>
  <div>

    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="日期">
          <el-date-picker
            v-model="searchInfo.date_range"
            type="datetimerange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          ></el-date-picker>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="searchInfo.upload_type" clearable placeholder="上传类型">
            <el-option
              v-for="item in fileTypeOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="文件名">
          <el-input v-model="searchInfo.file_name" placeholder="文件名" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" type="success" icon="el-icon-refresh" @click="showUploadS3">上传</el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-table
      ref="multipleTable"
      style="width: 100%"
      tooltip-effect="dark"
      border
      :data="tableData"
      row-key="ip"
    >
      <el-table-column align="center" label="上传时间" prop="c" width="180">
        <template #default="{row}">
          {{ formatDate(row.created_at) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="文件名称" prop="file_name" min-width="120"/>
      <el-table-column align="center" label="文件大小" prop="file_size" width="120">
        <template #default="{row}">
          {{ formatFileSize(row.file_size) }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="上传类型" prop="upload_type" width="120">
        <template #default="{row}">
          {{ fileTypeDict[row.upload_type] }}
        </template>
      </el-table-column>
      <el-table-column align="center" label="预览" prop="url" width="120">
        <template #default="{row}">
          <el-image
            style="width: 40px;height: 40px"
            :src="row.url"
            :preview-src-list="[row.url]"
            fit="cover"
          />
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="220">
        <template #default="{row}">
          <!--点击按钮复制文件url-->
          <el-button size="mini" type="primary" icon="el-icon-document" @click="copyText(row.url)">复制URL</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="gva-pagination">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="pageSizeList"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>

    <el-dialog v-model="dialogUploadShow" title="上传" top="10px" center>
      <el-form
        :model="formData"
        label-position="left"
        size="small"
        style="max-height: 80vh;overflow-y: auto;"
      >
        <el-form-item label="类型">
          <el-radio-group v-model="formData.type">
            <el-radio :label="1">icon</el-radio>
            <el-radio :label="2">Dilse</el-radio>
            <el-radio :label="3">other</el-radio>

          </el-radio-group>
        </el-form-item>
        <el-form-item label="文件">
          <el-upload
            ref="uploadRef"
            action="https://jsonplaceholder.typicode.com/posts/"
            multiple
            :auto-upload="false"
          >
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>

            <el-button class="ml-6" type="success" @click="submitUpload">上传</el-button>

            <template #tip>
              <div class="el-upload__tip">
                文件大小不能超过100MB
              </div>
            </template>
          </el-upload>
        </el-form-item>

      </el-form>


    </el-dialog>

  </div>
</template>

<script>
import infoList from '@/mixins/infoList'
import {S3Client, PutObjectCommand} from "@aws-sdk/client-s3";
import {amazonS3Token} from "@/api/amazonS3";
import {batchCreateUploadRecord, getUploadRecordList} from "@/api/uploadRecord";

export default {
  name: 'upload_tool',
  mixins: [infoList],
  data() {
    return {
      listApi: getUploadRecordList,
      dialogUploadShow: false,
      sizeUnit : ['Bytes', 'KB', 'MB', 'GB', 'TB'],
      fileTypeOptions: [
        {label: 'icon', value: 1},
        {label: 'Dilse', value: 2},
        {label: 'other', value: 3},
      ],
      fileTypeDict: {},
      formData: {
        type: 1,
      },
    }
  },
  created() {
    this.fileTypeDict = this.fileTypeOptions.reduce((acc, cur) => {
      acc[cur.value] = cur.label
      return acc
    }, {})
    this.getTableData()
  },
  methods: {
    formatFileSize(fileSizeInBytes) {
      const sizeType = parseInt(
        Math.floor(Math.log(fileSizeInBytes) / Math.log(1024)).toString(),
      );
      console.log('sizeType', sizeType);
      const size = (fileSizeInBytes / Math.pow(1024, sizeType)).toFixed(2);
      return size + this.sizeUnit[sizeType];
    },
    showUploadS3() {
      this.dialogUploadShow = true
    },
    onSubmit() {
      this.page = 1
      this.getTableData()
    },
    getFileExt(name) {
      let ext = ''
      if (name.lastIndexOf('.') !== -1) {
        ext = name.substr(name.lastIndexOf('.') + 1)
      }
      return ext
    },
    guid() {
      return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        var r = Math.random() * 16 | 0,
          v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
      });
    },
    uuidFileName(ext) {
      if (ext) {
        return this.guid() + '.' + ext
      } else {
        return this.guid()
      }
    },
    async submitUpload() {
      let fileList = this.$refs.uploadRef.uploadFiles
      let fLen = fileList.length
      if (fLen === 0) {
        this.$message.error('请选择文件')
        return
      }
      let valid = true
      fileList.forEach(f => {
        if (f.size > 100 * 1024 * 1024) {
          console.log(f)
          valid = false
          this.$message.error('文件大小不能超过100MB')
        }
      })
      if (!valid) {
        return
      }
      let keyPrefix = ''
      let bucketName = 's3.oklove.top'
      switch (this.formData.type) {
        case 1:
          keyPrefix = 'icon/'
          break
        case 2:
          bucketName = 'dilse.xyz'
          keyPrefix = 'downloads/'
          break
        case 3:
          keyPrefix = 'other/'
          break
      }

      let tokenRes = await amazonS3Token()
      if (tokenRes.code === 0) {
        let tokenData = tokenRes.data
        let s3Client = new S3Client({
          credentials: {
            accessKeyId: tokenData.AccessKeyId,
            secretAccessKey: tokenData.SecretAccessKey,
            sessionToken: tokenData.SessionToken,
          },
          region: tokenData.region,
        })
        let fileReq = []
        for (const f of fileList) {
          console.log('for (const f of fileList)', f)
          let fileExt = this.getFileExt(f.name)
          let fileName = f.name
          if (this.formData.type === 2 && fileExt === 'html') {
            keyPrefix=''
          }
          if (this.formData.type === 3) {
            fileName = this.uuidFileName(fileExt)
          }
          let objKey = `${keyPrefix}${fileName}`
          console.log('objKey', objKey)
          console.log('f.raw', f.raw)
          const command = new PutObjectCommand({
            Bucket: bucketName,
            Key: objKey,
            Body: f.raw,
            ACL: "public-read",
            ContentType: f.raw.type,
          });
          const sendData = await s3Client.send(command)
          console.log(sendData)
          if (sendData.$metadata.httpStatusCode === 200) {
            fileReq.push({
              upload_type: this.formData.type,
              file_name: fileName,
              file_size: f.size,
              url: `https://${bucketName}/${objKey}`,
            })
          }
        }
        let batRes = await batchCreateUploadRecord({data: fileReq})
        if (batRes.code === 0) {
          this.$refs.uploadRef.uploadFiles = []
        } else {
          this.$message.error(batRes.msg)
        }
      } else {
        this.$message.error(tokenRes.msg)
      }


    },

  },
}
</script>

