<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item v-if="unionIdBool" label="产品名称">
          <el-select filterable v-model="searchInfo.app_id" clearable>
            <el-option
                v-for="item in appList"
                :key="item.id"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工会">
          <el-select v-model="searchInfo.dst_union_id" clearable>
            <el-option
                v-for="item in uniosList"
                :key="item.id"
                :label="`${item.name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="主播ID">
          <el-input v-model="searchInfo.dst_id"></el-input>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="用户ID">
          <el-input v-model="searchInfo.src_id"></el-input>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="通话频道">
          <el-input v-model="searchInfo.project"></el-input>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="通话类型">
          <el-select collapse-tags v-model="searchInfo.src_free" placeholder="通话类型">
            <el-option label="全部" :value="null"/>
            <el-option label="免费通话" :value="true"/>
            <el-option label="付费通话" :value="false"/>
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="主叫方">
          <el-select filterable clearable v-model="searchInfo.call_type" placeholder="主叫方">
            <el-option label="用户" :value="1"/>
            <el-option label="主播" :value="2"/>
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="通话状态">
          <el-select collapse-tags v-model="searchInfo.state" placeholder="通话状态">
            <el-option
                v-for="item in connectedReportsStateOptions"
                :key="`${item.label}(${item.value})`"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="时间范围">
          <el-date-picker
              v-model="searchInfo.date_range"
              type="datetimerange"
              :shortcuts="dateRangeShortcuts"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DDTHH:mm:ssZ"
              :default-time="[new Date(2022, 1, 1, 0, 0, 0),new Date(2022, 1, 1, 23, 59, 59)]"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="text" :icon="queryTxtIcon" @click="queryTxtChange">{{ queryTxt }}</el-button>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
          style="width: 100%"
          :data="tableData"
          max-height="590"
          row-key="id">
        <el-table-column align="center" label="日期" min-width="160" fixed>
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="产品" min-width="120" show-overflow-tooltip>
          <template #default="scope">{{ formatAppName(scope.row.app_id) }}</template>
        </el-table-column>

        <el-table-column align="center" label="用户ID" prop="src_id" min-width="100"/>

        <el-table-column align="center" label="主播ID" prop="dst_id" min-width="100"/>
        <el-table-column align="center" label="类型" prop="is_transfer" min-width="100">
          <template #default="scope">
            {{formateIsTransfer(scope.row.is_transfer)}}
          </template>
        </el-table-column>

        <el-table-column align="center" label="主叫方" min-width="140">
          <template #default="scope">
            <el-tag v-if="scope.row.call_type === 1" type="warning">用户</el-tag>
            <el-tag v-else-if="scope.row.call_type === 2" type="success">主播</el-tag>
            <el-tag v-else type="danger">未知</el-tag>
          </template>
        </el-table-column>

        <el-table-column align="center" label="是否合作方" min-width="140">
          <template #default="scope">
            <el-tag v-if="scope.row.partner === 1">是</el-tag>
            <el-tag v-else-if="scope.row.partner === 0" type="danger">否</el-tag>
            <el-tag v-else type="danger">未知</el-tag>
          </template>
        </el-table-column>

        <el-table-column align="center" label="通话频道" prop="project" min-width="320"/>
        <el-table-column align="center" label="通话状态" min-width="160">
          <template #default="scope">
            <!--1通话生成 2通话进行 3拒绝接听 4通话结束-->
            <el-tag v-if="scope.row.state === 1" type="info">通话生成</el-tag>
            <el-tag v-else-if="scope.row.state === 2" type="success">通话进行</el-tag>
            <el-tag v-else-if="scope.row.state === 3" type="warning">拒绝接听</el-tag>
            <el-tag v-else-if="scope.row.state === 4">通话结束</el-tag>
            <el-tag v-else type="danger">未知</el-tag>
          </template>
        </el-table-column>

        <el-table-column align="center" label="通话时长" min-width="100">
          <template #default="scope">
            {{ formateSeconds(scope.row.duration) }}
          </template>
        </el-table-column>

        <el-table-column align="center" label="通话收入" prop="price" min-width="90"/>
        <el-table-column align="center" label="主播收入" prop="dst_income" min-width="90"/>
        <el-table-column align="center" label="真实收入" prop="dst_final_income" min-width="90"/>
        <el-table-column align="center" label="通话开始时间" min-width="160">
          <template #default="scope">{{ formatDate(scope.row.begin_ts) }}</template>
        </el-table-column>
        <el-table-column align="center" label="通话结束时间" min-width="160">
          <template #default="scope">{{ formatDate(scope.row.end_ts) }}</template>
        </el-table-column>
        <el-table-column align="center" label="通话类型" min-width="160">
          <template #default="scope">
            <el-tag v-if="scope.row.src_free" type="info">免费通话</el-tag>
            <el-tag v-else type="success">付费通话</el-tag>
          </template>
        </el-table-column>

        <el-table-column align="center" label="用户端APP" min-width="120">
          <template #default="scope">{{ formatAppName(scope.row.src_app_id) }}</template>
        </el-table-column>
        <el-table-column align="center" label="主播端APP" min-width="120">
          <template #default="scope">{{ formatAppName(scope.row.dst_app_id) }}</template>
        </el-table-column>
        <el-table-column align="center" label="用户UtmSource" prop="src_utm_source" min-width="140"/>
        <el-table-column align="center" label="用户平台标识" prop="src_platform" min-width="140"/>
        <el-table-column align="center" label="用户版本" prop="src_version" min-width="140"/>
        <el-table-column align="center" label="主播UtmSource" prop="dst_utm_source" min-width="140"/>
        <el-table-column align="center" label="主播平台标识" prop="dst_platform" min-width="140"/>
        <el-table-column align="center" label="主播版本" prop="dst_version" min-width="140"/>

        <el-table-column align="center" label="结束方" prop="close_id" min-width="180"/>
        <el-table-column align="center" label="礼物收益" prop="gift_income" min-width="140"/>
        <el-table-column align="center" label="主播等级" prop="dst_levels" min-width="80"/>

        <el-table-column align="center" label="工作状态" min-width="80">
          <template #default="scope">
            <el-tag v-if="scope.row.dst_working_condition === 1" type="warning">兼职</el-tag>
            <el-tag v-else-if="scope.row.dst_working_condition === 2" type="success">全职</el-tag>
            <el-tag v-else type="danger">未知</el-tag>
          </template>
        </el-table-column>

        <el-table-column align="center" label="工会" min-width="120">
          <template #default="scope">{{ formatUniosName(scope.row.dst_union_id) }}</template>
        </el-table-column>
        <el-table-column align="center" label="官方扣除" prop="deduction" min-width="120"/>
        <el-table-column align="center" label="结算比率" prop="settle_ratio" min-width="90"/>
        <el-table-column align="center" label="扣费折扣" prop="cost_ratio" min-width="90"/>
        <el-table-column align="center" label="备注日志" min-width="90"/>
        <el-table-column align="center" label="呼叫方式" min-width="90"/>
        <el-table-column align="center" label="评价" min-width="90"/>
        <el-table-column align="center" label="标签" min-width="90"/>
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {getList} from '@/api/connectedReports' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import connectedReportsMixins from '@/mixins/connectedReports'

export default {
  mixins: [infoList, connectedReportsMixins],
  data() {
    return {
      unionIdBool: null,
      searchInfo: {
        app_id: null,
        dst_union_id: null,
        dst_id: null,
        src_id: null,
        src_free: null,
        state: null,
      },
      listApi: getList,
    }
  },
  async created() {
    this.unionIdBool = this.userInfo.union_id === 0
    this.pageSize = 100
    await this.getTableData()
  },
  methods: {
    onReset() {
      this.searchInfo = {
        app_id: null,
        dst_union_id: null,
        dst_id: null,
        src_id: null,
        src_free: null,
        state: null,
      }
      this.pageSize = 100
      this.getTableData()
    },
    onSubmit() {
      this.page = 1
      this.pageSize = 100
      this.getTableData()
    },
    handleDownload() {
      this.handleProgressLoading()
      const that = this
      import('@/utils/excel').then((excel) => {
        let tHeader = ["日期", "产品", "用户ID", "主播ID", "类型",
          "主叫方", "是否合作方", "通话频道", "通话状态",
          "通话时长", "通话收入", "主播收入", "通话开始时间",
          "通话结束时间", "通话类型", "用户端APP", "主播端APP",
          "用户UtmSource", "用户平台标识", "用户版本", "主播UtmSource",
          "主播平台标识", "主播版本", "结束方", "礼物收益",
          "主播等级", "工作状态", "工会", "官方扣除",
          "结算比率", "扣费折扣", "备注日志", "呼叫方式",
          "评价", "标签",]
        let filterV = ["created_at", "AppName", "src_id", "dst_id", "is_transfer",
          "call_type", "partner", "project", "state",
          "duration", "price", "dst_income", "begin_ts",
          "end_ts", "src_free", "SrcAppName", "DstAppName",
          "src_utm_source", "src_platform", "src_version", "dst_utm_source",
          "dst_platform", "dst_version", "close_id", "gift_income",
          "dst_levels", "dst_working_condition", "UniosName", "deduction",
          "settle_ratio", "cost_ratio", "emp", "emp",
          "emp", "emp", ]
        const listData = JSON.parse(JSON.stringify(this.tableData))
        // 数据处理
        listData.map((currentValue, index, array) => {
          // currentValue -> 数组中正在处理的当前元素
          // index -> 数组中正在处理的当前元素的索引
          // array -> 指向map方法被调用的数组
          array[index].created_at = that.formatDate(currentValue.created_at)
          array[index].AppName = that.formatAppName(currentValue.app_id)
          array[index].call_type = that.formateCoRCallType(currentValue.call_type)
          array[index].partner = that.formateCoRpartner(currentValue.partner)
          array[index].state = that.formateCoRstate(currentValue.state)
          array[index].duration = that.formateSeconds(currentValue.duration)
          array[index].begin_ts = that.formatDate(currentValue.begin_ts)
          array[index].end_ts = that.formatDate(currentValue.end_ts)
          array[index].src_free = currentValue.src_free ? "免费通话" : "付费通话"
          array[index].SrcAppName = that.formatAppName(currentValue.src_app_id)
          array[index].DstAppName = that.formatAppName(currentValue.dst_app_id)
          array[index].UniosName = that.formatUniosName(currentValue.dst_union_id)
          array[index].is_transfer = that.formateIsTransfer(currentValue.is_transfer)
          array[index].dst_working_condition = that.formateCoRDstWorkingCondition(currentValue.dst_working_condition)
          array[index].emp = ""
        })

        const data = this.formatJson(filterV, listData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },

  },
}
</script>

<style lang="scss" scoped>

</style>
