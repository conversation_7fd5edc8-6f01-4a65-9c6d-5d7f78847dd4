<template>
  <div>
    <!--搜索框-->
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="IP">
          <el-input v-model="searchInfo.ip" placeholder="IP地址,多个IP以逗号分割" size="120px"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!--列表展示-->
    <el-table
      ref="multipleTable"
      style="width: 100%"
      tooltip-effect="dark"
      :data="tableData"
      row-key="ip"
      @selection-change="handleSelectionChange"
    >
      <el-table-column align="left" label="IP" prop="ip" min-width="120" />
      <el-table-column align="left" label="库一国家码" prop="country_code" min-width="120" />
      <el-table-column align="left" label="库二国家码" prop="country_iso_code" min-width="120" />
      <el-table-column align="left" label="库二国家名" prop="country_name" min-width="120" />
      <el-table-column align="left" label="库二城市名" prop="city_name" min-width="120" />
      <el-table-column align="left" label="库二运营商" prop="isp" min-width="120" />
      <el-table-column align="left" label="库三国家名" prop="api_country" min-width="120" />
      <el-table-column align="left" label="库三国家码" prop="api_country_code" min-width="120" />
      <el-table-column align="left" label="库三城市名" prop="api_city" min-width="120" />
      <el-table-column align="left" label="库三运营商" prop="api_isp" min-width="120" />
      <el-table-column align="left" label="库三组织" prop="api_org" min-width="120" />
    </el-table>
    <div class="gva-pagination">
      <el-pagination
        layout="total, sizes, prev, pager, next, jumper"
        :current-page="page"
        :page-size="pageSize"
        :page-sizes="pageSizeList"
        :total="total"
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
      />
    </div>
  </div>
</template>

<script>
import {
  getIPQueryList
} from '@/api/ipQuery' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
export default {
  name: 'IPQuery',
  mixins: [infoList],
  data() {
    return {
      listApi: getIPQueryList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        ip: '',
        country_code: '',
        country_iso_code: '',
        country_name: '',
        city_name: '',
        isp: '',
        api_country: '',
        api_country_code: '',
        api_city: '',
        api_isp: '',
        api_org: '',
      }
    }
  },
  async created() {
    await this.getTableData()
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
  },
}
</script>

