<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="主播ID">
          <el-input v-model="searchInfo.id" clearable placeholder="ID" />
        </el-form-item>
        <el-form-item label="昵称">
          <el-input v-model="searchInfo.nickname" clearable placeholder="昵称" />
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchInfo.is_selected" clearable placeholder="状态">
            <el-option
              v-for="item in isSelectedOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="parseInt(userInfo.app_id) === 0" label="APP:">
          <el-select v-model="searchInfo.appId" clearable filterable placeholder="AppId">
            <el-option
                v-for="item in appList"
                :key="item"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="国籍">
          <el-select v-model="searchInfo.countryCode" clearable placeholder="国籍">
            <el-option
              v-for="item in countryCodeOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" label="主播ID" prop="id" width="120" fixed />
        <el-table-column align="center" label="昵称" prop="nickname" min-width="160" />
        <el-table-column align="center" label="头像" min-width="50" style="line-height: 50px" prop="avatar">
          <template #default="scope">
            <el-image
              v-show="scope.row.avatar"
              style="width: 40px; height: 40px;line-height: 40px;overflow:visible"
              :src="scope.row.avatar"
              :lazy="true"
              :preview-src-list="[scope.row.avatar]"
            />
          </template>
        </el-table-column>

        <el-table-column align="center" label="性别" prop="status" width="120">
          <template #default="scope">{{ formatSex(scope.row.gender) }}</template>
        </el-table-column>
        <el-table-column align="center" label="国家" width="120">
          <template #default="scope">{{ formatCountryCode(scope.row.countryCode) }}</template>
        </el-table-column>

        <el-table-column v-if="parseInt(userInfo.app_id) === 0" align="center" label="最后登录时间" width="180">
          <template #default="scope">{{ formatDate(scope.row.lastLoginAt) }}</template>
        </el-table-column>

        <el-table-column align="center" label="状态" min-width="50" prop="partnerUsersId">
          <template #default="scope">
            <view v-if="scope.row.partnerUsersId===0">未选</view>
            <view v-else style="color: #409EFF">已选</view>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="120">
          <template #default="scope">
            <el-button type="text" icon="el-icon-view" size="small" class="table-button" @click="showUsers(scope.row)">
              查看
            </el-button>
            <el-button
              v-if="scope.row.partnerUsersId===0"
              type="text"
              icon="el-icon-check"
              size="small"
              class="table-button"
              @click="addPartnerUsers(scope.row)"
            >加入
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog
      v-model="dialogFormVisible"
      :before-close="closeDialog"
      :fullscreen="false"
      :lock-scroll="false"
      :append-to-body="true"
      title="弹窗操作"
      top="1%"
      width="60%"
    >
      <el-form
        ref="usersForm"
        :model="formData"
        :inline="true"
        label-position="top"
        label-width="85px"
        style="max-height: 80vh;overflow-y: auto;"
      >
        <el-form-item label="昵称:" style="width:30%">
          <el-input v-model="formData.nickname" clearable placeholder="请输入" />
        </el-form-item>

        <el-form-item label="生日:" style="width:30%">
          <el-date-picker v-model="formData.birthday" type="date" style="width:100%" placeholder="选择日期" clearable />
        </el-form-item>

        <el-form-item label="国籍:" style="width:30%">
          <el-select v-model="formData.countryCode" clearable placeholder="国籍">
            <el-option
              v-for="item in countryCodeOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="性别" style="width:30%">
          <el-select v-model="formData.gender" clearable placeholder="性别">
            <el-option
              v-for="item in genderOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="身高(cm)" style="width:30%">
          <el-input v-model="formData.height" clearable placeholder="请输入身高数值" />
        </el-form-item>

        <el-form-item label="体重(kg)" style="width:30%">
          <el-input v-model="formData.weight" clearable placeholder="请输入体重数值" />
        </el-form-item>

        <el-form-item label="标签:" style="width:60%">
          <el-select
            v-model="formData.labels"
            multiple
            filterable
            allow-create
            clearable
            default-first-option
            placeholder="请选择标签"
          >
            <el-option-group
              v-for="group in personalityLabelsOptions"
              :key="group.id"
              :label="group.name"
            >
              <el-option
                v-for="item in group.children"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-option-group>
          </el-select>
        </el-form-item>

        <el-form-item label="个人简介" style="width:100%">
          <el-input v-model="formData.introduction" type="textarea" />
        </el-form-item>

        <el-form-item label="头像:" style="width:100%">
          <draggable
            v-model="avatarFileList"
            class="el-upload-list el-upload-list--picture-card"
            item-key="id"
            @start="isDragging = true"
            @end="isDragging = false"
          >
            <template #item="{element}">
              <div class="el-upload-list__item">
                <img :src="element.url" class="el-upload-list__item-thumbnail">
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="handleImagePreview(element)"> <!--handlePictureCardPreview(element)-->
                    <i class="el-icon-zoom-in" />
                  </span>
                </span>
              </div>
            </template>
          </draggable>
        </el-form-item>

        <el-form-item label="相册:" style="width:100%">

          <draggable
            v-model="formData.photoAlbums"
            class="el-upload-list el-upload-list--picture-card"
            item-key="id"
            @start="isDragging = true"
            @end="isDragging = false"
            @update="updatePhotoAlbumsList"
          >
            <template #item="{element}">
              <div class="el-upload-list__item">
                <img :src="element.url" class="el-upload-list__item-thumbnail">
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="handleImagePreview(element)"> <!--handlePictureCardPreview(element)-->
                    <i class="el-icon-zoom-in" />
                  </span>
                </span>
              </div>
            </template>
          </draggable>
        </el-form-item>

        <el-form-item label="相册视频（多个）:" style="width:100%">

          <draggable
            v-model="formData.albumVideo"
            class="el-upload-list el-upload-list--picture-card"
            item-key="id"
            @start="isDragging = true"
            @end="isDragging = false"
          >
            <template #item="{element}">
              <div class="el-upload-list__item">
                <img :src="element.url" class="el-upload-list__item-thumbnail">
                <span class="el-upload-list__item-actions">
                  <span class="el-upload-list__item-preview" @click="handleVideoVisible(element)">
                    <i class="el-icon-zoom-in" />
                  </span>
                </span>
              </div>
            </template>
          </draggable>

        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="previewImageVisible" title="图片预览" width="50%" center>
      <img :src="previewImagePath" class="avatar video-avatar">
    </el-dialog>
    <el-dialog
      v-model="videoVisible"
      title="Notice"
      width="397px"
      top="0"
      destroy-on-close
      center
    >
      <video
        :src="videoUrl"
        class="avatar video-avatar"
        controls="controls"
      >
        您的浏览器不支持视频播放
      </video>
    </el-dialog>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { ElLoading } from 'element-plus'

const path = import.meta.env.VITE_BASE_API
import draggable from 'vuedraggable'
import {
  deleteUsers,
  findUsers,
  getAnchorTankList
} from '@/api/users' //  此处请自行替换地址
import {
  createPartnerUsers
} from '@/api/partnerUsers' //  此处请自行替换地址
import { getAppXesList } from '@/api/appXes'
import { getPersonalityLabelsTree } from '@/api/personality_labels'
import infoList from '@/mixins/infoList'
import CustomPic from '@/components/customPic/index.vue'
import ChooseImg from '@/components/chooseImg/index.vue'
import warningBar from '@/components/warningBar/warningBar.vue'

const genderOptions = [
  {
    value: 1,
    label: '未知',
  },
  {
    value: 2,
    label: '女',
  },
  {
    value: 3,
    label: '男',
  },
]

const isSelectedOptions = [
  {
    value: 1,
    label: '未选',
  },
  {
    value: 2,
    label: '已选',
  },
]

const roleOptions = [
  {
    value: 2,
    label: '主播',
  },
]

const countryCodeOptions = [
  {
    value: 'IN',
    label: 'India',
  },
  {
    value: 'SA',
    label: 'Saudi Arabia',
  },
  {
    value: 'PH',
    label: 'Philippines',
  },
  {
    value: 'BR',
    label: 'Brazil',
  },
  {
    value: 'CN',
    label: 'China',
  },
  {
    value: 'CO',
    label: 'Colombia',
  },
  {
    value: 'KR',
    label: 'South Korea',
  },
  {
    value: 'US',
    label: 'America',
  },
  {
    value: 'ID',
    label: 'Indonesia',
  },
  {
    value: 'IQ',
    label: 'Iraq',
  },
  {
    value: 'MY',
    label: 'Malaysia',
  },
  {
    value: 'KW',
    label: 'Kuwait',
  },
  {
    value: 'MX',
    label: 'Mexico',
  },
  {
    value: 'MA',
    label: 'Morocco',
  },
  {
    value: 'PK',
    label: 'Pakistan',
  },
  {
    value: 'RU',
    label: 'Russian',
  },
  {
    value: 'TH',
    label: 'Thailand',
  },
  {
    value: 'TR',
    label: 'Turkey',
  },
  {
    value: 'UK',
    label: 'United Kingdom',
  },
  {
    value: 'UA',
    label: 'Ukraine',
  },
  {
    value: 'VN',
    label: 'Viet Nam',
  },
]

export default {
  name: 'Users',
  components: { draggable },
  mixins: [infoList],
  data() {
    return {
      appListOptions: [],
      isDragging: false,
      roleOptions: roleOptions,
      previewImageVisible: false,
      previewImagePath: '',
      videoVisible: false,
      videoUrl: '',
      path: path,
      avatarFileList: [],
      photoAlbumsFileList: [],
      searchInfo: { role: 2 },
      listApi: getAnchorTankList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      personalityLabelsOptions: [],
      countryCodeOptions: countryCodeOptions,
      isSelectedOptions: isSelectedOptions,
      genderOptions: genderOptions,
      formData: {
        albumVideo: [],
        callVideo: [],
        constellationVideo: [],
        photoAlbums: [],
        userTags: [],
        nickname: '',
        avatar: '',
        birthday: '',
        countryCode: '',
        anchorFake: 1,
        role: 2,
        avoid: 0,
      }
    }
  },
  async created() {
    await this.getAppList()
    await this.getTableData()
    await this.getPersonalityLabelsList()
  },
  computed: {
    ...mapGetters('user', ['token', 'userInfo']),
  },
  methods: {
    formaAppList: function(bool) {
      let text
      this.appListOptions.forEach(function(element) {
        if (bool === element.id) {
          text = element.appName
          return text
        }
      })
      return text
    },
    async getAppList() {
      const res = await getAppXesList()
      this.appListOptions = res.data.list
    },
    async addPartnerUsers(row) {
      this.handleProgressLoading()
      const res = await createPartnerUsers({ user_id: row.id })
      this.getTableData()
      this.progressLoading.close()
    },
    onChangeUserTags(e) {
      this.formData.userTags.forEach(function(value, index, array) {
        if (value.id === e.id) {
          array[index].checked = !e.checked
        }
      })
    },
    async updatePhotoAlbumsList(e) {
      const newIndex = e.newIndex// 新位置下标
      const oldIndex = e.oldIndex// 原始位置下标
    },
    async getPersonalityLabelsList() {
      const res = await getPersonalityLabelsTree()
      this.personalityLabelsOptions = res.data.list
    },
    handleImageRemove(file, fileList) {
      this.formData.avatar = ''
      this.avatarFileList = []
    },
    handleImageSuccess(res) {
      const that = this
      that.avatarFileList = []
      const { data } = res
      if (data.url) {
        that.formData.avatar = data.url
        that.avatarFileList.push(data)
      }
      this.progressLoading.close()
    },
    handlePhotoAlbumsRemove(file) {
      this.formData.photoAlbums = this.formData.photoAlbums.filter(function(age) {
        return age.url !== file.url
      })
    },
    handleProgressLoading() {
      this.progressLoading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
    },
    async handlePhotoAlbumsSuccess(response, file, fileList) {
      fileList.forEach((item, i) => {
        if (item.response.data !== undefined) {
          this.formData.photoAlbums.push(item.response.data)
        } else {
          this.formData.photoAlbums.push(item)
        }
      })

      // 数据去重
      const temp = []
      this.formData.photoAlbums.forEach(function(a) {
        const check = temp.every(function(b) {
          return a.key !== b.key
        })
        check ? temp.push(a) : []
      })
      this.formData.photoAlbums = temp

      // 关闭loading
      this.progressLoading.close()
    },
    handleCallVideoSuccess(response, file, fileList) {
      fileList.forEach((item, i) => {
        if (item.response.data !== undefined) {
          if (item.response.data.url !== undefined) {
            this.formData.callVideo.push(item.response.data)
          }
        } else {
          this.formData.callVideo.push(item)
        }
      })

      // 数据去重
      const temp = []
      this.formData.callVideo.forEach(function(a) {
        const check = temp.every(function(b) {
          return a.url !== b.url
        })
        check ? temp.push(a) : []
      })
      this.formData.callVideo = temp

      // 关闭loading
      this.progressLoading.close()
    },
    handleCallVideoRemove(file, fileList) {
      this.formData.callVideo = this.formData.callVideo.filter(function(age) {
        return age.url !== file.url
      })
    },
    handleConstellationVideoSuccess(response, file, fileList) {
      const that = this
      that.formData.constellationVideo = []
      fileList.forEach((item, i) => {
        if (item.response !== undefined) {
          that.formData.constellationVideo.push(item.response.data)
        } else {
          that.formData.constellationVideo.push(item)
        }
      })

      // 关闭loading
      this.progressLoading.close()
    },
    handleConstellationVideoRemove(file, fileList) {
      this.formData.constellationVideo = []
      fileList.forEach((item, i) => {
        this.formData.constellationVideo.push(item)
      })
    },

    handleAlbumVideoSuccess(response, file, fileList) {
      this.formData.albumVideo = []
      fileList.forEach((item, i) => {
        if (item.response.data !== undefined) {
          this.formData.albumVideo.push(item.response.data)
        } else {
          this.formData.albumVideo.push(item)
        }
      })

      // 数据去重
      const temp = []
      this.formData.albumVideo.forEach(function(a) {
        const check = temp.every(function(b) {
          return a.url !== b.url
        })
        check ? temp.push(a) : []
      })
      this.formData.albumVideo = temp

      // 关闭loading
      this.progressLoading.close()
    },
    handleAlbumVideoRemove(file, fileList) {
      this.formData.albumVideo = this.formData.albumVideo.filter(function(age) {
        return age.url !== file.url
      })
    },

    handleVideoVisible(file) {
      this.videoVisible = true
      this.videoUrl = file.video
    },

    handleImagePreview(file) {
      // 把url地址赋值到本地
      this.previewImagePath = file.url
      // 打开预览图片的对话框
      this.previewImageVisible = true
    },

    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    async showUsers(row) {
      const that = this
      const res = await findUsers({ id: row.id })
      that.type = 'update'
      that.avatarFileList = []
      if (res.code === 0) {
        that.formData = res.data.reusers
        let uAvatar = ''
        if (that.formData.avatar !== '') {
          uAvatar = that.formData.avatar
        } else {
          uAvatar = that.formData.avatarVerify
        }
        that.avatarFileList = [{name: '', url: uAvatar}]
        that.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.avatarFileList = []
      this.dialogFormVisible = false
      this.formData = {
        albumVideo: [],
        callVideo: [],
        constellationVideo: [],
        photoAlbums: [],
        nickname: '',
        avatar: '',
        birthday: '',
        countryCode: '',
        anchorFake: 1,
        role: 2,
        avoid: 0,
      }
    },
    async deleteUsers(row) {
      const res = await deleteUsers({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    }
  },
}
</script>

<style lang="scss" scoped>
  .upload-container {
    width: 100%;
    display: flex;
    align-items: flex-end;

    .draggable-container {
      display: flex;

      .image-container {
        position: relative;
        width: 152px;
        height: 86px;
        margin-right: 17px;

        img {
          width: 100%;
          height: 100%;
        }

        .close {
          position: absolute;
          top: 4px;
          right: 4px;
          background: rgba(255, 255, 255, 1);
          border-radius: 50%;
          font-weight: 600;
        }
      }
    }

    .el-button {
      background: #EEF5FF;
      border: 1px solid #CFE3FD;
      color: #5E9FF8;
    }
  }
</style>
