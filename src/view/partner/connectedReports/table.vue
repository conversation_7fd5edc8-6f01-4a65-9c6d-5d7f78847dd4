<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline" clearable>
        <el-form-item label="Date">
          <el-date-picker
            v-model="searchInfoTime"
            type="daterange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="Start Date"
            end-placeholder="End Date"
            @change="onDaterangeChange"
          />
        </el-form-item>
        <el-form-item label="Anchor ID">
          <el-input v-model="searchInfo.dst_id" placeholder="Anchor ID" clearable />
        </el-form-item>
        <el-form-item v-if="parseInt(userInfo.union_id) === 0" label="Initial ID">
          <el-input v-model="searchInfo.identifier" placeholder="Initial ID" clearable />
        </el-form-item>
        <el-form-item v-if="parseInt(userInfo.union_id) === 0" label="User ID">
          <el-input v-model="searchInfo.src_id" placeholder="User ID" clearable />
        </el-form-item>
        <el-form-item v-if="parseInt(userInfo.app_id) === 0 && parseInt(userInfo.union_id) === 0" label="APP">
          <el-select v-model="searchInfo.app_id" clearable filterable placeholder="AppId">
            <el-option
                v-for="item in appList"
                :key="item"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="parseInt(userInfo.app_id) === 0 && parseInt(userInfo.union_id) === 0" label="Call type">
          <el-select v-model="searchInfo.src_free" clearable placeholder="Call type">
            <el-option
              v-for="item in callType"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="parseInt(userInfo.union_id) === 0" label="Call result">
          <el-select v-model="searchInfo.state" clearable placeholder="Call result">
            <el-option
              v-for="item in callState"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">Search</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">Reset</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">
            Export Excel
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
      >
        <el-table-column align="center" label="Id" prop="id" width="120" />
        <el-table-column align="center" label="Date" width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="App Name"
          prop="app_id"
          width="120"
        >
          <template #default="scope">{{ formaAppList(scope.row.app_id) }}</template>
        </el-table-column>
        <el-table-column align="center" label="Anchor Id" prop="dst_id" width="120" />
        <el-table-column
          v-if="parseInt(userInfo.app_id) === 0 && parseInt(userInfo.union_id) === 0"
          align="center"
          label="Union"
          prop="dst_union_id"
          width="120"
        >
          <template #default="scope">{{ formaUnionList(scope.row.dst_union_id) }}</template>
        </el-table-column>
        <el-table-column
          v-if="parseInt(userInfo.app_id) === 0 && parseInt(userInfo.union_id) === 0"
          align="center"
          label="Anchor Status"
          prop="dst_working_condition"
          width="120"
        >
          <template #default="scope">{{ formatWorkingCondition(scope.row.dst_working_condition) }}</template>
        </el-table-column>
        <el-table-column
          v-if="parseInt(userInfo.app_id) === 0 && parseInt(userInfo.union_id) === 0"
          align="center"
          label="Call Type"
          prop="src_free"
          width="120"
        >
          <template #default="scope">{{ formaCallFreePartner(scope.row.src_free) }}</template>
        </el-table-column>
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Anchor Levels"
          prop="dst_levels"
          width="120"
        />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Initial ID"
          prop="identifier"
          width="200"
        />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="User Id"
          prop="src_id"
          width="120"
        />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Call Initator"
          prop="call_type"
          width="120"
        >
          <template #default="scope">{{ formaCallTypePartner(scope.row.call_type) }}</template>
        </el-table-column>

        <el-table-column align="center" label="Call Result" prop="state" width="120">
          <template v-if="parseInt(userInfo.union_id) !== 0" #default="scope">{{ formaCallStateUnionPartner(scope.row.state) }}</template>
          <template v-else #default="scope">{{ formaCallStatePartner(scope.row.state) }}</template>
        </el-table-column>

        <el-table-column align="center" label="begin_ts" width="180">
          <template #default="scope">{{ formatDate(scope.row.begin_ts) }}</template>
        </el-table-column>
        <el-table-column align="center" label="end_ts" width="180">
          <template #default="scope">{{ formatDate(scope.row.end_ts) }}</template>
        </el-table-column>
        <el-table-column align="center" label="Call Duration" prop="duration" min-width="120" />
        <el-table-column align="center" label="Project" prop="project" min-width="120"/>
        <el-table-column align="center" label="Respond Code" prop="income_all" min-width="140"/>
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {getConnectedReportsList} from '@/api/connectedReports' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import {getAppXesList} from '@/api/appXes'
import {getUnionsList} from '@/api/unions'
import moment from 'moment'
import {ElLoading} from 'element-plus'

const callType = [
  {
    value: 0,
    label: 'ALL',
  },
  {
    value: 1,
    label: 'Paid',
  },
  {
    value: 2,
    label: 'Free',
  }
]

const callState = [
  {
    value: 0,
    label: 'ALL',
  },
  {
    value: 1,
    label: 'generate',
  },
  {
    value: 2,
    label: 'process',
  },
  {
    value: 3,
    label: 'reject',
  },
  {
    value: 4,
    label: 'succeed',
  }
]

export default {
  name: 'ConnectedReports',
  mixins: [infoList],
  data() {
    return {
      listApi: getConnectedReportsList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      downloadLoading: false,
      callType: callType,
      callState: callState,
      searchInfoTime: [],
      multipleSelection: [],
      appListOptions: [],
      unionListOptions: [],
      filename: 'call-data',
      autoWidth: true,
      bookType: 'xlsx',
      formData: {
        app_id: 0,
        src_id: 0,
        dst_id: 0,
        call_type: 0,
        state: 0,
        duration: 0,
        price: 0,
        src_free: false,
        src_app_id: 0,
        src_app: '',
        src_utm_source: '',
        src_platform: 0,
        src_version: '',
        dst_app: '',
        dst_utm_source: '',
        dst_platform: 0,
        dst_version: '',
        project: '',
        close_id: 0,
        video_url: '',
        dst_levels: 0,
        dst_working_condition: 0,
        dst_role: 0,
        dst_union_id: 0,
        partner: 0,
      }
    }
  },
  async created() {
    await this.getUnionList()
    await this.getAppList()
    this.searchInfo.start_at = moment().startOf('month').format('YYYY-MM-DD')
    this.searchInfo.end_at = moment().endOf('month').endOf('month').format('YYYY-MM-DD')
    this.searchInfoTime = [moment().startOf('month').format('YYYY-MM-DD'), moment().endOf('month').endOf('month').format('YYYY-MM-DD')]
    await this.getTableData()
  },
  methods: {
    handleProgressLoading() {
      this.progressLoading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
    },
    handleDownload() {
      this.handleProgressLoading()
      const that = this
      import('@/utils/excel').then((excel) => {
        let tHeader = ['Id', 'Date', 'App Name', 'Anchor Id', 'Union', 'Anchor Status', 'Anchor Levels', 'Initial ID', 'User ID', 'Call Initator', 'Call Type', 'Call Result', 'Call Start Time', 'Call End Time', 'Call Duration', 'Project', 'Respond Code']
        let filterVal = ['id', 'created_at', 'app_id', 'dst_id', 'dst_union_id', 'dst_working_condition', 'dst_levels', 'identifier', 'src_id', 'call_type', 'src_free', 'state', 'begin_ts', 'end_ts', 'duration', 'project', 'income_all']
        const listData = JSON.parse(JSON.stringify(this.tableData))
        // 数据处理
        listData.map(function (currentValue, index, array) {
          // currentValue -> 数组中正在处理的当前元素
          // index -> 数组中正在处理的当前元素的索引
          // array -> 指向map方法被调用的数组
          array[index].created_at = that.formatDate(currentValue.created_at)
          array[index].app_id = that.formaAppList(currentValue.app_id)
          array[index].dst_union_id = that.formaUnionList(currentValue.dst_union_id)
          array[index].dst_working_condition = that.formatWorkingCondition(currentValue.dst_working_condition)
          array[index].call_type = that.formaCallTypePartner(currentValue.call_type)
          array[index].src_free = that.formaCallFreePartner(currentValue.src_free)
          array[index].state = that.formaCallStatePartner(currentValue.state)
          array[index].begin_ts = that.formatDate(currentValue.begin_ts)
          array[index].end_ts = that.formatDate(currentValue.end_ts)
        })

        const data = this.formatJson(filterVal, listData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    onDaterangeChange(valueTime) {
      this.searchInfo.start_at = moment(this.searchInfoTime[0]).format('YYYY-MM-DD')
      this.searchInfo.end_at = moment(this.searchInfoTime[1]).format('YYYY-MM-DD')
    },
    formaAppList: function(bool) {
      let text
      this.appListOptions.forEach(function(element) {
        if (bool === element.id) {
          text = element.appName
          return text
        }
      })
      return text
    },
    async getAppList() {
      const res = await getAppXesList()
      this.appListOptions = res.data.list
    },
    formaUnionList: function(bool) {
      let text
      this.unionListOptions.forEach(function(element) {
        if (bool === element.id) {
          text = element.name
          return text
        }
      })
      return text
    },
    // 工会
    async getUnionList() {
      const res = await getUnionsList({page:1, pageSize: 1000})
      this.unionListOptions = res.data.list
    },
    onReset() {
      this.searchInfo = {}
      this.searchInfoTime = []
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      if (this.searchInfo.src_free === '') {
        this.searchInfo.src_free = null
      }
      this.getTableData()
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        app_id: 0,
        src_id: 0,
        dst_id: 0,
        call_type: 0,
        state: 0,
        duration: 0,
        price: 0,
        src_free: false,
        src_app_id: 0,
        src_app: '',
        src_utm_source: '',
        src_platform: 0,
        src_version: '',
        dst_app: '',
        dst_utm_source: '',
        dst_platform: 0,
        dst_version: '',
        project: '',
        close_id: 0,
        video_url: '',
        dst_levels: 0,
        dst_working_condition: 0,
        dst_role: 0,
        dst_union_id: 0,
        partner: 0,
      }
    },
  },
}
</script>

<style>
</style>

