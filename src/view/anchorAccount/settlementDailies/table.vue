<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="Union">
          <el-select v-model="searchInfo.union_id" clearable filterable placeholder="Union">
            <el-option
                v-for="item in uniosList"
                :key="item.id"
                :label="`${item.name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Anchor ID">
          <el-input v-model="searchInfo.user_id" class="cs-search-input"/>
        </el-form-item>
        <el-form-item label="Date">
          <el-date-picker
              v-model="searchInfo.date_range"
              type="daterange"
              :shortcuts="dateRangeShortcuts"
              start-placeholder="Start Date"
              end-placeholder="End Date"
          />
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">Search</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">Reset</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="handleDownload">
            Export Excel
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
          ref="tableData"
          style="width: 100%"
          tooltip-effect="dark"
          :data="tableData"
          max-height="590"
          row-key="id"
      >
        <el-table-column align="center" label="Date" prop="date" min-width="120"/>
        <el-table-column align="center" label="User Id" prop="user_id" min-width="120"/>
        <el-table-column align="center" label="复核" min-width="140">
          <template #default="scope">
            <el-button size="small" type="primary" @click="goAnchorIncomeDetail(scope.row)">复核</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="Blocked status" prop="BlockedStatus" min-width="120"/>
        <el-table-column align="center" label="Agency" prop="Agency" min-width="120"/>
        <el-table-column align="center" label="Level" prop="levels" min-width="120"/>
        <el-table-column align="center" label="CountryCode" prop="country_code" min-width="120"/>
        <el-table-column align="center" label="Number Of Call Me" prop="push_paid_call_numbers" min-width="160"/>
        <el-table-column align="center" label="Number Of Answered" prop="answer_paid_call_numbers" min-width="160"/>
        <el-table-column align="center" label="Answering Rate" prop="AnsweringRate" min-width="160"/>
        <el-table-column align="center" label="Income" prop="Income" min-width="160"/>
        <el-table-column align="center" label="Can Settle" prop="CanSettle" min-width="160"/>
        <el-table-column align="center" label="US Dollar" prop="USDollar" min-width="160"/>
        <el-table-column align="center" label="Agency revenue" prop="Agencyrevenue" min-width="160"/>
        <el-table-column align="center" label="Historical unsettled revenue" prop="Historicalunsettledrevenue"
                         min-width="240"/>
        <el-table-column align="center" label="Paid Call Duration" prop="paid_call_durations_str" min-width="135"/>
        <el-table-column align="center" label="Paid Call Income" prop="paid_call_income" min-width="130"/>
        <el-table-column align="center" label="Free Call Duration" prop="free_call_durations_str" min-width="135"/>
        <el-table-column align="center" label="Free Call Income" prop="free_call_income" min-width="130"/>
        <el-table-column align="center" label="Total Call Income" prop="TotalCallIncome" min-width="130"/>
        <el-table-column align="center" label="Gift Income" prop="live_gift_revenue" min-width="120"/>
        <el-table-column align="center" label="Text Income" prop="message_revenue" min-width="130"/>
        <el-table-column align="center" label="Task Income" prop="award_count" min-width="120"/>
        <el-table-column align="center" label="Program Income" prop="program_income" min-width="160"/>
        <el-table-column align="center" label="Online Duration" prop="online_durations_str" min-width="120"/>
        <el-table-column align="center" label="Super Mode Duration" prop="working_durations_str" min-width="160"/>

      </el-table>
      <div class="gva-pagination">
        <el-pagination
            v-show="total > 0"
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            :total="total"
            @current-change="pageChange"
            @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {getSettlementDailiesList} from '@/api/settlementDailies' //  此处请自行替换地址
import infoList from '@/mixins/infoList'

export default {
  name: 'SettlementDailies',
  mixins: [infoList],
  data() {
    return {
      listApi: getSettlementDailiesList,
      dialogFormVisible: false,
      type: '',
      unionDict: {},
      searchInfoTime: '',
      deleteVisible: false,
      multipleSelection: [],
      unionListOptions: [],
      formData: {
        date: '',
        user_id: 0,
        union_id: 0,
        working_condition: 0,
        state: 0,
        levels: 0,
        push_paid_call_numbers: 0,
        answer_paid_call_numbers: 0,
        paid_call_income: 0,
        free_call_income: 0,
      }
    }
  },
  created() {
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    goAnchorIncomeDetail(row) {
      let mDate = row.date
      let dateRange = this.getDateStartEnd(mDate)
      this.$router.push({
        name: 'anchorIncomeDetail', query: {
          'userId': row.user_id,
          'startTime': dateRange[0],
          'endTime': dateRange[1],
        }
      })
    },
    formatTotalCallIncome(item) {
      let totalCallIncome = 0.0
      totalCallIncome = parseFloat(item.paid_call_income) + parseFloat(item.free_call_income)
      return totalCallIncome.toFixed(1)
    },
    formatHistorical(item) {
      return item.forward_income
    },
    formatAgencyRevenue(item) {
      return (this.formatUSDollar(item) * 0.2).toFixed(2);
    },
    formatUSDollar(item) {
      return (this.formatIncome(item) / 1000.0).toFixed(2);
    },
    formatCanSettle(item) {
      return item.state !== 1 && this.formatIncome(item) >= 6000;
    },
    formatIncome(item) {
      let res = parseFloat(item.paid_call_income) + parseFloat(item.free_call_income) +
          parseFloat(item.live_gift_revenue) + parseFloat(item.message_revenue) + parseFloat(item.award_count) + parseFloat(item.forward_income) + parseFloat(+item.program_income)
      return res.toFixed(1)
    },
    formatAnsweringRate(item) {
      let rate = 0.0
      if (item.answer_paid_call_numbers !== undefined && item.push_paid_call_numbers !== undefined &&
          item.answer_paid_call_numbers > 0 && item.push_paid_call_numbers > 0) {
        rate = item.answer_paid_call_numbers / item.push_paid_call_numbers
      }
      let rateStr = rate.toFixed(2)
      let rateFloat = parseFloat(rateStr) * 100
      return `${rateFloat}%`
    },
    tableDataFormat() {
      this.tableData.map((currentValue, index, array) => {
        array[index].BlockedStatus = this.formatBlockedState(currentValue)
        array[index].Agency = this.formatUniosName(currentValue.union_id)
        array[index].AnsweringRate = this.formatAnsweringRate(currentValue)
        array[index].Income = this.formatIncome(currentValue)
        array[index].CanSettle = this.formatCanSettle(currentValue)
        array[index].USDollar = this.formatUSDollar(currentValue)
        array[index].Agencyrevenue = this.formatAgencyRevenue(currentValue)
        array[index].Historicalunsettledrevenue = this.formatHistorical(currentValue)
        array[index].paid_call_durations_str = this.formateSeconds(currentValue.paid_call_durations)
        array[index].free_call_durations_str = this.formateSeconds(currentValue.free_call_durations)
        array[index].TotalCallIncome = this.formatTotalCallIncome(currentValue)
        array[index].online_durations_str = this.formateSeconds(currentValue.online_durations)
        array[index].working_durations_str = this.formateSeconds(currentValue.working_durations)
      })
    },
    handleDownload() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },

    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
  },
}
</script>

<style>
</style>

