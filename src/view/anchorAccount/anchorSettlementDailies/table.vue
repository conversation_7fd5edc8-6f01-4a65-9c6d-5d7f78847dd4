<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item v-if="parseInt(userInfo.union_id) === 0" label="主播类型">
          <el-select v-model="searchInfo.working_condition" clearable placeholder="主播类型">
            <el-option
              v-for="item in workingCondition"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="parseInt(userInfo.union_id) === 0" label="Union">
          <el-select v-model="searchInfo.union_id" clearable filterable placeholder="Union">
            <el-option
              v-for="item in unionListOptions"
              :key="item.id"
              :label="`${item.name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Anchor ID">
          <el-input v-model="searchInfo.user_id" class="cs-search-input" />
        </el-form-item>
        <el-form-item label="Date">
          <el-date-picker
            v-model="searchInfoTime"
            type="daterange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="Start Date"
            end-placeholder="End Date"
            @change="onDaterangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">Search</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">Reset</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">
            Export Excel
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" label="Date" prop="date" width="120" />
        <el-table-column align="center" label="ID" prop="user_id" width="120" />
        <el-table-column align="center" label="Union" prop="union_id" width="120">
          <template #default="scope">{{ formaUnionList(scope.row.union_id) }}</template>
        </el-table-column>
        <el-table-column align="center" label="Blocked status" prop="state" width="120">
          <template #default="scope">{{ formatBlockedState(scope.row) }}</template>
        </el-table-column>
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Number of calls received"
          prop="push_total_call_numbers"
          min-width="200"
        />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Number of calls answered"
          prop="answer_total_call_numbers"
          min-width="200"
        />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Answered rate"
          prop="connect_rate"
          min-width="120"
        />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Free call duration"
          prop="free_call_durations"
          min-width="190"
        />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Pay call duration"
          prop="paid_call_durations"
          min-width="180"
        />
        <el-table-column align="center" label="Effective call duration" prop="effective_call_duration" min-width="220" />
        <el-table-column align="center" label="Text chat salary" prop="message_revenue" min-width="120" />
        <el-table-column align="center" label="Gift salary" prop="gift_total_revenue" min-width="120" />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Conversion income this week"
          prop="conversion_income"
          min-width="220"
        />
        <el-table-column align="center" label="Online Time" prop="online_durations" width="120" />
        <el-table-column align="center" label="Total salary" min-width="120">
          <template #default="scope">{{ formatTotalSalary(scope.row) }}</template>
        </el-table-column>
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="新手收益"
          prop="award_count"
          width="120"
        />
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" label-position="right" label-width="80px">
        <el-form-item label="日期:">
          <el-input v-model="formData.date" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="用户ID:">
          <el-input v-model.number="formData.user_id" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="工会ID:">
          <el-input v-model.number="formData.union_id" clearable placeholder="请输入" />
        </el-form-item>
        <el-table-column align="center" label="身份" prop="working_condition" width="120">
          <template #default="scope">{{ formatWorkingCondition(scope.row.working_condition) }}</template>
        </el-table-column>
        <el-form-item label="主播状态">
          <el-input v-model.number="formData.state" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="主播等级:">
          <el-input v-model.number="formData.levels" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="在线时长:">
          <el-input v-model.number="formData.online_durations" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="working 状态时长:">
          <el-input v-model.number="formData.working_durations" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="推送付费呼叫次数:">
          <el-input v-model.number="formData.push_paid_call_numbers" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="接听付费呼叫次数:">
          <el-input v-model.number="formData.answer_paid_call_numbers" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="付费通话时长:">
          <el-input v-model.number="formData.paid_call_durations" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="推送免费呼叫次数:">
          <el-input v-model.number="formData.push_free_call_numbers" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="接听的免费呼叫次数:">
          <el-input v-model.number="formData.answer_free_call_numbers" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="免费通话时长:">
          <el-input v-model.number="formData.free_call_durations" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="直播礼物收益:" />
        <el-form-item label="消息礼物收入:" />
        <el-form-item label="消息回复收入:" />
        <el-form-item label="转化付费用户数:">
          <el-input v-model.number="formData.conversion_user_numbers" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="转化收入:" />
        <el-form-item label="付费接听率:" />
        <el-form-item label="免费接听率:" />
        <el-form-item label="付费通话收入:" />
        <el-form-item label="免费通话收入:" />
        <el-form-item label="新手收益:" />
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>

import {
  createAnchorSettlementDailies,
  deleteAnchorSettlementDailies,
  deleteAnchorSettlementDailiesByIds,
  updateAnchorSettlementDailies,
  findAnchorSettlementDailies,
  getAnchorSettlementDailiesList
} from '@/api/anchorSettlementDailies' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import { getUnionsList } from '@/api/unions'
import moment from 'moment'
import { ElLoading } from 'element-plus'

const workingCondition = [
  {
    value: 1,
    label: '兼职',
  },
  {
    value: 2,
    label: '全职',
  },
]
export default {
  name: 'AnchorSettlementDailies',
  mixins: [infoList],
  data() {
    return {
      listApi: getAnchorSettlementDailiesList,
      dialogFormVisible: false,
      type: '',
      searchInfoTime: '',
      deleteVisible: false,
      workingCondition: workingCondition,
      multipleSelection: [],
      unionListOptions: [],
      filename: 'anchor-settlement-data',
      autoWidth: true,
      bookType: 'xlsx',
      formData: {
        date: '',
        user_id: 0,
        union_id: 0,
        working_condition: 0,
        state: 0,
        levels: 0,
        online_durations: 0,
        working_durations: 0,
        push_paid_call_numbers: 0,
        answer_paid_call_numbers: 0,
        paid_call_durations: 0,
        push_free_call_numbers: 0,
        answer_free_call_numbers: 0,
        free_call_durations: 0,
        conversion_user_numbers: 0,
      }
    }
  },
  async created() {
    await this.getUnionList()
    await this.getTableData()
  },
  methods: {
    formatTotalSalary(row) {
      // 文字收入+礼物收入+通话时长*0.0016
      return row.message_revenue + row.gift_total_revenue + (row.paid_call_durations + row.free_call_durations * 0.2) * 0.0016
    },
    formaUnionList: function(bool) {
      let text
      this.unionListOptions.forEach(function(element) {
        if (bool === element.id) {
          text = element.name
          return text
        }
      })
      return text
    },
    // 工会
    async getUnionList() {
      const res = await getUnionsList()
      this.unionListOptions = res.data.list
    },
    handleProgressLoading() {
      this.progressLoading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
    },
    handleDownload() {
      this.handleProgressLoading()
      const that = this
      import('@/utils/excel').then((excel) => {
        let tHeader = []
        let filterVal = []
        if (this.userInfo.union_id !== 0) {
          tHeader = ['Date', 'ID', 'Union', 'Blocked status', 'Effective call duration', 'Text chat salary', 'Gift salary', 'Online Time', 'Total salary']
          filterVal = ['date', 'user_id', 'union_id', 'state', 'effective_call_duration', 'message_revenue', 'gift_total_revenue', 'online_durations', 'total_salary']
        } else {
          tHeader = ['Date', 'ID', 'Union', 'Blocked status', 'Number of calls received', 'Number of calls answered', 'Answered rate', 'Free call duration', 'Pay call duration', 'Effective call duration', 'Text chat salary', 'Gift salary', 'Conversion income this week', 'Online Time', '新手收益']
          filterVal = ['date', 'user_id', 'union_id', 'state', 'push_total_call_numbers', 'answer_total_call_numbers', 'connect_rate', 'free_call_durations', 'paid_call_durations', 'effective_call_duration', 'message_revenue', 'gift_total_revenue', 'conversion_income', 'online_durations', 'award_count']
        }

        const listData = JSON.parse(JSON.stringify(this.tableData))
        // 数据处理
        listData.map(function(currentValue, index, array) {
          // currentValue -> 数组中正在处理的当前元素
          // index -> 数组中正在处理的当前元素的索引
          // array -> 指向map方法被调用的数组
          array[index].working_condition = that.formatWorkingCondition(currentValue.working_condition)
          array[index].state = that.formatBlockedState(currentValue)
          array[index].total_salary = that.formatTotalSalary(currentValue)
        })

        const data = this.formatJson(filterVal, listData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },

    onDaterangeChange(valueTime) {
      this.searchInfo.start_at = moment(this.searchInfoTime[0]).format('YYYY-MM-DD')
      this.searchInfo.end_at = moment(this.searchInfoTime[1]).format('YYYY-MM-DD')
    },
    onReset() {
      this.searchInfo = {}
      this.searchInfoTime = []
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteAnchorSettlementDailies(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
        this.multipleSelection.map(item => {
          ids.push(item.id)
        })
      const res = await deleteAnchorSettlementDailiesByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateAnchorSettlementDailies(row) {
      const res = await findAnchorSettlementDailies({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.reanchorSettlementDailies
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        date: '',
        user_id: 0,
        union_id: 0,
        working_condition: 0,
        state: 0,
        levels: 0,
        online_durations: 0,
        working_durations: 0,
        push_paid_call_numbers: 0,
        answer_paid_call_numbers: 0,
        paid_call_durations: 0,
        push_free_call_numbers: 0,
        answer_free_call_numbers: 0,
        free_call_durations: 0,
        conversion_user_numbers: 0,
      }
    },
    async deleteAnchorSettlementDailies(row) {
      const res = await deleteAnchorSettlementDailies({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createAnchorSettlementDailies(this.formData)
          break
        case 'update':
          res = await updateAnchorSettlementDailies(this.formData)
          break
        default:
          res = await createAnchorSettlementDailies(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    }
  },
}
</script>

<style>
</style>
