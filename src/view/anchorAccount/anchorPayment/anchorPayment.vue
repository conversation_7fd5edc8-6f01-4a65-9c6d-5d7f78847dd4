<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="状态">
          <el-select v-model="searchInfo.state" clearable filterable placeholder="状态">
            <el-option v-for="item in statusList" :key="item.id" :label="`${item.name}(${item.id})`" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间">
          <el-date-picker
            v-model="searchInfo.date_range"
            value-format="YYYY-MM-DD"
            type="daterange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="Start Date"
            end-placeholder="End Date"
          />
        </el-form-item>

        <el-form-item label="实付款ID">
          <el-input v-model="searchInfo.user_id" class="cs-search-input" />
        </el-form-item>
        <el-form-item label="提现渠道">
          <el-select v-model="searchInfo.method_id" clearable filterable placeholder="提现渠道">
            <el-option v-for="item in method_idList" :key="item.id" :label="`${item.name}(${item.id})`" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">Search</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">Reset</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="handleDownload">
            Export Excel
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <!-- <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-check" @click="batchSettle">批量结算</el-button>
      </div> -->
      <el-table ref="tableData" style="width: 100%" border tooltip-effect="dark" :data="tableData" max-height="590" row-key="id" >
        <el-table-column align="center" label="订单号" prop="order_no" min-width="180" show-overflow-tooltip />
        <el-table-column align="center" label="实付款ID" prop="user_id" min-width="120" />
        <el-table-column align="center" label="提现渠道" prop="method_name" min-width="120" />
        <el-table-column align="center" label="实际提现金额" prop="local_withdrawal_count" min-width="140" />
        <el-table-column align="center" label="实际提现币种" prop="monetary_unit" min-width="120" />
        <el-table-column align="center" label="实提美金" prop="dollar" min-width="140" />
        <el-table-column align="center" label="手续费" prop="fee_count" min-width="120" />
        <el-table-column align="center" label="手续费币种" prop="monetary_unit" min-width="120" />
        <el-table-column align="center" label="实付款金额" prop="actual_payment_amount" min-width="120" />
        <el-table-column align="center" label="实付款币种" prop="monetary_unit" min-width="120" />
        <el-table-column align="center" label="实付款账号" prop="account_no" min-width="180" />
        <el-table-column align="center" label="状态" prop="state_str" min-width="120">
          <template #default="scope">
            <span v-if="scope.row.state == 4" style="color: #F56C6C;">{{scope.row.state_str}}</span>
            <span v-else>{{scope.row.state_str}}</span>
          </template>
        </el-table-column>
        <el-table-column align="center" label="时间" prop="create_at_str" min-width="120" />
        <el-table-column align="center" label="类型" prop="withdrawal_type_str" min-width="160" />
        <el-table-column align="center" fixed="right" label="操作" min-width="180">
          <template #default="scope">
            <el-button type="primary" v-if="scope.row.state == '1'" size="small" @click="settled(scope.row, 1)">结算</el-button>
            <el-button type="success" v-if="scope.row.state == '2' && scope.row.manualPayment" size="small" @click="settled(scope.row, 3)">成功</el-button>
            <el-button type="danger" v-if="scope.row.state == '2' && scope.row.manualPayment" size="small" @click="settled(scope.row, 4)">失败</el-button>
            <el-button type="primary" v-if="scope.row.state == '4'" size="small" @click="settled(scope.row, 2)">修改</el-button>
            <el-button type="primary" v-if="scope.row.state == '4' && !scope.row.manualPayment" size="small" @click="settled(scope.row, 5)">重新发起</el-button>
            <el-button v-if="scope.row.state == '3'" size="small">完成</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="原因" prop="fail_reason" min-width="160" />
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>

    <el-dialog v-model="dialogFormVisible" title="付款失败" width="500px">
      <el-form :model="form">
        <el-form-item label="失败原因:" :label-width="120">
          <el-input v-model="form.reason" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFormVisible = false">取消</el-button>
          <el-button type="primary" @click="failSubmit">
            确认
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { batchUpdateWithdrawalPay, settle, getWithdrawalPayList } from '@/api/anchorPayment' //  此处请自行替换地址
import moment from 'moment/moment'
import infoList from '@/mixins/infoList'
import { debounce } from 'lodash-es'

export default {
  name: 'AnchorPayment',
  mixins: [infoList],

  data() {
    return {
      dialogFormVisible: false,
      type: '',
      unionDict: {},
      searchInfoTime: '',
      deleteVisible: false,
      multipleSelection: [],
      unionListOptions: [],
      formData: {},

      form: {
        reason: '',
      },

      statusList: [
        {
          id: 1,
          name: '待付款',
        },
        {
          id: 2,
          name: '付款中',
        },
        {
          id: 3,
          name: '付款成功',
        },
        {
          id: 4,
          name: '付款失败',
        },
      ],
      withDrawList: [
        {
          id: 1,
          name: '主播',
        },
        {
          id: 2,
          name: '工会',
        },
        {
          id: 3,
          name: '工会+主播',
        },
      ],
      method_idList: [
        {
          id: 2,
          name: 'Payoneer',
        },
        {
          id: 3,
          name: 'Bank',
        },
        {
          id: 4,
          name: 'Gcash',
        },
        {
          id: 5,
          name: 'Pix',
        },
        {
          id: 6,
          name: 'USDT',
        },
        {
          id: 8,
          name: 'EPay',
        },
        {
          id: 9,
          name: 'Sito',
        },
        {
          id: 10,
          name: 'Binance',
        },
      ],

      dialogFormVisible: false,
      searchInfo: {},
      currentRow: '',
      // 手动打款数组payoneer usdt
      manualPaymentArr: [2, 6],
    }
  },
  created() {
    let startDate = moment()
      .subtract(7, 'days')
      .format('YYYY-MM-DD')
    let endDate = moment().format('YYYY-MM-DD')
    this.searchInfo.date_range = [startDate, endDate]

    this.getTableDatas()
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    formatState(row) {
      let target = this.statusList.find((item) => item.id == row.state)
      if (!target) {
        return ''
      }
      return target.name
    },
    formatType(row) {
      let target = this.withDrawList.find((item) => item.id == row.withdrawal_type)
      if (!target) {
        return ''
      }
      return target.name
    },
    async getTableDatas() {
      console.log(this.searchInfo)
      const param = {
        page: this.page,
        pageSize: this.pageSize,
        state: this.searchInfo.state,
        user_id: this.searchInfo.user_id,
        method_id: this.searchInfo.method_id,
        begin: this.searchInfo.date_range ? this.searchInfo.date_range[0] : '',
        end: this.searchInfo.date_range ? this.searchInfo.date_range[1] : '',
      }
      const table = await getWithdrawalPayList(param)
      if (table.code === 0) {
        this.tableData = table.data.list || []

        this.tableDataFormat()
        this.total = table.data.total
        this.page = table.data.page
        this.pageSize = table.data.pageSize
      }
    },

    async failSubmit() {
      const res = await batchUpdateWithdrawalPay({ ids: [this.currentRow.id], state: 4, remarks: this.form.reason })
      if (res.code === 0) {
        this.dialogFormVisible = false
        this.getTableDatas()
        this.$message({
          type: 'success',
          message: '操作成功',
        })
      }
    },
    async settled(row, type) {
      let message = ''
      if (type == 1) {
        message = '确定结算该笔订单?'
      } else if (type == 3) {
        message = '是否已经付款成功?'
      } else if (type == 4) {
        this.currentRow = row
        this.dialogFormVisible = true
        rerurn
      } else if (type == 2) {
        message = '确认是否修改付款失败为付款成功?'
      } else if (type == 5) {
        message = '请确认是否重新发起该笔订单?'
      }
      this.$confirm(message, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        if (type == 1 || type == 5) {
          this.settledAmount(row, type)
        } else {
          if (type == 2) {
            type = 3
          }
          const res = await batchUpdateWithdrawalPay({ ids: [row.id], state: type })
          if (res.code === 0) {
            this.getTableDatas()
            this.$message({
              type: 'success',
              message: '修改成功',
            })
          }
        }
        // this.deleteAnchorDailyStatistics(row)
      })
    },

    settledAmount: debounce(async function (row, type){
      let param = {
        id: row.id,
      }
      if (type == 5) {
        param.retry = 1
      }
      const table = await settle(param)
      if (table.code === 0) {
        this.getTableDatas()
        this.$message({
          type: 'success',
          message: type == 1 ? '结算成功' : '重新发起成功',
        })
      }
    }, 500),

    tableDataFormat() {
      this.tableData.map((currentValue, index, array) => {
        array[index].state_str = this.formatState(currentValue)
        array[index].withdrawal_type_str = this.formatType(currentValue)
        array[index].manualPayment = this.manualPaymentArr.includes(currentValue.method_id)
        // array[index].BlockedStatus = this.formatBlockedState(currentValue)
        // array[index].Agency = this.formatUniosName(currentValue.union_id)
        // array[index].AnsweringRate = this.formatAnsweringRate(currentValue)
        // array[index].Income = this.formatIncome(currentValue)
        // array[index].CanSettle = this.formatCanSettle(currentValue)
        // array[index].USDollar = this.formatUSDollar(currentValue)
        // array[index].Agencyrevenue = this.formatAgencyRevenue(currentValue)
        // array[index].Historicalunsettledrevenue = this.formatHistorical(currentValue)
        // array[index].paid_call_durations_str = this.formateSeconds(currentValue.paid_call_durations)
        // array[index].free_call_durations_str = this.formateSeconds(currentValue.free_call_durations)
        // array[index].TotalCallIncome = this.formatTotalCallIncome(currentValue)
        // array[index].online_durations_str = this.formateSeconds(currentValue.online_durations)
        // array[index].working_durations_str = this.formateSeconds(currentValue.working_durations)
      })
    },
    getTableHeaderProp(tableColumns) {
      const tHeader = []
      const filterV = []
      if (tableColumns.length > 0) {
        tableColumns.forEach((item) => {
          tHeader.push(item.label)
          filterV.push(item.property)
        })
      }
      return [tHeader, filterV]
    },
    handleDownload() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },

    onReset() {
      this.searchInfo.state = ''
      this.searchInfo.user_id = ''
      this.searchInfo.method_id = ''
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableDatas()
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableDatas()
    },
    pageChange(v) {
      this.page = v
      this.getTableDatas()
    },
  },
}
</script>

<style></style>
