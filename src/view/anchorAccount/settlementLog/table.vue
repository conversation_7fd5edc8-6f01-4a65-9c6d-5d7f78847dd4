<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="执行类型">
          <el-select v-model="searchInfo.type" clearable placeholder="执行类型">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="执行状态">
          <el-select v-model="searchInfo.state" clearable placeholder="执行状态">
            <el-option
              v-for="item in stateOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="执行日期">
          <el-date-picker v-model="searchInfo.date" type="date" value-format="YYYY-MM-DD" placeholder="执行日期" />
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" label="日期" prop="date" width="120" />
        <el-table-column align="center" label="执行类型" prop="type" width="120">
          <template #default="scope">{{ formaTypePartner(scope.row.type) }}</template>
        </el-table-column>
        <el-table-column align="center" label="执行状态" prop="state" width="120">
          <template #default="scope">{{ formaStatePartner(scope.row.state) }}</template>
        </el-table-column>
        <el-table-column align="center" label="创建时间" width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="更新时间" width="180">
          <template #default="scope">{{ formatDate(scope.row.updated_at) }}</template>
        </el-table-column>
        <!--
        <el-table-column align="center" label="操作">
          <template #default="scope">
            <el-button type="text" icon="el-icon-delete" size="mini" @click="handleRow(scope.row)">重新执行</el-button>
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateSettlementLog(scope.row)">变更</el-button>
          </template>
        </el-table-column>
        -->
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" label-position="right" label-width="80px">
        <el-form-item label="日期:">
          <el-date-picker v-model="formData.date" type="date" value-format="YYYY-MM-DD" placeholder="执行日期" />
        </el-form-item>
        <el-form-item label="执行类型:">
          <el-radio-group v-model.number="formData.type">
            <el-radio :label="1">日结算</el-radio>
            <el-radio :label="2">周结算</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="执行状态:">
          <el-radio-group v-model.number="formData.state">
            <el-radio :label="1">执行成功</el-radio>
            <el-radio :label="2">执行中</el-radio>
            <el-radio :label="3">执行失败</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createSettlementLog,
  handleSettlementLog,
  deleteSettlementLogByIds,
  updateSettlementLog,
  findSettlementLog,
  getSettlementLogList
} from '@/api/settlementLog' //  此处请自行替换地址

import infoList from '@/mixins/infoList'

const typeOptions = [
  {
    value: 1,
    label: '日结算',
  },
  {
    value: 2,
    label: '周结算',
  },
]

const stateOptions = [
  {
    value: 1,
    label: '执行成功',
  },
  {
    value: 2,
    label: '执行中',
  },
  {
    value: 3,
    label: '执行失败',
  },
]

export default {
  name: 'SettlementLog',
  mixins: [infoList],
  data() {
    return {
      listApi: getSettlementLogList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      typeOptions: typeOptions,
      stateOptions: stateOptions,
      multipleSelection: [],
      formData: {
        date: '',
        type: 0,
        state: 0,
      }
    }
  },
  async created() {
    await this.getTableData()
  },
  methods: {
    formaTypePartner: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '日结算'
            break
          case 2:
            text = '周结算'
            break
        }
        return text
      } else {
        return ''
      }
    },
    formaStatePartner: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '执行成功'
            break
          case 2:
            text = '执行中'
            break
          case 3:
            text = '执行失败'
            break
        }
        return text
      } else {
        return ''
      }
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleRow(row) {
      this.$confirm('确定要重新执行吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.handleSettlement(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
        this.multipleSelection.map(item => {
          ids.push(item.id)
        })
      const res = await deleteSettlementLogByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateSettlementLog(row) {
      const res = await findSettlementLog({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.resettlementLog
      }
      this.dialogFormVisible = true
      this.getTableData()
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        date: '',
        type: 0,
        state: 0,
      }
    },
    async handleSettlement(row) {
      const res = await handleSettlementLog({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '提交成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createSettlementLog(this.formData)
          break
        case 'update':
          res = await updateSettlementLog(this.formData)
          break
        default:
          res = await createSettlementLog(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    }
  },
}
</script>

<style>
</style>

