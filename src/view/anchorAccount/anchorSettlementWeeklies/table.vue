<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item v-if="parseInt(userInfo.union_id) === 0" label="主播类型">
          <el-select v-model="searchInfo.working_condition" clearable placeholder="主播类型">
            <el-option
              v-for="item in workingCondition"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item v-if="parseInt(userInfo.union_id) === 0" label="Union">
          <el-select v-model="searchInfo.union_id" clearable filterable placeholder="Union">
            <el-option
              v-for="item in unionListOptions"
              :key="item.id"
              :label="`${item.name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="Anchor ID">
          <el-input v-model="searchInfo.user_id" class="cs-search-input" />
        </el-form-item>
        <el-form-item label="Date">
          <el-date-picker
            v-model="searchInfoTime"
            type="date"
            placeholder="Pick a day"
            @change="onDaterangeChange"
          />
        </el-form-item>
        <el-form-item v-if="parseInt(userInfo.union_id) === 0" label="Settlement">
          <el-select v-model="searchInfo.settlement_result" clearable filterable placeholder="AppId">
            <el-option
              v-for="item in settlementResultCondition"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">Search</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">Reset</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">
            Export Excel
          </el-button>
          <view v-if="searchInfo.start_at" style="margin-left: 10px;">{{ searchInfo.start_at }}:{{ searchInfo.end_at }}</view>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" label="ID" prop="user_id" width="120" />
        <el-table-column align="center" label="Union" prop="union_id" width="120">
          <template #default="scope">{{ formaUnionList(scope.row.union_id) }}</template>
        </el-table-column>
        <el-table-column align="center" label="Blocked status" prop="state" width="120">
          <template #default="scope">{{ formatBlockedState(scope.row) }}</template>
        </el-table-column>
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Number of calls received this week"
          prop="push_total_call_numbers"
          width="120"
        />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Number of calls answered this week"
          prop="answer_total_call_numbers"
          width="120"
        />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Answered rate this week"
          prop="connect_rate"
          width="120"
        />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Free call duration this week"
          prop="free_call_durations"
          width="120"
        />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Pay call duration this week"
          prop="paid_call_durations"
          width="120"
        />
        <el-table-column align="center" label="Effective call duration this week" prop="connect_durations" width="120" />
        <el-table-column align="center" label="Text chat salary  this week" prop="message_revenue" width="120" />
        <el-table-column align="center" label="Gift salary this week" prop="gift_total_revenue" width="120" />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Unsettlement income last week"
          prop="carry_over_income"
          width="120"
        />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Estimate salary"
          prop="estimated_income"
          width="120"
        />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Final salary"
          prop="settlement_income"
          width="120"
        />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Agency salary"
          prop="unions_income"
          width="120"
        />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Settlement result"
          prop="settlement_result"
          width="120"
        >
          <template #default="scope">{{ formaSettlementResultPartner(scope.row.settlement_result) }}</template>
        </el-table-column>
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="Conversion income this week"
          prop="conversion_income"
          width="120"
        />
        <el-table-column align="center" label="Online Time" prop="online_durations" width="120" />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="detail"
          prop="detail"
          width="120"
        />
        <el-table-column
          v-if="parseInt(userInfo.union_id) === 0"
          align="center"
          label="新手收益"
          prop="award_count"
          width="120"
        />
        <el-table-column align="center" label="Total salary" min-width="120">
          <template #default="scope">{{ formatTotalSalary(scope.row) }}</template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" label-position="right" label-width="80px">
        <el-form-item label="日期:">
          <el-input v-model="formData.week_date" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="主播ID:">
          <el-input v-model.number="formData.user_id" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="工会ID:">
          <el-input v-model.number="formData.union_id" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="主播身份, 1:兼职, 2:全职:">
          <el-input v-model.number="formData.working_condition" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="主播状态 2: 正常, 1:封禁:">
          <el-input v-model.number="formData.state" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="在线时长:">
          <el-input v-model.number="formData.online_durations" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="working 状态时长:">
          <el-input v-model.number="formData.working_durations" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="推送付费呼叫次数:">
          <el-input v-model.number="formData.push_paid_call_numbers" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="接听付费呼叫次数:">
          <el-input v-model.number="formData.answer_paid_call_numbers" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="付费通话时长:">
          <el-input v-model.number="formData.paid_call_durations" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="推送免费呼叫次数:">
          <el-input v-model.number="formData.push_free_call_numbers" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="接听的免费呼叫次数:">
          <el-input v-model.number="formData.answer_free_call_numbers" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="免费通话时长:">
          <el-input v-model.number="formData.free_call_durations" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="直播礼物收益:" />
        <el-form-item label="消息礼物收入:" />
        <el-form-item label="消息回复收入:" />
        <el-form-item label="转化付费用户数:">
          <el-input v-model.number="formData.conversion_user_numbers" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="转化收入:" />
        <el-form-item label="基本工资:" />
        <el-form-item label="全职主播结算通话时长,兼职主播有效通话时长:">
          <el-input v-model.number="formData.settlement_call_durations" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="全职主播基本奖金:" />
        <el-form-item label="全职主播充值分成:" />
        <el-form-item label="兼职主播预估收入:" />
        <el-form-item label="主播结算收入:" />
        <el-form-item label="兼职主播上周转结收入:" />
        <el-form-item label="工会收入:" />
        <el-form-item label="是否结算 1:结算 2:不结算:">
          <el-input v-model.number="formData.settlement_result" clearable placeholder="请输入" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import {
  createAnchorSettlementWeeklies,
  deleteAnchorSettlementWeeklies,
  deleteAnchorSettlementWeekliesByIds,
  updateAnchorSettlementWeeklies,
  findAnchorSettlementWeeklies,
  getAnchorSettlementWeekliesList
} from '@/api/anchorSettlementWeeklies' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import { ElLoading } from 'element-plus'
import { getUnionsList } from '@/api/unions'
import { getAppXesList } from '@/api/appXes'

const workingCondition = [
  {
    value: 1,
    label: '兼职',
  },
  {
    value: 2,
    label: '全职',
  },
]

const settlementResultCondition = [
  {
    value: 1,
    label: '结算',
  },
  {
    value: 2,
    label: '不结算',
  },
]

export default {
  name: 'AnchorSettlementWeeklies',
  mixins: [infoList],
  data() {
    return {
      appListOptions: [],
      unionListOptions: [],
      searchInfoTime: [],
      listApi: getAnchorSettlementWeekliesList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      settlementResultCondition: settlementResultCondition,
      workingCondition: workingCondition,
      multipleSelection: [],
      filename: 'anchor-settlement-data',
      autoWidth: true,
      bookType: 'xlsx',
      formData: {
        week_date: '',
        user_id: 0,
        union_id: 0,
        working_condition: 0,
        state: 0,
        online_durations: 0,
        working_durations: 0,
        push_paid_call_numbers: 0,
        answer_paid_call_numbers: 0,
        paid_call_durations: 0,
        push_free_call_numbers: 0,
        answer_free_call_numbers: 0,
        free_call_durations: 0,
        conversion_user_numbers: 0,
        settlement_call_durations: 0,
        settlement_result: 0,
      }
    }
  },
  async created() {
    await this.getUnionList()
    await this.getAppList()
    await this.getTableData()
  },
  methods: {
    formatTotalSalary(row) {
      // 文字收入+礼物收入+通话时长*0.0016
      return row.message_revenue + row.gift_total_revenue + (row.paid_call_durations + row.free_call_durations * 0.2) * 0.0016
    },
    formaUnionList: function(bool) {
      let text
      this.unionListOptions.forEach(function(element) {
        if (bool === element.id) {
          text = element.name
          return text
        }
      })
      return text
    },
    // 工会
    async getUnionList() {
      const res = await getUnionsList({page:1, pageSize: 1000})
      this.unionListOptions = res.data.list
    },
    formaAppList: function(bool) {
      let text
      this.appListOptions.forEach(function(element) {
        if (bool === element.id) {
          text = element.appName
          return text
        }
      })
      return text
    },
    async getAppList() {
      const res = await getAppXesList()
      this.appListOptions = res.data.list
    },
    handleProgressLoading() {
      this.progressLoading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
    },
    handleDownload() {
      this.handleProgressLoading()
      const that = this
      import('@/utils/excel').then((excel) => {
        let tHeader = []
        let filterVal = []
        if (this.userInfo.union_id !== 0) {
          tHeader = ['ID', 'Union', 'Blocked status', 'Number of calls received this week', 'Number of calls answered this week', 'Answered rate this week', 'Free call duration this week', 'Pay call duration this week', 'Effective call duration this week', 'Text chat salary  this week', 'Gift salary this week', 'Unsettlement income last week', 'Estimate salary', 'Final salary', 'Agency salary', 'Settlement result', '  income this week', 'Online Time', 'detail', '新手收益']
          filterVal = ['user_id', 'union_id', 'state', 'push_total_call_numbers', 'answer_total_call_numbers', 'connect_rate', 'free_call_durations', 'paid_call_durations', 'connect_durations', 'message_revenue', 'gift_total_revenue', 'carry_over_income', 'estimated_income', 'unions_income', 'settlement_result', 'conversion_income', 'online_durations', 'detail', 'award_count']
        } else {
          tHeader = ['ID', 'Union', 'Blocked status', 'Number of calls received this week', 'Number of calls answered this week', 'Answered rate this week', 'Free call duration this week', 'Pay call duration this week', 'Effective call duration this week', 'Text chat salary  this week', 'Gift salary this week', 'Unsettlement income last week', 'Estimate salary', 'Final salary', 'Agency salary', 'Settlement result', '  income this week', 'Online Time', 'detail', '新手收益']
          filterVal = ['user_id', 'union_id', 'state', 'push_total_call_numbers', 'answer_total_call_numbers', 'connect_rate', 'free_call_durations', 'paid_call_durations', 'connect_durations', 'message_revenue', 'gift_total_revenue', 'carry_over_income', 'estimated_income', 'unions_income', 'settlement_result', 'conversion_income', 'online_durations', 'detail', 'award_count']
        }
        const listData = JSON.parse(JSON.stringify(this.tableData))
        // 数据处理
        listData.map(function(currentValue, index, array) {
          array[index].state = that.formatBlockedState(currentValue)
          array[index].union_id = that.formaUnionList(currentValue.union_id)
          array[index].settlement_result = that.formaSettlementResultPartner(currentValue.settlement_result)
        })

        const data = this.formatJson(filterVal, listData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    onDaterangeChange() {
      if (this.searchInfoTime) {
        let conversionStr = ''
        if (moment(this.searchInfoTime).isoWeekday() === 7) {
          conversionStr = moment(this.searchInfoTime).add(-1, 'days').format('YYYY-MM-DD')
        } else {
          conversionStr = this.searchInfoTime
        }
        this.searchInfo.start_at = moment(conversionStr).day(1).format('YYYY-MM-DD')
        this.searchInfo.end_at = moment(conversionStr).day(7).format('YYYY-MM-DD')
      } else {
        this.searchInfo.start_at = ''
        this.searchInfo.end_at = ''
      }
    },
    onReset() {
      this.searchInfo = {}
      this.searchInfoTime = []
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteAnchorSettlementWeeklies(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
        this.multipleSelection.map(item => {
          ids.push(item.id)
        })
      const res = await deleteAnchorSettlementWeekliesByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateAnchorSettlementWeeklies(row) {
      const res = await findAnchorSettlementWeeklies({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.reanchorSettlementWeeklies
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        week_date: '',
        user_id: 0,
        union_id: 0,
        working_condition: 0,
        state: 0,
        online_durations: 0,
        working_durations: 0,
        push_paid_call_numbers: 0,
        answer_paid_call_numbers: 0,
        paid_call_durations: 0,
        push_free_call_numbers: 0,
        answer_free_call_numbers: 0,
        free_call_durations: 0,
        conversion_user_numbers: 0,
        settlement_call_durations: 0,
        settlement_result: 0,
      }
    },
    async deleteAnchorSettlementWeeklies(row) {
      const res = await deleteAnchorSettlementWeeklies({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createAnchorSettlementWeeklies(this.formData)
          break
        case 'update':
          res = await updateAnchorSettlementWeeklies(this.formData)
          break
        default:
          res = await createAnchorSettlementWeeklies(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    }
  },
}
</script>

<style>
</style>
