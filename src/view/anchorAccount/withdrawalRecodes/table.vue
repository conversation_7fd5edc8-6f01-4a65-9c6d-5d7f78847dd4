<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="Union：">
          <el-select v-model="searchInfo.union_id" placeholder="请选择" clearable>
            <el-option v-for="item in unionListOptions" :key="item.id" :label="`${item.name}(${item.id})`" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="CountryCode">
          <el-select v-model="searchInfo.country_code" placeholder="请选择" clearable>
            <el-option v-for="item in countryCodeOptions" :key="item.value" :label="`${item.label}(${item.value})`" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="Levels">
          <el-input-number v-model="searchInfo.levels" />
        </el-form-item>
        <el-form-item label="主播/工会长ID：">
          <el-input v-model="searchInfo.user_id" clearable>请输入</el-input>
        </el-form-item>
        <el-form-item label="Date">
          <div class="block">
            <span class="demonstration" />
            <el-date-picker
              v-model="settlementWeek"
              value-format="YYYY-MM-DD"
              type="daterange"
              :shortcuts="dateRangeShortcuts"
              start-placeholder="Start Date"
              end-placeholder="End Date"
              @change="bindSettlementCycle"
            />
          </div>
        </el-form-item>
        <el-form-item label="状态：">
          <el-select v-model="searchInfo.state" placeholder="请选择">
            <el-option v-for="item in stateOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="身份：">
          <el-select v-model="searchInfo.role" placeholder="请选择">
            <el-option v-for="item in roleOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">Search</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">Reset</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="exportExcel">
            Export Excel
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-check" @click="batchUpdateWithdrawalRecordsSuccess">提现成功</el-button>
        <el-button size="mini" type="primary" icon="el-icon-close" @click="batchUpdateWithdrawalRecordsFailed">提现失败</el-button>
        <el-button size="mini" type="primary" icon="el-icon-close" :disabled="examineIdList.length === 0" @click="batchExamineWithdrawalRecordsSuccess">审核成功</el-button>
        <el-button size="mini" type="primary" icon="el-icon-check" @click="batchInPayment">批量进入主播提现表</el-button>
      </div>
      <el-table
        ref="tableData"
        border
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
        :default-sort="{ prop: 'withdrawal_count', order: 'descending' }"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="center" label="week date" prop="weekData" width="200" />
        <el-table-column align="center" label="settle id" prop="id" width="80" />
        <el-table-column align="center" label="user id" prop="user_id" width="100" />
        <el-table-column align="center" label="实付款ID" prop="payer_id" width="100" />
        <el-table-column align="center" label="封禁状态" prop="recovery" width="120">
          <template #default="scope">
            <el-tag :type="scope.row.recovery === 0 ? 'success' : 'info'">
              {{ scope.row.recovery === 0 ? '正常' : '封禁' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="等级" prop="levels" width="100" />
        <el-table-column align="center" label="Type" prop="WithdrawalType" width="120" />
        <el-table-column align="center" label="App" prop="app_name" width="120" />
        <el-table-column align="center" label="上线工会" prop="union_str" width="120">
          <template #default="scope">{{ formatUnionList(scope.row.union_id) }}</template>
        </el-table-column>
        <el-table-column align="center" label="我的工会" prop="my_union_str" width="120">
          <template #default="scope">{{ formatMyUnionStr(scope.row.my_union_managed) }}</template>
        </el-table-column>
        <el-table-column align="center" label="Union ManagerID" prop="union_manager_id" width="150" />
        <el-table-column align="center" label="提现渠道" prop="method_name" min-width="150">
          <template #default="scope">
            {{ formatMethodName(scope.row) }}
          </template>
        </el-table-column>
        <el-table-column align="center" label="待结算宝石" prop="withdrawal_count" min-width="130" sortable />
        <el-table-column align="center" label="扣除宝石数" prop="deduct_diamond" width="120" />
        <el-table-column align="center" label="实际提现宝石数" prop="settleResult" width="120" />
        <el-table-column align="center" label="复核" min-width="140">
          <template #default="scope">
            <el-button size="small" type="primary" @click="goAnchorIncomeDetail(scope.row)">复核</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="提现结果" prop="state_str" width="120">
          <template #default="scope">
            <div v-if="scope.row.state === 1" style="color: #faad14">提现中</div>
            <div v-if="scope.row.state === 2" style="color: #14fa33">提现成功</div>
            <div v-if="scope.row.state === 3" style="color: #ee0a25">提现失败</div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="创建日期" prop="created_at_str" width="180" />
        <el-table-column align="center" fixed="right" label="操作" width="200">
          <template #default="scope">
            <div v-if="scope.row.state === 1">
              <el-button type="warning" @click="examineRecords(scope.row)">审核</el-button>
            </div>
            <div class="f" v-else>
              <el-button type="success" v-if="scope.row.is_done == 1" @click="updateWithdrawalRecordsSuccess(scope.row)">成功</el-button>
              <el-button type="danger" v-if="scope.row.state === 101 && (scope.row.is_done !=2 && scope.row.is_done !=3 )" @click="updateWithdrawalRecordsFailed(scope.row)">失败</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="examineDialogShow" title="审核" top="10px">
      <el-form :model="examineDialogForm">
        <el-form-item label="钻石">
          <el-input disabled v-model="examineDialogForm.record_diamond"></el-input>
        </el-form-item>
        <el-form-item label="工会名称">
          <el-input disabled v-model="examineDialogForm.union_str"></el-input>
        </el-form-item>
        <el-form-item label="扣除比例">
          <el-input-number style="width: 100%;" v-model="examineDialogForm.ratio" :min="0" :max="100" :precision="0"></el-input-number>
        </el-form-item>
        <el-form-item label="扣除原因">
          <el-input v-model="examineDialogForm.reason" type="textarea" :autosize="{ minRows: 6, maxRows: 10 }"></el-input>
        </el-form-item>
        <el-button style="width: 100%;" type="primary" v-loading="examineLoading" @click="submitExamineRecords">确定提交</el-button>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import {
  batchExamineWithdrawalRecords,
  batchUpdateWithdrawalRecords,
  createWithdrawalRecords,
  examineWithdrawalRecords,
  getWithdrawalRecordsList,
  updateWithdrawalRecords
} from '@/api/withdrawalRecodes' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import {findWithdrawalMethodAll} from "@/api/withdrawalMethod";
import XLSX from 'xlsx'
import {saveAs} from 'file-saver'
import { addPay,addPayList } from '@/api/anchorPayment' //  此处请自行替换地址


export default {
  name: 'WithdrawalRecords',
  components: {},

  mixins: [infoList],
  data() {
    return {
      examineIdList: [],
      settlementWeek: '',
      stateOptions: [
        { label: '提现中', value: 1 },
        { label: '审核完成', value: 101 },
        { label: '提现成功', value: 2 },
        { label: '提现失败', value: 3 },
        { label: '全部', value: 0 }
      ],
      roleOptions: [
        { label: '不限', value: 0 },
        { label: '工会长', value: 1 },
        { label: '主播', value: 2 },
      ],
      appListOptions: [],
      unionListOptions: [],
      methodListOptions: [],
      methodDict: {},
      listApi: getWithdrawalRecordsList,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      submitIds: [],
      examineDialogShow: false,
      examineLoading: false,
      examineDialogForm: {
        id: 0,
        record_diamond: 0,
        ratio: 0,
        reason: '',
      },
    }
  },
  async created() {
    this.searchInfo.state = 1
    this.initMethodAll()
    await this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
     batchInPayment(){
       addPay().then(
         res=>{
          if (res.code === 0) {
            this.$message({
              type: 'success',
              message: '操作成功',
            })
          }
          }
       )
    },
    batchExamineWithdrawalRecordsSuccess(){
      batchExamineWithdrawalRecords({id_list: this.examineIdList}).then(res=>{
        this.$message.success('成功')
        this.getTableData(this.nFunc, this.tableDataFormat)
      })
    },
    s2ab(s) {
      const buf = new ArrayBuffer(s.length)
      const view = new Uint8Array(buf)
      for (let i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xff
      return buf
    },
    async exportExcel() {
      let resData = this.tableData
      let aoaList = [['week date', 'settle id', 'user id', '实付款ID','封禁状态', '等级', 'type', 'App', '上线工会', '我的工会', 'Union ManagerID', '提现渠道', '待结算宝石', '扣除宝石数', '实际提现宝石数', '提现结果', '创建日期', 'Payoneer-账号', 'Bank-账号', 'Bank-国家', 'Bank-姓名', 'Bank-手机号', 'Bank-邮箱', 'Bank-地址', 'Bank-银行码', 'Gcash-账号', 'Gcash-国家']]
      resData.forEach(v=>{
        let methodName = this.formatMethodName(v)
        aoaList.push([
          `${v.begin}:${v.end}`,
          v.id,
          v.user_id,
          v.payer_id,
          v.recovery === 0 ? '正常' : '封禁',
          v.levels,
          v.WithdrawalType,
          v.app_name,
          v.union_str,
          v.my_union_str,
          v.union_manager_id,
          methodName,
          v.withdrawal_count,
          v.deduct_diamond,
          v.settleResult,
          v.state_str,
          v.created_at_str,
          v.AccountPayoneerInfo.account_no,
          v.AccountBankInfo.account_no,
          v.AccountBankInfo.country,
          v.AccountBankInfo.account_name,
          v.AccountBankInfo.account_phone,
          v.AccountBankInfo.account_email,
          v.AccountBankInfo.account_address,
          v.AccountBankInfo.bank_code,
          v.AccountGcashInfo.account_name,
          v.AccountGcashInfo.country,
        ])
      })
      const sheet = XLSX.utils.aoa_to_sheet(aoaList);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, sheet, 'Sheet1');
      const wbout = XLSX.write(workbook, {
        bookType: 'xlsx',
        bookSST: false,
        type: 'binary',
      });
      saveAs(
        new Blob([this.s2ab(wbout)], {
          type: 'application/octet-stream',
        }),
        'test.xlsx'
      )
    },
    initMethodAll() {
      findWithdrawalMethodAll().then(res=>{
        if (res.code === 0) {
          console.log(res.data)
          let resData = res.data || []
          this.methodListOptions = resData
          let resDict = {}
          resData.forEach(item=>{
            resDict[item.id] = item
          })
          this.methodDict = resDict
        }
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    formatMethodName(item) {
      if (item.method_id === 0) {
        return ''
      }
      let methodInfo = this.methodDict[item.method_id]
      if (methodInfo !== undefined) {
        return methodInfo.name
      }
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].weekData = `${item.begin}:${item.end}`
        data[i].WithdrawalType = this.formatWithdrawalType(item)
        data[i].app_name = this.formatAppName(item.app_id)
        data[i].settleResult = this.formatSettleResult(item)
        data[i].union_str = this.formatUnionList(item.union_id)
        data[i].my_union_str = this.formatMyUnionStr(item.my_union_managed)
        const uid = item.union_id > 0 ? item.union_id : item.union_managed
        data[i].union_manager_id = this.formatUniosManagerId(uid)
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].state_str = this.formatStateStr(item)
      })
    },
    formatStateStr(item) {
      let r
      switch (item.state) {
        case 1:
          r = "提现中"
          break
        case 2:
          r = "提现成功"
          break
        case 3:
          r = "提现失败"
          break
        default:
          r = "未知"
          break
      }
      return r
    },
    goAnchorIncomeDetail(row) {
      let goName = 'anchorIncomeDetail'
      let union_managed = row.union_managed
      if (union_managed !== 0) {
        goName = 'unionIncomeDetail'
      }
      this.$router.push({
        name: goName, query: {
          'userId': row.user_id,
          'startTime': row.begin + ' 00:00:00',
          'endTime':  row.end + ' 23:59:59',
        }
      })
    },
    // 批量更新记录
    async batchUpdate(formData) {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要更新的数据'
        })
        return
      }
      this.multipleSelection &&
      this.multipleSelection.map(item => {
        ids.push(item.id)
      })
      const data = { 'ids': ids, 'state': formData.state, 'remarks': formData.remarks }
      return await batchUpdateWithdrawalRecords(data)
    },
    examineRecords(row) {
      this.examineDialogForm = {
        id: row.id,
        record_diamond: row.withdrawal_count,
        union_str: row.union_str,
        ratio: 0,
        reason: '',
      }
      this.examineDialogShow = true
    },
    submitExamineRecords() {
      this.examineLoading = true
      examineWithdrawalRecords(this.examineDialogForm).then(res=>{
        this.examineLoading = false
        if (res.code === 0) {
          this.examineDialogShow = false
          this.examineDialogForm = {
            id: 0,
            record_diamond: 0,
            ratio: 0,
            reason: '',
          }
          this.getTableData(this.nFunc, this.tableDataFormat)
        }
      }).catch(err=>{
        this.examineLoading = false
      })
    },

    // 绑定日期范围
    bindSettlementCycle() {
      if (this.settlementWeek != null) {
        this.searchInfo.begin = this.settlementWeek[0]
        this.searchInfo.end = this.settlementWeek[1]
      }
    },
    formatState: function(bool) {
      let text
      if (bool !== null) {
        switch (bool) {
          case 1:
            text = '提现中'
            break
          case 2:
            text = '提现成功'
            break
          case 3:
            text = '提现失败'
            break
          default:
            text = ''
        }
        return text
      }
    },
    formatSettleResult(row) {
      return this.numF2(row.withdrawal_count - row.deduct_diamond)
    },
    // 格式化工会
    formatUnionList: function(unionId, unionManaged) {
      let text
      const uid = unionId > 0 ? unionId : unionManaged
      text = this.formatUniosName(uid)
      return `${text} (${uid})`
    },
    formatMyUnionStr(unionId) {
      let text
      text = this.formatUniosName(unionId)
      return `${text} (${unionId})`
    },
    onReset() {
      this.searchInfo = {
        state: 1
      }
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    handleSelectionChange(val) {
      let newList = []
      val.forEach(item=>{
        if (item.state === 1) {
          newList.push(item.id)
        }
      })
      this.examineIdList = newList
      this.multipleSelection = val
    },

        // 批量更新记录
        async batchUpdate1(formData) {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要更新的数据'
        })
        return
      }
      this.multipleSelection &&
      this.multipleSelection.map(item => {
        ids.push(item.id)
      })
      const data = { 'ids': ids }
      return await addPayList(data)
    },
    // 批量更新提现成功的点击事件
    async batchUpdateWithdrawalRecordsSuccess() {
      const data = {
        state: 2,
      }
      const res = await this.batchUpdate1(data)
      if (res.code === 0) {
        this.$message({
          message: '批量提现成功状态设置成功',
          type: 'success'
        })
      } else {
        this.$message.err('批量提现成功状态设置失败')
      }
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    async batchUpdateWithdrawalRecordsFailed() {
      const data = {
        state: 3,
        remarks: 'After a recent review, we found that you is not compliant with one or more of our Policies. Contact your agency for more information about your status and how to correct the issue.'
      }
      const res = await this.batchUpdate(data)
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '批量提现失败状态设置成功'
        })
      } else {
        this.$message.err('批量提现失败状态设置失败')
      }
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    async updateWithdrawalRecordsFailed(row) {
      const data = {
        id: row.id,
        state: 3,
        remarks: 'After a recent review, we found that you is not compliant with one or more of our Policies. Contact your agency for more information about your status and how to correct the issue.'
      }
      const res = await updateWithdrawalRecords(data)
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '设置状态成功'
        })
      } else {
        this.$message.err('设置状态失败')
      }
      row.state = 3
    },
    async updateWithdrawalRecordsSuccess(row) {
      // const data = {
      //   id: row.id,
      //   state: 2
      // }
      // const res = await updateWithdrawalRecords(data)
       const data = {
        ids: [row.id],
      }
      const res = await addPayList(data)
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '设置状态成功'
        })
      } else {
        this.$message.err('设置状态失败')
      }
      row.state = 2
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.enterDialog()
        } else {
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createWithdrawalRecords(this.formData)
          break
        case 'update':
          res = await updateWithdrawalRecords(this.formData)
          break
        default:
          res = await createWithdrawalRecords(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    handleDownload() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    formatWithdrawalType(row) {
      let typeText
      if (row.union_managed > 0) {
        typeText = '工会长提现'
      } else {
        typeText = '主播提现'
      }
      return typeText
    }
  },
}
</script>

<style></style>
