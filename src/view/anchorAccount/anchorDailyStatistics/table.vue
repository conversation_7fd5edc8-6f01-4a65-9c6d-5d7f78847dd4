<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="主播类型">
          <el-select v-model="searchInfo.working_condition" clearable placeholder="主播类型">
            <el-option
              v-for="item in workingCondition"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="是否结算">
          <el-select v-model="searchInfo.settlement_result" clearable placeholder="主播类型">
            <el-option
              v-for="item in settlementResultCondition"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />

          </el-select>
        </el-form-item>
        <el-form-item label="日期">
          <el-date-picker
            v-model="searchInfoTime"
            type="daterange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="Start Date"
            end-placeholder="End Date"
            @change="onDaterangeChange"
          />
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="multipleTable"
        v-loading="isLoading"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="center" label="日期" prop="date" width="120" />
        <el-table-column align="center" label="用户ID" prop="user_id" width="120" />
        <el-table-column align="center" label="union_id" prop="union_id" width="120" />
        <el-table-column align="center" label="用户名" prop="name" width="120" />
        <el-table-column align="center" label="工作环境" prop="working_condition" width="120" />
        <el-table-column align="center" label="状态" prop="state" width="120" />
        <el-table-column align="center" label="等级" prop="levels" width="120" />
        <el-table-column align="center" label="在线时间" prop="online_durations" width="120" />
        <el-table-column align="center" label="工作持续时间" prop="working_durations" width="120" />
        <el-table-column align="center" label="付费通话电话" prop="push_paid_call_numbers" width="120" />
        <el-table-column align="center" label="应答数" prop="answer_paid_call_numbers" width="120" />
        <el-table-column align="center" label="付费电话持续时间" prop="paid_call_durations" width="120" />
        <el-table-column align="center" label="推送免费呼叫数量" prop="push_free_call_numbers" width="120" />
        <el-table-column align="center" label="接听免费电话数量" prop="answer_free_call_numbers" width="120" />
        <el-table-column align="center" label="免费电话通话时长" prop="free_call_durations" width="120" />
        <el-table-column align="center" label="有效电话通话时长" prop="effective_call_duration" width="120" />
        <el-table-column align="center" label="结算电话通话时长" prop="settlement_call_durations" width="120" />
        <el-table-column align="center" label="连接持续时间" prop="connect_durations" width="120" />
        <el-table-column align="center" label="礼物" prop="live_gift_revenue" width="120" />
        <el-table-column align="center" label="消息礼物收入" prop="message_gift_revenue" width="120" />
        <el-table-column align="center" label="消息的收入" prop="message_revenue" width="120" />
        <el-table-column align="center" label="转换用户数量" prop="conversion_user_numbers" width="120" />
        <el-table-column align="center" label="转换收入" prop="conversion_income" width="120" />
        <el-table-column align="center" label="接通比例" prop="connect_rate" width="120" />
        <el-table-column align="center" label="转换比例" prop="conversion_rate" width="120" />
        <el-table-column align="center" label="付费应答比例" prop="paid_answer_rate" width="120" />
        <el-table-column align="center" label="免费应答比例" prop="free_answer_rate" width="120" />
        <el-table-column align="center" label="推送总通话数量" prop="push_total_call_numbers" width="120" />
        <el-table-column align="center" label="应答总通话数量" prop="answer_total_call_numbers" width="120" />
        <el-table-column align="center" label="礼物总收入" prop="gift_total_revenue" width="120" />
        <el-table-column align="center" label="新手任务总收入" prop="award_count" width="120" />
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" label-position="right" label-width="80px">
        <el-form-item label="日期:">
          <el-input v-model="formData.date" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="用户ID:">
          <el-input v-model.number="formData.user_id" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="UUID:">
          <el-input v-model.number="formData.union_id" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="用户名:">
          <el-input v-model="formData.name" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="工作环境:">
          <el-input v-model.number="formData.working_condition" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="状态:">
          <el-input v-model.number="formData.state" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="等级:">
          <el-input v-model.number="formData.levels" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="在线时间:">
          <el-input v-model.number="formData.online_durations" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="工作持续时间:">
          <el-input v-model.number="formData.working_durations" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="付费通话电话:">
          <el-input v-model.number="formData.push_paid_call_numbers" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="应答数:">
          <el-input v-model.number="formData.answer_paid_call_numbers" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="付费电话持续时间:">
          <el-input v-model.number="formData.paid_call_durations" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="推送免费呼叫数量:">
          <el-input v-model.number="formData.push_free_call_numbers" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="接听免费电话数量:">
          <el-input v-model.number="formData.answer_free_call_numbers" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="免费电话通话时长:">
          <el-input v-model.number="formData.free_call_durations" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="有效电话通话时长:">
          <el-input v-model.number="formData.effective_call_duration" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="结算电话通话时长:">
          <el-input v-model.number="formData.settlement_call_durations" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="连接持续时间:">
          <el-input v-model.number="formData.connect_durations" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="礼物:">
          <el-input v-model="formData.live_gift_revenue" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="消息礼物收入:">
          <el-input v-model="formData.message_gift_revenue" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="消息的收入:">
          <el-input v-model="formData.message_revenue" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="转换用户数量:">
          <el-input v-model.number="formData.conversion_user_numbers" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="转换收入:">
          <el-input v-model="formData.conversion_income" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="接通比例:">
          <el-input v-model="formData.connect_rate" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="转换比例:">
          <el-input v-model="formData.conversion_rate" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="付费应答比例:">
          <el-input v-model="formData.paid_answer_rate" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="免费应答比例:">
          <el-input v-model="formData.free_answer_rate" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="推送总通话数量:">
          <el-input v-model.number="formData.push_total_call_numbers" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="应答总通话数量:">
          <el-input v-model.number="formData.answer_total_call_numbers" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="礼物总收入:">
          <el-input v-model="formData.gift_total_revenue" clearable placeholder="请输入" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createAnchorDailyStatistics,
  deleteAnchorDailyStatistics,
  deleteAnchorDailyStatisticsByIds,
  updateAnchorDailyStatistics,
  findAnchorDailyStatistics,
  getAnchorDailyStatisticsList
} from '@/api/anchorDailyStatistics' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import moment from 'moment'
const settlementResultCondition = [
  {
    value: 1,
    label: '结算',
  },
  {
    value: 2,
    label: '不结算',
  },
]

const workingCondition = [
  {
    value: 1,
    label: '兼职',
  },
  {
    value: 2,
    label: '全职',
  },
]
export default {
  name: 'AnchorDailyStatistics',
  mixins: [infoList],
  data() {
    return {
      searchInfoTime: [],
      isLoading: false,
      listApi: getAnchorDailyStatisticsList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      settlementResultCondition: settlementResultCondition,
      workingCondition: workingCondition,
      multipleSelection: [],
      formData: {
        date: '',
        user_id: 0,
        union_id: 0,
        name: '',
        working_condition: 0,
        state: 0,
        levels: 0,
        online_durations: 0,
        working_durations: 0,
        push_paid_call_numbers: 0,
        answer_paid_call_numbers: 0,
        paid_call_durations: 0,
        push_free_call_numbers: 0,
        answer_free_call_numbers: 0,
        free_call_durations: 0,
        effective_call_duration: 0,
        settlement_call_durations: 0,
        connect_durations: 0,
        live_gift_revenue: '',
        message_gift_revenue: '',
        message_revenue: '',
        conversion_user_numbers: 0,
        conversion_income: '',
        connect_rate: '',
        conversion_rate: '',
        paid_answer_rate: '',
        free_answer_rate: '',
        push_total_call_numbers: 0,
        answer_total_call_numbers: 0,
        gift_total_revenue: '',
      }
    }
  },
  async created() {
    await this.getTableData()
  },
  methods: {
    onDaterangeChange(valueTime) {
      this.searchInfo.start_at = moment(this.searchInfoTime[0]).format('YYYY-MM-DD')
      this.searchInfo.end_at = moment(this.searchInfoTime[1]).format('YYYY-MM-DD')
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.beforeFunc(), this.afterFunc())
    },
    beforeFunc() {
      this.isLoading = true
    },
    afterFunc() {
      this.isLoading = false
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteAnchorDailyStatistics(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
                this.multipleSelection.map(item => {
                  ids.push(item.id)
                })
      const res = await deleteAnchorDailyStatisticsByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateAnchorDailyStatistics(row) {
      const res = await findAnchorDailyStatistics({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.reanchorDailyStatistics
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        date: '',
        user_id: 0,
        union_id: 0,
        name: '',
        working_condition: 0,
        state: 0,
        levels: 0,
        online_durations: 0,
        working_durations: 0,
        push_paid_call_numbers: 0,
        answer_paid_call_numbers: 0,
        paid_call_durations: 0,
        push_free_call_numbers: 0,
        answer_free_call_numbers: 0,
        free_call_durations: 0,
        effective_call_duration: 0,
        settlement_call_durations: 0,
        connect_durations: 0,
        live_gift_revenue: '',
        message_gift_revenue: '',
        message_revenue: '',
        conversion_user_numbers: 0,
        conversion_income: '',
        connect_rate: '',
        conversion_rate: '',
        paid_answer_rate: '',
        free_answer_rate: '',
        push_total_call_numbers: 0,
        answer_total_call_numbers: 0,
        gift_total_revenue: '',
      }
    },
    async deleteAnchorDailyStatistics(row) {
      const res = await deleteAnchorDailyStatistics({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createAnchorDailyStatistics(this.formData)
          break
        case 'update':
          res = await updateAnchorDailyStatistics(this.formData)
          break
        default:
          res = await createAnchorDailyStatistics(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    }
  },
}
</script>

<style>
</style>

