<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" ref="searchInfoRef" :model="searchInfo" :rules="searchInfoRules">
        <el-form-item label="日期">
          <el-date-picker
              v-model="searchInfo.date"
              type="date"
              value-format="YYYY-MM-DDTHH:mm:ssZ"
              format="YYYY-MM-DD"
              clearable
              placeholder="请选择日期"
          />
        </el-form-item>

        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
          ref="tableData"
          style="width: 100%"
          :data="tableData"
          row-key="id"
          max-height="590"
      >
        <el-table-column align="center" label="日期" prop="date" min-width="120" />
        <el-table-column align="center" label="工会增加数" prop="union_add" min-width="120" />
        <el-table-column align="center" label="主播审核通过数" prop="apply_ok" min-width="120" />
        <el-table-column align="center" label="主播审核拒绝数" prop="apply_refuse" min-width="120" />
        <el-table-column align="center" label="主播审核封禁数" prop="apply_ban" min-width="120" />
        <el-table-column align="center" label="主播封禁数" prop="anchor_ban" min-width="120" />
        <el-table-column align="center" label="LV4活跃人数" prop="active_lv4" min-width="120" />
        <el-table-column align="center" label="LV3活跃人数" prop="active_lv3" min-width="120" />
        <el-table-column align="center" label="LV2活跃人数" prop="active_lv2" min-width="120" />
        <el-table-column align="center" label="LV1活跃人数" prop="active_lv1" min-width="120" />
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            v-show="total > 0"
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            :total="total"
            @current-change="pageChange"
            @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {getApplyStaticsPanelList} from '@/api/applyStaticsPanel' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import moment from "moment";

export default {
  mixins: [infoList],
  data() {
    return {
      searchInfo: {},
      searchInfoRules: {},
      listApi: getApplyStaticsPanelList,
    }
  },
  async created() {
    this.searchInfo.date = moment().format('YYYY-MM-DDTHH:mm:ssZ')
    await this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    handleDownload() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {

      })
    },
    onReset() {
      this.searchInfo = {
        date: moment().format('YYYY-MM-DDTHH:mm:ssZ'),
      }
    },
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
  },
}
</script>

<style lang="scss" scoped>

</style>
