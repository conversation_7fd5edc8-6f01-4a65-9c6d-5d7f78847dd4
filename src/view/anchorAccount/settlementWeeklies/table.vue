<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item v-if="parseInt(userInfo.union_id) === 0" label="Union">
          <el-select v-model="searchInfo.union_id" clearable filterable placeholder="Union">
            <el-option
              v-for="item in uniosList"
              :key="item.id"
              :label="`${item.name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="Anchor ID">
          <el-input v-model="searchInfo.user_id" class="cs-search-input" />
        </el-form-item>
        <el-form-item label="Date">
          <el-date-picker
            v-model="searchInfoTime"
            type="date"
            placeholder="Pick a day"
            @change="onDaterangeChange"
          />
        </el-form-item>
        <el-form-item v-if="parseInt(userInfo.union_id) === 0" label="Settlement">
          <el-select v-model="searchInfo.settlement_result" clearable filterable>
            <el-option
              v-for="item in settlementResultCondition"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">Search</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">Reset</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">
            Export Excel
          </el-button>
          <view v-if="searchInfo.start_at" style="margin-left: 10px;">{{ searchInfo.start_at }}:{{ searchInfo.end_at }}
          </view>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="tableData"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
      >
        <el-table-column align="center" label="Date" prop="week_date" width="180" />
        <el-table-column align="center" label="User Id" prop="user_id" width="120" />
        <el-table-column align="center" label="复核" min-width="140">
          <template #default="scope">
            <el-button size="small" type="primary" @click="goAnchorIncomeDetail(scope.row)">复核</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="Blocked status" prop="BlockedStatus" width="120" />
        <el-table-column align="center" label="Level" prop="levels" width="120" />
        <el-table-column align="center" label="CountryCode" prop="country_code" width="120" />
        <el-table-column align="center" label="Agency" prop="Agency" width="120" />
        <el-table-column align="center" label="Number Of Call Me" prop="push_call_numbers" width="160" />
        <el-table-column align="center" label="Number Of Answered" prop="answer_call_numbers" width="160" />
        <el-table-column align="center" label="Answering Rate" prop="AnsweringRate" width="160" />
        <el-table-column align="center" label="Income" prop="Income" width="160" />
        <el-table-column align="center" label="Can Settle" prop="CanSettle" width="160" />
        <el-table-column align="center" label="Withdrawal Apply"  prop="WithdrawalApply" width="160" />
        <el-table-column align="center" label="US Dollar" prop="USDollar" width="160" />
        <el-table-column align="center" label="Agency revenue" prop="Agencyrevenue" width="160" />
        <el-table-column align="center" label="Historical unsettled revenue" prop="Historicalunsettledrevenue" width="240" />
        <el-table-column align="center" label="Paid Call Duration" prop="paid_call_durations_str" width="135" />
        <el-table-column align="center" label="Paid Call Income" prop="paid_call_income" width="130" />
        <el-table-column align="center" label="Free Call Duration" prop="free_call_durations_str" width="135" />
        <el-table-column align="center" label="Free Call Income" prop="free_call_income" width="130" />
        <el-table-column align="center" label="Total Call Income" prop="TotalCallIncome" width="130" />
        <el-table-column align="center" label="Gift Income" prop="gift_revenue" width="120" />
        <el-table-column align="center" label="Text Income" prop="message_revenue" width="130" />
        <el-table-column align="center" label="Task Income" prop="award_count" width="120" />
        <el-table-column align="center" label="Program Income" prop="program_income" min-width="160"/>
        <el-table-column align="center" label="Online Duration" prop="online_durations_str" width="120" />
        <el-table-column align="center" label="Super Mode Duration" prop="working_durations_str" width="160" />
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import { getSettlementWeekliesList } from '@/api/settlementWeeklies' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import { ElLoading } from 'element-plus'
import moment from 'moment'

const settlementResultCondition = [
  {
    value: 1,
    label: '结算',
  },
  {
    value: 2,
    label: '不结算',
  },
]
export default {
  name: 'SettlementWeeklies',
  mixins: [infoList],
  data() {
    return {
      unionListOptions: [],
      listApi: getSettlementWeekliesList,
      settlementResultCondition: settlementResultCondition,
      dialogFormVisible: false,
      type: '',
      unionDict: {},
      deleteVisible: false,
      multipleSelection: [],
      searchInfoTime: [],
      formData: {
        week_date: '',
        user_id: 0,
        union_id: 0,
        working_condition: 0,
        state: 0,
        push_call_numbers: 0,
        answer_call_numbers: 0,
        settlement_result: 0,
        paid_call_income: 0,
        free_call_income: 0,
        withdrawal_id: 0,
      }
    }
  },
  created() {
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    goAnchorIncomeDetail(row) {
      let mDate = row.week_date
      let dateRange = this.getDateStartEndFromWeek(mDate)
      this.$router.push({
        name: 'anchorIncomeDetail', query: {
          'userId': row.user_id,
          'startTime': dateRange[0],
          'endTime': dateRange[1],
        }
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    formatTotalCallIncome(item) {
      let totalCallIncome = 0.0
      totalCallIncome = parseFloat(item.paid_call_income) + parseFloat(item.free_call_income)
      return totalCallIncome.toFixed(1)
    },
    formatHistorical(item) {
      return item.no_settlement_income
    },
    formatAgencyRevenue(item) {
      return (this.formatUSDollar(item) * 0.2).toFixed(2)
    },
    formatUSDollar(item) {
      return (this.formatIncome(item) / 1000.0).toFixed(2)
    },
    formatCanSettle(item) {
      return item.state !== 1 && this.formatIncome(item) >= 6000
    },
    formatWithdrawalApply(item) {
      return item.withdrawal_id > 0
    },
    formatIncome(item) {
      // 判断这周是否结算,不结算的时候,总收入是no_settlement_income,结算的时候,是settlement_income
      // 因为这个值后端已经加过了
      return parseFloat(item.settlement_income).toFixed(1)
    },
    formatAnsweringRate(item) {
      let rate = 0.0
      if (item.answer_call_numbers !== undefined && item.push_call_numbers !== undefined &&
          item.answer_call_numbers > 0 && item.push_call_numbers > 0) {
        rate = item.answer_call_numbers / item.push_call_numbers
      }
      const rateStr = rate.toFixed(2)
      const rateFloat = parseFloat(rateStr) * 100
      return `${rateFloat}%`
    },
    tableDataFormat() {
      this.tableData.map((currentValue, index, array) => {
        array[index].BlockedStatus = this.formatBlockedState(currentValue)
        array[index].Agency = this.formatUniosName(currentValue.union_id)
        array[index].AnsweringRate = this.formatAnsweringRate(currentValue)
        array[index].Income = this.formatIncome(currentValue)
        array[index].CanSettle = this.formatCanSettle(currentValue)
        array[index].WithdrawalApply = this.formatWithdrawalApply(currentValue)
        array[index].USDollar = this.formatUSDollar(currentValue)
        array[index].Agencyrevenue = this.formatAgencyRevenue(currentValue)
        array[index].Historicalunsettledrevenue = this.formatHistorical(currentValue)
        array[index].paid_call_durations_str = this.formateSeconds(currentValue.paid_call_durations)
        array[index].free_call_durations_str = this.formateSeconds(currentValue.free_call_durations)
        array[index].TotalCallIncome = this.formatTotalCallIncome(currentValue)
        array[index].online_durations_str = this.formateSeconds(currentValue.online_durations)
        array[index].working_durations_str = this.formateSeconds(currentValue.working_durations)
      })
    },
    handleDownload() {
      const tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      const res = this.getTableHeaderProp(tableColumns)
      const tHeader = res[0]
      const filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    onDaterangeChange() {
      if (this.searchInfoTime) {
        let conversionStr = ''
        if (moment(this.searchInfoTime).isoWeekday() === 7) {
          conversionStr = moment(this.searchInfoTime).add(-1, 'days').format('YYYY-MM-DD')
        } else {
          conversionStr = this.searchInfoTime
        }
        this.searchInfo.start_at = moment(conversionStr).day(1).format('YYYY-MM-DD')
        this.searchInfo.end_at = moment(conversionStr).day(7).format('YYYY-MM-DD')
      } else {
        this.searchInfo.start_at = ''
        this.searchInfo.end_at = ''
      }
    },
    onReset() {
      this.searchInfo = {}
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
  },
}
</script>

<style>
</style>

