<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" type="success" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">
            导出
          </el-button>
          <el-checkbox style="margin-left: 8px" v-model="showRate" label="显示百分比" size="large"/>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table border ref="tableData" style="width: 100%" :data="tableData" row-key="id" :max-height="590">
        <el-table-column align="center" label="更新时间" prop="updated_at_str" width="170"/>
        <el-table-column align="center" label="国家代码" prop="country_code" min-width="100"/>
        <el-table-column align="center" label="总主播数" prop="anchor_count" min-width="120"/>
        <el-table-column align="center" label=">20" prop="gt_20" min-width="120"/>
        <el-table-column align="center" label="15_20" prop="range_15_to_20" min-width="120"/>
        <el-table-column align="center" label="10_15" prop="range_10_to_15" min-width="120"/>
        <el-table-column align="center" label="5_10" prop="range_5_to_10" min-width="120"/>
        <el-table-column align="center" label="3_5" prop="range_3_to_5" min-width="120"/>
        <el-table-column align="center" label="2_3" prop="range_2_to_3" min-width="120"/>
        <el-table-column align="center" label="1_2" prop="range_1_to_2" min-width="120"/>
        <el-table-column align="center" label="<1" prop="lt_1" min-width="120"/>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {getDstMsgRecordsList} from '@/api/dstMsgRecords' //  此处请自行替换地址
import infoList from '@/mixins/infoList'

export default {
  name: 'DstMsgRecords',
  mixins: [infoList],
  data() {
    return {
      listApi: getDstMsgRecordsList,
      showRate: false,
    }
  },
  created() {
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    onSubmit() {
      this.page = 1
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    showItemRate(v, all) {
      let rate = this.percent2(v, all)
      if (rate === 0) {
        return v
      } else {
        return `${v}  (${rate})`
      }
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].updated_at_str = this.formatDate(item.updated_at)
        if (this.showRate) {
          data[i].gt_20 = this.showItemRate(item.gt_20, item.anchor_count)
          data[i].range_15_to_20 = this.showItemRate(item.range_15_to_20, item.anchor_count)
          data[i].range_10_to_15 = this.showItemRate(item.range_10_to_15, item.anchor_count)
          data[i].range_5_to_10 = this.showItemRate(item.range_5_to_10, item.anchor_count)
          data[i].range_3_to_5 = this.showItemRate(item.range_3_to_5, item.anchor_count)
          data[i].range_2_to_3 = this.showItemRate(item.range_2_to_3, item.anchor_count)
          data[i].range_1_to_2 = this.showItemRate(item.range_1_to_2, item.anchor_count)
          data[i].lt_1 = this.showItemRate(item.lt_1, item.anchor_count)
        }
      })
    },
  },
}
</script>

<style>
</style>
