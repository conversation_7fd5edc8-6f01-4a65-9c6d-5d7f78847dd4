<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="工会ID">
          <el-input v-model="searchInfo.union_id" placeholder="请输入工会ID" />
        </el-form-item>
        <el-form-item label="工会封禁状态">
          <el-select v-model="searchInfo.union_ban_state" clearable filterable placeholder="封禁状态">
            <el-option
              v-for="item in banStateOption"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工会长封禁状态">
          <el-select v-model="searchInfo.manager_ban_state" clearable filterable placeholder="封禁状态">
            <el-option
              v-for="item in banStateOption"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table @sort-change="tableSort" border ref="tableData" style="width: 100%" :data="tableData" row-key="id" :max-height="590">
        <el-table-column align="center" label="编号" prop="id" sortable="custom" fixed="left" min-width="100"/>
        <el-table-column align="center" label="工会ID" prop="union_id" sortable="custom" fixed="left" min-width="120" />
        <el-table-column align="center" label="工会长ID" prop="manager_id" min-width="120" fixed="left" />
        <el-table-column align="center" label="主播数" prop="anchor_count" sortable="custom" min-width="120" />
        <el-table-column align="center" label="封禁主播数" prop="anchor_ban_count" sortable="custom" min-width="120" />
        <el-table-column align="center" label="封禁比例" prop="anchor_ban_ratio" min-width="120" />
        <el-table-column align="center" label="获得总额" prop="union_income" sortable="custom" min-width="120" />
        <el-table-column align="center" label="余额" prop="balance" min-width="120" />
        <el-table-column align="center" label="工会封禁状态" prop="union_ban_state_str" min-width="160" />
        <el-table-column align="center" label="工会长封禁状态" prop="manager_ban_state_str" min-width="180" />
        <el-table-column align="center" label="写入时间" prop="created_at_str" sortable="custom" min-width="180"/>
        <el-table-column align="center" label="操作时间" prop="updated_at_str" sortable="custom" min-width="180"/>
        <el-table-column align="center" label="操作" min-width="180" fixed="right">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="showBanUnionDialog(scope.row)">封禁</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" top="5px" :before-close="closeDialog" title="弹窗操作">
      <el-form ref="formDataRef" :model="formData" :rules="formDataRules" label-position="right" label-width="120px">
        <el-form-item label="工会ID:">
          <el-input disabled v-model="formData.union_id" clearable />
        </el-form-item>
        <el-form-item label="余额:">
          <el-input disabled v-model="formData.balance" clearable />
        </el-form-item>
        <el-form-item label="扣除比例:">
          <el-input-number class="w100" v-model="formData.deduction_ratio" :min="0" :max="100" :precision="0"/>
        </el-form-item>
        <el-form-item label="封禁工会长天数:">
          <el-input-number class="w100" v-model="formData.manager_ban_days" :min="0" :precision="0"/>
        </el-form-item>
        <el-form-item label="封禁工会天数:">
          <el-input-number class="w100" v-model="formData.union_ban_days" :min="0" :precision="0"/>
        </el-form-item>
        <el-form-item label="封禁原因:" prop="ban_reason">
          <el-input type="textarea" v-model="formData.ban_reason" :autosize="{ minRows: 6, maxRows: 10 }"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script>
import {
  createUnionViolationRecords,
  deleteUnionViolationRecords,
  deleteUnionViolationRecordsByIds,
  updateUnionViolationRecords,
  findUnionViolationRecords,
  getUnionViolationRecordsList,
  unionBanStateList
} from '@/api/unionViolationRecords' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import {banUnion} from "@/api/unionBanRecords";
import {banAnchor} from "@/api/banRecords";
export default {
  name: 'UnionViolationRecords',
  mixins: [infoList],
  data() {
    return {
      listApi: getUnionViolationRecordsList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      banStateOption: [],
      banStateDict: {},
      formData: {},
      formDataRules: {
        ban_reason: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
    }
  },
  created() {
    this.initStateOptions()
  },
  methods: {
    initStateOptions() {
      unionBanStateList().then(res=>{
        if (res.code === 0) {
          let resData = res.data || []
          let newDict = {}
          resData.forEach(item=>{
            newDict[item.value] = item.label
          })
          this.banStateOption = resData
          this.banStateDict = newDict
          this.getTableData(this.nFunc, this.tableDataFormat)
        }
      })
    },
    tableSort(data){
      let columnData = data.column
      let orderData = data.order
      let propData = data.prop
      if (propData !== null && orderData !== null) {
        switch (propData) {
          case 'created_at_str':
            this.searchInfo.sort_prop = 'created_at'
            break
          case 'updated_at_str':
            this.searchInfo.sort_prop = 'updated_at'
            break
          default:
            this.searchInfo.sort_prop = propData
        }
        this.searchInfo.sort_desc = orderData === 'descending'
      } else {
        this.searchInfo.sort_prop = null
        this.searchInfo.sort_desc = true
      }
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {}
    },
    showBanUnionDialog(row){
      this.formData = {
        union_id: row.union_id,
        manager_id: row.manager_id,
        deduction_ratio: 100,
        manager_ban_days: 0,
        union_ban_days: 0,
        ban_reason: '',
        balance: row.balance,
      }
      this.dialogFormVisible = true
    },
    async enterDialog() {
      this.$refs.formDataRef.validate(async (v)=> {
        if (!v) {
          return
        }
        let res = await banUnion(this.formData)
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功'
          })
          this.closeDialog()
          await this.getTableData(this.nFunc, this.tableDataFormat)
        }
      })
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].updated_at_str = this.formatDate(item.updated_at)
        data[i].union_ban_state_str = this.banStateDict[item.union_ban_state]
        data[i].manager_ban_state_str = this.banStateDict[item.manager_ban_state]
        data[i].anchor_ban_ratio = this.percent2(item.anchor_ban_count, item.anchor_count)
      })
    },
  },
}
</script>

<style>
</style>
