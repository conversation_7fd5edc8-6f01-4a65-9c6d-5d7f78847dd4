<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="用户ID">
          <el-input v-model="searchInfo.user_id" placeholder="请输入"/>
        </el-form-item>
        <el-form-item label="审核状态">
          <el-select v-model="searchInfo.task_review_state" placeholder="请选择" clearable>
            <el-option
              v-for="item in stateMapList"
              :key="item.value"
              :label="`${item.label_zh}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" type="success" icon="el-icon-plus" @click="openDialog">新增</el-button>
          <el-button size="mini" type="success" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table border ref="tableData" style="width: 100%" :data="tableData" row-key="id" :max-height="590">
        <el-table-column align="center" label="创建时间" prop="created_at_str" width="180"/>
        <el-table-column align="center" label="主播ID" prop="user_id" width="130"/>
        <el-table-column align="center" label="链接" prop="share_link_url" min-width="120" show-overflow-tooltip>
          <template #default="scope">
            <p class="link" @click="openLink(scope.row.share_link_url)">{{ scope.row.share_link_url }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="图片" prop="share_img_url" width="140">
          <template #default="scope">
            <el-image
              style="width: 34px; height: 34px"
              :src="scope.row.share_img_url"
              :preview-src-list="[scope.row.share_img_url]"
              :initial-index="4"
              fit="cover"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="状态" prop="task_review_state_str" width="120"/>
        <el-table-column align="center" label="奖励数量" prop="award_count" width="130"/>
        <!--<el-table-column align="center" label="奖励是否发放  1-待发放奖励，2-奖励啥时候发放" prop="award_send" min-width="120" />-->
        <!--<el-table-column align="center" label="用户任务id" prop="task_link_id" min-width="120" />-->
        <!--<el-table-column align="center" label="任务大类id" prop="task_class_db_id" min-width="120" />-->
        <el-table-column align="center" label="操作" width="150">
          <template #default="scope">
            <el-button type="text" size="small" @click="awardPass(scope.row)">通过</el-button>
            <el-button type="text" size="small" @click="submitRefuse(scope.row)">拒绝</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="awardDialogVisible" top="5px" title="奖励">
      <el-form :model="formAwardData" :rules="formAwardDataRules" label-position="right" label-width="120px">
        <el-form-item label="用户id:" prop="user_id">
          <el-input disabled v-model.number="formAwardData.user_id"/>
        </el-form-item>
        <el-form-item label="奖励数量:" prop="award_count">
          <el-input-number style="width: 100%;" v-model="formAwardData.award_count"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" type="primary" @click="submitPass">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createUserTaskShareRd,
  deleteUserTaskShareRd,
  findUserTaskShareRd,
  getUserTaskShareRdList,
  taskShareReviewStateMapList,
  updateUserTaskShareRd
} from '@/api/userTaskShareRd'
import infoList from '@/mixins/infoList'

export default {
  name: 'UserTaskShareRd',
  mixins: [infoList],
  data() {
    return {
      listApi: getUserTaskShareRdList,
      dialogFormVisible: false,
      awardDialogVisible: false,
      dialogTitle: '',
      dialogType: '',
      deleteVisible: false,
      multipleSelection: [],
      stateMapList: [],
      stateMap: [],
      formData: {
        user_id: null,
        share_link_url: null,
        share_img_url: null,
        award_count: null,
        award_send: null,
        task_review_state: null,
        task_link_id: null,
        task_class_db_id: null,
      },
      formAwardData: {
        user_id: null,
        award_count: null,
      },
      formDataRules: {
        user_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        share_link_url: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        share_img_url: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        award_count: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        award_send: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        task_review_state: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        task_link_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        task_class_db_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
      formAwardDataRules: {
        user_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        award_count: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
    }
  },
  created() {
    this.initStateMapList()
  },
  methods: {
    awardPass(row) {
      this.formAwardData = {
        id: row.id,
        user_id: row.user_id,
        award_count: row.award_count,
      }
      this.awardDialogVisible = true
    },

    openLink(url) {
      window.open(url, '_blank')
    },
    initStateMapList() {
      taskShareReviewStateMapList().then(res => {
        if (res.code === 0) {
          this.stateMapList = res.data || []
          this.stateMapList.forEach(item => {
            this.stateMap[item.value] = item.label_zh
          })
          this.getTableData(this.nFunc, this.tableDataFormat)
        }
      })
    },
    onReset() {
      this.searchInfo = {}
    },
    onSubmit() {
      this.page = 1
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteUserTaskShareRd(row)
      })
    },
    async updateUserTaskShareRd(row) {
      const res = await findUserTaskShareRd({id: row.id})
      this.dialogType = 'update'
      this.dialogTitle = '更新'
      if (res.code === 0) {
        this.formData = res.data
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {}
    },
    closeAwardDialog() {
      this.awardDialogVisible = false
      this.formAwardData = {}
    },
    async deleteUserTaskShareRd(row) {
      const res = await deleteUserTaskShareRd({id: row.id})
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    async enterDialog() {
      let res
      switch (this.dialogType) {
        case 'create':
          res = await createUserTaskShareRd(this.formData)
          break
        case 'update':
          res = await updateUserTaskShareRd(this.formData)
          break
        default:
          res = await createUserTaskShareRd(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '成功'
        })
        this.closeDialog()
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    async enterAwardDialog() {
      let res = await updateUserTaskShareRd(this.formAwardData)
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '成功'
        })
        this.closeAwardDialog()
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    submitPass() {
      this.formAwardData.task_review_state = 2
      this.enterAwardDialog()
    },
    submitRefuse(row) {
      this.formAwardData = row
      this.formAwardData.task_review_state = 3
      this.enterAwardDialog()
    },
    openDialog() {
      this.dialogTitle = '创建'
      this.dialogType = 'create'
      this.dialogFormVisible = true
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].updated_at_str = this.formatDate(item.updated_at)
        data[i].task_review_state_str = this.stateMap[item.task_review_state]
      })
    },
  },
}
</script>

<style>
.link {
  color: #8f8fff;
  cursor: pointer;
}
</style>
