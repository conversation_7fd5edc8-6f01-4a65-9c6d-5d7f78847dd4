<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <!--<el-button size="mini" type="success" icon="el-icon-plus" @click="openDialog">新增</el-button>-->
          <el-button size="mini" type="success" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>
          <el-checkbox style="margin-left: 8px" v-model="showRate" label="显示百分比" size="large" />
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table border ref="tableData" style="width: 100%" :data="tableData" row-key="id" :max-height="590">
        <el-table-column align="center" label="更新时间" prop="updated_at_str" min-width="180"/>
        <el-table-column align="center" label="国家代码" prop="country_code" min-width="120"/>
        <el-table-column align="center" label="总主播数" prop="anchor_count" min-width="120"/>
        <el-table-column align="center" label=">200" prop="gt_200" min-width="120"/>
        <el-table-column align="center" label="150_200" prop="range_150_to_200" min-width="120"/>
        <el-table-column align="center" label="120_150" prop="range_120_to_150" min-width="120"/>
        <el-table-column align="center" label="110_120" prop="range_110_to_120" min-width="120"/>
        <el-table-column align="center" label="100_110" prop="range_100_to_110" min-width="120"/>
        <el-table-column align="center" label="90_100" prop="range_90_to_100" min-width="120"/>
        <el-table-column align="center" label="80_90" prop="range_80_to_90" min-width="120"/>
        <el-table-column align="center" label="70_80" prop="range_70_to_80" min-width="120"/>
        <el-table-column align="center" label="60_70" prop="range_60_to_70" min-width="120"/>
        <el-table-column align="center" label="30_60" prop="range_30_to_60" min-width="120"/>
        <el-table-column align="center" label="<30" prop="lt_30" min-width="120"/>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {getDstVideoRecordsList} from '@/api/dstVideoRecords' //  此处请自行替换地址
import infoList from '@/mixins/infoList'

export default {
  name: 'DstVideoRecords',
  mixins: [infoList],
  data() {
    return {
      listApi: getDstVideoRecordsList,
      showRate: false,
    }
  },
  created() {
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    onSubmit() {
      this.page = 1
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    showItemRate(v, all) {
      let rate = this.percent2(v, all)
      if (rate === 0) {
        return v
      } else {
        return `${v}  (${rate})`
      }
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].updated_at_str = this.formatDate(item.updated_at)
        if (this.showRate) {
          data[i].gt_200 = this.showItemRate(item.gt_200, item.anchor_count)
          data[i].range_150_to_200 = this.showItemRate(item.range_150_to_200, item.anchor_count)
          data[i].range_120_to_150 = this.showItemRate(item.range_120_to_150, item.anchor_count)
          data[i].range_110_to_120 = this.showItemRate(item.range_110_to_120, item.anchor_count)
          data[i].range_100_to_110 = this.showItemRate(item.range_100_to_110, item.anchor_count)
          data[i].range_90_to_100 = this.showItemRate(item.range_90_to_100, item.anchor_count)
          data[i].range_80_to_90 = this.showItemRate(item.range_80_to_90, item.anchor_count)
          data[i].range_70_to_80 = this.showItemRate(item.range_70_to_80, item.anchor_count)
          data[i].range_60_to_70 = this.showItemRate(item.range_60_to_70, item.anchor_count)
          data[i].range_30_to_60 = this.showItemRate(item.range_30_to_60, item.anchor_count)
          data[i].lt_30 = this.showItemRate(item.lt_30, item.anchor_count)
        }
      })
    },
  },
}
</script>

<style>
</style>
