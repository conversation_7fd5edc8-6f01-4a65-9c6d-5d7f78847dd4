<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="用户id">
          <el-input v-model.number="searchInfo.user_id" placeholder="请输入用户id" clearable/>
        </el-form-item>
        <el-form-item label="用户国家">
          <el-input v-model="searchInfo.country_code" placeholder="请输入用户国家" clearable/>
        </el-form-item>
        <el-form-item label="充值Min">
          <el-input v-model="searchInfo.charge_min" clearable/>
        </el-form-item>
        <el-form-item label="充值Max">
          <el-input v-model="searchInfo.charge_max" clearable/>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table border ref="tableData" style="width: 100%" @sort-change="tableSort" :data="tableData" row-key="id"
                :max-height="590">
        <el-table-column align="center" label="编号" prop="id" fixed="left" sortable="custom" min-width="80"/>
        <el-table-column align="center" label="用户id" prop="user_id" sortable="custom" fixed="left" min-width="120">
          <template #default="scope">
            <p class="blue" @click="viewUserDetailInfo(scope.row)">{{ scope.row.user_id }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="用户国家" prop="country_code" sortable="custom" min-width="120"/>
        <el-table-column align="center" label="注册天数" prop="reg_days" sortable="custom" min-width="120"/>
        <el-table-column align="center" label="累计充值($)" prop="charge_usd" min-width="120"/>
        <el-table-column align="center" label="亲密好友" prop="intimate_count" min-width="120"/>
        <el-table-column align="center" label="总计充值(钻石)" prop="charge_diamonds" min-width="120"/>
        <el-table-column align="center" label="总计消耗(钻石)" prop="consume_diamonds" min-width="120"/>
        <el-table-column align="center" label="视频消耗" prop="consume_call_rate" min-width="120"/>
        <el-table-column align="center" label="消息消耗" prop="consume_msg_rate" min-width="120"/>
        <el-table-column align="center" label="礼物消耗" prop="consume_gift_rate" min-width="120"/>
        <el-table-column align="center" label="其他消耗" prop="consume_other_rate" min-width="120"/>
        <el-table-column align="center" label="前3国家占比" prop="country_rate" min-width="260" show-overflow-tooltip/>
        <el-table-column align="center" label="用户标签" prop="tags" min-width="120"/>
        <el-table-column align="center" label="更新时间" prop="updated_at_str" sortable="custom" min-width="180"/>
        <el-table-column align="center" label="操作" fixed="right" min-width="120">
          <template #default="scope">
            <el-button type="primary" size="small" @click="showTagDialog(scope.row)">标签</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeListNormal"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作">
      <el-form ref="formDataRef" :model="formData" :rules="formDataRules" label-position="right" label-width="120px">
        <el-form-item label="用户id:">
          <el-input disabled v-model.number="formData.user_id"/>
        </el-form-item>
        <el-form-item label="用户标签:" prop="tags">
          <el-input v-model="formData.tags">
            <template #suffix>
              <el-tooltip effect="dark" content="允许输入多个用英文逗号,隔开" placement="bottom">
                <i class="el-icon-question el-q"></i>
              </el-tooltip>
            </template>
          </el-input>
        </el-form-item>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-drawer v-model="userDetailVisible" @close="userDetailDrawerClose" size="70%" :with-header="false">
      <el-row :gutter="5">
        <el-col :span="4">
          <el-image
            style="width: 100%;"
            :src="userDetailInfo.avatar"
            :preview-src-list="[userDetailInfo.avatar]"
          >
            <template #error>
              <el-image
                style="width: 100%;"
                :src="defaultAvatar"
                :preview-src-list="[defaultAvatar]"
              />
            </template>
          </el-image>
          <p>应用: {{formatAppName(userDetailInfo.appId)}} ({{userDetailInfo.appId}})</p>
        </el-col>
        <el-col :span="20">
          <el-descriptions direction="horizontal" :column="2" size="small" border>
            <el-descriptions-item align="center" label="ID">{{ userDetailInfo.id }}</el-descriptions-item>
            <el-descriptions-item align="center" label="昵称">{{ userDetailInfo.nickname }}</el-descriptions-item>
            <el-descriptions-item align="center" label="性别">{{ formatSex(userDetailInfo.gender) }}
            </el-descriptions-item>
            <el-descriptions-item align="center" label="出生日期">{{ formatDate(userDetailInfo.birthday) }}
            </el-descriptions-item>
            <el-descriptions-item align="center" label="定位">
              <el-tooltip
                class="box-item"
                effect="dark"
                :content="JSON.stringify(userDetailInfo.location)"
                placement="top"
              >
                <div>
                  {{ userDetailInfo.location === null ? "" : userDetailInfo.location?.address }}
                </div>
              </el-tooltip>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="身份">{{ formatRole(userDetailInfo.role) }}
            </el-descriptions-item>
            <el-descriptions-item align="center" label="注册时间">{{ formatDate(userDetailInfo.created_at) }}
            </el-descriptions-item>
            <el-descriptions-item align="center" label="活跃时间">{{ formatDate(userDetailInfo.updated_at) }}
            </el-descriptions-item>
            <el-descriptions-item align="center" label="钻石余量">{{ userDetailInfo.diamonds }}</el-descriptions-item>
            <el-descriptions-item align="center" label="VIP到期">{{ formatDate(userDetailInfo.vipExpireAt) }}
            </el-descriptions-item>
            <el-descriptions-item align="center" label="最后付费时间">{{ formatDate(userOrderStatistics.payment_at) }}
            </el-descriptions-item>
            <el-descriptions-item align="center" label="累计付费">共 {{ userOrderStatistics.cnt }} 次, 累计
              {{ userOrderStatistics.price }} 美金
            </el-descriptions-item>
            <el-descriptions-item align="center" label="绑定关系">{{ userDetailInfo.manager_id?userDetailInfo.manager_id:'' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
      <el-row :gutter="5">
        <el-col :span="12">
          <h1>充值记录</h1>
          <div class="gva-table-box">
            <el-table
              size="small"
              style="width: 100%"
              v-loading="chargeTableLoading"
              :data="chargeTableData"
              max-height="390"
              row-key="id"
            >
              <el-table-column align="center" label="日期" prop="created_at" width="160">
                <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
              </el-table-column>
              <el-table-column align="center" label="充值渠道" prop="payment" min-width="100">
                <template #default="scope">{{ formatPayment(scope.row.payment) }}</template>
              </el-table-column>
              <el-table-column align="center" label="充值产品" prop="product_name" min-width="100"/>
              <el-table-column align="center" label="订单金额" prop="price" min-width="100"/>
            </el-table>
            <div class="gva-pagination">
              <el-pagination
                v-show="chargeTableTotal > 0"
                layout="total, pager"
                v-loading="chargeTableLoading"
                :current-page="chargeTablePage"
                :page-size="chargeTablePageSize"
                :page-sizes="pageSizeList"
                :total="chargeTableTotal"
                @current-change="chargeTablePageChange"
                @size-change="chargeTableSizeChange"
              />
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <h1>消耗记录</h1>
          <div class="gva-table-box">
            <el-table
              size="small"
              style="width: 100%"
              :data="consumeTableData"
              v-loading="consumeTableLoading"
              max-height="390"
              row-key="id"
            >
              <el-table-column align="center" label="日期" prop="created_at" width="160">
                <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
              </el-table-column>
              <el-table-column align="center" label="主播ID" prop="dst_id" width="100"/>
              <el-table-column align="center" label="通话时长" prop="duration" min-width="80"/>
              <el-table-column align="center" label="钻石消耗" prop="dst_income" min-width="80"/>
              <el-table-column align="center" label="通话类型" min-width="80">
                <template #default="scope">
                  <el-tag v-if="scope.row.src_free" type="info">免费通话</el-tag>
                  <el-tag v-else type="success">付费通话</el-tag>
                </template>
              </el-table-column>
            </el-table>
            <div class="gva-pagination">
              <el-pagination
                v-show="consumeTableTotal > 0"
                layout="total, pager"
                :current-page="consumeTablePage"
                :page-size="consumeTablePageSize"
                :page-sizes="pageSizeList"
                v-loading="consumeTableLoading"
                :total="consumeTableTotal"
                @current-change="consumeTablePageChange"
                @size-change="consumeTableSizeChange"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </el-drawer>
  </div>
</template>

<script>
import {getUserChargeTagQueuesListNew, updateUserChargeTagQueues} from '@/api/userChargeTagQueues' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import defaultAvatar from '@/assets/default_avatar.png'
import {getOrdersList, getUserOrderStatistics} from "@/api/orders";
import {getList} from "@/api/connectedReports";
import {getUsersList} from "@/api/users";

export default {
  name: 'UserChargeTagQueues',
  mixins: [infoList],
  data() {
    return {
      listApi: getUserChargeTagQueuesListNew,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      userDetailVisible: false,
      defaultAvatar: defaultAvatar,
      userDetailInfo: {},
      chargeTableLoading: false,
      chargeTableData: [],
      chargeTablePage: 1,
      chargeTablePageSize: 10,
      chargeTableTotal: 0,
      consumeTableLoading: false,
      consumeTableData: [],
      consumeTablePage: 1,
      consumeTablePageSize: 10,
      consumeTableTotal: 0,
      userOrderStatistics: {
        cnt: 0,
        price: 0,
        payment_at: '',
      },
      multipleSelection: [],
      formData: {
        app_id: null,
        user_id: null,
        country_code: null,
        created_time: null,
        updated_time: null,
        tags: null,
      },
      formDataRules: {
        user_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        tags: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
    }
  },
  created() {
    // this.searchInfo.user_id = 76725685
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    userDetailDrawerClose() {
      this.userDetailInfo = {}
      this.chargeTableData = []
      this.chargeTablePage = 1
      this.chargeTablePageSize = 10
      this.chargeTableTotal = 0
      this.consumeTableData = []
      this.consumeTablePage = 1
      this.consumeTablePageSize = 10
      this.consumeTableTotal = 0
      this.userOrderStatistics = {
        cnt: 0,
        price: 0,
        payment_at: '',
      }
    },
    chargeTablePageChange(v) {
      this.chargeTablePage = v
      this.getUserChargeTableData()
    },
    chargeTableSizeChange(v) {
      this.chargeTablePageSize = v
      this.getUserChargeTableData()
    },
    consumeTablePageChange(v) {
      this.consumeTablePage = v
      this.getUserConsumeTableData()
    },
    consumeTableSizeChange(v) {
      this.consumeTablePageSize = v
      this.getUserConsumeTableData()
    },
    viewUserDetailInfo(row) {
      // 获取用户信息
      getUsersList({id: row.user_id}).then(res => {
        if (res.code === 0) {
          this.userDetailInfo = res.data.list.find(item => item.id === row.user_id)
          this.getUserChargeTableData()
          this.getUserConsumeTableData()
          this.getUserOrderStatistics()
          this.userDetailVisible = true
        }
      })
    },
    getUserChargeTableData() {
      this.chargeTableLoading = true
      getOrdersList({
        page: this.chargeTablePage,
        pageSize: this.chargeTablePageSize,
        user_id: this.userDetailInfo.id,
        state: 4, // 已完成的订单
      }).then(res => {
        this.chargeTableLoading = false
        this.chargeTableData = res.data.list
        this.chargeTableTotal = res.data.total
      }).catch(err=>{
        this.chargeTableLoading = false
      })
    },
    getUserOrderStatistics() {
      getUserOrderStatistics({
        user_id: this.userDetailInfo.id,
      }).then(res => {
        if (res.code === 0) {
          this.userOrderStatistics = res.data
        }
      })
    },
    getUserConsumeTableData() {
      this.consumeTableLoading = true
      getList({
        page: this.consumeTablePage,
        pageSize: this.consumeTablePageSize,
        src_id: this.userDetailInfo.id,
        state: 4, // 已完成的订单
      }).then(res => {
        this.consumeTableLoading = false
        this.consumeTableData = res.data.list
        this.consumeTableTotal = res.data.total
      }).catch(err=>{
        this.consumeTableLoading = false
      })
    },

    showTagDialog(row) {
      this.formData = row
      this.dialogFormVisible = true
    },
    tableSort(data) {
      let orderData = data.order
      let propData = data.prop
      if (propData !== null && orderData !== null) {
        switch (propData) {
          case "id":
            this.searchInfo.sort_prop = 'c.id'
            break
          case "user_id":
            this.searchInfo.sort_prop = 'c.user_id'
            break
          case "country_code":
            this.searchInfo.sort_prop = 'c.country_code'
            break
          case "reg_days":
            this.searchInfo.sort_prop = 'u.created_at'
            break
          case "updated_at_str":
            this.searchInfo.sort_prop = 'u.updated_at'
            break
          default:
            this.searchInfo.sort_prop = propData
            break
        }
        if (propData === 'reg_days') {
          this.searchInfo.sort_desc = orderData === 'ascending'
        } else {
          this.searchInfo.sort_desc = orderData !== 'ascending'
        }
      } else {
        this.searchInfo.sort_prop = null
        this.searchInfo.sort_desc = false
      }
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    onReset() {
      this.searchInfo = {}
    },
    onSubmit() {
      this.page = 1
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    closeDialog() {
      this.$refs.formDataRef.resetFields()
      this.dialogFormVisible = false
      this.formData = {}
    },
    enterDialog() {
      this.$refs.formDataRef.validate((valid) => {
        if (valid) {
          updateUserChargeTagQueues(this.formData).then(res => {
            if (res.code === 0) {
              this.$message({
                type: 'success',
                message: '成功'
              })
              this.closeDialog()
              this.getTableData(this.nFunc, this.tableDataFormat)
            }
          })
        }
      })
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].updated_at_str = this.formatDate(item.updated_at)
      })
    },
  },
}
</script>

<style>
.blue {
  color: #0d84ff;
  cursor: pointer;
}
</style>
