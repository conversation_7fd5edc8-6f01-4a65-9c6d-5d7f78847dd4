<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="主播id">
          <el-input v-model.number="searchInfo.anchor_id" clearable placeholder="请输入主播id"/>
        </el-form-item>
        <el-form-item label="封禁状态">
          <el-select v-model="searchInfo.recover_state" clearable style="width: 80px;">
            <el-option label="正常" :value="1"/>
            <el-option label="封禁" :value="2"/>
          </el-select>
        </el-form-item>
        <el-form-item label="封面Min">
          <el-input v-model="searchInfo.cover_count_min" clearable style="width: 60px"/>
        </el-form-item>
        <el-form-item label="封面Max">
          <el-input v-model="searchInfo.cover_count_max" clearable style="width: 60px"/>
        </el-form-item>
        <el-form-item label="国家">
          <el-select v-model="searchInfo.country_code" placeholder="请选择" clearable>
            <el-option
              v-for="item in countryCodeOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="在线情况">
          <el-select v-model="searchInfo.online" placeholder="请选择" clearable>
            <el-option
              v-for="item in onlineOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="视频等级">
          <el-select v-model="searchInfo.levels" clearable style="width: 100px;">
            <el-option
              v-for="item in levelsOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="封面状态">
          <el-select v-model="searchInfo.cover_state" clearable style="width: 80px;">
            <el-option label="正常" :value="1"/>
            <el-option label="作废" :value="2"/>
          </el-select>
        </el-form-item>
        <el-form-item label="主播类型">
          <el-select v-model="searchInfo.fake_anchor" clearable style="width: 80px;">
            <el-option label="真实" :value="1"/>
            <el-option label="虚拟" :value="2"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" type="success" icon="el-icon-plus" @click="openAddAnchorDialog">添加主播</el-button>
          <!--<el-button size="mini" type="success" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>-->
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table border ref="tableData" style="width: 100%" :data="tableData" row-key="id" :max-height="590"
                @sort-change="tableSort">
        <el-table-column align="center" label="创建日期" prop="created_at_str" :min-width="180"/>
        <el-table-column align="center" label="主播id" prop="anchor_id" :min-width="120">
          <template #default="scope">
            <p @click="updateUsers(scope.row)" class="link">{{ scope.row.anchor_id }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="头像" prop="avatar" :width="70">
          <template #default="scope">
            <el-image
              style="width: 40px; height: 40px;"
              :src="scope.row.avatar"
              :preview-src-list="[scope.row.avatar]"
              :initial-index="4"
              fit="cover"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="封禁状态" prop="recovery" :min-width="120">
          <template #default="scope">
            <el-tag :type="scope.row.recovery === 0 ? 'success' : 'info'">
              {{ scope.row.recovery === 0 ? '正常' : '封禁' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="近7天接听率" prop="answer_rate_7_day" :min-width="130"/>
        <el-table-column align="center" label="近7天次均时长" prop="recent_7_day_duration_avg" :min-width="160"/>
        <el-table-column align="center" label="封面数量" prop="cover_count" :min-width="120">
          <template #default="scope">
            <p @click="showImgDialog(scope.row)" class="link">{{ scope.row.cover_count }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="在线" prop="online_str" :min-width="120">
          <template #default="scope">
            <el-tag v-if="scope.row.online === 3" type="success">{{ scope.row.online_str }}</el-tag>
            <el-tag v-else-if="scope.row.online === 2" type="danger">{{ scope.row.online_str }}</el-tag>
            <el-tag v-else type="info">{{ scope.row.online_str }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="国家" prop="country_code" :min-width="120"/>
        <el-table-column align="center" label="新主播" prop="is_new" :min-width="120">
          <template #default="scope">
            <p @click="showUpdateIsNewDialog(scope.row)" class="link">{{ scope.row.is_new }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="主播类型" prop="fake_str" :min-width="120"/>
        <el-table-column align="center" label="视频等级" prop="video_level" :min-width="120">
          <template #default="scope">
            <p @click="showAnchorDataDialog(scope.row.anchor_id)" class="link">{{ scope.row.video_level }}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="收入等级" prop="income_level" :min-width="120"/>
        <el-table-column align="center" label="封面状态" prop="cover_state_str" :min-width="120"/>
        <el-table-column align="center" label="月呼叫" sortable="custom" prop="month_call" :min-width="120"/>
        <el-table-column align="center" label="周呼叫" sortable="custom" prop="week_call" :min-width="120"/>
        <el-table-column align="center" label="日呼叫" sortable="custom" prop="day_call" :min-width="120"/>
        <el-table-column align="center" label="操作" :min-width="180" fixed="right">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" @click="updateFunc(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog
      v-model="dialogFormVisible"
      top="5px"
      :before-close="closeDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="主播推荐"
      width="600px">
      <el-form :model="formData" label-position="right">
        <el-form-item label="主播id:" prop="anchor_id">
          <el-input v-model.number="formData.anchor_id" @blur="searchAnchorInfo">
            <template #append>
              <el-button icon="el-icon-search" @click.stop="searchAnchorInfo"></el-button>
            </template>
          </el-input>
        </el-form-item>
      </el-form>

      <el-row :gutter="10">
        <el-col :span="12">
          <el-image
            style="width: 100%"
            :src="userForm.avatar"
            :zoom-rate="1.2"
            :preview-src-list="[userForm.avatar]"
            :initial-index="4"
            fit="cover"
          />
        </el-col>
        <el-col :span="12">
          <el-descriptions direction="horizontal" :column="1" border size="mini">
            <el-descriptions-item label="ID">{{ userForm.id }}</el-descriptions-item>
            <el-descriptions-item label="名称">{{ userForm.nickname }}</el-descriptions-item>
            <el-descriptions-item label="工会">{{ formatUniosName(userForm.union_id) }}</el-descriptions-item>
            <el-descriptions-item label="等级">{{ userForm.levels }}</el-descriptions-item>
            <el-descriptions-item label="国家">{{ userForm.country_code }}</el-descriptions-item>
            <el-descriptions-item label="时间">{{ formatDate(userForm.updated_at) }}</el-descriptions-item>
            <el-descriptions-item label="状态">{{ formatRecovery(userForm.recovery) }}</el-descriptions-item>
            <el-descriptions-item label="类型">
              <el-tag :type="isFakeAnchor(userForm) ? 'info' : 'success'">
                {{ isFakeAnchor(userForm) ? '虚拟主播' : '真实主播' }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-button :disabled="userForm.id === undefined" style="width: 100%;" type="warning"
                     @click="submitRecomNotDialog">取消推荐
          </el-button>
        </el-col>
        <el-col :span="12">
          <el-button :disabled="userForm.id === undefined" style="width: 100%;" type="primary"
                     @click="submitRecomDialog">确定推荐
          </el-button>
        </el-col>
      </el-row>

    </el-dialog>
    <el-dialog
      v-model="imgDialogVisible"
      top="5px"
      :before-close="closeImgDialog"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      title="添加主播封面图"
      destroy-on-close
    >
      <div class="flex justify-between">
        <p>主播ID: {{ imgForm.anchor_id }}</p>
        <el-checkbox v-model="upClose">上传完关闭</el-checkbox>
        <el-upload
          ref="uploadRef"
          action="https://jsonplaceholder.typicode.com/posts/"
          multiple
          :auto-upload="false"
        >
          <template #trigger>
            <el-button :disabled="upDisabled" type="primary">选择文件</el-button>
          </template>
          <el-button :disabled="upDisabled" class="ml-6" type="success" @click="submitUpload">上传</el-button>
        </el-upload>
      </div>

      <el-row :gutter="10">
        <el-col :span="8" v-for="(item, index) in imgTableData" :key="index">
          <el-card>
            <el-image
              style="width: 100%; height: 200px;"
              :src="item.cover_url"
              :preview-src-list="cover_url_list"
              :initial-index="4"
              fit="cover"
            />
            <p>时间: {{ formatDate(item.created_at) }}</p>
            <el-button type="success" v-if="item.deleted_at" @click="restoreRecomDetail(item)">恢复</el-button>
            <el-button type="warning" v-else @click="deleteRecomDetail(item)">作废</el-button>
          </el-card>
        </el-col>
      </el-row>
    </el-dialog>

    <el-dialog v-model="isNewDialogShow" top="5px" :before-close="closeIsNewDialog" title="弹窗操作">
      <el-form ref="formIsNewRef" :model="formIsNew" label-position="right" label-width="120px">
        <el-form-item label="主播ID:">
          <el-input disabled v-model="formIsNew.anchor_id"/>
        </el-form-item>
        <el-form-item label="新主播:">
          <el-input-number class="w100" v-model="formIsNew.is_new" :min="0" :precision="0"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" type="primary" @click="enterIsNewDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <user-info-update-dialog ref="userInfoUpdateDialogRef" :user_id="userInfoUpdateId"></user-info-update-dialog>
    <anchor-data-dialog ref="anchorDataDialogRef" :dst_id="anchorDstId"></anchor-data-dialog>
  </div>
</template>

<script>
import {
  createAnchorCoverRecommendations,
  deleteAnchorCoverRecommendations,
  getAnchorCoverRecommendationsList,
  updateAnchorCoverRecommendations
} from '@/api/anchorCoverRecommendations'
import infoList from '@/mixins/infoList'
import {banUser, cancelAuditUsers, createUsers, getUserDetail, unsealUser, updateUsers} from "@/api/users";
import {
  batchCreateCoverDetails,
  createAnchorCoverRecommendationsDetails,
  deleteAnchorCoverRecommendationsDetails,
  getAnchorCoverRecommendationsDetailsAll,
  restoreAnchorCoverRecommendationsDetails
} from "@/api/anchorCoverRecommendationsDetails";
import draggable from "vuedraggable";
import {getPersonalityLabelsTree} from "@/api/personality_labels";
import {amazonS3Token} from "@/api/amazonS3";
import {PutObjectCommand, S3Client} from "@aws-sdk/client-s3";
import UserInfoUpdateDialog from "@/components/UserInfoUpdateDialog/index.vue";

const path = import.meta.env.VITE_BASE_API
export default {
  name: 'AnchorCoverRecommendations',
  components: {UserInfoUpdateDialog, draggable},
  mixins: [infoList],
  data() {
    return {
      // 修改主播信息弹窗开始
      dialogAnchorFormVisible: false,
      isDragging: false,
      videoVisible: false,
      previewImageVisible: false,
      videoUrl: null,
      previewImagePath: null,
      dialogAnchorType: null,
      avatarFileList: [],
      personalityLabelsOptions: [],
      formAnchorData: {},
      // 修改主播信息弹窗结束
      listApi: getAnchorCoverRecommendationsList,
      dialogFormVisible: false,
      path,
      imgDialogVisible: false,
      upDisabled: false,
      upClose: true,
      anchorDstId: 0,
      userInfoUpdateId: 0,
      imgTableData: [],
      cover_url_list: [],
      imgForm: {
        anchor_id: null,
      },
      userForm: {},
      formData: {anchor_id: null},
      formIsNew: {anchor_id: null, is_new: 0},
      isNewDialogShow: false,
    }
  },
  created() {
    this.getTableData(this.nFunc, this.tableDataFormat)
    this.getPersonalityLabelsList()
  },
  methods: {
    showUserInfoUpdateDialog(dst_id) {
      this.userInfoUpdateId = dst_id
      this.$refs.userInfoUpdateDialogRef.dialogVisible = true
    },
    showUpdateIsNewDialog(row) {
      this.formIsNew = {id: row.id, anchor_id: row.anchor_id, is_new: row.is_new}
      this.isNewDialogShow = true
    },
    closeIsNewDialog() {
      this.isNewDialogShow = false
      this.formIsNew = {anchor_id: null, is_new: 0}
    },
    enterIsNewDialog() {
      updateAnchorCoverRecommendations(this.formIsNew).then(res => {
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功'
          })
          this.closeIsNewDialog()
          this.getTableData(this.nFunc, this.tableDataFormat)
        }
      })

    },
    tableSort(data) {
      let orderData = data.order
      let propData = data.prop
      if (propData !== null && orderData !== null) {
        this.searchInfo.sort_prop = propData
        this.searchInfo.sort_desc = orderData !== 'ascending'
      } else {
        this.searchInfo.sort_prop = null
        this.searchInfo.sort_desc = false
      }
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    async submitUpload() {
      let fileList = this.$refs.uploadRef.uploadFiles
      let fLen = fileList.length
      if (fLen === 0) {
        this.$message.error('请选择文件')
        return
      }
      let valid = true
      fileList.forEach(f => {
        if (f.size > 10 * 1024 * 1024) {
          console.log(f)
          valid = false
          this.$message.error('文件大小不能超过10MB')
        }
      })
      if (!valid) {
        return
      }
      let keyPrefix = 'image'
      let bucketName = 's3.oklove.top'
      this.upDisabled = true
      let tokenRes = await amazonS3Token()
      if (tokenRes.code === 0) {
        let tokenData = tokenRes.data
        let s3Client = new S3Client({
          credentials: {
            accessKeyId: tokenData.AccessKeyId,
            secretAccessKey: tokenData.SecretAccessKey,
            sessionToken: tokenData.SessionToken,
          },
          region: tokenData.region,
        })
        let fileReq = []
        for (const f of fileList) {
          let fileExt = this.getFileExt(f.name)
          let fileName = this.uuidFileName(fileExt)
          let objKey = `${keyPrefix}${fileName}`
          const command = new PutObjectCommand({
            Bucket: bucketName,
            Key: objKey,
            Body: f.raw,
            ACL: "public-read",
            ContentType: f.raw.type,
          });
          let sendData
          try {
            sendData = await s3Client.send(command)
          } catch (e) {
            console.log(e)
            this.$message.error(`上传失败${f.name},错误原因请打开控制台查看`)
            return
          }
          if (sendData.$metadata.httpStatusCode === 200) {
            fileReq.push({
              anchor_id: this.imgForm.anchor_id,
              cover_url: `https://${bucketName}/${objKey}`,
            })
          } else {
            this.upDisabled = false
            this.$message.error(`上传失败${f.name}`)
            return
          }
        }

        let batRes = await batchCreateCoverDetails({anchor_id: this.imgForm.anchor_id, list: fileReq})
        if (batRes.code === 0) {
          this.upDisabled = false
          this.$refs.uploadRef.uploadFiles = []

          this.$message({
            type: 'success',
            message: '成功'
          })
          if (this.upClose) {
            this.closeImgDialog()
            this.getTableData(this.nFunc, this.tableDataFormat)
          }
          this.getImgData(this.imgForm.anchor_id)

        } else {
          this.upDisabled = false
          this.$message.error(batRes.msg)
        }
      } else {
        this.upDisabled = false
        this.$message.error(tokenRes.msg)
      }
    },
    showAnchorDataDialog(dst_id) {
      this.anchorDstId = dst_id
      this.$refs.anchorDataDialogRef.dialogVisible = true
    },
    // 修改主播信息弹窗开始
    async updateUsers(row) {
      this.showUserInfoUpdateDialog(row.anchor_id)
    },
    closeAnchorDialog() {
      this.avatarFileList = []
      this.dialogAnchorFormVisible = false
      this.formAnchorData = {}
    },
    async enterAnchorDialog() {
      let res
      switch (this.dialogAnchorType) {
        case 'create':
          res = await createUsers(this.formAnchorData)
          break
        case 'update':
          res = await updateUsers(this.formAnchorData)
          break
        default:
          res = await createUsers(this.formAnchorData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeAnchorDialog()
      }
    },
    handleImagePreview(file) {
      this.previewImagePath = file.url
      this.previewImageVisible = true
    },
    handleImageSuccess(res) {
      this.avatarFileList = []
      const {data} = res
      if (data.url) {
        this.formAnchorData.avatar = data.url
        this.avatarFileList.push(data)
      }
      this.progressLoading.close()
    },
    handleImageRemove(file, fileList) {
      this.formAnchorData.avatar = ''
      this.avatarFileList = []
    },
    updatePhotoAlbumsList(e) {
      const newIndex = e.newIndex// 新位置下标
      const oldIndex = e.oldIndex// 原始位置下标
    },
    handlePhotoAlbumsRemove(file) {
      this.formAnchorData.photoAlbums = this.formAnchorData.photoAlbums.filter(function (age) {
        return age.url !== file.url
      })
    },
    handlePhotoAlbumsSuccess(response, file, fileList) {
      if (!this.formAnchorData.photoAlbums) {
        this.formAnchorData.photoAlbums = []
      }
      this.formAnchorData.photoAlbums.push(file.response.data)
      // 数据去重
      const temp = []
      this.formAnchorData.photoAlbums.forEach(a => {
        const check = temp.every(b => {
          return a.key !== b.key
        })
        check ? temp.push(a) : []
      })
      this.formAnchorData.photoAlbums = temp
      // 关闭loading
      this.progressLoading.close()
      this.$refs['handlePhotoAlbums'].clearFiles()
    },
    handleVideoVisible(file) {
      this.videoVisible = true
      this.videoUrl = file.video
    },
    handleAlbumVideoRemove(file, fileList) {
      this.formAnchorData.albumVideo = this.formAnchorData.albumVideo.filter(function (age) {
        return age.url !== file.url
      })
    },
    handleAlbumVideoSuccess(response, file, fileList) {
      if (!this.formAnchorData.albumVideo) {
        this.formAnchorData.albumVideo = []
      }
      this.formAnchorData.albumVideo.push(file.response.data)
      // 数据去重
      const temp = []
      this.formAnchorData.albumVideo.forEach(a => {
        const check = temp.every(b => {
          return a.url !== b.url
        })
        check ? temp.push(a) : []
      })
      this.formAnchorData.albumVideo = temp
      // 关闭loading
      this.progressLoading.close()
      this.$refs['handleAlbumVideo'].clearFiles()
    },
    handleConstellationVideoSuccess(response, file, fileList) {
      this.formAnchorData.constellationVideo = []
      fileList.forEach((item, i) => {
        if (item.response !== undefined) {
          this.formAnchorData.constellationVideo.push(item.response.data)
        } else {
          this.formAnchorData.constellationVideo.push(item)
        }
      })
      // 关闭loading
      this.progressLoading.close()
      this.$refs['handleConstellationVideo'].clearFiles()
    },
    handleConstellationVideoRemove(file, fileList) {
      this.formAnchorData.constellationVideo = []
      fileList.forEach((item, i) => {
        this.formAnchorData.constellationVideo.push(item)
      })
    },
    handleCallVideoSuccess(response, file, fileList) {
      if (!this.formAnchorData.callVideo) {
        this.formAnchorData.callVideo = []
      }
      this.formAnchorData.callVideo.push(file.response.data)
      // 数据去重
      const temp = []
      this.formAnchorData.callVideo.forEach(function (a) {
        const check = temp.every(function (b) {
          return a.url !== b.url
        })
        check ? temp.push(a) : []
      })
      this.formAnchorData.callVideo = temp
      this.progressLoading.close()
      this.$refs['handleCallVideo'].clearFiles()
    },
    handleCallVideoRemove(file, fileList) {
      this.formAnchorData.callVideo = this.formAnchorData.callVideo.filter(function (age) {
        return age.url !== file.url
      })
    },
    async getPersonalityLabelsList() {
      const res = await getPersonalityLabelsTree()
      this.personalityLabelsOptions = res.data.list
    },
    onChangeUserTags(e) {
      this.formAnchorData.userTags.forEach(function (value, index, array) {
        if (value.id === e.id) {
          array[index].checked = !e.checked
        }
      })
    },
    cancleAuth(formData) {
      this.handleProgressLoading()
      cancelAuditUsers({"userId": formData.id}).then(res => {
        // 关闭loading
        this.progressLoading.close()
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.closeAnchorDialog()
        } else {
          this.$message({
            type: 'danger',
            message: res.msg
          })
        }
      })
    },
    banAnchor() {
      banUser({
        userId: this.formAnchorData.id,
        role: this.formAnchorData.role,
        app_id: this.formAnchorData.appId,
        isDeleteApply: false
      }).then(res => {
        if (res.code === 0) {
          this.$message.success("成功")
          this.dialogAnchorFormVisible = false
        }
      })
    },
    unsealAnchor() {
      unsealUser({userId: this.formAnchorData.id}).then(res => {
        if (res.code === 0) {
          this.$message.success("成功")
          this.dialogAnchorFormVisible = false
        }
      })
    },
    // 修改主播信息弹窗结束

    deleteRecomDetail(item) {
      deleteAnchorCoverRecommendationsDetails({id: item.id, anchor_id: this.imgForm.anchor_id}).then(res => {
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功'
          })
          this.getImgData(this.imgForm.anchor_id)
        }
      })
    },
    restoreRecomDetail(item) {
      restoreAnchorCoverRecommendationsDetails({id: item.id, anchor_id: this.imgForm.anchor_id}).then(res => {
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功'
          })
          this.getImgData(this.imgForm.anchor_id)
        }
      })
    },
    handleRecomImageSuccess(res) {
      this.progressLoading.close()
      if (res.code !== 0) {
        this.$message({
          type: 'error',
          message: res.message
        })
        return
      }
      let imgUrl = res.data.url
      createAnchorCoverRecommendationsDetails({
        anchor_id: this.imgForm.anchor_id,
        cover_url: imgUrl,
      }).then(res => {
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功'
          })
          if (this.upClose) {
            this.closeImgDialog()
            this.getTableData(this.nFunc, this.tableDataFormat)
          }
          this.getImgData(this.imgForm.anchor_id)
        }
      })
    },
    showImgDialog(row) {
      this.imgForm.anchor_id = row.anchor_id
      this.getImgData(this.imgForm.anchor_id)
      this.imgDialogVisible = true
    },
    getImgData(anchor_id) {
      getAnchorCoverRecommendationsDetailsAll({anchor_id: anchor_id}).then(res => {
        if (res.code === 0) {
          this.imgTableData = res.data || []
          this.cover_url_list = []
          this.imgTableData.forEach(item => {
            this.cover_url_list.push(item.cover_url)
          })
        }
      })
    },
    closeImgDialog() {
      this.imgDialogVisible = false
      this.imgTableData = []
      this.imgForm = {}
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    searchAnchorInfo() {
      getUserDetail({id: this.formData.anchor_id, role: 2}).then(res => {
        if (res.code === 0) {
          this.userForm = res.data
        }
      })
    },
    submitRecomNotDialog() {
      if (this.userForm.id === 0 || this.userForm.id === null || this.userForm.id === undefined) {
        this.$message({
          type: 'error',
          message: '请搜索主播'
        })
        return
      }
      updateAnchorCoverRecommendations({
        anchor_id: this.userForm.id,
        cover_state: 2,
      }).then(res => {
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功'
          })
          this.closeDialog()
          this.getTableData(this.nFunc, this.tableDataFormat)
        }
      })
    },
    submitRecomDialog() {
      if (this.userForm.id === 0 || this.userForm.id === null || this.userForm.id === undefined) {
        this.$message({
          type: 'error',
          message: '请搜索主播'
        })
        return
      }
      // 2级主播才是新主播,其他的不是
      let isNew = this.userForm.levels === 2 ? 1 : 2
      createAnchorCoverRecommendations({anchor_id: this.userForm.id, cover_state: 1, is_new: isNew}).then(res => {
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功'
          })
          this.closeDialog()
          this.getTableData(this.nFunc, this.tableDataFormat)
        }
      })
    },
    openAddAnchorDialog() {
      this.dialogFormVisible = true
    },
    onSubmit() {
      this.page = 1
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteAnchorCoverRecommendations(row)
      })
    },
    async updateFunc(row) {
      this.formData.anchor_id = row.anchor_id
      this.openAddAnchorDialog()
      this.searchAnchorInfo()
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {}
      this.userForm = {}
    },
    async deleteAnchorCoverRecommendations(row) {
      const res = await deleteAnchorCoverRecommendations({id: row.id})
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].updated_at_str = this.formatDate(item.updated_at)
        data[i].online_str = this.formatOnline(item.online)
        data[i].recent_7_day_duration_avg = this.numF2(item.recent_7_day_duration_avg || 0)
        data[i].fake_str = item.fake === 0 ? '真实' : '虚拟'
        data[i].cover_state_str = item.cover_state === 1 ? '正常' : '作废'
      })
    },
  },
}
</script>

<style>
.link {
  color: #509cff;
  cursor: pointer;
}
</style>
