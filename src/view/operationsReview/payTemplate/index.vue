<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" type="success" icon="el-icon-plus" @click="openDialog">新增</el-button>
          <el-button size="mini" type="success" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table border ref="tableData" style="width: 100%" :data="tableData" row-key="id" :max-height="590">
        <el-table-column align="center" label="模板ID" prop="template_id" min-width="120" />
        <el-table-column align="center" label="支付方式数量" prop="pay_count" min-width="120">
          <template #default="scope">
            <p class="blue" @click="showDialogPayWay(scope.row)">{{scope.row.pay_count}}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" min-width="180">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updatePayTemplate(scope.row)">修改</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" top="5px" :before-close="closeDialog" :title="dialogTitle">
      <el-form :model="formData" :rules="formDataRules" label-position="right" label-width="120px">
        <el-form-item label="模板ID:" prop="template_id">
          <el-input-number :disabled="dialogType === 'update'" class="w100" v-model="formData.template_id" clearable/>
        </el-form-item>
        <el-form-item label="支付方式:">
          <el-select class="w100" v-model="payIDs" multiple collapse-tags clearable filterable placeholder="请选择支付方式">
            <el-option
              v-for="item in payWayOptions"
              :key="item.id"
              :label="`${item.internal_name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="dialogPayWayVisible" top="5px" :before-close="closePayWayDialog" title="支付方式">
      <div class="gva-table-box">
        <el-table border ref="tableData" style="width: 100%" :data="dialogPayWayTableData" row-key="id" :max-height="590">
          <el-table-column align="center" label="模板ID" prop="template_id" min-width="120" />
          <el-table-column align="center" label="支付国家" prop="country_code" min-width="120" />
          <el-table-column align="center" label="支付名称" prop="internal_name" min-width="120" />
          <el-table-column align="center" label="支付权重" prop="pay_weight" min-width="120" />
          <el-table-column align="center" label="20单成功率" prop="success_rate_20" min-width="120" />
          <el-table-column align="center" label="支付状态" prop="state_str" min-width="120" />
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { createPayTemplate, updatePayTemplate, findPayTemplate, getPayTemplateList} from '@/api/payTemplate'
import {allPayWaysData} from "@/api/payWays";
import infoList from '@/mixins/infoList'
import {getPayTemplateDetailList} from "@/api/payTemplateDetail";

export default {
  name: 'PayTemplate',
  mixins: [infoList],
  data() {
    return {
      dialogPayWayVisible: false,
      dialogPayWayTableData: [],




      listApi: getPayTemplateList,
      dialogFormVisible: false,
      dialogTitle: '',
      dialogType: '',
      deleteVisible: false,
      multipleSelection: [],
      payWayOptions: [],
      payWayNameDict: {},
      payWayItemDict: {},
      payIDs: [],
      originPayIDs: [],
      formData: {
        template_id: null,
        pay_count: null,
      },
      formDataRules: {
        template_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        pay_count: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
    }
  },
  created() {
    this.initPayWayData()
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    showDialogPayWay (row) {
      getPayTemplateDetailList({template_id: row.template_id, page: 1, pageSize: 10000}).then(res=>{
        if (res.code === 0) {
          let resList = res.data.list || []
          resList.map((item, i, data) => {
            data[i].created_at_str = this.formatDate(item.created_at)
            data[i].updated_at_str = this.formatDate(item.updated_at)
            data[i].internal_name = this.payWayItemDict[item.pay_id].internal_name
            data[i].country_code = this.payWayItemDict[item.pay_id].country_code
            data[i].state_str = this.payWayItemDict[item.pay_id].state === 1 ? '启用' : '禁用'
          })
          this.dialogPayWayTableData = resList
          this.dialogPayWayVisible = true
        }
      })
    },
    closePayWayDialog () {
      this.dialogPayWayVisible = false
      this.dialogPayWayTableData = []
    },
    initPayWayData() {
      allPayWaysData().then(res=>{
        if (res.code === 0) {
          this.payWayOptions = res.data.listRes || []
          let pDict = {}
          let itemDict = {}
          this.payWayOptions.forEach(item=>{
            pDict[item.id] = item.internal_name
            itemDict[item.id] = item
          })
          this.payWayNameDict = pDict
          this.payWayItemDict = itemDict
        }
      })
    },
    onReset() {
      this.searchInfo = {}
    },
    onSubmit() {
      this.page = 1
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    async updatePayTemplate(row) {
      const res = await findPayTemplate({ id: row.id })
      this.dialogType = 'update'
      this.dialogTitle = '更新'
      if (res.code === 0) {
        this.formData.template_id = res.data.template_id
        this.payIDs = res.data.pay_ids
        this.originPayIDs = res.data.pay_ids
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {}
      this.payIDs = []
    },
    async enterDialog() {
      let reduceList = []
      let addList = []
      if (this.dialogType === 'update') {
        if (this.payIDs.length === 0) {
          this.$message.error('请选择支付方式')
          return
        }
        // 循环原列表,判断源列表的数据是否在新列表中,如果不在就放到已删除的列表里
        this.originPayIDs.forEach(item=>{
          if (!this.payIDs.includes(item)) {
            reduceList.push(item)
          }
        })
        // 循环新列表,判断新列表的数据是否在原列表中,如果不在就放到新增的列表里
        this.payIDs.forEach(item=>{
          if (!this.originPayIDs.includes(item)) {
            addList.push(item)
          }
        })
      }

      let res
      switch (this.dialogType) {
        case 'create':
          res = await createPayTemplate({
            template_id: this.formData.template_id,
            pay_ids: this.payIDs
          })
          break
        case 'update':
          res = await updatePayTemplate({
            template_id: this.formData.template_id,
            add: addList,
            reduce: reduceList
          })
          break
        default:
          res = await createPayTemplate({
            template_id: this.formData.template_id,
            pay_ids: this.payIDs
          })
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '成功'
        })
        this.closeDialog()
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    openDialog() {
      this.dialogTitle = '创建'
      this.dialogType = 'create'
      this.dialogFormVisible = true
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].updated_at_str = this.formatDate(item.updated_at)
      })
    },
  },
}
</script>

<style>
.blue {
  color: #0d84ff;
  cursor: pointer;
}
</style>
