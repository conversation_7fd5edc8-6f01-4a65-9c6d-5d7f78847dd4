<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="用户id">
          <el-input v-model="searchInfo.user_id" placeholder="请输入用户id"/>
        </el-form-item>
        <el-form-item label="日期范围">
          <el-date-picker
            v-model="searchInfo.date_range"
            type="datetimerange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="审核状态">
          <!--<el-input v-model="searchInfo.award_state"/>-->
          <el-select v-model="searchInfo.award_state" clearable placeholder="审核状态">
            <el-option
              v-for="item in stateMapListData"
              :key="item.value"
              :label="item.label_zh"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="奖励状态">
          <el-select v-model="searchInfo.status_search" clearable>
            <el-option label="未领取" value="1"/>
            <el-option label="已领取" value="2"/>
          </el-select>
        </el-form-item>
        <el-form-item label="活动类型">
          <el-select v-model="searchInfo.act_type" clearable placeholder="活动类型">
            <el-option
              v-for="item in typeMapListData"
              :key="item.value"
              :label="item.label_zh"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" type="success" icon="el-icon-download" :disabled="total === 0" @click="tableExport('tableDataRef')">
            导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="flex mb-6">
        <el-button size="small" type="primary" @click="selectAll">全选</el-button>
        <el-button size="small" type="success" :disabled="multipleInfoList.length === 0" @click="batchPass">批量通过</el-button>
        <el-button size="small" type="danger" :disabled="multipleInfoList.length === 0" @click="batchRefuse">批量拒绝</el-button>
      </div>
      <el-table
        border
        ref="tableDataRef"
        class="w100"
        :data="tableData"
        row-key="id"
        :max-height="590"
        @selection-change="selectionChange"
        @sort-change="tableSort">
        <el-table-column align="center" :selectable="checkSelect" type="selection" width="55" />
        <el-table-column align="center" label="活动周期" prop="act_time_str" :width="180"/>
        <el-table-column align="center" label="活动类型" prop="act_type_str" :width="180"/>
        <el-table-column align="center" label="排名" prop="rank" :width="100" sortable/>
        <el-table-column align="center" label="主播ID" prop="user_id" :width="120" sortable="custom"/>
        <el-table-column align="center" label="主播昵称" prop="nick_name" :min-width="120"/>
        <el-table-column align="center" label="收入(积分)" prop="income_cnt" :width="130" sortable="custom"/>
        <el-table-column align="center" label="奖励(积分)" prop="award_count" :width="130" sortable="custom"/>
        <el-table-column align="center" label="审核状态" prop="award_state_str" :width="130" sortable="custom"/>
        <el-table-column align="center" label="奖励状态" prop="status_str" :width="100">
          <template #default="scope">
            <el-tag v-if="scope.row.status === 0" type="info">未领取</el-tag>
            <el-tag v-else type="success">已领取</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="数据详情" :width="130">
          <template #default="scope">
            <el-button type="text" icon="el-icon-view" size="mini" @click="showAnchorDataDialog(scope.row.user_id)">
              数据详情
            </el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" :width="180">
          <template #default="scope">
            <el-button size="small" :disabled="scope.row.award_state === 2" type="success" @click="singlePass(scope.row)">通过</el-button>
            <el-button size="small" :disabled="scope.row.award_state === 2 || scope.row.award_state === 3" type="danger" @click="singleRefuse(scope.row)">拒绝</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
    <anchor-data-dialog ref="anchorDataDialogRef" :dst_id="anchorDstId" ></anchor-data-dialog>
  </div>
</template>

<script>
import {getUActAwardInfoList, actStateMapList, actTypeMapList, batchAwardStateUpdate} from '@/api/uActAwardInfo'
import infoList from '@/mixins/infoList'
import dayjs from "dayjs";
import {utils as xlsxUtils, write as xlsxWrite} from 'xlsx'
import FileSaver from 'file-saver'

export default {
  name: 'UActAwardInfo',
  mixins: [infoList],
  data() {
    return {
      listApi: getUActAwardInfoList,
      multipleInfoList: [],
      multipleIdList: [],
      stateMapListData: [],
      stateMap: {},
      typeMapListData: [],
      typeMap: {},
      batchUpdateReq: {
        info_list: [],
        award_state: 2,
      },
      anchorDstId: 0,
    }
  },
  created() {
    this.pageSize = 100
    this.searchInfo.award_state = 1
    this.getTableData(this.nFunc, this.tableDataFormat)
    this.initActStateMapList()
    this.initActTypeMapList()
  },
  methods: {
    onSubmit() {
      this.page = 1
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableExport(tRef) {
      let fileName = dayjs().format('YYYYMMDDHHmmss') + '.xlsx'
      let el = this.$refs[tRef].$el
      debugger
      if (el === undefined) {
        el = document.getElementById(tRef)
      }
      if (el === undefined) {
        return
      }
      const wb = xlsxUtils.table_to_book(el, {raw: true})
      const wbout = xlsxWrite(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array',
      })
      try {
        FileSaver.saveAs(
          new Blob([wbout], {
            type: 'application/octet-stream',
          }),
          fileName,
        )
      } catch (e) {
        console.log(e)
      }
    },
    tableSort(data) {
      let orderData = data.order
      let propData = data.prop
      if (propData !== null && orderData !== null) {
        this.searchInfo.sort_prop = propData
        this.searchInfo.sort_desc = orderData !== 'ascending'
      } else {
        this.searchInfo.sort_prop = null
        this.searchInfo.sort_desc = false
      }
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    checkSelect(row, index){
      return row.award_state === 1 || row.award_state === 3
    },
    selectionChange(v) {
      let vList = []
      v.forEach(item=>{
        vList.push({
          id: item.id,
          user_id: item.user_id,
          rank: item.rank,
          act_start_time: item.act_start_time,
          act_end_time: item.act_end_time,
          award_count: item.award_count,
        })
      })
      this.multipleInfoList = vList
      this.batchUpdateReq.info_list = vList
    },
    selectAll() {
      this.$refs.tableDataRef.toggleAllSelection()
    },
    batchPass() {
      this.batchUpdateReq.award_state = 2
      this.submitBatchAwardStateUpdate()
    },
    singlePass(row){
      this.batchUpdateReq.info_list = [{
        id: row.id,
        user_id: row.user_id,
        rank: row.rank,
        act_start_time: row.act_start_time,
        act_end_time: row.act_end_time,
        award_count: row.award_count,
      }]
      this.batchUpdateReq.award_state = 2
      this.submitBatchAwardStateUpdate()
    },
    singleRefuse(row){
      this.batchUpdateReq.info_list = [{
        id: row.id,
        user_id: row.user_id,
        rank: row.rank,
        act_start_time: row.act_start_time,
        act_end_time: row.act_end_time,
        award_count: row.award_count,
      }]
      this.batchUpdateReq.award_state = 3
      this.submitBatchAwardStateUpdate()
    },
    batchRefuse() {
      this.batchUpdateReq.award_state = 3
      this.submitBatchAwardStateUpdate()
    },
    submitBatchAwardStateUpdate() {
      console.log(this.batchUpdateReq)
      batchAwardStateUpdate(this.batchUpdateReq).then(res=>{
        if (res.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
          })
          this.getTableData(this.nFunc, this.tableDataFormat)
        } else {
          this.$message({
            message: res.msg,
            type: 'error',
          })
        }
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].updated_at_str = this.formatDate(item.updated_at)
        data[i].award_state_str = this.stateMap[item.award_state]
        data[i].status_str = item.status === 0 ? '未领取' : '已领取'
        let startStr = dayjs(item.act_start_time).format('YYYY-MM-DD')
        let endStr = dayjs(item.act_end_time).format('YYYY-MM-DD')
        data[i].act_time_str = `${startStr}:${endStr}`
        data[i].act_type_str = this.typeMap[item.act_type]
      })
    },
    showAnchorDataDialog(dst_id) {
      this.anchorDstId = dst_id
      this.$refs.anchorDataDialogRef.dialogVisible = true
    },
    initActStateMapList() {
      actStateMapList().then(res => {
        if (res.code !== 0) {
          this.$message.error(res.msg)
          return
        }
        this.stateMapListData = res.data
        this.stateMapListData.forEach(item => {
          this.stateMap[item.value] = item.label_zh
        })
      })
    },
    initActTypeMapList() {
      actTypeMapList().then(res => {
        if (res.code !== 0) {
          this.$message.error(res.msg)
          return
        }
        this.typeMapListData = res.data
        this.typeMapListData.forEach(item => {
          this.typeMap[item.value] = item.label_zh
        })
      })
    },
  },
}
</script>

<style>
</style>
