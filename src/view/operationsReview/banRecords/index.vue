<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="封禁记录类型">
          <el-input v-model="searchInfo.record_type" placeholder="请输入封禁记录类型" />
        </el-form-item>
        <el-form-item label="封禁账号ID">
          <el-input v-model="searchInfo.ban_id" placeholder="请输入封禁账号ID" />
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" type="success" icon="el-icon-plus" @click="openDialog">新增</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table border ref="tableData" style="width: 100%" :data="tableData" row-key="id" :max-height="590">
        <el-table-column align="center" label="日期" prop="created_at_str" min-width="180"/>
        <el-table-column align="center" label="封禁记录类型" prop="record_type" min-width="120" />
        <el-table-column align="center" label="封禁账号ID" prop="ban_id" min-width="120" />
        <el-table-column align="center" label="扣除金币" prop="deduction_diamond" min-width="120" />
        <el-table-column align="center" label="封禁天数" prop="ban_days" min-width="120" />
        <el-table-column align="center" label="封禁到期时间" prop="ban_time" min-width="120" />
        <el-table-column align="center" label="封禁原因" prop="ban_reason" min-width="120" />
        <el-table-column align="center" label="用户投诉次数" prop="user_complaints" min-width="120" />
        <el-table-column align="center" label="图片违规次数" prop="img_violation" min-width="120" />
        <el-table-column align="center" label="消息违规次数" prop="msg_violation" min-width="120" />
        <el-table-column align="center" label="操作" min-width="180">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateBanRecords(scope.row)">修改</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" top="5px" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" :rules="formDataRules" label-position="right" label-width="120px">
        <el-form-item label="封禁记录类型:" prop="record_type">
          <el-input v-model.number="formData.record_type" clearable/>
        </el-form-item>
        <el-form-item label="封禁账号ID:" prop="ban_id">
          <el-input v-model.number="formData.ban_id" clearable/>
        </el-form-item>
        <el-form-item label="扣除金币:" prop="deduction_diamond">
          <el-input v-model.number="formData.deduction_diamond" clearable/>
        </el-form-item>
        <el-form-item label="封禁天数:" prop="ban_days">
          <el-input v-model.number="formData.ban_days" clearable/>
        </el-form-item>
        <el-form-item label="封禁到期时间:" prop="ban_time">
          <el-date-picker v-model="formData.ban_time" type="date" style="width:100%" placeholder="选择日期" clearable />
        </el-form-item>
        <el-form-item label="封禁原因:" prop="ban_reason">
          <el-input v-model="formData.ban_reason" clearable />
        </el-form-item>
        <el-form-item label="用户投诉次数:" prop="user_complaints">
          <el-input v-model.number="formData.user_complaints" clearable/>
        </el-form-item>
        <el-form-item label="图片违规次数:" prop="img_violation">
          <el-input v-model.number="formData.img_violation" clearable/>
        </el-form-item>
        <el-form-item label="消息违规次数:" prop="msg_violation">
          <el-input v-model.number="formData.msg_violation" clearable/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { createBanRecords, deleteBanRecords, deleteBanRecordsByIds, updateBanRecords, findBanRecords, getBanRecordsList} from '@/api/banRecords' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
export default {
  name: 'BanRecords',
  mixins: [infoList],
  data() {
    return {
      listApi: getBanRecordsList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        record_type: null,
        ban_id: null,
        deduction_diamond: null,
        ban_days: null,
        ban_time: null,
        ban_reason: null,
        user_complaints: null,
        img_violation: null,
        msg_violation: null,
      },
      formDataRules: {
        record_type: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        ban_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        deduction_diamond: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        ban_days: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        ban_time: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        ban_reason: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        user_complaints: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        img_violation: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        msg_violation: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
    }
  },
  created() {
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteBanRecords(row)
      })
    },
    async updateBanRecords(row) {
      const res = await findBanRecords({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.rebanRecords
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {}
    },
    async deleteBanRecords(row) {
      const res = await deleteBanRecords({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createBanRecords(this.formData)
          break
        case 'update':
          res = await updateBanRecords(this.formData)
          break
        default:
          res = await createBanRecords(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '成功'
        })
        this.closeDialog()
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
      })
    },
  },
}
</script>

<style>
</style>
