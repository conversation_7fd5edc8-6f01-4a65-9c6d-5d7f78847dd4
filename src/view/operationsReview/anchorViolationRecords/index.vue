<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="国家代码">
          <el-input v-model="searchInfo.country_code" placeholder="请输入国家代码" clearable/>
        </el-form-item>
        <el-form-item label="主播ID">
          <el-input v-model="searchInfo.anchor_id" placeholder="请输入主播ID" clearable/>
        </el-form-item>
        <el-form-item label="审核等级">
          <el-input v-model.number="searchInfo.examine_level" placeholder="审核等级" clearable/>
        </el-form-item>
        <el-form-item label="视频等级">
          <el-input v-model.number="searchInfo.video_level" clearable/>
        </el-form-item>
        <el-form-item label="消息等级">
          <el-input v-model.number="searchInfo.msg_level" clearable/>
        </el-form-item>
        <el-form-item label="违规图片Min">
          <el-input v-model="searchInfo.img_violation" clearable/>
        </el-form-item>
        <el-form-item label="违规消息Min">
          <el-input v-model="searchInfo.msg_violation" clearable/>
        </el-form-item>
        <el-form-item label="更新时间范围">
          <el-date-picker
            v-model="searchInfo.updated_at_range"
            type="datetimerange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="封禁状态">
          <el-select v-model="searchInfo.ban_state" clearable filterable placeholder="封禁状态">
            <el-option
              v-for="item in banStateOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table @sort-change="tableSort" border ref="tableData" style="width: 100%" :data="tableData" row-key="id"
                :max-height="590">
        <el-table-column align="center" label="编号" prop="id" sortable="custom" fixed="left" min-width="100">
          <template #default="scope">
            <div class="blue" @click="showAnchorBindDrawer(scope.row)">
              {{ scope.row.id }}
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="主播ID" prop="anchor_id" min-width="120" fixed="left">
          <template #default="scope">
            <p class="blue" @click="updateUsers(scope.row)">{{scope.row.anchor_id}}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="主播国家" prop="country_code" min-width="120"/>
        <el-table-column align="center" label="互动用户数" prop="send_msg_user_count" min-width="120">
          <template #default="scope">
            <div :class="{'blue': scope.row.send_msg_user_count > 0}" @click="showSendMsgUserDrawer(scope.row)">
              {{ scope.row.send_msg_user_count }}
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="亲密用户数" prop="intimate_user_count" min-width="120"/>
        <el-table-column align="center" label="投诉用户数" prop="user_complaints" min-width="120"/>
        <el-table-column align="center" label="总回复消息" prop="msg_income_count" min-width="120"/>
        <el-table-column align="center" label="人均消息条数" prop="per_msg_count" min-width="120"/>
        <el-table-column align="center" label="接听次数" prop="call_answer_count" sortable min-width="120">
          <template #default="scope">
            <div :class="{'blue': scope.row.call_answer_count > 0}" @click="showCallAnswerDrawer(scope.row)">
              {{ scope.row.call_answer_count }}
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="100次接听均时" prop="recent_hundred_avg_second" min-width="120"/>
        <el-table-column align="center" label="视频等级" prop="video_level" min-width="120"/>
        <el-table-column align="center" label="消息等级" prop="msg_level" min-width="120"/>
        <el-table-column align="center" label="违规图片" prop="img_violation" min-width="120">
          <template #default="scope">
            <div :class="{'blue': scope.row.img_violation > 0}" @click="showImgViolationDialog(scope.row)">
              {{ scope.row.img_violation }}
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="违规消息" prop="msg_violation" min-width="120">
          <template #default="scope">
            <div :class="{'blue': scope.row.msg_violation > 0}" @click="showMsgViolationDialog(scope.row)">
              {{ scope.row.msg_violation }}
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="可提现余额" prop="balance" min-width="120">
          <template #default="scope">
            <div class="blue" @click="showBalanceDrawer(scope.row)">{{ scope.row.balance }}</div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="数据概况" min-width="120">
          <template #default="scope">
            <el-button type="text" icon="el-icon-view" size="mini" @click="showAnchorDataDialog(scope.row.anchor_id)">
              数据详情
            </el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="创建时间" prop="created_at_str" sortable="custom" min-width="180"/>
        <el-table-column align="center" label="更新时间" prop="updated_at_str" sortable="custom" min-width="180"/>
        <el-table-column align="center" label="审核状态" prop="examine_state_str" min-width="120"/>
        <el-table-column align="center" label="审核等级" prop="examine_level" min-width="120">
          <template #default="scope">
            <p class="blue" @click="showExamineLevel(scope.row)">{{scope.row.examine_level}}</p>
          </template>
        </el-table-column>
        <el-table-column align="center" label="封禁状态" prop="ban_state_str" min-width="120" fixed="right"/>
        <el-table-column align="center" label="操作" min-width="220" fixed="right">
          <template #default="scope">
            <el-button size="small" type="primary" icon="el-icon-setting" @click="showEditLevelDialog(scope.row)">调级
            </el-button>
            <el-button v-if="scope.row.ban_state === 1" size="small" icon="el-icon-close" type="danger"
                       @click="showBanAnchorDialog(scope.row)">封禁
            </el-button>
            <el-popconfirm title="确认解封?" @confirm="unsealConfirm(scope.row.anchor_id)">
              <template #reference>
                <el-button v-if="scope.row.ban_state !== 1" size="small" icon="el-icon-check" type="success">
                  解封
                </el-button>
              </template>
            </el-popconfirm>

            <!--<el-button type="success" @click="updateExamineState(scope.row)">通过</el-button>-->
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="banDialogFormVisible" top="5px" :before-close="closeDialog" title="弹窗操作">
      <el-form ref="formDataRef" :model="formData" :rules="formDataRules" label-position="right" label-width="120px">
        <el-form-item label="主播ID:">
          <el-input disabled v-model="formData.ban_id" clearable/>
        </el-form-item>
        <el-form-item label="可提现余额:">
          <el-input disabled v-model="formData.balance" clearable/>
        </el-form-item>
        <el-form-item label="扣除比例:">
          <el-input-number class="w100" v-model="formData.deduction_ratio" :min="0" :max="100" :precision="0"/>
        </el-form-item>
        <el-form-item label="封禁天数:">
          <el-input-number class="w100" v-model="formData.ban_days" :min="0" :precision="0"/>
        </el-form-item>
        <el-form-item label="封禁原因:" prop="ban_reason">
          <el-input type="textarea" v-model="formData.ban_reason" :autosize="{ minRows: 6, maxRows: 10 }"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="examineLevelDialogShow" top="5px" :before-close="closeExamineLevelDialog" title="弹窗操作">
      <el-form ref="formDataRef" :model="formExamineLevelData" label-position="right" label-width="120px">
        <el-form-item label="主播ID:">
          <el-input disabled v-model="formExamineLevelData.anchor_id"/>
        </el-form-item>
        <el-form-item label="审核等级:">
          <el-input-number class="w100" v-model="formExamineLevelData.examine_level" :min="0" :precision="0"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" type="primary" @click="enterExamineLevelDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="imgViolationDialogShow" :close-on-click-modal="false" :close-on-press-escape="false" top="5px"
               width="80%" title="违规图片">
      <template #title>
        <div class="flex flex-row justify-between items-center">
          <p style="font-size: 20px">违规图片</p>
          <el-button style="margin-right: 60px" type="danger" size="small" @click="clearAnchorImgData">清除数据</el-button>
        </div>
      </template>

      <el-row :gutter="10">
        <el-col :span="imgColSpan" v-for="(item, index) in imgViolationList" :key="index">
          <p>{{formatDate(item.created_at)}}</p>
          <el-image class="img-box" :src="item.content" fit="contain"
                    :preview-src-list="imgViolationPreviewList"></el-image>
        </el-col>
      </el-row>
    </el-dialog>

    <el-dialog v-model="msgViolationDialogShow" top="5px">
      <template #title>
        <div class="flex flex-row justify-between items-center">
          <p style="font-size: 20px">违规消息</p>
          <el-button style="margin-right: 60px" type="danger" size="small" @click="clearAnchorMsgData">清除数据</el-button>
        </div>
      </template>
      <el-table border ref="msgTableData" style="width: 100%" :data="msgViolationList" row-key="id" :max-height="590">
        <el-table-column align="center" label="创建时间" prop="created_at_str" min-width="120"/>
        <el-table-column align="center" label="主播ID" prop="user_id" min-width="120"/>
        <el-table-column align="center" label="消息等级" prop="msg_level" width="120"/>
        <el-table-column align="center" label="消息" prop="content" min-width="220"/>
      </el-table>
    </el-dialog>

    <el-dialog v-model="editLevelDialogShow" top="5px" width="400px" title="调整等级" @close="editLevelDialogClose">
      <el-form ref="formEditLevelRef" :model="formEditLevelData" :rules="formEditLevelDataRules" label-position="right"
               label-width="90px">
        <el-form-item label="主播ID:">
          <el-input disabled v-model="formEditLevelData.anchor_id"/>
        </el-form-item>
        <el-form-item label="原视频等级:">
          <el-input disabled v-model="formEditLevelData.o_video_level"/>
        </el-form-item>
        <el-form-item label="原消息等级:">
          <el-input disabled v-model="formEditLevelData.o_msg_level"/>
        </el-form-item>
        <el-tabs v-model="activeTabName">
          <el-tab-pane label="视频等级" name="video_level">
            <el-form-item label="视频等级:">
              <el-input-number class="w100" v-model="formEditLevelData.video_level" :min="0" :precision="0"/>
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="primary" @click="enterUpdateVideoLevel">修改视频等级</el-button>
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="消息等级" name="msg_level">
            <el-form-item label="消息等级:">
              <el-input-number class="w100" v-model="formEditLevelData.msg_level" :min="0" :precision="0"/>
            </el-form-item>
            <el-form-item>
              <el-button size="small" type="primary" @click="enterUpdateMsgLevel">修改消息等级</el-button>
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
    </el-dialog>

    <anchor-data-dialog ref="anchorDataDialogRef" :dst_id="anchorDstId" ></anchor-data-dialog>

    <user-info-update-dialog ref="userInfoUpdateDialogRef" :user_id="userInfoUpdateId"></user-info-update-dialog>

    <el-dialog v-model="previewImageVisible" title="图片预览" width="50%" top="1%" center>
      <img :src="previewImagePath" class="avatar video-avatar" style="max-height: 80vh;overflow-y: auto;">
    </el-dialog>
    <el-dialog v-model="videoVisible" title="视频预览" width="397px" top="0" destroy-on-close center>
      <video :src="videoUrl" class="avatar video-avatar" controls="controls">您的浏览器不支持视频播放</video>
    </el-dialog>
    <el-drawer v-model="sendMsgUserDrawerShow" @close="sendMsgUserDrawerClose" size="70%" :with-header="false">
      <el-row :gutter="5">
        <el-col :span="8">
          <p class="flex justify-center fs-18 mb-6">互动用户列表</p>
          <div class="flex flex-row">
            <el-input v-model.number="sendMsgUserSearch.src_id" size="small" placeholder="请输入用户ID"/>
            <el-button style="margin-left: 10px" size="small" type="primary" @click="submitSendMsgUserListRes">搜索</el-button>
          </div>
          <div class="gva-table-box">
            <el-table border ref="tableDataMsgUser" @row-click="msgRowClick" :data="tableDataMsgUser" row-key="src_id" :max-height="590" highlight-current-row>
              <el-table-column align="center" label="用户" prop="src_id" min-width="100"/>
              <el-table-column align="center" label="时间" prop="created_at_str" min-width="120"/>
            </el-table>
            <div class="gva-pagination">
              <el-pagination
                layout="total, sizes, prev, pager, next"
                :current-page="msgUserPage"
                :page-size="msgUserPageSize"
                :page-sizes="pageSizeList"
                :total="msgUserTotal"
                @current-change="msgUserPageChange"
                @size-change="msgUserSizeChange"
              />
            </div>
          </div>
        </el-col>
        <el-col :span="20">
        </el-col>
      </el-row>
    </el-drawer>
    <el-drawer v-model="callAnswerDrawerShow" @close="callAnswerDrawerClose" size="70%" :with-header="false">
      <el-row :gutter="25">
        <el-col :span="10" v-loading="tableDataCallAnswerLoading">
          <p class="flex justify-center fs-18 mb-6">接听用户列表</p>
          <div class="flex flex-row">
            <el-input v-model.number="callAnswerSearch.src_id" size="small" placeholder="请输入用户ID"/>
            <el-button style="margin-left: 10px" size="small" type="primary" @click="submitCallAnswerListRes">搜索</el-button>
          </div>
          <div class="gva-table-box">
            <el-table border ref="tableDataMsgUser" @row-click="callAnswerRowClick" :data="tableDataCallAnswer" row-key="src_id" :max-height="780" highlight-current-row>
              <el-table-column align="center" label="用户" prop="src_id" min-width="100"/>
              <el-table-column align="center" label="数量" prop="count" min-width="120"/>
            </el-table>
            <el-pagination
              background
              layout="total, sizes, prev, pager, next"
              :current-page="callAnswerPage"
              :page-size="callAnswerPageSize"
              :page-sizes="pageSizeList"
              :pager-count="5"
              :total="callAnswerTotal"
              @current-change="callAnswerPageChange"
              @size-change="callAnswerSizeChange"
            />
          </div>
        </el-col>
        <el-col :span="14" v-loading="submitCallAnswerCardLoading">
          <div class="right-area">
            <p class="flex justify-center fs-18 mb-6">接听记录</p>
            <el-card style="margin-bottom: 14px" class="box-card" v-for="(item, index) in callAnswerCardData" :key="index">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="时长" align="center">{{item.duration}}</el-descriptions-item>
                <el-descriptions-item label="收入" align="center">{{item.dst_income}}</el-descriptions-item>
                <el-descriptions-item label="呼叫人" align="center">{{item.src_id}}</el-descriptions-item>
                <el-descriptions-item label="创建时间" align="center">{{formatDate(item.created_at)}}</el-descriptions-item>
                <el-descriptions-item label="频道" align="center" :span="2">
                  <div class="flex items-center justify-center">
                    <p>{{item.project}}</p>
                    <el-button style="margin-left: 12px;" size="mini" type="text" @click="copyText(item.project)">复制</el-button>
                  </div>
                </el-descriptions-item>
              </el-descriptions>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </el-drawer>
    <el-drawer v-model="balanceDrawerShow" @close="balanceDrawerClose" size="70%" :with-header="false">
      <el-row :gutter="25">
        <el-col :span="10" v-loading="tableDataBalanceLoading">
          <p class="flex justify-center fs-18 mb-6">收入用户列表</p>
          <div class="flex flex-row">
            <el-input v-model.number="balanceSearch.src_id" size="small" placeholder="请输入用户ID"/>
            <el-button style="margin-left: 10px" size="small" type="primary" @click="submitBalanceListRes">搜索</el-button>
          </div>
          <div class="gva-table-box">
            <el-table border ref="tableDataMsgUser" @row-click="balanceRowClick" :data="tableDataBalance" row-key="src_id" :max-height="780" highlight-current-row>
              <el-table-column align="center" label="用户" prop="src_id" min-width="100"/>
              <el-table-column align="center" label="数量" prop="count" min-width="120"/>
            </el-table>
            <el-pagination
              background
              layout="total, sizes, prev, pager, next"
              :current-page="balancePage"
              :page-size="balancePageSize"
              :page-sizes="pageSizeList"
              :pager-count="5"
              :total="balanceTotal"
              @current-change="balancePageChange"
              @size-change="balanceSizeChange"
            />
          </div>
        </el-col>
        <el-col :span="14" v-loading="submitBalanceCardLoading">
          <div class="right-area">
            <p class="flex justify-center fs-18 mb-6">收入记录</p>
            <el-card style="margin-bottom: 14px" class="box-card" v-for="(item, index) in balanceCardData" :key="index">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="类型" align="center">{{item.extra_desc}}</el-descriptions-item>
                <el-descriptions-item label="用户ID" align="center">{{item.user_id}}</el-descriptions-item>
                <el-descriptions-item label="收入" align="center">{{item.income}}</el-descriptions-item>
                <el-descriptions-item label="工会" align="center">{{item.union_income}}</el-descriptions-item>
                <el-descriptions-item label="创建时间" align="center">{{formatDate(item.created_at)}}</el-descriptions-item>
              </el-descriptions>
            </el-card>
          </div>
        </el-col>
      </el-row>
    </el-drawer>
    <el-drawer v-model="anchorBindDrawerShow" @close="anchorBindDrawerClose" size="30%" :with-header="false">
      <div class="flex flex-row justify-between items-center">
        <p>主播ID: {{anchorBindSearch.anchor_id}}</p>
        <el-button type="success" @click="showAddAnchorBindDialog()">添加</el-button>
      </div>
      <div class="gva-table-box">
        <el-table border ref="tableDataAnchorBind" :data="tableDataAnchorBind" row-key="id" :max-height="780" highlight-current-row>
          <el-table-column align="center" label="主播ID" prop="fake_anchor_id" min-width="100"/>
          <el-table-column align="center" label="主播昵称" prop="nickname" min-width="120"/>
        </el-table>
        <el-pagination
          background
          layout="total, sizes, prev, pager, next"
          :current-page="anchorBindPage"
          :page-size="anchorBindPageSize"
          :page-sizes="pageSizeList"
          :pager-count="5"
          :total="anchorBindTotal"
          @current-change="anchorBindPageChange"
          @size-change="anchorBindSizeChange"
        />
      </div>
    </el-drawer>
    <el-dialog v-model="anchorBindDialogShow" title="创建" width="50%" top="1%" center append-to-body>
      <el-form ref="formDataRef" :model="formAnchorBindData" label-position="right" label-width="120px">
        <el-form-item label="主播ID:">
          <el-input disabled v-model.number="formAnchorBindData.anchor_id"/>
        </el-form-item>
        <el-form-item label="虚拟主播ID:">
          <el-input v-model.number="formAnchorBindData.fake_anchor_id" clearable/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" type="primary" @click="enterAnchorBindDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script>
import {
  anchorBanStateList,
  callUserList,
  examineStateList,
  getAnchorViolationRecordsList,
  incomeUserList,
  recentMsgHistory,
  sendMsgUserList,
  updateAnchorViolationRecords
} from '@/api/anchorViolationRecords'
import {banAnchor} from '@/api/banRecords'
import {getList as connectedReportsGetList} from '@/api/connectedReports'
import infoList from '@/mixins/infoList'
import {clearImgContentReview, clearMsgContentReview, getContentReviewList} from "@/api/contentReview";
import {updateMsgLevel, updateVideoLevel} from "@/api/userLevels";
import draggable from "vuedraggable";
import {getPersonalityLabelsTree} from "@/api/personality_labels";
import {banUser, cancelAuditUsers, createUsers, unsealUser, updateUsers} from "@/api/users";
import {getAnchorRewardLogsList} from "@/api/anchorRewardLogs";
import {createVirtuallyAnchorBind, getVirtuallyAnchorBindList} from "@/api/virtuallyAnchorBind";
import UserInfoUpdateDialog from "@/components/UserInfoUpdateDialog/index.vue";

const path = import.meta.env.VITE_BASE_API

export default {
  name: 'AnchorViolationRecords',
  components: {UserInfoUpdateDialog, draggable},
  mixins: [infoList],
  data() {
    return {
      listApi: getAnchorViolationRecordsList,
      editLevelDialogShow: false,
      msgViolationDialogShow: false,
      imgViolationDialogShow: false,
      imgColSpan: 4,
      imgViolationList: [],
      imgViolationPreviewList: [],
      clearMsgDstId: null,
      clearImgDstId: null,
      msgViolationList: [],
      banDialogFormVisible: false,
      path: path,
      dialogAnchorFormVisible: false,
      dialogAnchorType: '',
      previewImageVisible: false,
      isDragging: false,
      videoVisible: false,
      videoUrl: '',
      avatarFileList: [],
      personalityLabelsOptions: [],
      formAnchorData: {},
      previewImagePath: '',
      anchorDstId: 0,
      userInfoUpdateId: 0,
      formData: {},
      activeTabName: 'video_level',
      formEditLevelData: {},
      formExamineLevelData: {},
      examineLevelDialogShow: false,
      formEditLevelDataRules: {},
      formDataRules: {
        ban_reason: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
      banStateOptions: [],
      banStateDict: {},
      examineStateOptions: [],
      examineStateDict: {},

      anchorBindDialogShow: false,
      formAnchorBindData: {
        anchor_id: null,
        fake_anchor_id: null,
      },
      anchorBindDrawerShow: false,
      tableDataAnchorBind: [],
      tableDataAnchorBindLoading: false,
      anchorBindTotal: 0,
      anchorBindPage: 1,
      anchorBindPageSize: 10,
      anchorBindSearch: {
        anchor_id: null,
        page: 1,
        pageSize: 10,
      },


      balanceDrawerShow:false,
      tableDataBalance: [],
      balanceTotal: 0,
      balancePage: 1,
      balancePageSize: 10,
      balanceSearch: {
        src_id: null,
        anchor_id: null,
        page: 1,
        pageSize: 10,
      },
      balanceCardData: [],
      balanceCardTotal: 0,
      submitBalanceCardLoading: false,
      tableDataBalanceLoading: false,
      balanceCardSearch: {
        src_id: null,
        dst_id: null,
        page: 1,
        pageSize: 1000,
      },


      callAnswerDrawerShow:false,
      tableDataCallAnswer: [],
      callAnswerTotal: 0,
      callAnswerPage: 1,
      callAnswerPageSize: 10,
      callAnswerSearch: {
        src_id: null,
        anchor_id: null,
        page: 1,
        pageSize: 10,
      },
      callAnswerCardData: [],
      callAnswerCardTotal: 0,
      submitCallAnswerCardLoading: false,
      tableDataCallAnswerLoading: false,
      callAnswerCardSearch: {
        src_id: null,
        dst_id: null,
        page: 1,
        pageSize: 1000,
      },

      sendMsgUserDrawerShow: false,
      tableDataMsgUser: [],
      msgUserTotal: 0,
      msgUserPage: 1,
      msgUserPageSize: 10,
      sendMsgUserSearch: {
        src_id: null,
        anchor_id: null,
        page: 1,
        pageSize: 10,
      },
      tableDataMsgHistory: [],
      msgHistoryTotal: 0,
      sendMsgHistorySearch: {
        src_id: null,
        anchor_id: null,
        page: 1,
        pageSize: 100,
      },
    }
  },
  created() {
    // this.searchInfo.anchor_id = 4164 // 互动用户数test
    // this.searchInfo.anchor_id = 89166980 // 接听次数test
    this.initDictList()
    this.getPersonalityLabelsList()
    // this.showAnchorDataDialog(6548)
  },
  methods: {
    showUserInfoUpdateDialog(dst_id) {
      this.userInfoUpdateId = dst_id
      this.$refs.userInfoUpdateDialogRef.dialogVisible = true
    },
    unsealConfirm(anchor_id) {
      unsealUser({userId: anchor_id}).then(res=>{
        if (res.code === 0) {
          this.$message.success("成功")
        }
      })
    },
    clearAnchorMsgData() {
      clearMsgContentReview({user_id: this.clearMsgDstId}).then(res => {
       if (res.code === 0) {
         this.$message({
           type: 'success',
           message: '成功'
         })
         this.msgViolationDialogShow = false
         this.getTableData(this.nFunc, this.tableDataFormat)
       }
      })
    },
    clearAnchorImgData() {
      clearImgContentReview({user_id: this.clearImgDstId}).then(res => {
       if (res.code === 0) {
         this.$message({
           type: 'success',
           message: '成功'
         })
         this.imgViolationDialogShow = false
         this.getTableData(this.nFunc, this.tableDataFormat)
       }
      })
    },
    showAddAnchorBindDialog() {
      this.formAnchorBindData.anchor_id = this.anchorBindSearch.anchor_id
      this.anchorBindDialogShow = true
    },
    enterAnchorBindDialog() {
      createVirtuallyAnchorBind(this.formAnchorBindData).then(res => {
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功'
          })
          this.anchorBindDialogShow = false
          this.submitAnchorBindListRes()
        }
      })
    },
    showAnchorBindDrawer(row) {
      this.anchorBindSearch.anchor_id = row.anchor_id
      this.submitAnchorBindListRes()
      this.anchorBindDrawerShow = true
    },
    anchorBindDrawerClose() {

    },
    submitAnchorBindListRes() {
      this.tableDataAnchorBind = []
      this.anchorBindTotal = 0
      this.tableDataAnchorBindLoading = true
      this.anchorBindSearch.page = this.anchorBindPage
      this.anchorBindSearch.pageSize = this.anchorBindPageSize
      getVirtuallyAnchorBindList(this.anchorBindSearch).then(res => {
        this.tableDataAnchorBindLoading = false
        if (res.code === 0) {
          this.tableDataAnchorBind = res.data.list
          this.anchorBindTotal = res.data.total
        }
      })
    },
    anchorBindSizeChange(v) {
      this.anchorBindPageSize = v
      this.submitAnchorBindListRes()
    },
    anchorBindPageChange(v) {
      this.anchorBindPage = v
      this.submitAnchorBindListRes()
    },


    balanceDrawerClose() {
      this.balancePage = 1
      this.balanceSearch.src_id = null
      this.tableDataBalance = []
      this.tableDataBalanceLoading = false
      this.balanceDrawerShow = false
    },
    showBalanceDrawer(row) {
      this.balanceSearch.anchor_id = row.anchor_id
      this.balanceCardSearch.src_id = null
      this.balanceCardSearch.dst_id = row.anchor_id
      this.submitBalanceListRes()
      this.balanceDrawerShow = true
      this.submitBalanceCardRes()
    },
    balanceRowClick(row) {
      // {"src_id":80660976,"count":1}
      this.balanceCardSearch.src_id = row.src_id
      this.submitBalanceCardRes()
    },
    submitBalanceListRes(){
      this.tableDataBalanceLoading = true
      this.balanceSearch.page = this.balancePage
      this.balanceSearch.pageSize = this.balancePageSize
      incomeUserList(this.balanceSearch).then(res=>{
        this.tableDataBalanceLoading = false
        if (res.code === 0) {
          this.tableDataBalance = res.data.list || []
          this.balanceTotal = res.data.total
        }
      }).catch(err=>{
        this.tableDataBalanceLoading = false
      })
    },
    submitBalanceCardRes(){
      this.submitBalanceCardLoading = true
      getAnchorRewardLogsList(this.balanceCardSearch).then(res=>{
        this.submitBalanceCardLoading = false
        if (res.code === 0) {
          this.balanceCardData = res.data.list || []
          this.balanceCardTotal = res.data.total
        }
      }).catch(err=>{
        this.submitBalanceCardLoading = false
      })
    },
    balanceSizeChange(v) {
      this.balancePageSize = v
      this.submitBalanceListRes()
    },
    balancePageChange(v) {
      this.balancePage = v
      this.submitBalanceListRes()
    },



    callAnswerDrawerClose() {
      this.callAnswerPage = 1
      this.callAnswerSearch.src_id = null
      this.tableDataCallAnswer = []
      this.callAnswerDrawerShow = false
    },
    showCallAnswerDrawer(row) {
      this.callAnswerSearch.anchor_id = row.anchor_id
      this.callAnswerCardSearch.src_id = null
      this.callAnswerCardSearch.dst_id = row.anchor_id
      this.callAnswerCardSearch.call_type = 1
      this.callAnswerCardSearch.state = 4
      this.submitCallAnswerListRes()
      this.callAnswerDrawerShow = true
      this.submitCallAnswerCardRes()
    },
    callAnswerRowClick(row) {
      // {"src_id":80660976,"count":1}
      this.callAnswerCardSearch.src_id = row.src_id
      this.submitCallAnswerCardRes()
    },
    submitCallAnswerListRes(){
      this.tableDataCallAnswerLoading = true
      this.callAnswerSearch.page = this.callAnswerPage
      this.callAnswerSearch.pageSize = this.callAnswerPageSize
      callUserList(this.callAnswerSearch).then(res=>{
        this.tableDataCallAnswerLoading = false
        if (res.code === 0) {
          this.tableDataCallAnswer = res.data.list || []
          this.callAnswerTotal = res.data.total
        }
      })
    },
    submitCallAnswerCardRes(){
      this.submitCallAnswerCardLoading = true
      connectedReportsGetList(this.callAnswerCardSearch).then(res=>{
        this.submitCallAnswerCardLoading = false
        if (res.code === 0) {
          this.callAnswerCardData = res.data.list || []
          this.callAnswerCardTotal = res.data.total
        }
      })
    },
    callAnswerSizeChange(v) {
      this.callAnswerPageSize = v
      this.submitCallAnswerListRes()
    },
    callAnswerPageChange(v) {
      this.callAnswerPage = v
      this.submitCallAnswerListRes()
    },



    sendMsgUserDrawerClose() {
      this.msgUserPage = 1
      this.sendMsgUserSearch.src_id = null
      this.tableDataMsgUser = []
      this.sendMsgUserDrawerShow = false
    },
    showSendMsgUserDrawer(row) {
      this.sendMsgUserSearch.anchor_id = row.anchor_id
      this.sendMsgHistorySearch.anchor_id = row.anchor_id
      this.submitSendMsgUserListRes()
      this.sendMsgUserDrawerShow = true
    },
    submitSendMsgUserListRes(){
      this.sendMsgUserSearch.page = this.msgUserPage
      this.sendMsgUserSearch.pageSize = this.msgUserPageSize
      sendMsgUserList(this.sendMsgUserSearch).then(res=>{
        if (res.code === 0) {
          let resList = res.data.list || []
          resList.map((item, i, data) => {
            data[i].created_at_str = this.formatDate(item.created_at)
          })

          this.tableDataMsgUser = resList
          this.msgUserTotal = res.data.total
        }
      })
    },
    msgRowClick(row) {
      // {"src_id":80660976,"created_at_str":xxxxxx}
      this.sendMsgHistorySearch.src_id = row.src_id
      this.submitSendMsgHistoryRes()
    },
    submitSendMsgHistoryRes(){
      this.sendMsgHistorySearch.page = this.msgUserPage
      this.sendMsgHistorySearch.pageSize = this.msgUserPageSize
      recentMsgHistory(this.sendMsgHistorySearch).then(res=>{
        if (res.code === 0) {
          let resList = res.data.list || []
          resList.map((item, i, data) => {
            data[i].created_at_str = this.formatDate(item.created_at)
          })

          this.tableDataMsgHistory = resList
          this.msgHistoryTotal = res.data.total
        }
      })
    },
    msgUserSizeChange(v) {
      this.msgUserPageSize = v
      this.submitSendMsgUserListRes()
    },
    msgUserPageChange(v) {
      this.msgUserPage = v
      this.submitSendMsgUserListRes()
    },
    enterExamineLevelDialog() {
      updateAnchorViolationRecords(this.formExamineLevelData).then(res=>{
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功'
          })
        }
      })
    },
    closeExamineLevelDialog() {
      this.examineLevelDialogShow = false
      this.formExamineLevelData = {}
    },
    showExamineLevel(row) {
      this.formExamineLevelData = row
      this.examineLevelDialogShow = true
    },
    async updateUsers(row) {
      this.showUserInfoUpdateDialog(row.anchor_id)
    },
    async enterAnchorDialog() {
      let res
      switch (this.dialogAnchorType) {
        case 'create':
          res = await createUsers(this.formAnchorData)
          break
        case 'update':
          res = await updateUsers(this.formAnchorData)
          break
        default:
          res = await createUsers(this.formAnchorData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeAnchorDialog()
      }
    },
    banAnchor() {
      banUser({
        userId: this.formAnchorData.id,
        role:this.formAnchorData.role,
        app_id:this.formAnchorData.appId,
        isDeleteApply: false
      }).then(res=>{
        if (res.code === 0) {
          this.$message.success("成功")
          this.dialogAnchorFormVisible = false
        }
      })
    },
    unsealAnchor() {
      unsealUser({userId: this.formAnchorData.id}).then(res=>{
        if (res.code === 0) {
          this.$message.success("成功")
          this.dialogAnchorFormVisible = false
        }
      })
    },
    cancleAuth(formData) {
      this.handleProgressLoading()
      cancelAuditUsers({"userId": formData.id}).then(res => {
        // 关闭loading
        this.progressLoading.close()
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.closeAnchorDialog()
        } else {
          this.$message({
            type: 'danger',
            message: res.msg
          })
        }
      })
    },
    onChangeUserTags(e) {
      this.formAnchorData.userTags.forEach(function (value, index, array) {
        if (value.id === e.id) {
          array[index].checked = !e.checked
        }
      })
    },
    async getPersonalityLabelsList() {
      const res = await getPersonalityLabelsTree()
      this.personalityLabelsOptions = res.data.list
    },
    handleCallVideoSuccess(response, file, fileList) {
      if (!this.formAnchorData.callVideo) {
        this.formAnchorData.callVideo = []
      }
      this.formAnchorData.callVideo.push(file.response.data)
      // 数据去重
      const temp = []
      this.formAnchorData.callVideo.forEach(function (a) {
        const check = temp.every(function (b) {
          return a.url !== b.url
        })
        check ? temp.push(a) : []
      })
      this.formAnchorData.callVideo = temp
      this.progressLoading.close()
      this.$refs['handleCallVideo'].clearFiles()
    },
    handleCallVideoRemove(file, fileList) {
      this.formAnchorData.callVideo = this.formAnchorData.callVideo.filter(function (age) {
        return age.url !== file.url
      })
    },
    handleConstellationVideoSuccess(response, file, fileList) {
      this.formAnchorData.constellationVideo = []
      fileList.forEach((item, i) => {
        if (item.response !== undefined) {
          this.formAnchorData.constellationVideo.push(item.response.data)
        } else {
          this.formAnchorData.constellationVideo.push(item)
        }
      })
      // 关闭loading
      this.progressLoading.close()
      this.$refs['handleConstellationVideo'].clearFiles()
    },
    handleConstellationVideoRemove(file, fileList) {
      this.formAnchorData.constellationVideo = []
      fileList.forEach((item, i) => {
        this.formAnchorData.constellationVideo.push(item)
      })
    },
    handleAlbumVideoSuccess(response, file, fileList) {
      if (!this.formAnchorData.albumVideo) {
        this.formAnchorData.albumVideo = []
      }
      this.formAnchorData.albumVideo.push(file.response.data)
      // 数据去重
      const temp = []
      this.formAnchorData.albumVideo.forEach(a=> {
        const check = temp.every(b=> {
          return a.url !== b.url
        })
        check ? temp.push(a) : []
      })
      this.formAnchorData.albumVideo = temp
      // 关闭loading
      this.progressLoading.close()
      this.$refs['handleAlbumVideo'].clearFiles()
    },
    handleAlbumVideoRemove(file, fileList) {
      this.formAnchorData.albumVideo = this.formAnchorData.albumVideo.filter(function (age) {
        return age.url !== file.url
      })
    },
    handleVideoVisible(file) {
      this.videoVisible = true
      this.videoUrl = file.video
    },
    handleImagePreview(file) {
      this.previewImagePath = file.url
      this.previewImageVisible = true
    },
    handleImageSuccess(res) {
      this.avatarFileList = []
      const {data} = res
      if (data.url) {
        this.formAnchorData.avatar = data.url
        this.avatarFileList.push(data)
      }
      this.progressLoading.close()
    },
    handleImageRemove(file, fileList) {
      this.formAnchorData.avatar = ''
      this.avatarFileList = []
    },
    updatePhotoAlbumsList(e) {
      const newIndex = e.newIndex// 新位置下标
      const oldIndex = e.oldIndex// 原始位置下标
    },
    handlePhotoAlbumsRemove(file) {
      this.formAnchorData.photoAlbums = this.formAnchorData.photoAlbums.filter(function (age) {
        return age.url !== file.url
      })
    },
    handlePhotoAlbumsSuccess(response, file, fileList) {
      if (!this.formAnchorData.photoAlbums) {
        this.formAnchorData.photoAlbums = []
      }
      this.formAnchorData.photoAlbums.push(file.response.data)
      // 数据去重
      const temp = []
      this.formAnchorData.photoAlbums.forEach(a => {
        const check = temp.every(b => {
          return a.key !== b.key
        })
        check ? temp.push(a) : []
      })
      this.formAnchorData.photoAlbums = temp
      // 关闭loading
      this.progressLoading.close()
      this.$refs['handlePhotoAlbums'].clearFiles()
    },
    closeAnchorDialog() {
      this.avatarFileList = []
      this.dialogAnchorFormVisible = false
      this.formAnchorData = {}
    },
    editLevelDialogClose() {
      // this.getTableData(this.nFunc, this.tableDataFormat)
    },
    showEditLevelDialog(row) {
      this.formEditLevelData = {
        anchor_id: row.anchor_id,
        o_video_level: row.video_level,
        o_msg_level: row.msg_level,
      }
      this.editLevelDialogShow = true
    },
    enterUpdateVideoLevel() {
      updateVideoLevel({
        user_id: this.formEditLevelData.anchor_id,
        video_level: this.formEditLevelData.video_level
      }).then(res => {
        if (res.code === 0) {
          this.$message.success("成功")
        }
      })
    },
    enterUpdateMsgLevel() {
      updateMsgLevel({
        user_id: this.formEditLevelData.anchor_id,
        msg_level: this.formEditLevelData.msg_level
      }).then(res => {
        if (res.code === 0) {
          this.$message.success("成功")
        }
      })
    },
    showImgViolationDialog(row) {
      if (row.img_violation === 0) {
        return
      }
      this.clearImgDstId = row.anchor_id
      getContentReviewList({page: 1, pageSize: 50, user_id: row.anchor_id, type: 2}).then(res => {
        if (res.code === 0) {
          let resData = res.data.list || []
          let resTotal = res.data.total || 0
          if (resTotal === 0) {
            this.$message.error("无违规图片")
            return
          }
          let previewImgList = []
          resData.forEach(item => {
            previewImgList.push(item.content)
          })
          this.imgViolationList = resData
          this.imgViolationPreviewList = previewImgList
          this.imgViolationDialogShow = true
        }
      })
    },
    showMsgViolationDialog(row) {
      if (row.msg_violation === 0) {
        return
      }
      this.clearMsgDstId = row.anchor_id
      getContentReviewList({page: 1, pageSize: 100, user_id: row.anchor_id, type: 3}).then(res => {
        if (res.code === 0) {
          let resData = res.data.list || []
          let resTotal = res.data.total || 0
          if (resTotal === 0) {
            this.$message.error("无违规消息")
            return
          }
          let newList = []
          resData.forEach(item => {
            item.msg_level = row.msg_level
            item.created_at_str = this.formatDate(item.created_at)
            newList.push(item)
          })
          this.msgViolationList = newList
          this.msgViolationDialogShow = true
        }
      })
    },
    tableSort(data) {
      let columnData = data.column
      let orderData = data.order
      let propData = data.prop
      if (propData === 'call_answer_count') {
        return
      }
      if (propData !== null && orderData !== null) {
        switch (propData) {
          case 'created_at_str':
            this.searchInfo.sort_prop = 'created_at'
            break
          case 'updated_at_str':
            this.searchInfo.sort_prop = 'updated_at'
            break
          default:
            this.searchInfo.sort_prop = propData
        }
        this.searchInfo.sort_desc = orderData === 'descending'
      } else {
        this.searchInfo.sort_prop = null
        this.searchInfo.sort_desc = true
      }
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    onReset() {
      this.searchInfo = {}
    },
    showAnchorDataDialog(dst_id) {
      this.anchorDstId = dst_id
      this.$refs.anchorDataDialogRef.dialogVisible = true
    },
    async initDictList() {
      let res
      res = await anchorBanStateList()
      this.banStateOptions = res.data || []
      let sDict = {}
      this.banStateOptions.forEach(item => {
        sDict[item.value] = item.label
      })
      this.banStateDict = sDict
      res = await examineStateList()
      this.examineStateOptions = res.data || []

      let eDict = {}
      this.examineStateOptions.forEach(item => {
        eDict[item.value] = item.label
      })
      this.examineStateDict = eDict

      await this.getTableData(this.nFunc, this.tableDataFormat)
    },
    showBanAnchorDialog(row) {
      this.formData = {
        record_type: 1, // 封禁主播
        ban_id: row.anchor_id,
        user_complaints: row.user_complaints,
        img_violation: row.img_violation,
        msg_violation: row.msg_violation,
        deduction_ratio: 100,
        ban_days: 0,
        ban_reason: '',
        balance: row.balance,
      }
      this.banDialogFormVisible = true
    },
    updateExamineState(row) {
      row.examine_state = 1
      updateAnchorViolationRecords(row).then(res => {
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.getTableData(this.nFunc, this.tableDataFormat)
        }
      })
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    closeDialog() {
      this.banDialogFormVisible = false
      this.formData = {}
    },
    async enterDialog() {
      this.$refs.formDataRef.validate(async (v) => {
        if (!v) {
          return
        }
        let res = await banAnchor(this.formData)
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功'
          })
          this.closeDialog()
          await this.getTableData(this.nFunc, this.tableDataFormat)
        }
      })
    },
    openDialog() {
      this.type = 'create'
      this.banDialogFormVisible = true
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].updated_at_str = this.formatDate(item.updated_at)
        data[i].ban_state_str = this.banStateDict[item.ban_state]
        data[i].examine_state_str = this.examineStateDict[item.examine_state]
        data[i].recent_hundred_avg_second = this.numF2(item.recent_hundred_avg_second || 0)
      })
    },
  },
}
</script>

<style>
.right-area {
  height: 780px;
  overflow: auto
}
.blue {
  color: #0d84ff;
  cursor: pointer;
}

.img-box {
  width: 100%;
}
</style>
