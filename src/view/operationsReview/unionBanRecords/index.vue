<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="工会ID">
          <el-input v-model="searchInfo.union_id" placeholder="请输入工会ID" />
        </el-form-item>
        <el-form-item label="工会长ID">
          <el-input v-model="searchInfo.manager_id" placeholder="请输入工会长ID" />
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" type="success" icon="el-icon-plus" @click="openDialog">新增</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table border ref="tableData" style="width: 100%" :data="tableData" row-key="id" :max-height="590">
        <el-table-column align="center" label="日期" prop="created_at_str" min-width="180"/>
        <el-table-column align="center" label="工会ID" prop="union_id" min-width="120" />
        <el-table-column align="center" label="工会长ID" prop="manager_id" min-width="120" />
        <el-table-column align="center" label="扣除比例" prop="deduction_ratio" min-width="120" />
        <el-table-column align="center" label="工会长封禁天数" prop="manager_ban_days" min-width="120" />
        <el-table-column align="center" label="工会长封禁到期时间" prop="manager_ban_time" min-width="120" />
        <el-table-column align="center" label="工会封禁天数" prop="union_ban_days" min-width="120" />
        <el-table-column align="center" label="工会封禁到期时间" prop="union_ban_time" min-width="120" />
        <el-table-column align="center" label="封禁原因" prop="ban_reason" min-width="120" />
        <el-table-column align="center" label="操作" min-width="180">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateUnionBanRecords(scope.row)">修改</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" top="5px" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" :rules="formDataRules" label-position="right" label-width="120px">
        <el-form-item label="工会ID:" prop="union_id">
          <el-input v-model.number="formData.union_id" clearable/>
        </el-form-item>
        <el-form-item label="工会长ID:" prop="manager_id">
          <el-input v-model.number="formData.manager_id" clearable/>
        </el-form-item>
        <el-form-item label="扣除比例:" prop="deduction_ratio">
          <el-input v-model.number="formData.deduction_ratio" clearable/>
        </el-form-item>
        <el-form-item label="工会长封禁天数:" prop="manager_ban_days">
          <el-input v-model.number="formData.manager_ban_days" clearable/>
        </el-form-item>
        <el-form-item label="工会长封禁到期时间:" prop="manager_ban_time">
          <el-date-picker v-model="formData.manager_ban_time" type="date" style="width:100%" placeholder="选择日期" clearable />
        </el-form-item>
        <el-form-item label="工会封禁天数:" prop="union_ban_days">
          <el-input v-model.number="formData.union_ban_days" clearable/>
        </el-form-item>
        <el-form-item label="工会封禁到期时间:" prop="union_ban_time">
          <el-date-picker v-model="formData.union_ban_time" type="date" style="width:100%" placeholder="选择日期" clearable />
        </el-form-item>
        <el-form-item label="封禁原因:" prop="ban_reason">
          <el-input v-model="formData.ban_reason" clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { createUnionBanRecords, deleteUnionBanRecords, deleteUnionBanRecordsByIds, updateUnionBanRecords, findUnionBanRecords, getUnionBanRecordsList} from '@/api/unionBanRecords' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
export default {
  name: 'UnionBanRecords',
  mixins: [infoList],
  data() {
    return {
      listApi: getUnionBanRecordsList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        union_id: null,
        manager_id: null,
        deduction_ratio: null,
        manager_ban_days: null,
        manager_ban_time: null,
        union_ban_days: null,
        union_ban_time: null,
        ban_reason: null,
      },
      formDataRules: {
        union_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        manager_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        deduction_ratio: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        manager_ban_days: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        manager_ban_time: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        union_ban_days: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        union_ban_time: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        ban_reason: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
    }
  },
  created() {
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteUnionBanRecords(row)
      })
    },
    async updateUnionBanRecords(row) {
      const res = await findUnionBanRecords({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.reunionBanRecords
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {}
    },
    async deleteUnionBanRecords(row) {
      const res = await deleteUnionBanRecords({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createUnionBanRecords(this.formData)
          break
        case 'update':
          res = await updateUnionBanRecords(this.formData)
          break
        default:
          res = await createUnionBanRecords(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '成功'
        })
        this.closeDialog()
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
      })
    },
  },
}
</script>

<style>
</style>
