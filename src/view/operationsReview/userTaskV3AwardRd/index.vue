<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="用户id">
          <el-input v-model="searchInfo.user_id" placeholder="请输入用户id" clearable/>
        </el-form-item>
        <el-form-item label="功能标题">
          <!--<el-input v-model="searchInfo.func_title" placeholder="请输入" clearable/>-->
          <el-select v-model="searchInfo.func_title" clearable placeholder="功能标题">
            <el-option
              v-for="item in funcTitleOptions"
              :key="item.value"
              :label="item.label_en"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="完成时间">
          <el-date-picker
            v-model="searchInfo.date_range"
            type="datetimerange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="任务状态">
          <el-select v-model="searchInfo.task_review_state" clearable placeholder="审核状态">
            <el-option
              v-for="item in stateMapListData"
              :key="item.value"
              :label="item.label_zh"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="flex mb-6">
        <el-button size="small" type="primary" @click="selectAll">全选</el-button>
        <el-button size="small" type="success" :disabled="multipleInfoList.length === 0" @click="batchPass">批量通过</el-button>
        <el-button size="small" type="danger" :disabled="multipleInfoList.length === 0" @click="batchRefuse">批量拒绝</el-button>
      </div>
      <el-table border ref="tableDataRef" style="width: 100%" @sort-change="tableSort"
                @selection-change="selectionChange" :data="tableData" row-key="id" :max-height="590">
        <el-table-column align="center" :selectable="checkSelect" type="selection" width="55" />
        <el-table-column align="center" label="完成时间" prop="created_at_str" width="180"/>
        <el-table-column align="center" label="用户id" prop="user_id" width="130"/>
        <el-table-column align="center" label="功能标题" prop="func_title" min-width="120"/>
        <el-table-column align="center" label="任务文案" prop="task_content" min-width="120" show-overflow-tooltip/>
        <el-table-column align="center" label="奖励数量" prop="task_award_count" sortable="custom" width="100"/>
        <el-table-column align="center" label="审核状态" prop="task_review_state_str" width="100"/>
        <el-table-column align="center" label="数据详情" prop="task_class_key_id" width="100">
          <template #default="scope">
            <el-button type="text" icon="el-icon-view" size="mini" @click="showAnchorDataDialog(scope.row.user_id)">
              数据详情
            </el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" :width="150">
          <template #default="scope">
            <el-button size="small" :disabled="scope.row.task_review_state === 2" type="success" @click="singlePass(scope.row)">通过</el-button>
            <el-button size="small" :disabled="scope.row.task_review_state === 2 || scope.row.task_review_state === 3" type="danger" @click="singleRefuse(scope.row)">拒绝</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>

    <anchor-data-dialog ref="anchorDataDialogRef" :dst_id="anchorDstId" ></anchor-data-dialog>
  </div>
</template>

<script>
import {getUserTaskV3AwardRdList, batchStateUpdate, stateMapList, funcTitleMapList} from '@/api/userTaskV3AwardRd'
import infoList from '@/mixins/infoList'

export default {
  name: 'UserTaskV3AwardRd',
  mixins: [infoList],
  data() {
    return {
      listApi: getUserTaskV3AwardRdList,
      stateMapListData: [],
      stateMap: {},
      multipleInfoList: [],
      funcTitleOptions: [],
      batchUpdateReq: {
        info_list: [],
        state: 2,
      },
      anchorDstId: 0,
    }
  },
  created() {
    this.getTableData(this.nFunc, this.tableDataFormat)
    this.initStateMapList()
    this.initFuncTitleMapList()
  },
  methods: {
    tableSort(data) {
      let orderData = data.order
      let propData = data.prop
      if (propData !== null && orderData !== null) {
        this.searchInfo.sort_prop = propData
        this.searchInfo.sort_desc = orderData !== 'ascending'
      } else {
        this.searchInfo.sort_prop = null
        this.searchInfo.sort_desc = false
      }
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    initStateMapList() {
      stateMapList().then(res => {
        if (res.code !== 0) {
          this.$message.error(res.msg)
          return
        }
        this.stateMapListData = res.data
        this.stateMapListData.forEach(item => {
          this.stateMap[item.value] = item.label_zh
        })
      })
    },
    checkSelect(row, index){
      return row.task_review_state === 1 || row.task_review_state === 3
    },
    selectionChange(v) {
      let vList = []
      v.forEach(item=>{
        vList.push({
          id: item.id,
          user_id: item.user_id,
        })
      })
      this.multipleInfoList = vList
      this.batchUpdateReq.info_list = vList
    },
    selectAll() {
      this.$refs.tableDataRef.toggleAllSelection()
    },
    batchPass() {
      this.batchUpdateReq.state = 2
      this.submitBatchStateUpdate()
    },
    batchRefuse() {
      this.batchUpdateReq.state = 3
      this.submitBatchStateUpdate()
    },
    singlePass(row){
      this.batchUpdateReq.info_list = [{
        id: row.id,
        user_id: row.user_id,
      }]
      this.batchUpdateReq.state = 2
      this.submitBatchStateUpdate()
    },
    singleRefuse(row){
      this.batchUpdateReq.info_list = [{
        id: row.id,
        user_id: row.user_id,
      }]
      this.batchUpdateReq.state = 3
      this.submitBatchStateUpdate()
    },
    submitBatchStateUpdate() {
      batchStateUpdate(this.batchUpdateReq).then(res=>{
        if (res.code === 0) {
          this.$message({
            message: '操作成功',
            type: 'success',
          })
          this.getTableData(this.nFunc, this.tableDataFormat)
        } else {
          this.$message({
            message: res.msg,
            type: 'error',
          })
        }
      })
    },
    onReset() {
      this.searchInfo = {}
    },
    onSubmit() {
      this.page = 1
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
        data[i].updated_at_str = this.formatDate(item.updated_at)
        data[i].task_review_state_str = this.stateMap[item.task_review_state]
      })
    },
    initFuncTitleMapList() {
      funcTitleMapList().then(res => {
        if (res.code !== 0) {
          this.$message.error(res.msg)
          return
        }
        this.funcTitleOptions = res.data || []
      })
    },
    showAnchorDataDialog(dst_id) {
      this.anchorDstId = dst_id
      this.$refs.anchorDataDialogRef.dialogVisible = true
    },
  },
}
</script>

<style>
</style>
