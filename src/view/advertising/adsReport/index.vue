<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="产品名称">
          <el-select filterable multiple collapse-tags v-model="searchInfo.app_id_range" clearable>
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="`${item.app_name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="投放国家">
          <el-select multiple collapse-tags v-model="searchInfo.country_code_range" clearable>
            <el-option
              v-for="item in countryCodeOptions"
              :key="item"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="投放语种">
          <el-select multiple collapse-tags v-model="searchInfo.language_range" clearable>
            <el-option
              v-for="item in anchorLangOptions"
              :key="item"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="广告系列名">
          <el-input v-model="searchInfo.campaign_name"></el-input>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="时间范围">
          <el-date-picker
            v-model="searchInfo.date_range"
            type="daterange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DDTHH:mm:ssZ"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="设计师">
          <el-select multiple collapse-tags v-model="searchInfo.designer_range" clearable>
            <el-option
              v-for="item in designerOptions"
              :key="item"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="投放平台">
          <el-select multiple collapse-tags v-model="searchInfo.channel_range" clearable @change="channelChange">
            <el-option
              v-for="item in channelOptions"
              :key="item"
              :disabled="item.disabled"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="Group by">
          <el-select multiple collapse-tags v-model="searchInfo.group_range" clearable>
            <el-option
              v-for="item in adsGroupOptions"
              :key="item"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="text" :icon="queryTxtIcon" @click="queryTxtChange">{{ queryTxt }}</el-button>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        border
        ref="tableData"
        style="width: 100%"
        @sort-change="tableSort"
        :data="tableData"
        max-height="590"
        row-key="id">
        <el-table-column align="center" label="日期" prop="date" min-width="180"
                         sortable="custom" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="App名称" prop="app_name" v-if="columnShow('app_id')" min-width="120"
                         show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="投放国家" prop="country_code" v-if="columnShow('country_code')"
                         min-width="140"></el-table-column>
        <el-table-column align="center" label="投放语种" prop="language" v-if="columnShow('language')"
                         min-width="140"></el-table-column>
        <el-table-column align="center" label="优化师" prop="optimizer" v-if="columnShow('optimizer')"
                         min-width="140"></el-table-column>
        <el-table-column align="center" label="投放平台" prop="channel" v-if="columnShow('channel')"
                         min-width="140"></el-table-column>
        <el-table-column align="center" label="Campaign" prop="campaign_name" v-if="columnShow('campaign_name')"
                         sortable="custom" min-width="180" show-overflow-tooltip></el-table-column>
        <el-table-column align="center" label="消耗" prop="cost" min-width="140" sortable="custom"></el-table-column>
        <el-table-column align="center" label="CPI" prop="CPI" min-width="140"></el-table-column>
        <el-table-column align="center" label="展示" prop="impression" min-width="140" sortable="custom"></el-table-column>
        <el-table-column align="center" label="点击" prop="click" min-width="140" sortable="custom"></el-table-column>
        <el-table-column align="center" label="安装" prop="install" min-width="140" sortable="custom"></el-table-column>
        <el-table-column align="center" label="CTR" prop="CTR" min-width="140"></el-table-column>
        <el-table-column align="center" label="CVR" prop="CVR" min-width="140"></el-table-column>
        <el-table-column align="center" label="支付金额" prop="revenue" min-width="140" sortable="custom"></el-table-column>
        <el-table-column align="center" label="支付次数" prop="paid_count" min-width="140" sortable="custom"></el-table-column>
        <el-table-column align="center" label="支付人数" prop="paid_user" min-width="140" sortable="custom"></el-table-column>
        <el-table-column align="center" label="IR" prop="IR" min-width="140"></el-table-column>
        <el-table-column align="center" label="CPC" prop="CPC" min-width="140"></el-table-column>
        <el-table-column align="center" label="ROI" prop="ROI" min-width="140"></el-table-column>
        <el-table-column align="center" label="付费率" prop="pay_rate" min-width="140"></el-table-column>
        <el-table-column align="center" label="ARPPU" prop="ARPPU" min-width="140"></el-table-column>
        <el-table-column align="center" label="人均付费次数" prop="pay_of_per" min-width="140"></el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import {getAdsReportList} from '@/api/adsReport' //  此处请自行替换地址
  import infoList from '@/mixins/infoList'
  import adsReportMixins from '@/mixins/adsReport'
  import moment from "moment";

  export default {
    mixins: [infoList, adsReportMixins],
    data() {
      return {
        searchInfo: {
          campaign_name: '',
          date_range: [],
          app_id_range: [],
          country_code_range: [],
          language_range: [],
          designer_range: [],
          channel_range: [],
          // group_range: ['date', 'app_id', 'country_code', 'language', 'optimizer', 'channel'],
          group_range: ['campaign_name', 'date'],
        },
        listApi: getAdsReportList,
      }
    },
    created() {
      this.pageSize = 100
      let nDate = moment()
      let nDateStr = nDate.format("YYYY-MM-DDT00:00:00Z")
      this.searchInfo.date_range = [nDateStr, nDateStr]
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    methods: {
      channelChange(v) {
        if (v.length === 0) {
          this.channelOptions.map((item, i, data) => {
            data[i].disabled = false
          })
          return
        }

        if (v.some(item => item === 1)) {
          this.channelOptions.map((item, i, data) => {
            if (i !== 0) {
              data[i].disabled = true
            }
          })
        } else {
          this.channelOptions.map((item, i, data) => {
            if (i === 0) {
              data[i].disabled = true
            }
          })
        }

      },
      columnShow(v) {
        return this.searchInfo.group_range.some((item) => {
          return item === v
        })
      },
      tableSort({column, prop, order}) {
        if (prop !== null && order !== null) {
          if (order === 'descending') {
            this.searchInfo.sort_str = prop + " DESC"
          } else {
            this.searchInfo.sort_str = prop + " ASC"
          }
        } else {
          this.searchInfo.sort_str = null
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      onReset() {
        this.pageSize = 100
        this.searchInfo = {
          date_range: [],
          app_id: null,
          country_code_range: [],
          language_range: [],
          designer_range: [],
          channel: null,
          group_range: [],
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      onSubmit() {
        this.page = 1
        this.pageSize = 100
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      getSummaries({columns, data}) {
        /*
        // 获取表头和数据
        // :show-summary="this.searchInfo.group_range.length > 0"
        // :summary-method="getSummaries"
        let labelList = []
        let vList = []
        columns.forEach((column, i) => {
          labelList.push(column.label)
          vList.push(column.property)
        })
        console.log(labelList)
        console.log(vList)
         */

        let sums = []
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = 'Total Cost'
            return
          }
          const values = data.map((item) => Number(item[column.property]))
          if (!values.every((value) => Number.isNaN(value))) {
            sums[index] = `${values.reduce((prev, curr) => {
              const value = Number(curr)
              if (!Number.isNaN(value)) {
                return prev + curr
              } else {
                return prev
              }
            }, 0)}`
          } else {
            sums[index] = 'N/A'
          }
        })
        return sums
      },
      tableDataFormat() {
        this.tableData.map((item, i, data) => {
          data[i].app_name = this.formatAppName(item.app_id)
          data[i].channel = this.formatChannel(item.channel)[0]
          data[i].CPI = this.formateAdsRCPI(item)
          data[i].CTR = this.formateAdsRCTR(item)
          data[i].CVR = this.formateAdsRCVR(item)
          data[i].IR = this.formateAdsRIR(item)
          data[i].CPC = this.formateAdsRCPC(item)
          data[i].ROI = this.formateAdsRROI(item)
          data[i].pay_rate = this.formateAdsRpay_rate(item)
          data[i].ARPPU = this.formateAdsRARPPU(item)
          data[i].pay_of_per = this.formateAdsRpay_of_per(item)

          if (!this.columnShow('date')) {
            if (this.searchInfo.date_range.length === 2) {
              data[i].date = `${this.formatDateYMD(this.searchInfo.date_range[0])}-${this.formatDateYMD(this.searchInfo.date_range[1])}`
            } else {
              data[i].date = "-"
            }
          } else {
            data[i].date = this.formatDateYMD(item.date)
          }
          data[i].cost = this.strCheckN3F2(item.cost)
          data[i].impression = this.strCheckN3int(item.impression)
          data[i].click = this.strCheckN3int(item.click)
          data[i].install = this.strCheckN3int(item.install)
          data[i].paid_count = this.strCheckN3int(item.paid_count)
          data[i].paid_user = this.strCheckN3int(item.paid_user)
          data[i].revenue = this.strCheckN3F2(item.revenue)
        })
      },
      handleDownload() {
        let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
        let res = this.getTableHeaderProp(tableColumns)
        let tHeader = res[0]
        let filterV = res[1]
        this.handleProgressLoading()
        import('@/utils/excel').then((excel) => {
          const data = this.formatJson(filterV, this.tableData)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.filename,
            autoWidth: this.autoWidth,
            bookType: this.bookType,
          })
          this.progressLoading.close()
        })
      },
      sizeChange(v) {
        this.pageSize = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      pageChange(v) {
        this.page = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
    },
  }
</script>

<style lang="scss" scoped>

</style>
