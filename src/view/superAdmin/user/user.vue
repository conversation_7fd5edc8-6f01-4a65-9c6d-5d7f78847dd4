<template>
  <div>
    <warning-bar title="注：右上角头像下拉可切换角色"/>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="addUser">新增用户</el-button>
      </div>
      <el-table :data="tableData" max-height="590">
        <el-table-column align="center" label="头像" min-width="50">
          <template #default="scope">
            <CustomPic style="margin-top:8px" :pic-src="scope.row.headerImg"/>
          </template>
        </el-table-column>
        <el-table-column align="center" label="UUID" min-width="250" prop="uuid" show-overflow-tooltip/>
        <el-table-column align="center" label="用户名" min-width="150" prop="userName" show-overflow-tooltip/>
        <el-table-column align="center" label="昵称" min-width="150" prop="nickName" show-overflow-tooltip/>
        <el-table-column align="center" label="客服ID" min-width="150" prop="services_id"/>
        <el-table-column align="center" label="AppId" prop="app_id" width="120" show-overflow-tooltip>
          <template #default="scope">{{ formaAppList(scope.row.app_id) }}</template>
        </el-table-column>
        <el-table-column align="center" label="工会ID" prop="union_id" width="120">
          <template #default="scope">{{ formaUnionList(scope.row.union_id) }}</template>
        </el-table-column>
        <el-table-column align="center" label="用户角色" min-width="150">
          <template #default="scope">
            <el-cascader
              v-model="scope.row.authorityIds"
              :options="authOptions"
              :show-all-levels="false"
              collapse-tags
              :props="{ multiple:true,checkStrictly: true,label:'authorityName',value:'authorityId',disabled:'disabled',emitPath:false}"
              :clearable="false"
              @visible-change="(flag)=>{changeAuthority(scope.row,flag)}"
              @remove-tag="()=>{changeAuthority(scope.row,false)}"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" min-width="350">
          <template #default="scope">
            <el-popover :visible="scope.row.visible" placement="top" width="160">
              <p>确定要删除此用户吗</p>
              <div style="text-align: right; margin-top: 8px;">
                <el-button size="mini" type="text" @click="scope.row.visible = false">取消</el-button>
                <el-button type="primary" size="mini" @click="deleteUser(scope.row)">确定</el-button>
              </div>
              <template #reference>
                <el-button type="text" icon="el-icon-delete" size="mini">删除</el-button>
              </template>
            </el-popover>
            <el-popconfirm title="重置用户密码为123456" @confirm="resetPwd(scope.row)">
              <template #reference>
                <el-button type="text" icon="el-icon-refresh-left" size="mini">重置密码</el-button>
              </template>
            </el-popconfirm>
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button"
                       @click="setUsersServices(scope.row)">配置客服
            </el-button>
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="setAppId(scope.row)">
              配置APP
            </el-button>
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="setUnionId(scope.row)">
              配置工会
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       layout="total, sizes, prev, pager, next, jumper"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog width="30%" v-model="addUserDialog" custom-class="user-dialog" title="新增用户">
      <el-form ref="userForm" :rules="rules" :model="userInfo" label-width="80px">
        <el-form-item label="用户名" prop="username">
          <el-input v-model="userInfo.username"/>
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="userInfo.password"/>
        </el-form-item>
        <el-form-item label="别名" prop="nickName">
          <el-input v-model="userInfo.nickName"/>
        </el-form-item>
        <el-form-item label="用户角色" prop="authorityId">
          <el-cascader
            v-model="userInfo.authorityIds"
            style="width:100%"
            :options="authOptions"
            :show-all-levels="false"
            :props="{ multiple:true,checkStrictly: true,label:'authorityName',value:'authorityId',disabled:'disabled',emitPath:false}"
            :clearable="false"
          />
        </el-form-item>
        <el-form-item label="头像" label-width="80px">
          <div style="display:inline-block" @click="openHeaderChange">
            <img v-if="userInfo.headerImg" class="header-img-box"
                 :src="(userInfo.headerImg && userInfo.headerImg.slice(0, 4) !== 'http')?path+userInfo.headerImg:userInfo.headerImg">
            <div v-else class="header-img-box">从媒体库选择</div>
          </div>
        </el-form-item>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeAddUserDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterAddUserDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="setUserServicesDialog" custom-class="user-dialog" title="配置客服" :width="400">
      <el-form ref="userServicesForm" :rules="rules" :model="userInfo" label-width="80px">
        <el-form-item label="客服ID" prop="services_id">
          <el-select v-model="userServicesForm.services_id" clearable placeholder="客服ID">
            <el-option
              v-for="item in usersOptions"
              :key="item.id"
              :label="`${item.nickname}(${item.id})`"
              :value="item.id"/>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeUsersServicesDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterUsersServicesDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="setAppIdDialog" custom-class="user-dialog" title="配置APP" :width="400">
      <el-form ref="userServicesForm" :rules="rules" :model="userInfo" label-width="80px">
        <el-form-item label="AppId" prop="app_id">
          <el-select v-model="userServicesForm.app_id" clearable filterable placeholder="AppId">
            <el-option
                v-for="item in appList"
                :key="item"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeAppIdDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterAppIdDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="setUnionIdDialog" custom-class="user-dialog" title="配置工会" :width="400">
      <el-form ref="userServicesForm" :rules="rules" :model="userInfo" label-width="80px">
        <el-form-item label="工会" prop="union_id">
          <el-select v-model="userServicesForm.union_id" clearable filterable placeholder="AppId">
            <el-option
              v-for="item in unionListOptions"
              :key="item.id"
              :label="item.name"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeUnionIdDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterUnionIdDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>

    <ChooseImg ref="chooseImg" :target="userInfo" :target-key="`headerImg`"/>
  </div>
</template>

<script>
  // 获取列表内容封装在mixins内部  getTableData方法 初始化已封装完成
  const path = import.meta.env.VITE_BASE_API
  import {
    getUserList,
    setUserAuthorities,
    setUserInfo,
    register,
    deleteUser, resetUserPwd
  } from '@/api/user'
  import {
    getUsersList
  } from '@/api/users'
  import {getUnionsList} from '@/api/unions'
  import {getAuthorityList} from '@/api/authority'
  import infoList from '@/mixins/infoList'
  import {getAppXesList} from '@/api/appXes'
  import CustomPic from '@/components/customPic/index.vue'
  import ChooseImg from '@/components/chooseImg/index.vue'
  import warningBar from '@/components/warningBar/warningBar.vue'

  export default {
    name: 'Api',
    components: {CustomPic, ChooseImg, warningBar},
    mixins: [infoList],
    data() {
      return {
        unionListOptions: [],
        servicesId: null,
        listApi: getUserList,
        path: path,
        authOptions: [],
        usersOptions: [],
        appListOptions: [],
        addUserDialog: false,
        setUnionIdDialog: false,
        userInfo: {
          username: '',
          password: '',
          nickName: '',
          headerImg: '',
          authorityId: '',
          app_id: 0,
          authorityIds: []
        },
        setUserServicesDialog: false,
        setAppIdDialog: false,
        userServicesForm: {
          id: 0,
          app_id: 0,
          services_id: 0,
        },
        rules: {
          username: [
            {required: true, message: '请输入用户名', trigger: 'blur'},
            {min: 5, message: '最低5位字符', trigger: 'blur'}
          ],
          password: [
            {required: true, message: '请输入用户密码', trigger: 'blur'},
            {min: 6, message: '最低6位字符', trigger: 'blur'}
          ],
          nickName: [
            {required: true, message: '请输入用户昵称', trigger: 'blur'}
          ],
          authorityId: [
            {required: true, message: '请选择用户角色', trigger: 'blur'}
          ]
        }
      }
    },
    watch: {
      tableData() {
        this.setAuthorityIds()
      }
    },
    async created() {
      await this.getUnionList()
      await this.getAppList()
      await this.getTableData()
      const res = await getAuthorityList({page: 1, pageSize: 999})
      this.setOptions(res.data.list)
      this.getUsersList()
    },
    methods: {
      async resetPwd(row) {
        console.log(row)
        let res = resetUserPwd(row.id)
        if (res.code === 0) {
          this.$message.success("成功")
        }
      },
      formaUnionList: function (bool) {
        let text
        this.unionListOptions.forEach(function (element) {
          if (bool === element.id) {
            text = element.name
            return text
          }
        });
        return text
      },
      // 工会
      async getUnionList() {
        const res = await getUnionsList({page:1, pageSize: 1000})
        this.unionListOptions = res.data.list
      },
      formaAppList: function (bool) {
        let text
        this.appListOptions.forEach(function (element) {
          if (bool === element.id) {
            text = element.appName
            return text
          }
        });
        return text
      },
      async getAppList() {
        const res = await getAppXesList()
        this.appListOptions = res.data.list
      },
      async getUsersList() {
        const res = await getUsersList({role: 3})
        this.usersOptions = res.data.list
      },
      setAuthorityIds() {
        this.tableData && this.tableData.forEach((user) => {
          const authorityIds = user.authorities && user.authorities.map(i => {
            return i.authorityId
          })
          user.authorityIds = authorityIds
        })
      },
      openHeaderChange() {
        this.$refs.chooseImg.open()
      },
      setOptions(authData) {
        this.authOptions = []
        this.setAuthorityOptions(authData, this.authOptions)
      },
      setAuthorityOptions(AuthorityData, optionsData) {
        AuthorityData &&
        AuthorityData.forEach(item => {
          if (item.children && item.children.length) {
            const option = {
              authorityId: item.authorityId,
              authorityName: item.authorityName,
              children: []
            }
            this.setAuthorityOptions(item.children, option.children)
            optionsData.push(option)
          } else {
            const option = {
              authorityId: item.authorityId,
              authorityName: item.authorityName
            }
            optionsData.push(option)
          }
        })
      },
      async deleteUser(row) {
        const res = await deleteUser({id: row.id})
        if (res.code === 0) {
          this.$message.success('删除成功')
          await this.getTableData()
          row.visible = false
        }
      },
      async enterAddUserDialog() {
        this.userInfo.authorityId = this.userInfo.authorityIds[0]
        this.$refs.userForm.validate(async valid => {
          if (valid) {
            const res = await register(this.userInfo)
            if (res.code === 0) {
              this.$message({type: 'success', message: '创建成功'})
            }
            await this.getTableData()
            this.closeAddUserDialog()
          }
        })
      },
      closeAddUserDialog() {
        this.$refs.userForm.resetFields()
        this.userInfo.headerImg = ''
        this.userInfo.authorityIds = []
        this.addUserDialog = false
      },
      closeUsersServicesDialog() {
        this.setUserServicesDialog = false
        this.userServicesForm = {}
      },
      addUser() {
        this.addUserDialog = true
      },
      async setUsersServices(row) {
        this.userServicesForm.id = row.id
        this.userServicesForm.services_id = parseInt(row.services_id)
        this.setUserServicesDialog = true
      },
      // 设置AppId
      async setAppId(row) {
        this.userServicesForm.id = row.id
        this.userServicesForm.app_id = parseInt(row.app_id)
        this.setAppIdDialog = true
      },
      async setUnionId(row) {
        this.userServicesForm.id = row.id
        this.userServicesForm.union_id = parseInt(row.union_id)
        this.setUnionIdDialog = true
      },
      closeUnionIdDialog() {
        this.setUnionIdDialog = false
        this.userServicesForm = {}
      },
      async enterUnionIdDialog() {
        const res = await setUserInfo(this.userServicesForm)
        if (res.code === 0) {
          this.$message({type: 'success', message: '更新成功'})
        }
        await this.getTableData()
        this.closeUnionIdDialog()
      },
      closeAppIdDialog() {
        this.setAppIdDialog = false
        this.userServicesForm = {}
      },
      async enterAppIdDialog() {
        const res = await setUserInfo(this.userServicesForm)
        if (res.code === 0) {
          this.$message({type: 'success', message: '更新成功'})
        }
        await this.getTableData()
        this.closeAppIdDialog()
      },
      async enterUsersServicesDialog() {
        const res = await setUserInfo(this.userServicesForm)
        if (res.code === 0) {
          this.$message({type: 'success', message: '客服设置成功'})
        }
        this.closeUsersServicesDialog()
      },
      async changeAuthority(row, flag) {
        if (flag) {
          return
        }
        this.$nextTick(async () => {
          const res = await setUserAuthorities({
            id: row.id,
            authorityIds: row.authorityIds
          })
          if (res.code === 0) {
            this.$message({type: 'success', message: '角色设置成功'})
          }
        })
      },
    }
  }
</script>

<style lang="scss">
  .user-dialog {
    .header-img-box {
      width: 200px;
      height: 200px;
      border: 1px dashed #ccc;
      border-radius: 20px;
      text-align: center;
      line-height: 200px;
      cursor: pointer;
    }

    .avatar-uploader .el-upload:hover {
      border-color: #409eff;
    }

    .avatar-uploader-icon {
      border: 1px dashed #d9d9d9 !important;
      border-radius: 6px;
      font-size: 28px;
      color: #8c939d;
      width: 178px;
      height: 178px;
      line-height: 178px;
      text-align: center;
    }

    .avatar {
      width: 178px;
      height: 178px;
      display: block;
    }
  }
</style>
