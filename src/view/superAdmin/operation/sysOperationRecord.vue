<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="操作人">
          <el-select v-model="searchInfo.user_id" placeholder="请选择操作人" clearable>
            <el-option
                v-for="item in userAllList"
                :key="item.id"
                :label="item.nickName"
                :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="请求时间">
          <el-date-picker
              v-model="searchInfo.time_list"
              type="datetimerange"
              :shortcuts="dateRangeShortcuts"
              range-separator="-"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="请求方法">
          <el-select v-model="searchInfo.method" placeholder="请选择请求方法" clearable>
            <el-option label="GET" value="GET"/>
            <el-option label="POST" value="POST"/>
            <el-option label="PUT" value="PUT"/>
            <el-option label="DELETE" value="DELETE"/>
          </el-select>
        </el-form-item>
        <el-form-item label="请求路径">
          <el-input v-model="searchInfo.path" placeholder="请求路径"/>
        </el-form-item>
        <el-form-item label="结果状态码">
          <el-input v-model="searchInfo.status" placeholder="结果状态码"/>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
          ref="multipleTable"
          :data="tableData"
          border
          style="width: 100%"
          tooltip-effect="dark"
          row-key="id"
          max-height="590"
          @selection-change="handleSelectionChange"
      >
        <el-table-column fixed="left" align="center" label="操作人" prop="nickName" width="140"></el-table-column>
        <el-table-column align="center" label="日期" width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="状态码" prop="status" width="120">
          <template #default="scope">
            <div>
              <el-tag type="success">{{ scope.row.status }}</el-tag>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="请求IP" prop="ip" width="140" show-overflow-tooltip/>
        <el-table-column align="center" label="请求方法" prop="method" width="120"/>
        <el-table-column align="center" label="请求路径" prop="path" min-width="340" show-overflow-tooltip/>
        <el-table-column fixed="right" align="center" label="请求" prop="path" width="80">
          <template #default="scope">
            <div class="d-f-r-c">
              <div>
                <el-popover v-if="scope.row.body" placement="left" trigger="hover">
                  <div class="popover-box">
                    <p>{{ scope.row.body }}</p>
                  </div>
                  <template #reference>
                    <i class="el-icon-view"/>
                  </template>
                </el-popover>
                <span v-else>无</span>
              </div>
              <el-button style="margin-left: 6px" type="text" @click="copyText(scope.row.body)">复制</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column fixed="right" align="center" label="响应" prop="path" width="80">
          <template #default="scope">
            <div class="d-f-r-c">
              <div>
                <el-popover v-if="scope.row.resp" placement="left" trigger="hover">
                  <div class="popover-box">
                    <p>{{ scope.row.resp }}</p>
                  </div>
                  <template #reference>
                    <i class="el-icon-view"/>
                  </template>
                </el-popover>
                <span v-else>无</span>
              </div>
              <el-button style="margin-left: 6px" type="text" @click="copyText(scope.row.resp)">复制</el-button>
            </div>
          </template>
        </el-table-column>

      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       layout="total, sizes, prev, pager, next, jumper"
                       @current-change="pageChange"
                       @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import {
    deleteSysOperationRecord,
    getSysOperationRecordList,
    deleteSysOperationRecordByIds
  } from '@/api/sysOperationRecord' // 此处请自行替换地址
  import infoList from '@/mixins/infoList'
  import {getUserList} from '@/api/user'
  const shortcuts = [
    {
      text: '近一周',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
        return [start, end]
      },
    },
    {
      text: '近一月',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
        return [start, end]
      },
    },
    {
      text: '近一季度',
      value: () => {
        const end = new Date()
        const start = new Date()
        start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
        return [start, end]
      },
    },
  ]
  export default {
    name: 'SysOperationRecord',
    mixins: [infoList],
    data() {
      return {
        listApi: getSysOperationRecordList,
        shortcuts: shortcuts,
        dialogFormVisible: false,
        type: '',
        deleteVisible: false,
        multipleSelection: [],
        userAllList: [],
        userListData: {},
        formData: {
          ip: null,
          method: null,
          path: null,
          status: null,
          latency: null,
          agent: null,
          error_message: null,
          user_id: null
        }
      }
    },
    created() {
      this.getAllUserList()

    },
    methods: {
      onReset() {
        this.searchInfo = {}
      },
      getAllUserList() {
        getUserList({page: 1, pageSize: 500}).then(res => {
          this.userListData = {}
          this.userAllList = res.data.list
          res.data.list.forEach((item, index) => {
            this.userListData[item.id] = item
          })
          this.getTableData(this.nFunc, this.tableDataFormat)
        })
      },
      // 条件搜索前端看此方法
      onSubmit() {
        this.page = 1
        this.pageSize = 10
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      tableDataFormat() {
        this.tableData.map((item, i, data) => {
          data[i].nickName = this.formatNickName(item.user_id)
        })
      },
      formatNickName(user_id) {
        let u = this.userListData[user_id]
        if (u) {
          return u.nickName
        }
        return "未知"
      },
      handleSelectionChange(val) {
        this.multipleSelection = val
      },
      async onDelete() {
        const ids = []
        this.multipleSelection &&
        this.multipleSelection.forEach(item => {
          ids.push(item.id)
        })
        const res = await deleteSysOperationRecordByIds({ids})
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '删除成功'
          })
          if (this.tableData.length === ids.length && this.page > 1) {
            this.page--
          }
          this.deleteVisible = false
          this.getTableData(this.nFunc, this.tableDataFormat)
        }
      },
      sizeChange(v) {
        this.pageSize = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      pageChange(v) {
        this.page = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      async deleteSysOperationRecord(row) {
        row.visible = false
        const res = await deleteSysOperationRecord({ID: row.id})
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '删除成功'
          })
          if (this.tableData.length === 1 && this.page > 1) {
            this.page--
          }
          this.getTableData(this.nFunc, this.tableDataFormat)
        }
      },
      fmtBody(value) {
        try {
          return JSON.parse(value)
        } catch (err) {
          return value
        }
      }
    }
  }
</script>

<style lang="scss">
  .table-expand {
    padding-left: 60px;
    font-size: 0;

    label {
      width: 90px;
      color: #99a9bf;

      .el-form-item {
        margin-right: 0;
        margin-bottom: 0;
        width: 50%;
      }
    }
  }

  .popover-box {
    background: #112435;
    color: #f08047;
    height: 200px;
    width: 150px;
    overflow: auto;
  }

  .popover-box::-webkit-scrollbar {
    display: none; /* Chrome Safari */
  }
</style>
