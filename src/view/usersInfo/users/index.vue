<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="注册时间">
          <el-date-picker
              v-model="searchInfo.register_time"
              type="date"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              clearable
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="产品:">
          <el-select v-model="searchInfo.appId" clearable filterable placeholder="请选择APP">
            <el-option
                v-for="item in appList"
                :key="item.id"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="ID">
          <el-input v-model="searchInfo.id" placeholder="ID" clearable/>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="昵称">
          <el-input v-model="searchInfo.nickname" placeholder="昵称"/>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="封禁状态">
          <el-select v-model.number="searchInfo.recovery" clearable filterable placeholder="封禁状态">
            <el-option
                v-for="item in recoveryStateOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="国籍">
          <el-select v-model="searchInfo.countryCode" clearable filterable placeholder="国籍">
            <el-option
                v-for="item in countryCodeOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="角色">
          <el-select v-model="searchInfo.role" clearable filterable placeholder="角色类型">
            <el-option
                v-for="item in roleOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="用户分级">
          <el-select v-model="searchInfo.levels" clearable filterable placeholder="用户分级">
            <el-option v-for="item in level_list" :key="item.id" :label="`${item.desc}(${item.id})`" :value="item.id" />
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button size="mini" type="text" :icon="queryTxtIcon" @click="queryTxtChange">{{ queryTxt }}</el-button>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
      </div>
      <el-table
          ref="multipleTable"
          style="width: 100%"
          tooltip-effect="dark"
          :data="tableData"
          row-key="id"
          max-height="590"
      >
        <el-table-column align="center" label="操作" width="280" fixed="left">
          <template #default="scope">
            <el-button type="text" icon="el-icon-view" size="small" @click="viewDetailInfo(scope.row)">查看</el-button>
            <el-button type="text" icon="el-icon-view" size="small" @click="viewAfDetailInfo(scope.row)">查看AF信息
            </el-button>
            <el-button
                type="text"
                icon="el-icon-edit"
                size="small"
                class="table-button"
                @click="updateUsers(scope.row)"
            >变更
            </el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="用户ID" prop="id" width="120"/>
        <el-table-column align="center" label="用户分级" prop="level" width="120">
          <template #default="scope">{{ `${scope.row.levels_desc}(${scope.row.levels})` }}</template>
        </el-table-column>
        <el-table-column align="center" label="产品" prop="appId" width="120">
          <template #default="scope">{{ formatAppName(scope.row.appId) }}</template>
        </el-table-column>
        <el-table-column align="center" label="昵称" prop="nickname" width="120"/>
        <el-table-column align="center" label="手机号" prop="phone" width="180"/>
        <el-table-column v-if="searchInfo.role == 2" align="center" label="所属工会" prop="unionId" width="120"/>
        <!--        <el-table-column align="center" label="头像" min-width="50">-->
        <!--          <template #default="scope">-->
        <!--            <CustomPic style="margin-top:8px" :pic-src="scope.row.avatar"/>-->
        <!--          </template>-->
        <!--        </el-table-column>-->

        <el-table-column align="center" label="性别" width="120">
          <template #default="scope">{{ formatSex(scope.row.gender) }}</template>
        </el-table-column>

        <el-table-column align="center" label="注册日期" width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>

        <el-table-column align="center" label="身份" prop="role" width="120">
          <template #default="scope">{{ formatRole(scope.row.role) }}</template>
        </el-table-column>

        <el-table-column align="center" label="工作状态" prop="role" width="120">
          <template #default="scope">{{ formatWorkingCondition(scope.row.workingCondition) }}</template>
        </el-table-column>

        <el-table-column align="center" label="级别" prop="levels" width="120"/>
        <el-table-column v-if="searchInfo.role == 1" align="center" label="邀请人id" prop="manager_id" width="120"/>
        <el-table-column v-if="searchInfo.role == 1" align="center" label="邀请码" prop="invite_code" width="120"/>

        <el-table-column align="center" label="是否VIP" prop="vipExpireAt" width="120">
          <template #default="scope">
            <el-tag v-if="Date.parse(scope.row.vipExpireAt) > new Date()" type="success">是</el-tag>
            <el-tag v-else type="info">否</el-tag>
          </template>
        </el-table-column>

        <el-table-column align="center" label="VIP过期时间" width="180">
          <template #default="scope">{{ formatDate(scope.row.vipExpireAt) }}</template>
        </el-table-column>

        <el-table-column align="center" label="用户状态" width="180">
          <template #default="scope">{{ formatState(scope.row.state) }}</template>
        </el-table-column>

        <el-table-column align="center" label="最后登录IP" prop="lastLoginIp" width="120"/>

        <el-table-column align="center" label="最后登录时间" width="180">
          <template #default="scope">{{ formatDate(scope.row.lastLoginAt) }}</template>
        </el-table-column>

        <el-table-column align="center" label="账号类型" width="120">
          <template #default="scope">{{ formatLoginType(scope.row.loginType) }}</template>
        </el-table-column>
        <el-table-column align="center" label="在线状态" width="120">
          <template #default="scope">{{ formatOnline(scope.row.online) }}</template>
        </el-table-column>
        <el-table-column align="center" label="用户余额" prop="diamonds" width="120"/>
        <el-table-column align="center" label="渠道" prop="utmSource" width="120"/>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            v-show="total > 0"
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作" top="0">
      <el-form :model="formData" label-width="85px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="头像:">
              <el-upload
                  ref="handleImage"
                  :action="`${path}/files/createFiles`"
                  :headers="{ 'x-token': token }"
                  list-type="picture-card"
                  :file-list="avatarFileList"
                  :on-success="handleAvatarSuccess"
                  :on-progress="handleProgressLoading"
                  :on-exceed="handleImgExceed"
                  :multiple="false"
                  :limit="1"
                  accept=".jpg, .jpeg, .png, .gif"
                  :on-remove="handleAvatarRemove"
              >
                <i class="el-icon-plus"/>
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="App:" prop="appId">
              <el-select v-model="formData.appId" filterable placeholder="AppId">
                <el-option
                    v-for="item in appList"
                    :key="item.id"
                    :label="`${item.app_name}(${item.id})`"
                    :value="item.id"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="用户名:">
              <el-input v-model="formData.username" clearable placeholder="请输入"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="密码:">
              <el-input v-model="formData.password" show-password placeholder="请输入"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="昵称:">
              <el-input v-model="formData.nickname" placeholder="请输入"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别">
              <el-select v-model="formData.gender" placeholder="性别">
                <el-option
                    v-for="item in genderOptions"
                    :key="item.value"
                    :label="`${item.label}(${item.value})`"
                    :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="生日:">
              <el-date-picker v-model="formData.birthday" type="date" style="width:100%" placeholder="选择日期"
                              clearable/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="24">
            <el-form-item label="自我介绍:">
              <el-input v-model="formData.introduction" clearable placeholder="请输入"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="8">
            <el-form-item label="国籍:">
              <el-select v-model="formData.countryCode" clearable filterable placeholder="国籍">
                <el-option
                    v-for="item in countryCodeOptions"
                    :key="item.value"
                    :label="`${item.label}(${item.value})`"
                    :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="登录IP:">
              <el-input v-model="formData.lastLoginIp" clearable placeholder="请输入"/>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="邮箱地址:">
              <el-input v-model="formData.email" clearable placeholder="请输入"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="10">
          <el-col :span="12">
            <el-form-item label="最后登录:">
              <el-date-picker v-model="formData.lastLoginAt" type="datetime"/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Vip过期:">
              <el-date-picker v-model="formData.vipExpireAt" type="datetime"/>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="角色:">
              <el-select v-model.number="formData.role" placeholder="角色">
                <el-option
                    v-for="item in roleOptions"
                    :key="item.value"
                    :label="`${item.label}(${item.value})`"
                    :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="钻石余量:">
              <el-input-number v-model="formData.diamonds" :controls="true" placeholder="请输入"/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="16">
            <el-form-item label="卡片">
              <el-input-number v-model="userCardNum"/>
              <el-button style="margin-left: 6px" size="small" type="success" @click="saveCardNum">保存卡片</el-button>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="UtmSource">
              <el-input v-model="formData.utmSource" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Campaign">
              <el-input disabled v-model="formData.campaign" clearable/>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="online">
          <el-radio-group v-model="formData.online">
            <el-radio :label="1">离线</el-radio>
            <el-radio :label="2">忙线</el-radio>
            <el-radio :label="3">在线</el-radio>
          </el-radio-group>
        </el-form-item>

        <el-row>
          <el-col :span="12">
            <el-form-item label="AdSet">
              <el-input disabled v-model="formData.ad_set" clearable/>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="Ads">
              <el-input disabled v-model="formData.ads" clearable/>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div style="display: flex; justify-content: space-between">
          <div>
            <el-button size="small" type="danger" @click="cancleAuth(formData)">取消认证</el-button>
            <el-popconfirm
                v-if="formData.recovery === 0"
                confirm-button-text="确定"
                cancel-button-text="取消"
                title="确定要封禁吗?"
                @confirm="banAnchor"
            >
              <template #reference>
                <el-button size="small" type="danger">封禁</el-button>
              </template>
            </el-popconfirm>
            <el-popconfirm
                v-if="formData.recovery === 1"
                confirm-button-text="确定"
                cancel-button-text="取消"
                title="确定要解封吗?"
                @confirm="unsealAnchor"
            >
              <template #reference>
                <el-button size="small" type="success">解封</el-button>
              </template>
            </el-popconfirm>
          </div>
          <div>
            <el-button size="small" @click="closeDialog">取 消</el-button>
            <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
          </div>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="afDialogVisible" top="5px" :before-close="closeAfDialog" :title="afDialogTitle">
      <el-form ref="afForm" :model="afFormData" :rules="afFormDataRules" label-position="right" label-width="120px">
        <el-form-item label="用户ID:" prop="user_id">
          <el-input disabled v-model.number="afFormData.user_id" clearable/>
        </el-form-item>
        <el-form-item label="app_id:" prop="app_id">
          <el-input v-model.number="afFormData.app_id" clearable/>
        </el-form-item>
        <el-form-item label="广告id:" prop="ad_id">
          <el-input v-model="afFormData.ad_id" clearable/>
        </el-form-item>
        <el-form-item label="用户归因:" prop="utm_source">
          <el-input v-model="afFormData.utm_source" clearable/>
        </el-form-item>
        <el-form-item label="campaign:" prop="campaign">
          <el-input v-model="afFormData.campaign" clearable/>
        </el-form-item>
        <el-form-item label="国家码:" prop="country_code">
          <el-input v-model="afFormData.country_code" clearable/>
        </el-form-item>
        <el-form-item label="af_id:" prop="af_id">
          <el-input v-model="afFormData.af_id" clearable/>
        </el-form-item>
        <el-form-item label="ad_set:" prop="ad_set">
          <el-input v-model="afFormData.ad_set" clearable/>
        </el-form-item>
        <el-form-item label="ads:" prop="ads">
          <el-input v-model="afFormData.ads" clearable/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeAfDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterAfDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-drawer v-model="userDetailVisible" size="70%" :with-header="false">
      <el-row :gutter="5">
        <el-col :span="4">
          <el-image
              style="width: 100%;"
              :src="userDetailInfo.avatar"
              :preview-src-list="[userDetailInfo.avatar]"
          >
            <template #error>
              <el-image
                  style="width: 100%;"
                  :src="default_avatar"
                  :preview-src-list="[default_avatar]"
              />
            </template>
          </el-image>
        </el-col>
        <el-col :span="20">
          <el-descriptions direction="horizontal" :column="2" size="small" border>
            <el-descriptions-item align="center" label="ID">{{ userDetailInfo.id }}</el-descriptions-item>
            <el-descriptions-item align="center" label="昵称">{{ userDetailInfo.nickname }}</el-descriptions-item>
            <el-descriptions-item align="center" label="性别">{{ formatSex(userDetailInfo.gender) }}
            </el-descriptions-item>
            <el-descriptions-item align="center" label="出生日期">{{ formatDate(userDetailInfo.birthday) }}
            </el-descriptions-item>
            <el-descriptions-item align="center" label="定位">
              <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="JSON.stringify(userDetailInfo.location)"
                  placement="top"
              >
                <div>
                  {{ userDetailInfo.location === null ? "" : userDetailInfo.location.address }}
                </div>
              </el-tooltip>
            </el-descriptions-item>
            <el-descriptions-item align="center" label="身份">{{
                formatRole(userDetailInfo.role)
              }}
            </el-descriptions-item>
            <el-descriptions-item align="center" label="注册时间">{{ formatDate(userDetailInfo.created_at) }}
            </el-descriptions-item>
            <el-descriptions-item align="center" label="活跃时间">{{ formatDate(userDetailInfo.updated_at) }}
            </el-descriptions-item>
            <el-descriptions-item align="center" label="钻石余量">{{ userDetailInfo.diamonds }}</el-descriptions-item>
            <el-descriptions-item align="center" label="VIP到期">{{ formatDate(userDetailInfo.vipExpireAt) }}
            </el-descriptions-item>
            <!--<el-descriptions-item align="center" :span="2" label="自我介绍">{{ userDetailInfo.introduction }}
            </el-descriptions-item>-->
            <el-descriptions-item align="center" label="最后付费时间">{{ formatDate(userOrderStatistics.payment_at) }}
            </el-descriptions-item>
            <el-descriptions-item align="center" label="累计付费">共 {{ userOrderStatistics.cnt }} 次, 累计
              {{ userOrderStatistics.price }} 美金
            </el-descriptions-item>
            <el-descriptions-item align="center" label="绑定关系">{{ userDetailInfo.manager_id?userDetailInfo.manager_id:'' }}
            </el-descriptions-item>
          </el-descriptions>
        </el-col>
      </el-row>
      <el-row :gutter="5">
        <el-col :span="12">
          <h1>充值记录</h1>
          <div class="gva-table-box">
            <el-table
                size="small"
                style="width: 100%"
                :data="chargeTableData"
                max-height="390"
                row-key="id"
            >
              <el-table-column align="center" label="日期" prop="created_at" width="160">
                <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
              </el-table-column>
              <el-table-column align="center" label="充值渠道" prop="payment" min-width="100">
                <template #default="scope">{{ formatPayment(scope.row.payment) }}</template>
              </el-table-column>
              <el-table-column align="center" label="充值产品" prop="product_name" min-width="100"/>
              <el-table-column align="center" label="订单金额" prop="price" min-width="100"/>
            </el-table>
            <div class="gva-pagination">
              <el-pagination
                  v-show="chargeTableTotal > 0"
                  layout="total, pager"
                  :current-page="chargeTablePage"
                  :page-size="chargeTablePageSize"
                  :page-sizes="pageSizeList"
                  :total="chargeTableTotal"
                  @current-change="chargeTablePageChange"
                  @size-change="chargeTableSizeChange"
              />
            </div>
          </div>
        </el-col>
        <el-col :span="12">
          <h1>消耗记录</h1>
          <div class="gva-table-box">
            <el-table
                size="small"
                style="width: 100%"
                :data="consumeTableData"
                max-height="390"
                row-key="id"
            >
              <el-table-column align="center" label="日期" prop="created_at" width="160">
                <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
              </el-table-column>
              <el-table-column align="center" label="主播ID" prop="dst_id" width="100"/>
              <el-table-column align="center" label="通话时长" prop="duration" min-width="80"/>
              <el-table-column align="center" label="钻石消耗" prop="dst_income" min-width="80"/>
              <el-table-column align="center" label="通话类型" min-width="80">
                <template #default="scope">
                  <el-tag v-if="scope.row.src_free" type="info">免费通话</el-tag>
                  <el-tag v-else type="success">付费通话</el-tag>
                </template>
              </el-table-column>
            </el-table>
            <div class="gva-pagination">
              <el-pagination
                  v-show="consumeTableTotal > 0"
                  layout="total, pager"
                  :current-page="consumeTablePage"
                  :page-size="consumeTablePageSize"
                  :page-sizes="pageSizeList"
                  :total="consumeTableTotal"
                  @current-change="consumeTablePageChange"
                  @size-change="consumeTableSizeChange"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </el-drawer>
  </div>
</template>

<script>
import {
  banUser,
  cancelAuditUsers,
  createUsers,
  deleteUsers,
  deleteUsersByIds,
  findUsers,
  getUsersList,
  unsealUser,
  updateUsers
} from '@/api/users'
import {getOrdersList, getUserOrderStatistics} from '@/api/orders'
import {findUserCardInfos, saveUserCard} from '@/api/userCardInfos'
import {getList} from '@/api/connectedReports'
import infoList from '@/mixins/infoList'
import CustomPic from '@/components/customPic/index.vue'
import default_avatar from '@/assets/default_avatar.png'
import {createAfCallbackInfos, findAfCallbackInfos, updateAfCallbackInfos} from "@/api/afCallbackInfos";
import { filterList } from '@/api/userLevel' //  此处请自行替换地址


const path = import.meta.env.VITE_BASE_API
export default {
  name: 'Users',
  components: {CustomPic},
  mixins: [infoList],
  data() {
    return {
      level_list: [],

      userCardNum: 0,
      afDialogVisible: false,
      afFormData: {},
      afFormDataRules: {
        user_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        app_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        // ad_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        // utm_source: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        // campaign: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        // country_code: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        // af_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        // ad_set: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        // ads: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
      afDialogType: '',
      afDialogTitle: '',

      listApi: getUsersList,

      path: path,
      default_avatar,
      avatarFileList: [],
      chargeTableData: [],
      chargeTablePage: 1,
      chargeTablePageSize: 10,
      chargeTableTotal: 0,

      consumeTableData: [],
      consumeTablePage: 1,
      consumeTablePageSize: 10,
      consumeTableTotal: 0,

      appListOptions: [],
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      userDetailVisible: false,
      userDetailInfo: {
        avatar: '',
      },
      userOrderStatistics: {
        cnt: 0,
        price: 0,
        payment_at: '',
      },
      multipleSelection: [],
      searchInfo: {recovery: 0},
      formData: {
        username: '',
        password: '',
        nickname: '',
        avatar: '',
        avatarVerify: '',
        gender: 0,
        birthday: new Date(),
        introduction: '',
        email: '',
        state: 0,
        countryCode: '',
        countryName: '',
        province: '',
        city: '',
        lastLoginIp: '',
        lastLoginAt: new Date(),
        vipExpireAt: new Date(),
        expand: '',
        location: '',
        appId: 0,
        loginType: 0,
        recovery: 0,
        role: 0,
        category: 0,
        online: 0,
        officalDesc: '',
        diamonds: 0,
        version: '',
        platform: 0,
        utmSource: '',
        avoid: 0,
        unionId: 0,
        joinUnionAt: new Date(),
        fcmToken: '',
        fake: 0,
        height: '',
        weight: '',
        levels: 0,
        conversion: 0,
        createdTime: 0,
        updatedTime: 0,
        workingCondition: 0,
        isWorking: 0,
        coverVideo: '',
        callState: 0,
        langTags: '',
        authState: 0,
        newAnchorTaskState: 0,
        clientScale: '',
        anchorFake: 0,
      }
    }
  },
  async created() {
    await this.queryOption()
    await this.getTableData()
  },
  methods: {
    queryOption() {
      filterList().then((res) => {
        if (res.code === 0) {
          console.log(res.data)
          let resData = res.data || []
          this.level_list = resData.user_level
        }
      })
    },
    closeAfDialog() {
      this.afDialogVisible = false
      this.afFormData = {}
    },
    enterAfDialog() {
      this.$refs['afForm'].validate((valid) => {
        if (valid) {
          switch (this.afDialogType) {
            case 'create':
              createAfCallbackInfos(this.afFormData).then(res => {
                if (res.code === 0) {
                  this.$message({
                    type: 'success',
                    message: res.msg
                  })
                  this.afDialogVisible = false
                  this.$refs.afForm.resetFields()
                }
              })
              break
            case 'update':
              updateAfCallbackInfos(this.afFormData).then(res => {
                if (res.code === 0) {
                  this.$message({
                    type: 'success',
                    message: res.msg
                  })
                  this.afDialogVisible = false
                  this.$refs.afForm.resetFields()
                }
              })
              break
          }
        }
      })
    },
    viewAfDetailInfo(row) {
      findAfCallbackInfos({user_id: row.id}).then(res => {
        if (res.code === 0) {
          this.afFormData = res.data
          this.afDialogType = 'update'
          this.afDialogTitle = '修改'
          this.afDialogVisible = true
        } else if (res.msg === '查询失败record not found') {
          this.$messageBox.confirm("当前用户没有AF回调信息,是否添加?", "提示", {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
          }).then(_ => {
            this.afFormData.user_id = row.id
            this.afDialogType = 'create'
            this.afDialogTitle = '创建'
            this.afDialogVisible = true
          })
        }
      })

    },

    handleAvatarExceed() {
      this.$message.error('如果需要更换用户头像,请先删除,再上传')
    },
    handleAvatarSuccess(res) {
      this.avatarFileList = []
      const {data} = res
      if (data.url) {
        this.formData.avatar = data.url
        this.avatarFileList.push(data)
      }
      this.progressLoading.close()
    },
    handleAvatarRemove() {
      this.formData.avatar = ''
      this.avatarFileList = []
    },
    chargeTablePageChange(v) {
      this.chargeTablePage = v
      this.getUserChargeTableData()
    },
    chargeTableSizeChange(v) {
      this.chargeTablePageSize = v
      this.getUserChargeTableData()
    },
    getUserChargeTableData() {
      getOrdersList({
        page: this.chargeTablePage,
        pageSize: this.chargeTablePageSize,
        user_id: this.userDetailInfo.id,
        state: 4, // 已完成的订单
      }).then(res => {
        this.chargeTableData = res.data.list
        this.chargeTableTotal = res.data.total
      })
    },
    getUserOrderStatistics() {
      getUserOrderStatistics({
        user_id: this.userDetailInfo.id,
      }).then(res => {
        if (res.code === 0) {
          this.userOrderStatistics = res.data
        }
      })
    },
    consumeTablePageChange(v) {
      this.consumeTablePage = v
      this.getUserConsumeTableData()
    },
    consumeTableSizeChange(v) {
      this.consumeTablePageSize = v
      this.getUserConsumeTableData()
    },
    getUserConsumeTableData() {
      getList({
        page: this.consumeTablePage,
        pageSize: this.consumeTablePageSize,
        src_id: this.userDetailInfo.id,
        state: 4, // 已完成的订单
      }).then(res => {
        this.consumeTableData = res.data.list
        this.consumeTableTotal = res.data.total
      })
    },

    cancleAuth(formData) {
      this.handleProgressLoading()
      cancelAuditUsers({'userId': formData.id}).then(res => {
        // 关闭loading
        this.progressLoading.close()
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.closeDialog()
        } else {
          this.$message({
            type: 'danger',
            message: res.msg
          })
        }
        this.getTableData()
      })
    },
    banAnchor() {
      banUser({
        userId: this.formData.id,
        role: this.formData.role,
        app_id: this.formData.appId,
        isDeleteApply: false
      }).then(res => {
        if (res.code === 0) {
          this.$message.success('成功')
          this.dialogFormVisible = false
        }
      })
    },
    unsealAnchor() {
      unsealUser({userId: this.formData.id}).then(res => {
        if (res.code === 0) {
          this.$message.success('成功')
          this.dialogFormVisible = false
        }
      })
    },
    onReset() {
      this.searchInfo = {recoveryState: 0}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    viewDetailInfo(row) {
      this.userDetailInfo = row
      this.getUserChargeTableData()
      this.getUserConsumeTableData()
      this.getUserOrderStatistics()
      this.userDetailVisible = true
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteUsers(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
      this.multipleSelection.map(item => {
        ids.push(item.id)
      })
      const res = await deleteUsersByIds({ids})
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async saveCardNum() {
      const res = await saveUserCard({user_id: this.formData.id, card_num: this.userCardNum})
      if (res.code === 0) {
        this.$message.success("成功")
      }
    },
    async updateUsers(row) {
      const cardRes = await findUserCardInfos({user_id: row.id})
      if (cardRes.code === 0) {
        this.userCardNum = cardRes.data.card_num
      } else {
        this.userCardNum = 0
      }
      const res = await findUsers({id: row.id})
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.reusers
        let uAvatar = ''
        if (this.formData.avatar !== '') {
          uAvatar = this.formData.avatar
        } else {
          uAvatar = this.formData.avatarVerify
        }
        this.avatarFileList = [{name: '', url: uAvatar}]
        this.dialogFormVisible = true
      } else {
        this.$message({
          type: 'error',
          message: res.msg
        })
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.avatarFileList = []
      this.formData = {
        username: '',
        password: '',
        nickname: '',
        avatar: '',
        avatarVerify: '',
        gender: 0,
        birthday: new Date(),
        introduction: '',
        email: '',
        state: 0,
        countryCode: '',
        countryName: '',
        province: '',
        city: '',
        lastLoginIp: '',
        lastLoginAt: new Date(),
        vipExpireAt: new Date(),
        expand: '',
        location: '',
        appId: 0,
        loginType: 0,
        recovery: 0,
        role: 0,
        category: 0,
        online: 0,
        officalDesc: '',
        diamonds: 0,
        version: '',
        platform: 0,
        utmSource: '',
        avoid: 0,
        unionId: 0,
        joinUnionAt: new Date(),
        fcmToken: '',
        fake: 0,
        height: '',
        weight: '',
        levels: 0,
        conversion: 0,
        createdTime: 0,
        updatedTime: 0,
        workingCondition: 0,
        isWorking: 0,
        coverVideo: '',
        callState: 0,
        langTags: '',
        authState: 0,
        newAnchorTaskState: 0,
        clientScale: '',
        anchorFake: 0,
      }
      this.userOrderStatistics = {
        cnt: 0,
        price: 0,
        payment_at: '',
      }
      this.getTableData()
    },
    async deleteUsers(row) {
      const res = await deleteUsers({id: row.id})
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createUsers(this.formData)
          break
        case 'update':
          res = await updateUsers(this.formData)

          break
        default:
          res = await createUsers(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
      this.photoAlbumsFileList = []
      this.avatarFileList = []
      this.formData = {
        albumVideo: [],
        callVideo: [],
        constellationVideo: [],
        photoAlbums: [],
        nickname: '',
        avatar: '',
        birthday: '',
        countryCode: '',
        anchorFake: 1,
        role: 2,
        avoid: 0,
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.el-row {
  padding: 0 !important;
}
</style>
