<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="产品名称">
          <el-select v-model="searchInfo.app_id" filterable clearable>
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="`${item.app_name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="工会">
          <el-select v-model="searchInfo.dst_union_id" clearable>
            <el-option
              v-for="item in uniosList"
              :key="item.id"
              :label="`${item.name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="主播ID">
          <el-input v-model="searchInfo.dst_id"></el-input>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="用户ID">
          <el-input v-model="searchInfo.src_id"></el-input>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="类型">
          <el-select v-model="searchInfo.trade_type" clearable>
            <el-option
              v-for="item in tradeTypeOptions"
              :key="item"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="时间范围">
          <el-date-picker
            v-model="searchInfo.date_range"
            type="datetimerange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DDTHH:mm:ssZ"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="text" :icon="queryTxtIcon" @click="queryTxtChange">{{ queryTxt }}</el-button>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="tableData"
        style="width: 100%"
        :data="tableData"
        max-height="590"
        row-key="id">
        <el-table-column align="center" label="日期" prop="created_at" min-width="160" fixed></el-table-column>
        <el-table-column align="center" label="用户ID" prop="src_id" min-width="120"/>
        <el-table-column align="center" label="用户昵称" prop="nickname" min-width="120" show-overflow-tooltip/>
        <el-table-column align="center" label="主播ID" prop="dst_id" min-width="120"/>
        <el-table-column align="center" label="钻石消耗" prop="cost" min-width="100"/>
        <el-table-column align="center" label="钻石余量" prop="remain" min-width="100"/>
        <el-table-column align="center" label="消耗类型" prop="trade_type_name" min-width="180"/>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import {getUserTradeRecordsList, getTradeTypeDict} from '@/api/tradeRecords' //  此处请自行替换地址
  import infoList from '@/mixins/infoList'
  import tradeRecordsMixins from '@/mixins/tradeRecords'

  export default {
    mixins: [infoList, tradeRecordsMixins],
    data() {
      return {
        searchInfo: {},
        listApi: getUserTradeRecordsList,
      }
    },
    async created() {
      await this.getAllTradeTypeDict()
      await this.getTableData(this.nFunc, this.tableDataFormat)
    },
    methods: {
      onReset() {
        this.searchInfo = {}
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      onSubmit() {
        this.page = 1
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      sizeChange(v) {
        this.pageSize = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      pageChange(v) {
        this.page = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      getAllTradeTypeDict() {
        getTradeTypeDict().then(res => {
          let t = []
          Object.keys(res.data).forEach((key, index) => {
            t.push({value: parseInt(key), label: res.data[key]})
          })
          this.tradeTypeOptions = t
        })
      },
      tableDataFormat() {
        this.tableData.map((item, i, data) => {
          data[i].created_at = this.formatDate(item.created_at)
          data[i].trade_type_name = this.formatTradeTypeName(item.trade_type)[0]
          data[i].cost = this.formatCost(item)
        })
      },
      handleDownload() {
        let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
        let res = this.getTableHeaderProp(tableColumns)
        let tHeader = res[0]
        let filterV = res[1]
        this.handleProgressLoading()
        import('@/utils/excel').then((excel) => {
          const data = this.formatJson(filterV, this.tableData)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.filename,
            autoWidth: this.autoWidth,
            bookType: this.bookType,
          })
          this.progressLoading.close()
        })
      },

    },
  }
</script>

<style lang="scss" scoped>

</style>
