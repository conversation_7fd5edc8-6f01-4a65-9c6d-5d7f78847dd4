<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="开播时间">
          <el-date-picker
            v-model="searchInfo.date_range"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            type="daterange"
            :clearable="false"
            :shortcuts="dateRangeShortcuts"
            range-separator="至"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="用户分级">
          <el-select v-model="searchInfo.user_level" clearable filterable placeholder="用户分级">
            <el-option v-for="item in level_list" :key="item.id" :label="`${item.desc}(${item.id})`" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="分包查询">
          <el-select v-model="searchInfo.app_id" clearable filterable placeholder="分包查询">
            <el-option v-for="item in appList" :key="item.id" :label="`${item.app_name}(${item.id})`" :value="item.id" />
          </el-select>
        </el-form-item>
        <el-form-item label="国家">
          <el-select v-model="searchInfo.country_code" multiple clearable filterable placeholder="国家">
            <el-option v-for="item in countryCodeOptions" :key="item.value" :label="`${item.label}(${item.value})`" :value="item.value" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="区域">
            <el-select v-model="searchInfo.area_code" clearable filterable placeholder="区域">
              <el-option v-for="item in area_list" :key="item.id" :label="`${item.name}(${item.id})`" :value="item.id" />
            </el-select>
          </el-form-item> -->
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-data-line" @click="onSubmit">生成曲线</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <!-- <div class="gva-btn-list">
        <span>总人数(人):100022</span>
      </div> -->

      <div v-if="isQueryData" class="echart-box" ref="echart"></div>
      <div v-else><el-empty description="No Data" /></div>
    </div>
  </div>
</template>

<script>
import { getUserLevelList, filterList } from '@/api/userLevel' //  此处请自行替换地址
import moment from 'moment/moment'
import infoList from '@/mixins/infoList'
import * as echarts from 'echarts'
import { markRaw } from 'vue'

export default {
  name: 'UserLevel',
  mixins: [infoList],

  data() {
    return {
      chart: null,
      searchInfo: {},
      xData: [],
      yData: [],
      level_list: [],
      isQueryData: false,
    }
  },
  async created() {
    // 默认时间为最近一周
    this.searchInfo.date_range = [
      moment()
        .subtract(1, 'week')
        .format('YYYY-MM-DD'),
      moment().format('YYYY-MM-DD'),
    ]
    //根据上面的开始时间和结束时间生成每天的日期放进xData里面
    // this.generateDateRange(this.searchInfo.date_range[0], this.searchInfo.date_range[1])
    await this.queryOption()
    //   this.initChart()
    // await this.onSubmit()
  },
  methods: {
    onReset() {
      this.searchInfo = {}
      this.searchInfo.date_range = [
        moment()
          .subtract(1, 'week')
          .format('YYYY-MM-DD'),
        moment().format('YYYY-MM-DD'),
      ]
    },

    queryOption() {
      filterList().then((res) => {
        if (res.code === 0) {
          console.log(res.data)
          let resData = res.data || []
          this.level_list = resData.user_level
          // let resDict = {}
          // resData.forEach((item) => {
          //   resDict[item.id] = item
          // })
          // this.methodDict = resDict
        }
      })
    },
    generateDateRange(startDate, endDate) {
      const xData = []
      let currentDate = new Date(startDate)

      // 确保结束日期也被包含在内
      endDate = new Date(endDate)
      endDate.setHours(23, 59, 59, 999)

      while (currentDate <= endDate) {
        // 将日期格式化为YYYY-MM-DD
        const formattedDate = currentDate.toISOString().split('T')[0]
        xData.push(formattedDate)

        // 日期增加一天
        currentDate.setDate(currentDate.getDate() + 1)
      }

      this.xData = xData
    },

    initChart(xData, series, totalCount) {
      let option = {
        title: {
          text: '总人数(人):' + totalCount,
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross',
            label: {
              backgroundColor: '#6a7985',
            },
          },
        },
        legend: {
          type: 'scroll',
          // left: '6%',
          icon: 'circle',
        },
        // toolbox: {
        //   feature: {
        //     saveAsImage: {},
        //   },
        // },
        grid: {
          left: '3%',
          right: '4%',
          top: '15%',
          bottom: '4%',
          containLabel: true,
        },
        xAxis: [
          {
            data: xData,
            boundaryGap: false,
          },
        ],
        yAxis: [
          {
            type: 'value',
            name: '人数(人)',
            axisLine: {
              show: true,
            },
          },
        ],
        // dataZoom: [
        //   {
        //     type: 'inside',
        //     start: 0,
        //     end: 100,
        //   },
        //   {
        //     start: 0,
        //     end: 10,
        //   },
        // ],
        series: series,
      }
      this.$nextTick(() => {
        this.chart = markRaw(echarts.init(this.$refs.echart))
        window.addEventListener('resize', () => {
          this.chart.resize()
        })
        this.chart.setOption(option, true)
      })
    },

    async onSubmit() {
      this.searchInfo.begin_time = this.searchInfo.date_range[0]
      this.searchInfo.end_time = this.searchInfo.date_range[1]

      const res = await getUserLevelList(this.searchInfo)
      if (res.code === 0) {
        this.isQueryData = true
        let uniqueXData = []
        let series = {}
        const rspData = res.data.data || []
        const totalCount = rspData.reduce((acc, cur) => acc + cur.count, 0)
        const xData = rspData.map((item) => item.date)
        // 对xData进行去重
        uniqueXData = [...new Set(xData)]

        // 根据code进行分组，
        const groupByCode = rspData.reduce((acc, cur) => {
          if (!acc[cur.code]) {
            acc[cur.code] = []
          }
          acc[cur.code].push(cur)
          return acc
        }, {})
        series = Object.keys(groupByCode).map((code) => {
          const data = groupByCode[code].map((item) => item.count)
          return {
            name: code,
            type: 'line',
            data,
          }
        })
        console.log(uniqueXData, series, totalCount)
        this.initChart(uniqueXData, series, totalCount)
      }
    },
  },
}
</script>

<style lang="scss" scoped>
.echart-box {
  min-height: 600px;
}
:deep(.el-date-editor .el-range-separator) {
  height: auto;
}
</style>
