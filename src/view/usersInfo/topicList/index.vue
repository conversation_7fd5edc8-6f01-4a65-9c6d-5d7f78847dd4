<template>
  <div>
    <div class="gva-search-box">
      <el-button size="mini" type="success" icon="el-icon-plus" @click="openDialog">新增</el-button>
    </div>
    <div class="gva-table-box">
      <el-table ref="tableData" style="width: 100%" :data="tableData" row-key="id" :max-height="590" border>
        <el-table-column align="center" label="话题组标题" prop="title" min-width="100"/>
        <el-table-column align="center" label="话题条数" prop="total" width="160"/>
        <el-table-column align="center" label="排序" prop="sort" width="120"/>
        <el-table-column align="center" label="创建时间" prop="created_at_str" width="180"/>
        <el-table-column align="center" label="操作" width="210">
          <template #default="scope">
            <el-button type="text" icon="el-icon-view" size="small" @click="viewObj(scope.row)">查看</el-button>
            <el-button type="text" icon="el-icon-edit" size="small" @click="updateObj(scope.row)">修改</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <el-dialog width="70%" v-model="dialogFormVisible" top="5px" :before-close="closeDialog"
               :close-on-click-modal="false" :close-on-press-escape="false" :title="dialogTitle"
               @opened="rowDrop(this)">
      <div class="gva-search-box">
        <el-form v-if="type !== 'create'" :inline="true" :model="searchInfoT">
          <el-form-item>
            <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmitT">查询</el-button>
            <el-button size="mini" icon="el-icon-refresh" @click="onResetT">重置</el-button>
            <el-button size="mini" icon="el-icon-download" :disabled="totalT < 1" @click="handleDownloadT">导出
            </el-button>
          </el-form-item>
        </el-form>
        <el-form v-if="formData.id !== 0" :inline="true" :model="searchInfoT">
          <el-form-item label="话题组标题">
            <el-input :disabled="type === 'view'" v-model="formData.title" clearable/>
          </el-form-item>
          <el-form-item label="话题组排序">
            <el-input-number :disabled="type === 'view'" v-model="formData.sort"/>
          </el-form-item>
        </el-form>
      </div>
      <div class="gva-table-box" id="userTable">
        <el-table ref="dragTable" style="width: 100%" :data="tableDataT" row-key="id" :max-height="550" border>
          <el-table-column align="center" label="排序" prop="sort" width="100"></el-table-column>
          <el-table-column align="center" label="话题内容" prop="content" show-overflow-tooltip min-width="120">
            <template #default="scope">
              <el-input :disabled="type === 'view'"
                        v-model="scope.row.content"
                        :autosize="{ minRows: 1, maxRows: 4 }"
                        :maxlength="64"
                        show-word-limit
                        type="textarea"
              />
            </template>
          </el-table-column>
          <el-table-column align="center" label="是否推荐" prop="is_hot" width="160">
            <template #default="scope">
              <el-radio-group :disabled="type === 'view'" v-model="scope.row.is_hot">
                <el-radio :label="true">是</el-radio>
                <el-radio :label="false">否</el-radio>
              </el-radio-group>
            </template>
          </el-table-column>
          <el-table-column align="center" label="操作" width="210">
            <template #default="scope">
              <el-button :disabled="type === 'view'" type="text" icon="el-icon-delete" size="mini"
                         @click="deleteRowT(scope.row)">删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <div v-if="formData.id !== 0 && type !== 'create'" class="gva-pagination">
          <el-pagination
              layout="total, sizes, prev, pager, next, jumper"
              :current-page="pageT"
              :page-size="pageSizeT"
              :page-sizes="pageSizeList"
              :total="totalT"
              @current-change="pageChangeT"
              @size-change="sizeChangeT"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer" v-show="type !== 'view'">
          <el-button size="small" type="success" @click="addItem">添加一条</el-button>
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialogT">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createQuickReplyGroupNew,
  updateQuickReplyGroup,
  findQuickReplyGroup,
  getQuickReplyGroupList,
  deleteQuickReplyGroup,
} from '@/api/quick_reply_group'
import {
  deleteQuickReply,
  getQuickReplyList
} from '@/api/quickReply'
import infoList from '@/mixins/infoList'
import Sortable from 'sortablejs'
import moment from 'moment'

export default {
  name: 'topicList',
  mixins: [infoList],
  data() {
    return {
      listApi: getQuickReplyGroupList,
      dialogFormVisible: false,
      pageT: 1,
      pageSizeT: 10,
      totalT: 10,
      type: '',
      dialogTitle: '',
      loading: false,
      tableDataT: [],
      searchInfoT: {},
      formData: {},
      formDataRules: {},
    }
  },
  created() {
    this.getTableData()
  },
  methods: {
    // 行拖拽排序
    rowDrop() {
      // 此时找到的元素是要拖拽元素的父容器
      const tbody = this.$refs.dragTable.$el.querySelectorAll('.el-table__body-wrapper > table > tbody')[0]
      Sortable.create(tbody, {
        // 指定父元素下可被拖拽的子元素
        draggable: ".el-table__row",
        onEnd: evt => {
          let oldIndex = evt.oldIndex
          let newIndex = evt.newIndex
          const oldObj = this.tableDataT[oldIndex]
          const newObj = this.tableDataT[newIndex]
          this.tableDataT[newIndex] = {...oldObj, sort: newObj.sort}
          this.tableDataT[oldIndex] =  {...newObj, sort: oldObj.sort}
        }
      });
      if (!this.formData.id) {
        return
      }
      this.getQuickTableData()
    },
    onReset() {
      this.searchInfo = {}
    },
    onResetT() {
      this.searchInfoT = {}
    },
    onSubmit() {
      this.page = 1
      this.getTableData()
    },
    onSubmitT() {
      this.pageT = 1
      this.getQuickTableData()
    },
    handleDownload() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    handleDownloadT() {
      let tableColumns = this.$refs.dragTable.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableDataT)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    addItem() {
      let maxSort = 0
      if (this.tableDataT.length > 0) {
        maxSort = this.tableDataT[0].sort
        this.tableDataT.unshift({
          id: moment().valueOf(),
          group_id: this.formData.id,
          content: '',
          sort: maxSort + 1,
          is_hot: false
        })
      } else {
        this.tableDataT.push({
          id: moment().valueOf(),
          group_id: this.formData.id,
          content: '',
          sort: maxSort + 1,
          is_hot: false
        })
      }
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteObj(row)
      })
    },
    deleteRowT(row) {
      if (!row.id || row.id > 100000000) {
        var index = this.tableDataT.indexOf(row)
        if (index !== -1) {
          this.tableDataT.splice(index, 1)
        }
      } else {
        this.$confirm('确定要删除吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.deleteObjT(row)
        })
      }
    },
    async viewObj(row) {
      const res = await findQuickReplyGroup({id: row.id})
      this.type = 'view'
      this.dialogTitle = '查看'
      if (res.code === 0) {
        this.formData = res.data
        this.dialogFormVisible = true
      }
    },
    async updateObj(row) {
      const res = await findQuickReplyGroup({id: row.id})
      this.type = 'update'
      this.dialogTitle = '更新'
      if (res.code === 0) {
        this.formData = res.data
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.pageT = 1
      this.pageSizeT = 10
      this.totalT = 0
      this.tableDataT = []
      this.formData = {}
      this.searchInfoT = {}
    },
    async deleteObj(row) {
      const res = await deleteQuickReplyGroup({id: row.id})
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        this.getTableData()
      }
    },
    async deleteObjT(row) {
      const res = await deleteQuickReply({id: row.id})
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        this.getQuickTableData()
      }
    },
    async enterDialogT() {
      let res
      if (this.type === "create") {
        this.tableDataT.map((item, i, data) => {
          data[i].id = null
        })
      }
      this.tableDataT.map((item, i, data) => {
        let dId = data[i].id
        if (dId && dId > 100000000) {
          data[i].id = null
        }
      })
      if (this.tableDataT.length === 0) {
        this.$message({
          type: 'error',
          message: '内容不能为空'
        })
        return
      }
      let flag = false

      for (let i = 0; i < this.tableDataT.length; i++) {
        let dContent = this.tableDataT[i].content
        if (dContent === '') {
          flag = true
          this.$message({
            type: 'error',
            message: '内容不能为空'
          })
          break
        }
      }
      if (flag) {
        return
      }
      if (!this.formData.title) {
        this.$message({
          type: 'error',
          message: '标题不能为空'
        })
        return
      }
      let reqData = {
        ...this.formData,
        data: this.tableDataT,
      }
      res = await createQuickReplyGroupNew(reqData)
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogTitle = '创建'
      if (this.tableData.length === 0) {
        this.formData.sort = 1
      } else {
        this.formData.sort = this.tableData[0].sort + 1
      }
      this.dialogFormVisible = true
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData()
    },
    pageChange(v) {
      this.page = v
      this.getTableData()
    },
    sizeChangeT(v) {
      this.pageSizeT = v
      this.getQuickTableData()
    },
    pageChangeT(v) {
      this.pageT = v
      this.getQuickTableData()
    },
    getTableData() {
      getQuickReplyGroupList({...this.searchInfo}).then(res => {
        if (res.code === 0) {
          this.tableData = res.data || []
          this.tableDataFormat()
        }
      })
    },
    getQuickTableData() {
      if (!this.formData.id) {
        this.searchInfoT.group_id = -1
      } else {
        this.searchInfoT.group_id = this.formData.id
      }
      getQuickReplyList({page: this.pageT, pageSize: this.pageSizeT, ...this.searchInfoT}).then(res => {
        if (res.code === 0) {
          this.tableDataT = res.data.list || []
          let resTotal = res.data.total || 0
          if (resTotal === 0) {
            resTotal = this.tableDataT.length
          }
          this.totalT = resTotal
        }
      })
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
      })
    },
  },
}
</script>

<style lang="scss">

</style>
