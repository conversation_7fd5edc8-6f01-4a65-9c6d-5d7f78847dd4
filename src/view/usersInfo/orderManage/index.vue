<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item v-if="unionIdBool" label="APP">
          <el-select filterable v-model="searchInfo.app_id" clearable>
            <el-option
                v-for="item in appList"
                :key="item.id"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker
              v-model="searchInfo.created_at_range"
              type="datetimerange"
              :shortcuts="dateRangeShortcuts"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DDTHH:mm:ssZ"
              clearable
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="订单状态">
          <el-select v-model="searchInfo.state" clearable placeholder="订单状态">
            <el-option
                v-for="item in orderStateOptions"
                :key="item"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="用户ID">
          <el-input v-model="searchInfo.user_id" placeholder="用户ID" clearable/>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="商品名称">
          <el-input v-model="searchInfo.product_name" placeholder="商品名称" clearable/>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="订单号">
          <el-input v-model="searchInfo.order_no" placeholder="订单号" clearable/>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="三方账单号">
          <el-input v-model="searchInfo.trade_no" placeholder="账单号" clearable/>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="商品ID">
          <el-input v-model="searchInfo.product_id" placeholder="商品ID" clearable/>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="版本号">
          <el-input v-model="searchInfo.version" placeholder="版本号" clearable/>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="商品类型">
          <el-select v-model="searchInfo.product_type" placeholder="商品类型" clearable>
            <el-option
                v-for="item in productTypeOptions"
                :key="item"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="支付渠道">
          <el-select v-model="searchInfo.payment" placeholder="支付渠道" clearable>
            <el-option
                v-for="item in payWaysList"
                :key="item"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-show="queryTxtState" label="搜索支付聚合数据">
          <el-switch v-model="searchInfo.paymentSum"/>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="text" :icon="queryTxtIcon" @click="queryTxtChange">{{ queryTxt }}</el-button>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
          style="width: 100%"
          tooltip-effect="dark"
          :data="tableData"
          row-key="id"
          max-height="590"
      >
        <el-table-column align="center" label="数据ID" prop="id" min-width="120" fixed/>
        <el-table-column align="center" label="APP" min-width="120">
          <template #default="scope">{{ formatAppName(scope.row.app_id) }}</template>
        </el-table-column>
        <el-table-column align="center" label="订单创建时间" min-width="160">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="支付时间" prop="payment_at" min-width="160">
          <template #default="scope">{{ formatDate(scope.row.payment_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="状态" min-width="120">
          <template #default="scope">
            <el-tag effect="dark" :type="formatOrderState(scope.row.state)[1]">
              {{ formatOrderState(scope.row.state)[0] }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="用户ID" prop="user_id" min-width="120"/>
        <el-table-column align="center" label="支付渠道" prop="payment" min-width="120">
          <template #default="scope">{{ formatPayment(scope.row.payment) }}</template>
        </el-table-column>
        <el-table-column align="center" label="三方账单号" prop="trade_no" min-width="260" show-overflow-tooltip/>
        <el-table-column align="center" label="订单号" prop="order_no" min-width="300" show-overflow-tooltip/>
        <el-table-column align="center" label="商品名称" prop="product_name" min-width="120" show-overflow-tooltip/>
        <el-table-column align="center" label="金额" prop="price" min-width="120">
          <template #default="scope">{{ numF2(scope.row.price) }}</template>
        </el-table-column>
        <el-table-column align="center" label="版本" prop="version" min-width="120"/>
        <el-table-column align="center" label="商品ID" prop="product_id" min-width="120"/>
        <el-table-column align="center" label="商品类型" prop="product_type" min-width="120">
          <template #default="scope">{{ formatProductType(scope.row.product_type) }}</template>
        </el-table-column>
        <el-table-column align="center" label="商品别名" prop="product_alias" min-width="120"/>
        <el-table-column align="center" label="数据更新时间" min-width="160">
          <template #default="scope">{{ formatDate(scope.row.updated_at) }}</template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>

      <el-table
        v-if="searchInfo.paymentSum"
        style="width: 100%"
        border
        tooltip-effect="dark"
        :data="paymentSumData"
        row-key="id"
      >
        <el-table-column align="center" label="支付渠道" prop="payment" min-width="120">
          <template #default="scope">{{ formatPayment(scope.row.payment) }} ({{scope.row.payment}})</template>
        </el-table-column>
        <el-table-column align="center" label="原始金额" prop="marked_price" min-width="260" show-overflow-tooltip/>
        <el-table-column align="center" label="美元" prop="usd_price" min-width="300" show-overflow-tooltip/>
      </el-table>


    </div>
  </div>
</template>

<script>
import {getOrdersList, orderPaymentSum} from '@/api/orders' //  此处请自行替换地址
import infoList from '@/mixins/infoList'

export default {
  mixins: [infoList],
  data() {
    return {
      unionIdBool: null,
      searchInfo: {},
      paymentSumData: [],
      listApi: getOrdersList,
    }
  },
  async created() {
    this.unionIdBool = this.userInfo.union_id === 0
    await this.onSubmit()
  },
  methods: {
    onReset() {
      this.searchInfo = {}
      this.onSubmit()
    },
    onSubmit() {
      this.getTableData()
      if (this.searchInfo.paymentSum) {
        this.getPaymentSum()
      }
    },
    getPaymentSum() {
      orderPaymentSum(this.searchInfo).then(res => {
        if (res.code === 0) {
          this.paymentSumData = res.data || []
        }
      })
    },
    handleDownload() {
      this.handleProgressLoading()
      const that = this
      import('@/utils/excel').then((excel) => {
        let tHeader = ["数据ID", "APP", "订单创建时间",
          "支付时间", "状态", "用户ID",
          "支付渠道", "三方账单号", "订单号",
          "商品名称", "金额", "版本",
          "商品ID", "商品类型", "商品别名",
          "数据更新时间"]
        let filterV = ["id", "AppName", "created_at",
          "payment_at", "OrderState", "user_id",
          "payment", "trade_no", "order_no",
          "product_name", "price", "version",
          "product_id", "product_type", "product_alias",
          "updated_at"]
        const listData = JSON.parse(JSON.stringify(this.tableData))
        // 数据处理
        listData.map((currentValue, index, array) => {
          // currentValue -> 数组中正在处理的当前元素
          // index -> 数组中正在处理的当前元素的索引
          // array -> 指向map方法被调用的数组
          array[index].AppName = that.formatAppName(currentValue.app_id)
          array[index].created_at = that.formatDate(currentValue.created_at)
          array[index].payment_at = that.formatDate(currentValue.payment_at)
          array[index].OrderState = that.formatOrderState(currentValue.state)[0]
          array[index].payment = that.formatPayment(currentValue.payment)
          array[index].product_type = that.formatProductType(currentValue.product_type)
          array[index].price = that.numF2(currentValue.price)
          array[index].updated_at = that.formatDate(currentValue.updated_at)
        })

        const data = this.formatJson(filterV, listData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
  },
}
</script>

<style lang="scss">

</style>
