<template>
  <div style="margin: 10px" />
</template>

<script>
import { ElLoading } from 'element-plus'
import { mapActions } from 'vuex'
import { checkDB } from '@/api/initdb'

export default {
  name: 'SSO',
  data() {
    return {
      loginForm: {
        token: this.$route.query.token,
      }
    }
  },
  created() {
    this.login()
  },
  methods: {
    ...mapActions('user', ['ssoLoginIn']),
    async checkInit() {
      const res = await checkDB()
      if (res.code === 0) {
        if (res.data?.needInit) {
          this.$store.commit('user/NeedInit')
          this.$router.push({ name: 'Init' })
        } else {
          this.$message({
            type: 'info',
            message: 'The database information has been configured and cannot be initialized'
          })
        }
      }
    },
    login() {
      return this.ssoLoginIn(this.loginForm)
    },

  }
}

</script>

<style lang="scss" scoped>

</style>
