<template>
  <div id="userLayout">
    <div class="login_panle">
      <div class="login_panle_form">
        <div class="login_panle_form_title">
          <img
            class="login_panle_form_title_logo"
            :src="$GIN_VUE_ADMIN.appLogo"
            alt
          >
          <p class="login_panle_form_title_p">{{ $GIN_VUE_ADMIN.appName }}</p>
        </div>
        <el-form
          ref="loginForm"
          :model="loginForm"
          :rules="rules"
          @keyup.enter="submitForm"
        >
          <el-form-item prop="username">
            <el-input v-model="loginForm.username" placeholder="Please enter a user name">
              <template #suffix>
                <i class="el-input__icon el-icon-user" />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item prop="password">
            <el-input
              v-model="loginForm.password"
              :type="lock === 'lock' ? 'password' : 'text'"
              placeholder="Please enter your password"
            >
              <template #suffix>
                <i
                  :class="'el-input__icon el-icon-' + lock"
                  @click="changeLock"
                />
              </template>
            </el-input>
          </el-form-item>
          <el-form-item style="position: relative" prop="captcha">
            <el-input
              v-model="loginForm.captcha"
              name="logVerify"
              placeholder="Please enter the verification code"
              style="width: 60%"
            />
            <div class="vPic">
              <img
                v-if="picPath"
                :src="picPath"
                alt="Please enter the verification code"
                @click="loginVerify()"
              >
            </div>
          </el-form-item>
          <el-form-item>
            <el-button
              type="primary"
              style="width: 40%; float: left"
              @click="submitForm"
            >Login
            </el-button>
            <el-button
              type="success"
              style="width: 40%; float: right"
              @click="dingTalk"
            >DingTalk
            </el-button>
          </el-form-item>

        </el-form>
      </div>
      <div class="login_panle_right" />
    </div>
  </div>
</template>

<script>
import { mapActions } from 'vuex'
import { captcha } from '@/api/user'
import { checkDB } from '@/api/initdb'
import { ElLoading } from 'element-plus'

export default {
  name: 'Login',
  data() {
    const checkUsername = (rule, value, callback) => {
      if (value.length < 2) {
        return callback(new Error('Please enter a correct user name'))
      } else {
        callback()
      }
    }
    const checkPassword = (rule, value, callback) => {
      if (value.length < 6) {
        return callback(new Error('Please enter the correct password'))
      } else {
        callback()
      }
    }
    return {
      curYear: 0,
      lock: 'lock',
      loginForm: {
        username: '',
        password: '',
        captcha: '',
        captchaId: ''
      },
      rules: {
        username: [{ validator: checkUsername, trigger: 'blur' }],
        password: [{ validator: checkPassword, trigger: 'blur' }],
        captcha: [{ required: true, message: 'Please enter the verification code', trigger: 'blur' },
          {
            min: 1,
            max: 6,
            message: 'The verification code format is incorrect',
            trigger: 'blur',
          }]
      },
      logVerify: '',
      picPath: ''
    }
  },
  created() {
    this.loginVerify()
    this.curYear = new Date().getFullYear()
  },
  methods: {
    ...mapActions('user', ['LoginIn']),
    async checkInit() {
      const res = await checkDB()
      if (res.code === 0) {
        if (res.data?.needInit) {
          this.$store.commit('user/NeedInit')
          this.$router.push({ name: 'Init' })
        } else {
          this.$message({
            type: 'info',
            message: 'The database information has been configured and cannot be initialized'
          })
        }
      }
    },
    dingTalk() {
      window.location.href = '/api/base/ssoRedirect'
    },
    login() {
      return this.LoginIn(this.loginForm)
    },
    async submitForm() {
      const that = this
      that.handleProgressLoading()
      this.$refs.loginForm.validate(async(v) => {
        if (v === true) {
          const flag = that.login()
          if (!flag) {
            that.loginVerify()
          }
        } else {
          that.$message({
            type: 'error',
            message: 'Please fill in the login information correctly',
            showClose: true
          })
          that.loginVerify()
          return false
        }
      })
      setTimeout(function() {
        window.location.reload()
        that.progressLoading.close()
      }, 1250)
    },
    changeLock() {
      this.lock = this.lock === 'lock' ? 'unlock' : 'lock'
    },
    loginVerify() {
      captcha({}).then((ele) => {
        this.picPath = ele.data.picPath
        this.loginForm.captchaId = ele.data.captchaId
      })
    },
    handleProgressLoading() {
      this.progressLoading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
    },
  }
}

</script>

<style lang="scss" scoped>
  @import "@/style/newLogin.scss";
</style>
