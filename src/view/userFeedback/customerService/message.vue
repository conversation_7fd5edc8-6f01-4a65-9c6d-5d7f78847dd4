<template>
  <div>
    <div class="displayFlex cs-content">

      <div>
        <div v-for="item in data" :key="item.index">{{ item.name }}</div>
      </div>

      <div class="cs-left">
        <div class="displayFlex cs-search">
          <el-input v-model="searchForm.id" class="cs-search-input" />
          <el-button type="primary" @click="searchFriend" size="small">搜索</el-button>
        </div>
        <ul v-infinite-scroll="listenScroll" class="cs-list" infinite-scroll-disabled="busy" infinite-scroll-distance="10">
          <li v-for="(item, index) in usersListData" :key="item.id" :class="item.id===receiverUser.id ? 'chat-active' : ''" @click="setChat(item)">
            <img class="cs-list-avatar" :src="item.avatar" alt="">
            <div class="cs-list-desc">
              <h2>{{ item.nickname }}</h2>
              <p class="cs-list-level">{{ item.levels }}</p>
              <p class="displayFlex">
                <span>工会: {{ item.unionId }}</span>
                <span>{{ formatDate(item.created_at) }}</span>
              </p>
            </div>
            <p v-if="item.msgCount > 0" class="cs-news-length">{{ item.msgCount }}</p>
          </li>
        </ul>
      </div>
      <div class="displayFlex cs-right">
        <div class="loading-toolbar">
          <span v-if="loadRecord.status === 0" class="color-blue">
            <i class="el-icon-loading" /> 正在加载数据中...
          </span>
          <span
            v-else-if="loadRecord.status === 1"
            class="pointer color-blue"
            @click="refreshMessage"
          >
            <i class="el-icon-bottom" /> 查看更多消息...
          </span>
          <span v-else> 没有更多消息了... </span>
        </div>
        <ul id="new_message" class="cs-chat" style="overflow-y:scroll" @click="clearMark" @scroll="talkPanelScroll($event)">
          <li v-for="(item, index) in currentChatList" :key="index">
            <chatItem :data="item" :sender-user="senderUser" :receiver-user="receiverUser" />
          </li>
        </ul>
        <div class="displayFlex cs-operate">
          <div class="displayFlex cs-operate-left">
            <el-input v-model="formData.message_content" class="cs-enter" @keypress.native.enter="sendMessageToPeer" />
            <el-button class="cs-submit" type="primary" @click="sendMessageToPeer">Send</el-button>
          </div>
          <div class="cs-operate-updata">
            <el-upload
              :action="`${path}/files/createFiles`"
              :headers="{ 'x-token': token }"
              :auto-upload="true"
              :file-list="photoAlbums"
              :show-file-list="false"
              :on-success="handlePhotoAlbumsSuccess"
              :on-change="handlePhotoAlbumsChange"
              accept=".jpg,.jpeg,.png,.gif,.mp4,.mp3"
              :multiple="false"
              :limit="1"
            >
              <i class="el-icon-folder" style="font-size: 40px" />
            </el-upload>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import { ElMessage } from 'element-plus'
const path = import.meta.env.VITE_BASE_API
import infiniteScroll from 'vue-infinite-scroll'
import {
  getUsersMonitorList,
  findUsers
} from '@/api/users'
import {
  getAgoraToken,
  sendMessage,
  messageList
} from '@/api/agora' //  此处请自行替换地址
import AgoraRTM from 'agora-rtm-sdk'
import chatItem from '@/components/chatItem/chatItem.vue'
import infoList from '@/mixins/infoList'
import { store } from '@/store'

// 聊天室配置
const rtm = {
  client: null,
}

export default {
  name: 'CustomerService',
  components: { chatItem, infiniteScroll },
  filters: {},
  mixins: [infoList],
  data() {
    return {
      path: path,
      tipsBoard: false,
      loadRecord: {
        status: 1
      },
      photoAlbums: [],
      searchForm: { id: 0 },
      formData: {
        message_content: undefined,
      },
      currentUser: {},
      // chatListData: new Map(),
      senderUser: {},
      receiverUser: {},
      currentChatList: [],
      chatPage: 1,
      page: 1,
      pageSize: 20,
      count: 0,
      busy: false,
      usersListData: [],
    }
  },
  watch() {

  },
  computed: {
    ...mapGetters('user', ['token']),
  },
  mounted() {

  },
  async created() {
    this.currentUser = store.getters['user/userInfo']
    this.getSenderUser()
    this.login()
    this.getUsersMonitorList()
  },
  methods: {
    clearMark() {
      const that = this
      const userIndex = that.usersListData.findIndex(item => parseInt(item.id) === parseInt(that.receiverUser.id))
      that.usersListData[userIndex].msgCount = 0
    },
    handlePhotoAlbumsChange(file, filelist) {

    },
    async handlePhotoAlbumsSuccess(response, file, fileList) {
      let params = {}
      const that = this
      fileList.forEach((item, i) => {
        const message_type = this.fileType(item.response.data.url)
        params = {
          'receiver_id': parseInt(this.receiverUser.id),
          'message_content': '',
          'media_url': item.response.data.url,
          'message_type': message_type,
        }
      })
      const res = await sendMessage(params)
      if (res.code === 0) {
        that.currentChatList.push(res.data)
      }
      fileList.length = 0
    },
    async getSenderUser() {
      const res = await findUsers({ id: this.currentUser.services_id })
      this.senderUser = res.data.reusers
    },
    async searchFriend() {
      this.page = 1
      const res = await getUsersMonitorList({page: this.page, pageSize: this.pageSize, id: this.searchForm.id})
      if (res.data.total === 0) {
        this.$message.error("没有更多了")
        return
      }
      this.usersListData = res.data.list
      if (parseInt(this.page) === 1 && this.usersListData.length > 0) {
        this.setChat(this.usersListData[0])
      }
    },
    async login() {
      const that = this
      const res = await getAgoraToken()

      rtm.client = AgoraRTM.createInstance(res.data.app_id, { enableLogUpload: true })

      // 监听状态
      rtm.client.on('ConnectionStateChanged', (newState, reason) => {
        console.log('on connection state changed to ' + newState + ' reason: ' + reason)
      })

      rtm.client.login({
        token: res.data.token,
        uid: res.data.user_id.toString(),
      }).then(() => {
        console.log('登录成功' + res.data.user_id.toString())
        // 接收消息
        rtm.client.on('MessageFromPeer', function(message, peerId) {
          console.log('收到来自' + peerId)
          // console.log(message)
          // console.log(message.text)

          const userIndex = that.usersListData.findIndex(item => parseInt(item.id) === parseInt(peerId))
          if (userIndex === -1) {
            that.setPeerIdData(peerId)
          } else {
            that.usersListData[userIndex].msgCount += 1
          }

          if (parseInt(peerId) === parseInt(that.receiverUser.id)) {
            that.currentChatList.push(JSON.parse(message.text))
          }

          that.scrollToBottom()
        })
      }).catch(err => {
        console.log('登录失败', err)
      })
    },
    listenScroll() { // 监听滚动
      this.busy = true
      setTimeout(() => {
        this.page += 1
        this.getUsersMonitorList()
        this.busy = false
      }, 500)
    },
    async getUsersMonitorList() {
      const res = await getUsersMonitorList({page: this.page, pageSize: this.pageSize, id: this.searchForm.id})
      if (res.data.total === 0) {
        this.$message.error("没有更多了")
        return
      }
      this.usersListData.push(...res.data.list)
      if (parseInt(this.page) === 1 && this.usersListData.length > 0) {
        this.setChat(this.usersListData[0])
      }
    },
    async refreshMessage() {
      const that = this
      this.chatPage += 1
      const res = await messageList({ receiver_id: that.receiverUser.id, page: this.chatPage, pageSize: this.pageSize })
      if (res.code === 0) {
        if (res.data.total > 0 && res.data.list) {
          res.data.list.forEach(function(value, index, array) {
            that.currentChatList.unshift(value)
          })
        } else {
          that.loadRecord.status = 3
        }
      }
    },
    // 设置新聊天
    async setPeerIdData(peerId) {
      const that = this
      const res = await findUsers({ id: peerId })
      if (res.code === 0) {
        res.data.reusers.msgCount = 1
        that.usersListData.splice(0, 0, res.data.reusers)
      }
    },
    async setChat(user) {
      const that = this
      that.loadRecord.status = 0
      that.chatPage = 1
      that.receiverUser = user
      that.currentChatList = []

      const res = await messageList({
        receiver_id: that.receiverUser.id,
        page: that.chatPage,
        pageSize: that.pageSize,
        is_sort: true
      })

      if (res.code === 0) {
        if (res.data.total > 0) {
          res.data.list.forEach(function(value, index, array) {
            that.currentChatList.push(value)
          })
          that.loadRecord.status = 1
        } else {
          that.loadRecord.status = 2
        }
      }

      // 处理聊天未读图标
      const userIndex = that.usersListData.findIndex(item => parseInt(item.id) === parseInt(that.receiverUser.id))
      that.usersListData[userIndex].msgCount = 0

      that.scrollToBottom()
      // that.chatListData.set(that.receiverUser.id,that.currentChatList)
    },
    // 点对点消息
    async sendMessageToPeer() {
      const that = this
      const params = {
        'receiver_id': parseInt(this.receiverUser.id),
        'message_content': this.formData.message_content,
        'message_type': 1,
      }
      if (this.formData.message_content === '' || parseInt(this.receiverUser.id) <= 0) {
        ElMessage({
          message: 'Message is empty.',
          type: 'warning',
        })
        return
      }
      const res = await sendMessage(params)
      if (res.code === 0) {
        that.currentChatList.push(res.data)
        that.formData.message_content = ''
      }
      that.scrollToBottom()
    },

    fileType(filePath) {
      // 获取最后一个.的位置
      var index = filePath.lastIndexOf('.')
      // 获取后缀
      var ext = filePath.substr(index + 1)
      // 判断是否是图片类型
      if (['png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp', 'psd', 'svg', 'tiff'].indexOf(ext.toLowerCase()) != -1) {
        return 2
      }
      // 判断是否是视频类型
      if (['mp4', 'avi', 'mov', 'rmvb', 'rm', 'flv', '3gp'].indexOf(ext.toLowerCase()) != -1) {
        return 5
      }
      // 判断是否是音频类型
      if (['cda', 'wav', 'mp3', 'wmv', 'flac', 'aac'].indexOf(ext.toLowerCase()) != -1) {
        return 3
      }
      // 判断是否是音频类型
      return 1
    },
    scrollToBottom: function() {
      this.$nextTick(() => {
        var container = this.$el.querySelector('#new_message')
        container.scrollTop = container.scrollHeight
      })
    },
    talkPanelScroll: function(e) {
      if (e.target.scrollTop === 0 && this.loadRecord.status === 1) {
        this.refreshMessage()
        return
      }
    }
  },
}
</script>

<style lang="scss" scoped>
.displayFlex {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.cs-content {
  height: 86vh; // 这里高度根据你页面上部组建自己调整一下
  min-height: 400px;
  margin: 10px;
  border-radius: 10px;
  border: 1px solid #cfcccc;
  overflow: hidden;
  .cs-left {
    position: relative;
    height: 100%;
    flex: 0 1 20%;
    padding: 70px 2px 20px 2px;
    box-sizing: border-box;
    overflow-y: auto;
    border-right: 1px solid #cfcccc;
    background: #fff;
  }
  .cs-right {
    height: 100%;
    flex: 0 1 80%;
    flex-direction: column;
  }
  .cs-search {
    position: absolute;
    top: 0;
    left: 0;
    z-index: 2;
    height: 70px;
    padding: 0 8px;
    box-sizing: border-box;
    width: 100%;
    .cs-search-input {
      margin-right: 20px;
    }
  }
  .cs-list {
    & > li {
      position: relative;
      display: flex;
      justify-content: flex-start;
      align-items: center;
      border: 1px solid #ccc;
      border-radius: 6px;
      margin-bottom: 10px;
      padding: 10px 6px;
      box-sizing: border-box;
    }
    .cs-list-avatar {
      width: 50px;
      height: 50px;
      border-radius: 4px;
      margin-right: 10px;
    }
    .cs-list-desc {
      flex: 1 1 auto;
      color: #333;
      & > h2 {
        max-width: 170px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        font-size: 14px;
      }
      & > p {
        font-size: 12px;
      }
      .cs-list-level {
        margin: 6px 0;
      }
    }
    .cs-news-length {
      position: absolute;
      top: -6px;
      right: 0;
      z-index: 1;
      width: 20px;
      height: 20px;
      border-radius: 50%;
      text-align: center;
      line-height: 20px;
      color: #fff;
      font-size: 12px;
      font-weight: 400;
      background: red;
    }
  }
  .cs-chat {
    width: 100%;
    flex: 1 1 auto;
    overflow-y: auto;
    padding: 20px;
    box-sizing: border-box;
    background: #f1f1f1;
    & > li {
      margin-bottom: 12px;
    }
  }
  .cs-operate {
    width: 100%;
    height: 70px;
    padding: 0 20px;
    box-sizing: border-box;
    .cs-operate-left {
      flex: 1 1 auto;
      margin-right: 20px;
      height: 56px;
      padding: 0 10px;
      box-sizing: border-box;
      border: 1px solid #cfcccc;
      border-radius: 20px;
      .cs-enter {
        margin-right: 20px;
        .el-input__inner {
          border: none;
        }
      }
    }
    .cs-operate-updata {
      font-size: 36px;
      font-weight: bold;
      cursor: pointer;
    }
  }
}

.refresh-container {
  transition: all 0.2s;
  &:hover{
    right: 0
  }
  position: fixed;
  right: -11px;
  top: 124px;
  height: 40px;
  width: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999;
  color: #fff;
  border-radius: 4px 0 0 4px;
  cursor: pointer;
  -webkit-box-shadow: inset 0 0 6px rgba(0 ,0 ,0, 10%);
}
.loading-toolbar {
  height: 30px;
  line-height: 30px;
  margin: 5px 0;
  text-align: center;
  user-select: none;
  font-size: 13px;
  color: #cec4c4;

  .color-blue {
    color: #409eff;
  }
}
.chat-active{
  background: #afcfff;
}
</style>
<style lang="scss">
.cs-enter {
  .el-input__inner {
    border: none;
  }
}
</style>
