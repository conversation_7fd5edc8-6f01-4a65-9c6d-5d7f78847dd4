<template>
    <div class="message">
        <el-container class="container" direction="vertical">
            <!--            <el-aside style="width:180px;">-->
            <!--                <el-menu>-->
            <!--                    <el-submenu index="1">-->
            <!--                        <template slot="title"><i class="el-icon-message"></i>导航一</template>-->
            <!--                        <el-menu-item index="1-1">菜单1</el-menu-item>-->
            <!--                        <el-menu-item index="1-2">菜单2</el-menu-item>-->
            <!--                        <el-menu-item index="1-3">菜单3</el-menu-item>-->
            <!--                    </el-submenu>-->
            <!--                    <el-submenu index="2">-->
            <!--                        <template slot="title"><i class="el-icon-menu"></i>导航二</template>-->
            <!--                    </el-submenu>-->
            <!--                    <el-submenu index="3">-->
            <!--                        <template slot="title"><i class="el-icon-setting"></i>导航三</template>-->
            <!--                    </el-submenu>-->
            <!--                </el-menu>-->
            <!--            </el-aside>-->
            <el-container>
                <el-header>消息</el-header>
                <el-main>

                </el-main>
                <el-footer>
                    <el-input v-model="messageForm" type="textarea"></el-input>
                </el-footer>
            </el-container>
        </el-container>
        <el-button type="primary" round @click="MessageFromPeer">Primary</el-button>

        <el-form ref="elForm" :model="formData" :rules="rules" size="medium" label-width="100px"
                 label-position="top">
            <el-form-item label="消息内容" prop="message_content">
                <el-input v-model="formData.message_content" type="textarea" placeholder="请输入消息内容"
                          :autosize="{minRows: 4, maxRows: 4}" :style="{width: '100%'}"></el-input>
            </el-form-item>
            <el-form-item label="接受用户" prop="receiver_id">
                <el-input v-model="formData.receiver_id" placeholder="接受用户" clearable :style="{width: '100%'}">
                </el-input>
            </el-form-item>
            <el-form-item size="large">
                <el-button type="primary" @click="submitForm">发送</el-button>
            </el-form-item>
        </el-form>

    </div>
</template>
<script>
    import {
        getAgoraToken,
        sendMessage
    } from '@/api/agora' //  此处请自行替换地址

    import AgoraRTM from "agora-rtm-sdk"

    let uid = ''
    const agoraClient = AgoraRTM.createInstance('291cfd62a7f4472f87f1216cc45da0bb', {enableLogUpload: true});

    agoraClient.on('MessageFromPeer', function (message, peerId) {
        console.log(message)
        console.log(message.text)
    })

    // 显示连接状态变化
    agoraClient.on('ConnectionStateChanged', function (state, reason) {
        console.log("State changed To: " + state + " Reason: " + reason)
    })

    export default {
        components: {},
        props: [],
        data() {
            return {
                messageForm: '',
                searchFriendName: '',
                subMenu: false,
                loadStatus: 0,
                subHeaderShadow: false,
                launchGroupShow: false,
                interval: null,
                talkItems: [],

                // 对话面板的传递参数
                params: {
                    source: 0,
                    receive_id: 0,
                    nickname: '',
                },
                agoraChannel: null,
                formData: {
                    message_content: undefined,
                    receiver_id: undefined,
                },
            }
        },
        computed: {
            isFriendOnline() {
                return true
            },
        },
        watch: {},
        created() {
            let that = this
            this.sharRTM()
            this.login()
            setInterval(function () {
                // that.MessageFromPeer()
            }, 1000);
        },
        mounted() {
        },
        methods: {
            async submitForm() {
                this.sendMessageToPeer()
            },
            sharRTM() {

            },
            ConnectionStateChanged() {
                agoraClient.on('ConnectionStateChanged', (newState, reason) => {
                    console.log('--链接状态---');
                    console.log(newState, reason);
                    if (reason === 'REMOTE_LOGIN') {
                        console.log("您已被踢下线")
                    }
                })
            },
            async login() {

                const res = await getAgoraToken()
                // agoraClient = AgoraRTM.createInstance(res.data.app_id,{ enableLogUpload: true });
                uid = res.data.user_id.toString()
                agoraClient.login({
                    token: res.data.token,
                    uid: uid,
                }).then(() => {
                    console.log('登录成功' + uid);
                }).catch(err => {
                    console.log('登录失败', err);
                });

            },
            MessageFromPeer() {
                // 接受单人消息
                // agoraClient.on('MessageFromPeer', ({text}, peerId) => { // text 为消息文本，peerId 是消息发送方 User ID
                //     /* 收到点对点消息的处理逻辑 */
                //     console.log('收到来自' + peerId + '的消息：' + text);
                //     //$('.usermsg').append(`<p>${peerId}:${text}</p>`)
                // });
                // console.log(999999)

                agoraClient.on('MessageFromPeer', function (message, peerId) {
                    console.log('收到来自' + peerId + '的消息：' + message);
                })

            },

            ChannelMessage() {
                // 接受频道消息
                agoraClient.on('ChannelMessage', ({text}, senderId) => { // text 为收到的频道消息文本，senderId 为发送方的 User ID
                    /* 收到频道消息的处理逻辑 */
                    console.log(senderId + '频道消息：' + '：' + text);
                    //$('.msg').append(`<p>${senderId}:${text}</p>`)
                });
            },

            MemberJoined() {
                // 加入频道
                agoraClient.on('MemberJoined', (res) => {
                    //$('.msg').append(`<p>${res+'加入频道'}</p>`)
                })
            },

            MemberLeft() {
                //离开频道
                agoraClient.on('MemberLeft', (res) => {
                    // $('.msg').append(`<p>${res+'离开频道'}</p>`)
                })
            },

            MemberCountUpdated() {
                // 频道人数变动
                agoraClient.on('MemberCountUpdated', (res) => {
                    console.log(res);
                    // $('.usernum').html('当前在线人数' + res + '人')
                })
            },

            // 发送个人消息
            async sendMessageToPeer() {
                let params = {
                    'receiver_id': parseInt(this.formData.receiver_id),
                    'message_content': this.formData.message_content,
                    'message_type': 1,
                }

                const res = await sendMessage(params)
            },
            sendMessage() {
                // 发送频道消息
                agoraClient.sendMessage({text: ""}).then((res) => {
                    // 你的代码：频道消息发送成功处理逻辑。
                    console.log('频道信息发送成功o(*￣︶￣)');
                    // $('.msg').append(`<p>${uid}:${t2}</p>`)
                }).catch(error => {
                    // 你的代码：频道消息发送失败处理逻辑。
                    console.log('频道信息没有收到o(╥﹏╥)o~', error);
                })
            },

            loginoOut() {
                agoraClient.logout();
            },
        }
    }
</script>
<style lang="scss" scoped>
    .message {
        .el-container {
            height: 100%;
        }

        .el-header,
        .el-footer {
            background-color: #b3c0d1;
            color: #333;
            text-align: center;
            line-height: 60px;
        }

        .el-aside {
            background-color: #d3dce6;
            color: #333;
            text-align: center;
            line-height: 200px;
        }

        .el-main {
            background-color: #e9eef3;
            color: #333;
            text-align: center;
            line-height: 160px;
        }

        .el-menu {
            background-color: #d3dce6;
        }
    }
</style>
