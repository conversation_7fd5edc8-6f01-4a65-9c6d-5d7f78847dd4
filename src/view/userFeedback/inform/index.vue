<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-popover v-model:visible="deleteVisible" placement="top" width="160">
          <p>确定要删除吗？</p>
          <div style="text-align: right; margin-top: 8px;">
            <el-button size="mini" type="text" @click="deleteVisible = false">取消</el-button>
            <el-button size="mini" type="primary" @click="onDelete">确定</el-button>
          </div>
          <template #reference>
            <el-button icon="el-icon-delete" size="mini" style="margin-left: 10px;" :disabled="!multipleSelection.length">删除</el-button>
          </template>
        </el-popover>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="center" label="日期" width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="举报类型" width="180">
          <template #default="scope">{{ formatInform(scope.row.type) }}</template>
        </el-table-column>
        <el-table-column align="center" label="APPID" prop="app_id" width="120" />
        <el-table-column align="center" label="举报人ID" prop="user_id" width="120" />
        <el-table-column align="center" label="被举报ID" prop="anchor_id" width="120" />
        <el-table-column align="center" label="举报内容" prop="content" width="120" />
        <el-table-column align="center" label="版本号" prop="version" width="120" />
        <el-table-column align="center" label="是否处理" prop="status" width="120">
          <template #default="scope">{{ formatBoolean(scope.row.status) }}</template>
        </el-table-column>
        <el-table-column align="center" label="按钮组">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateInform(scope.row)">变更</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" label-position="right" label-width="80px">
        <el-form-item label="举报类型:">
          <el-select v-model="formData.type" clearable placeholder="举报类型">
            <el-option
              v-for="item in informTypeOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="APPID:">
          <el-input v-model.number="formData.app_id" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="举报人ID:">
          <el-input v-model.number="formData.user_id" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="被举报ID:">
          <el-input v-model.number="formData.anchor_id" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="举报内容:">
          <el-input v-model="formData.content" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="版本号:">
          <el-input v-model="formData.version" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="是否处理:">
          <el-switch v-model="formData.status" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createInform,
  deleteInform,
  deleteInformByIds,
  updateInform,
  findInform,
  getInformList
} from '@/api/inform' //  此处请自行替换地址
const informTypeOptions = [
  {
    value: 1,
    label: '视频页',
  },
  {
    value: 2,
    label: '聊天页',
  },
  {
    value: 3,
    label: '通话页',
  },
]
import infoList from '@/mixins/infoList'
export default {
  name: 'Inform',
  mixins: [infoList],
  data() {
    return {
      informTypeOptions: informTypeOptions,
      listApi: getInformList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        type: 0,
        app_id: 0,
        user_id: 0,
        anchor_id: 0,
        content: '',
        version: '',
        status: false,
      }
    }
  },
  async created() {
    await this.getTableData()
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      if (this.searchInfo.status === '') {
        this.searchInfo.status = null
      }
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteInform(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
        this.multipleSelection.map(item => {
          ids.push(item.id)
        })
      const res = await deleteInformByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateInform(row) {
      const res = await findInform({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.reinform
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        type: 0,
        app_id: 0,
        user_id: 0,
        anchor_id: 0,
        content: '',
        version: '',
        status: false,
      }
    },
    async deleteInform(row) {
      const res = await deleteInform({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createInform(this.formData)
          break
        case 'update':
          res = await updateInform(this.formData)
          break
        default:
          res = await createInform(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    }
  },
}
</script>

<style>
</style>

