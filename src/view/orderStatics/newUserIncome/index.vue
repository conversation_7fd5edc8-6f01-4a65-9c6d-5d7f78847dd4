<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="产品名称">
          <el-select filterable multiple collapse-tags v-model="searchInfo.app_ids" clearable>
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="`${item.app_name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchInfo.date_range"
            type="daterange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DDTHH:mm:ssZ"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="text" :icon="queryTxtIcon" @click="queryTxtChange">{{ queryTxt }}</el-button>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="tableData"
        style="width: 100%"
        :data="tableData"
        show-summary
        sum-text="本页合计"
        max-height="590"
        row-key="id">
        <el-table-column align="center" label="日期" prop="date" min-width="140"/>
        <el-table-column align="center" label="APP" prop="app_name" min-width="140"/>
        <el-table-column align="center" label="总新增收入" prop="total_new_user_income" min-width="140"/>
        <el-table-column
          align="center"
          min-width="160"
          v-for="item in payWaysList"
          :key="item"
          :label="item.label"
          :prop="`income_${item.value}`"
        />
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import {getOrderStaticsList} from '@/api/orderStatics' //  此处请自行替换地址
  import infoList from '@/mixins/infoList'
  import orderStaticsMixins from '@/mixins/orderStatics'

  export default {
    mixins: [infoList, orderStaticsMixins],
    data() {
      return {
        searchInfo: {
          app_ids: [],
          date_range: [],
        },
        listApi: getOrderStaticsList,
      }
    },
    async created() {
      await this.getTableData(this.nFunc, this.tableDataFormat)
    },
    methods: {
      onReset() {
        this.searchInfo = {}
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      onSubmit() {
        this.page = 1
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      getSummaries({columns, data}) {
        // 获取表头和数据
        // :show-summary="this.searchInfo.group_range.length > 0"
        // :summary-method="getSummaries"
        let labelList = []
        let vList = []
        columns.forEach((column, i) => {
          labelList.push(column.label)
          vList.push(column.property)
        })
        console.log(JSON.stringify(labelList))
        console.log(JSON.stringify(vList))
        return []
      },
      tableDataFormat() {
        this.tableData.map((item, i, data) => {
          data[i].app_name = this.formatAppName(item.app_id)
          data[i].date = this.formatDateYMD(item.date)
          let pRes = this.formatTotalNewUserIncome(item.new_user_income)
          let paymentIncomeDict = pRes[1]
          this.payWaysList.forEach((item, index) => {
            let paymentPrice = paymentIncomeDict[item.value]
            if (paymentPrice === undefined) {
              data[i][`income_${item.value}`] = 0
            } else {
              data[i][`income_${item.value}`] = paymentPrice
            }
          })
          data[i].total_new_user_income = pRes[0]
        })
      },
      handleDownload() {
        let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
        let res = this.getTableHeaderProp(tableColumns)
        let tHeader = res[0]
        let filterV = res[1]
        this.handleProgressLoading()
        import('@/utils/excel').then((excel) => {
          const data = this.formatJson(filterV, this.tableData)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.filename,
            autoWidth: this.autoWidth,
            bookType: this.bookType,
          })
          this.progressLoading.close()
        })
      },
      sizeChange(v) {
        this.pageSize = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      pageChange(v) {
        this.page = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
    },
  }
</script>

<style lang="scss" scoped>

</style>
