<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="产品名称">
          <el-select filterable v-model="searchInfo.app_id">
            <el-option
                v-for="item in appList"
                :key="item.id"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" @click="handleDownload">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
          size="small"
          ref="tableData"
          style="width: 100%"
          :data="paymentData"
          show-summary
          sum-text="本页合计"
          row-key="id">
        <el-table-column align="center" label="今天" prop="payment_label" min-width="140"/>
        <el-table-column align="center" label="APP" prop="app_name" min-width="140"/>
        <el-table-column align="center" label="收入" prop="income" min-width="140"/>
        <el-table-column align="center" label="支付次数" prop="payCount" min-width="140"/>
        <el-table-column align="center" label="支付人数" prop="payUserCount" min-width="140"/>
        <el-table-column align="center" label="新增收入" prop="newUserIncome" min-width="140"/>
        <el-table-column align="center" label="新增支付次数" prop="newPayCount" min-width="140"/>
        <el-table-column align="center" label="新增支付人数" prop="newPayUserCount" min-width="140"/>
      </el-table>
    </div>
  </div>
</template>

<script>
import {getTodayOrderStatics} from '@/api/orderStatics' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import orderStaticsMixins from '@/mixins/orderStatics'

export default {
  mixins: [infoList, orderStaticsMixins],
  data() {
    return {
      searchInfo: {
        app_ids: [],
        app_id: null,
        date_range: [],
      },
      paymentData: [],
      listApi: getTodayOrderStatics,
    }
  },
  async created() {
    // this.searchInfo.app_ids = [this.appIds[0]]
    this.searchInfo.app_id = this.appIds[0]
    await this.getTodayData()
  },
  methods: {
    getTodayData() {
      getTodayOrderStatics(this.searchInfo).then(res => {
        if (res.code === 0) {
          let dataNew = res.data
          let incomeRes = this.formatTotalIncome(dataNew.income)[1]
          let newPayCountRes = this.formatTotalNewPayCount(dataNew.new_pay_count)[1]
          let newPayUserCountRes = this.formatTotalNewPayUserCount(dataNew.new_pay_user_count)[1]
          let newUserIncomeRes = this.formatTotalNewUserIncome(dataNew.new_user_income)[1]
          let payCountRes = this.formatTotalPayCount(dataNew.pay_count)[1]
          let payUserCountRes = this.formatTotalPayUserCount(dataNew.pay_user_count)[1]
          let app_name = this.formatAppName(dataNew.app_id)
          let pRes = []
          this.payWaysList.forEach((pitem) => {
            let pId = pitem.value
            // if (pId === 16) {
            //   debugger
            // }
            pRes.push({
              payment_id: pId,
              payment_label: pitem.label,
              app_name: app_name,
              income: incomeRes[pId] ? incomeRes[pId] : 0,
              newPayCount: newPayCountRes[pId] ? newPayCountRes[pId] : 0,
              newPayUserCount: newPayUserCountRes[pId] ? newPayUserCountRes[pId] : 0,
              newUserIncome: newUserIncomeRes[pId] ? newUserIncomeRes[pId] : 0,
              payCount: payCountRes[pId] ? payCountRes[pId] : 0,
              payUserCount: payUserCountRes[pId] ? payUserCountRes[pId] : 0,
            })
          })
          this.paymentData = pRes
          console.log(this.paymentData)
        }
      })
    },
    onReset() {
      this.searchInfo.app_id = this.appIds[0]
      this.searchInfo.app_id = 4
      this.getTodayData()
    },
    onSubmit() {
      this.page = 1
      this.getTodayData()
    },
    handleDownload() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.paymentData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
  },
}
</script>

<style lang="scss" scoped>

</style>
