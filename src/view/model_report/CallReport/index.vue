<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="产品名称">
          <el-tooltip
            class="box-item"
            effect="dark"
            content="是否统计所有产品？"
            placement="top"
          >
            <el-switch v-model="searchInfo.is_all" @change="isAllChange"/>
          </el-tooltip>
          <el-select multiple collapse-tags v-model="searchInfo.app_ids" filterable>
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="`${item.app_name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="渠道">
          <el-select v-model="searchInfo.channel" filterable>
            <el-option label="总计数据" :value="1"/>
            <el-option label="自然量" :value="2"/>
            <el-option label="Facebook" :value="3"/>
            <el-option label="google" :value="4"/>
          </el-select>
        </el-form-item>
        <el-form-item label="颗粒度">
          <el-radio-group v-model="searchInfo.type" size="small">
            <el-radio-button :label="1">月</el-radio-button>
            <el-radio-button :label="2">周</el-radio-button>
            <el-radio-button :label="3">天</el-radio-button>
            <el-radio-button :label="4">小时</el-radio-button>
            <el-radio-button :label="5">30min</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchInfo.date_range"
            type="datetimerange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DDTHH:mm:ssZ"
            :default-time="[new Date(2022, 1, 1, 0, 0, 0),new Date(2022, 1, 1, 23, 59, 59)]"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="tableData"
        style="width: 100%"
        :data="tableData"
        row-key="id"
        max-height="590"
      >
        <el-table-column align="center" label="日期" prop="date" width="120" fixed="left"/>
        <el-table-column align="center" label="时间点" prop="time" width="80" fixed="left"/>
        <el-table-column align="center" label="DAU" prop="dau" min-width="140"/>
        <el-table-column align="center" label="通话次数" prop="call_count" min-width="140"/>
        <el-table-column align="center" label="通话人数" prop="call_users" min-width="140"/>
        <el-table-column align="center" label="通话时长(h:m:s)" prop="call_duration_str" min-width="140"/>
        <el-table-column align="center" label="人均通话次数" prop="CallOfPer" min-width="120"/>
        <el-table-column align="center" label="人均通话时长(s)" prop="CallDurationOfPer" min-width="140"/>
        <el-table-column align="center" label="通话人数占比(%)" prop="CallPerRate" min-width="140"/>
        <el-table-column align="center" label="30秒以下通话占比" prop="InvalidCallRate" min-width="160"/>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import {getCallReportList} from '@/api/callReport' //  此处请自行替换地址
  import infoList from '@/mixins/infoList'
  import callReportMixins from '@/mixins/callReport'

  export default {
    mixins: [infoList, callReportMixins],
    data() {
      return {
        searchInfo: {
          is_all: false,
          date_range: [],
          app_ids: [],
          channel: 1,
          type: 3
        },
        listApi: getCallReportList,
      }
    },
    async created() {
      this.searchInfo.app_ids = [this.appIds[0]]
      await this.getTableData(this.nFunc, this.tableDataFormat)
    },
    methods: {
      isAllChange(v) {
        if (v) {
          this.searchInfo.app_ids = this.appIds
        } else {
          this.searchInfo.app_ids = [this.appIds[0]]
        }
      },
      onReset() {
        this.searchInfo = {
          is_all: false,
          date_range: [],
          app_ids: [this.appIds[0]],
          channel: 1,
          type: 3
        }
      },
      onSubmit() {
        this.page = 1
        this.pageSize = 10
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      getSummaries({columns, data}) {
        // 获取表头和数据
        // :show-summary="this.searchInfo.group_range.length > 0"
        // :summary-method="getSummaries"
        let labelList = []
        let vList = []
        columns.forEach((column, i) => {
          labelList.push(column.label)
          vList.push(column.property)
        })
        console.log(labelList)
        console.log(vList)
      },
      handleDownload() {
        let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
        let res = this.getTableHeaderProp(tableColumns)
        let tHeader = res[0]
        let filterV = res[1]
        this.handleProgressLoading()
        import('@/utils/excel').then((excel) => {
          const data = this.formatJson(filterV, this.tableData)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.filename,
            autoWidth: this.autoWidth,
            bookType: this.bookType,
          })
          this.progressLoading.close()
        })
      },
      sizeChange(v) {
        this.pageSize = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      pageChange(v) {
        this.page = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      tableDataFormat() {
        this.tableData.map((item, i, data) => {
          data[i].date = this.formatDateYMD(item.date)
          data[i].time = this.formateSecondsHM(item.time)
          data[i].CallDurationOfPer = this.formatCRCallDurationOfPer(item)
          data[i].dau = this.numF2(item.dau)
          data[i].call_count = this.numF2(item.call_count)
          data[i].call_users = this.numF2(item.call_users)
          data[i].call_duration_str = this.formateSeconds(item.call_duration)
          data[i].CallOfPer = this.formatCRCallOfPer(item)
          data[i].CallDurationOfPer = this.formatCRCallDurationOfPer(item)
          data[i].CallPerRate = this.formatCRCallPerRate(item)
          data[i].InvalidCallRate = this.formatCRInvalidCallRate(item)
        })
      },
    },
  }
</script>

<style lang="scss" scoped>

</style>
