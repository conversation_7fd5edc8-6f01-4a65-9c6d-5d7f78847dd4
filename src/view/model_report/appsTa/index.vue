<template>
  <div>
    <el-button type="primary" @click="addAppsTa">增加</el-button>
    <div class="gva-table-box">
      <el-table
          ref="tableData"
          style="width: 100%"
          :data="tableData"
          max-height="590"
          row-key="id">
        <el-table-column align="center" label="操作" width="180">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" @click="showEditDialog(scope.row)">变更</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="APPID" prop="app_id" width="80"></el-table-column>
        <el-table-column align="center" label="APP" prop="app_name" width="180"></el-table-column>
        <el-table-column align="center" label="数数项目ID" prop="project_id" min-width="140"></el-table-column>
        <el-table-column align="center" label="AfAppId" prop="af_app_id" min-width="140"></el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            v-show="total > 0"
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            :total="total"
            @current-change="pageChange"
            @size-change="sizeChange"
        />
      </div>
      <el-dialog
          v-model="dialogVisible"
          :title="dialogTitle"
          :close-on-click-modal="false"
          width="25%"
          :before-close="dialogClose"
      >
        <el-form ref="appsTaForm" :model="appsTaForm" label-width="120px">
          <el-form-item label="产品">
            <el-select filterable v-model="appsTaForm.app_id" @change="appChange">
              <el-option
                  v-for="item in appList"
                  :key="item.id"
                  :label="`${item.app_name}(${item.id})`"
                  :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="APPID" prop="app_id">
            <el-input disabled readonly v-model.number="appsTaForm.app_id"/>
          </el-form-item>
          <el-form-item label="APP" prop="app_name">
            <el-input disabled readonly v-model="appsTaForm.app_name"/>
          </el-form-item>
          <el-form-item label="数数项目ID" prop="project_id">
            <el-input-number style="width: 100%;" v-model="appsTaForm.project_id"/>
          </el-form-item>
          <el-form-item label="AfAppId" prop="af_app_id">
            <el-input v-model="appsTaForm.af_app_id"/>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button type="primary" @click="enterDialog">保存</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {getAppsTaList, createAppsTa, updateAppsTa} from '@/api/appsTa' //  此处请自行替换地址
import infoList from '@/mixins/infoList'

export default {
  mixins: [infoList],
  data() {
    return {
      searchInfo: {},
      listApi: getAppsTaList,
      appsTaForm: {},
      dialogVisible: false,
      dialogTitle: '',
      dialogType: 1,
    }
  },
  async created() {
    this.pageSize = 100
    await this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    appChange(v) {
      this.appsTaForm.app_name = this.formatAppName(v)
    },
    addAppsTa() {
      this.dialogType = 1
      this.dialogTitle = '增加'
      this.appsTaForm = {}
      this.dialogVisible = true
    },
    showEditDialog(row) {
      this.appsTaForm = row
      this.dialogType = 2
      this.dialogTitle = '修改'
      this.dialogVisible = true
    },
    dialogClose() {
      this.dialogType = 1
      this.dialogTitle = '增加'
      this.appsTaForm = {}
    },
    enterDialog() {
      switch (this.dialogType) {
        case 1:
          createAppsTa(this.appsTaForm).then(res => {
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.dialogVisible = false
            }
          })
          break
        case 2:
          updateAppsTa(this.appsTaForm).then(res => {
            if (res.code === 0) {
              this.$message.success(res.msg)
              this.dialogVisible = false
            }
          })
          break
        default:
          this.$message.error("弹窗类型错误")
      }
    },
    onReset() {
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    onSubmit() {
      this.page = 1
      this.pageSize = 100
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
  },
}
</script>

<style lang="scss" scoped>

</style>
