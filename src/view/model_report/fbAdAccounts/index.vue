<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" ref="searchInfoRef" :model="searchInfo">
        <el-form-item label="产品名称">
          <el-select filterable v-model="searchInfo.app_id" clearable>
            <el-option
                v-for="item in appList"
                :key="item.id"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="个人号">
          <el-select filterable v-model="searchInfo.user_name" clearable>
            <el-option
                v-for="item in tokenNameList"
                :key="item"
                :label="item"
                :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="账户名称">
          <el-input clearable v-model="searchInfo.account_name" maxlength="32" placeholder="请输入"></el-input>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchInfo.state">
            <el-option label="正常" :value="1"/>
            <el-option label="封禁" :value="2"/>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="tableData"
        style="width: 100%"
        :data="tableData"
        row-key="id"
        max-height="590"
      >
        <el-table-column align="center" label="广告账户编号" prop="account_id" min-width="140"/>
        <el-table-column align="center" label="App Name" prop="app_name" min-width="140"/>
        <el-table-column align="center" label="账户名称" prop="account_name" min-width="140"/>
        <el-table-column align="center" label="个人号" prop="user_name" min-width="140"/>
        <el-table-column align="center" label="状态" prop="stateStr" min-width="140"/>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import {getFbAdAccountsList} from '@/api/fb_ad_accounts' //  此处请自行替换地址
  import {getFacebookAll} from '@/api/fbToken' //  此处请自行替换地址
  import infoList from '@/mixins/infoList'
  import callReportMixins from '@/mixins/callReport'

  export default {
    mixins: [infoList, callReportMixins],
    data() {
      return {
        searchInfo: {
          state: 1,
        },
        tokenNameList: [],
        listApi: getFbAdAccountsList,
      }
    },
    async created() {
      await this.getTableData(this.nFunc, this.tableDataFormat)
      await this.loadAllTokenName()
    },
    methods: {
      onReset() {
        this.searchInfo = {
          state: 1,
        }
      },
      loadAllTokenName() {
        getFacebookAll().then(res=>{
          if (res.code === 0) {
            console.log(res.data)
            this.tokenNameList = res.data.list || []
          }
        })
      },
      onSubmit() {
        this.page = 1
        this.pageSize = 10
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      handleDownload() {
        let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
        let res = this.getTableHeaderProp(tableColumns)
        let tHeader = res[0]
        let filterV = res[1]
        this.handleProgressLoading()
        import('@/utils/excel').then((excel) => {
          const data = this.formatJson(filterV, this.tableData)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.filename,
            autoWidth: this.autoWidth,
            bookType: this.bookType,
          })
          this.progressLoading.close()
        })
      },
      sizeChange(v) {
        this.pageSize = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      pageChange(v) {
        this.page = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      formatState(v) {
        let vStr = ""
        switch (v) {
          case 1:
            vStr = "正常"
            break
          case 2:
            vStr = "封禁"
            break
          default:
            vStr = "未知"
            break
        }
        return vStr

      },

      tableDataFormat() {
        this.tableData.map((item, i, data) => {
          data[i].stateStr = this.formatState(item.state)

        })
      },
    },
  }
</script>

<style lang="scss" scoped>

</style>
