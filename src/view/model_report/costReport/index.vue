<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="产品名称">
          <el-tooltip
            class="box-item"
            effect="dark"
            content="是否统计所有产品？"
            placement="top"
          >
            <el-switch v-model="searchInfo.is_all" @change="isAllChange"/>
          </el-tooltip>
          <el-select multiple collapse-tags v-model="searchInfo.app_ids" filterable>
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="`${item.app_name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="渠道">
          <el-select v-model="searchInfo.channel" filterable>
            <el-option label="总计数据" :value="1"/>
            <el-option label="自然量" :value="2"/>
            <el-option label="Facebook" :value="3"/>
            <el-option label="google" :value="4"/>
          </el-select>
        </el-form-item>
        <el-form-item label="颗粒度">
          <el-radio-group v-model="searchInfo.type" size="small">
            <el-radio-button :label="1">月</el-radio-button>
            <el-radio-button :label="2">周</el-radio-button>
            <el-radio-button :label="3">天</el-radio-button>
            <el-radio-button :label="4">小时</el-radio-button>
            <el-radio-button :label="5">30min</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchInfo.date_range"
            type="datetimerange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DDTHH:mm:ssZ"
            :default-time="[new Date(2022, 1, 1, 0, 0, 0),new Date(2022, 1, 1, 23, 59, 59)]"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="tableData"
        style="width: 100%"
        :data="tableData"
        row-key="id"
        max-height="590"
      >
        <el-table-column align="center" label="日期" prop="date" width="180" fixed="left"/>
        <el-table-column align="center" label="时间点" prop="time" fixed="left"/>
        <el-table-column align="center" label="总收入" prop="total_income" min-width="140"/>
        <el-table-column align="center" label="总成本" prop="total_cost" min-width="140"/>
        <el-table-column align="center" label="其他成本" prop="other_cost" min-width="140"/>
        <el-table-column align="center" label="其他成本占比" prop="OtherCostRatio" min-width="140"/>
        <el-table-column align="center" label="主播成本" prop="anchor_cost" min-width="140"/>
        <el-table-column align="center" label="节目单成本" prop="program_guide_cost" min-width="140"/>
        <el-table-column align="center" label="推广成本" prop="advertising_cost" min-width="140"/>
        <el-table-column align="center" label="归因成本" prop="attribution_cost" min-width="140"/>
        <el-table-column align="center" label="支付渠道成本" prop="pay_cost" min-width="120"/>
        <el-table-column align="center" label="通话成本" prop="call_cost" min-width="140"/>
        <el-table-column align="center" label="文字聊天成本" prop="message_cost" min-width="140"/>
        <el-table-column align="center" label="礼物成本" prop="gift_cost" min-width="140"/>
        <el-table-column align="center" label="Gpay渠道费" prop="google_pay_cost" min-width="140"/>
        <el-table-column align="center" label="Paytm渠道费" prop="paytm_cost" min-width="140"/>
        <el-table-column align="center" label="Dlocal渠道费" prop="dlocal_cost" min-width="140"/>
        <el-table-column align="center" label="payssion渠道费" prop="payssion_cost" min-width="140"/>
        <el-table-column align="center" label="coda渠道费" prop="coda_cost" min-width="140"/>
        <el-table-column align="center" label="巴基斯坦支付成本" prop="gre_pay_cost" min-width="160"/>
        <el-table-column align="center" label="liv支付成本" prop="liv_pay_cost" min-width="160"/>
        <el-table-column align="center" label="joy支付成本" prop="joy_pay_cost" min-width="160"/>
        <el-table-column align="center" label="aamirPay支付成本" prop="aamir_pay_cost" min-width="160"/>
        <el-table-column align="center" label="sunPay支付成本" prop="sun_pay_cost" min-width="160"/>
        <el-table-column align="center" label="wow支付成本" prop="wow_pay_cost" min-width="160"/>
        <el-table-column align="center" label="accpay支付成本" prop="accpay_lvy_cost" min-width="160"/>
        <el-table-column align="center" label="adkjk支付成本" prop="adkjk_cost" min-width="160"/>
        <el-table-column align="center" label="PayU支付成本" prop="pay_u_cost" min-width="160"/>
        <el-table-column align="center" label="RarPay支付成本" prop="rar_pay_cost" min-width="160"/>
        <el-table-column align="center" label="QartPay支付成本" prop="qart_pay_cost" min-width="160"/>\
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import {getCostReportList} from '@/api/costReport' //  此处请自行替换地址
  import infoList from '@/mixins/infoList'
  import costReportMixins from '@/mixins/costReport'

  export default {
    mixins: [infoList, costReportMixins],
    data() {
      return {
        searchInfo: {
          is_all: false,
          date_range: [],
          app_ids: [5,],
          channel: 1,
          type: 3
        },
        listApi: getCostReportList,
      }
    },
    async created() {
      this.searchInfo.app_ids = [this.appIds[0]]
      await this.getTableData(this.nFunc, this.tableDataFormat)
    },
    methods: {
      isAllChange(v) {
        if (v) {
          this.searchInfo.app_ids = this.appIds
        } else {
          this.searchInfo.app_ids = [this.appIds[0]]
        }
      },
      onReset() {
        this.searchInfo = {
          is_all: false,
          date_range: [],
          app_ids: [this.appIds[0]],
          channel: 1,
          type: 3
        }
      },
      onSubmit() {
        this.page = 1
        this.pageSize = 10
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      getSummaries({columns, data}) {
        // 获取表头和数据
        // :show-summary="this.searchInfo.group_range.length > 0"
        // :summary-method="getSummaries"
        let labelList = []
        let vList = []
        columns.forEach((column, i) => {
          labelList.push(column.label)
          vList.push(column.property)
        })
        console.log(labelList)
        console.log(vList)
      },
      handleDownload() {
        let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
        let res = this.getTableHeaderProp(tableColumns)
        let tHeader = res[0]
        let filterV = res[1]
        this.handleProgressLoading()
        import('@/utils/excel').then((excel) => {
          const data = this.formatJson(filterV, this.tableData)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.filename,
            autoWidth: this.autoWidth,
            bookType: this.bookType,
          })
          this.progressLoading.close()
        })
      },
      tableDataFormat() {
        this.tableData.map((item, i, data) => {
          data[i].date = this.formatDateYMD(item.date)
          data[i].time = this.formateSecondsHM(item.time)
          data[i].total_cost = this.strCheckN3F2(item.total_cost)
          data[i].total_income = this.strCheckN3F2(item.total_income)
          data[i].other_cost = this.strCheckN3F2(item.other_cost)
          data[i].OtherCostRatio = this.formateCROtherCostRatio(item)
          data[i].anchor_cost = this.strCheckN3F2(item.anchor_cost)
          data[i].advertising_cost = this.strCheckN3F2(item.advertising_cost)
          data[i].attribution_cost = this.strCheckN3F2(item.attribution_cost)
          data[i].pay_cost = this.strCheckN3F2(item.pay_cost)
          data[i].call_cost = this.strCheckN3F2(item.call_cost)
          data[i].message_cost = this.strCheckN3F2(item.message_cost)
          data[i].gift_cost = this.strCheckN3F2(item.gift_cost)
          data[i].google_pay_cost = this.strCheckN3F2(item.google_pay_cost)
          data[i].paytm_cost = this.strCheckN3F2(item.paytm_cost)
          data[i].dlocal_cost = this.strCheckN3F2(item.dlocal_cost)
          data[i].payssion_cost = this.strCheckN3F2(item.payssion_cost)
          data[i].gre_pay_cost = this.strCheckN3F2(item.gre_pay_cost)
          data[i].liv_pay_cost = this.strCheckN3F2(item.liv_pay_cost)
          data[i].joy_pay_cost = this.strCheckN3F2(item.joy_pay_cost)
          data[i].aamir_pay_cost = this.strCheckN3F2(item.aamir_pay_cost)
          data[i].sun_pay_cost = this.strCheckN3F2(item.sun_pay_cost)
          data[i].wow_pay_cost = this.strCheckN3F2(item.wow_pay_cost)
          data[i].accpay_lvy_cost = this.strCheckN3F2(item.accpay_lvy_cost)
          data[i].adkjk_cost = this.strCheckN3F2(item.adkjk_cost)
          data[i].pay_u_cost = this.strCheckN3F2(item.pay_u_cost)
          data[i].rar_pay_cost = this.strCheckN3F2(item.rar_pay_cost)
          data[i].qart_pay_cost = this.strCheckN3F2(item.qart_pay_cost)
        })
      },
      sizeChange(v) {
        this.pageSize = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      pageChange(v) {
        this.page = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
    },
  }
</script>

<style lang="scss" scoped>

</style>
