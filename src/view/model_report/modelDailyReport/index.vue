<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="产品名称">
          <el-tooltip
            class="box-item"
            effect="dark"
            content="是否统计所有产品？"
            placement="top"
          >
            <el-switch v-model="searchInfo.is_all" @change="isAllChange"/>
          </el-tooltip>
          <el-select multiple collapse-tags v-model="searchInfo.app_ids" filterable>
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="`${item.app_name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="渠道">
          <el-select v-model="searchInfo.channel" filterable>
            <el-option label="总计数据" :value="1"/>
            <el-option label="自然量" :value="2"/>
            <el-option label="Facebook" :value="3"/>
            <el-option label="google" :value="4"/>
          </el-select>
        </el-form-item>
        <el-form-item label="颗粒度">
          <el-radio-group v-model="searchInfo.type" size="small">
            <el-radio-button :label="1">月</el-radio-button>
            <el-radio-button :label="2">周</el-radio-button>
            <el-radio-button :label="3">天</el-radio-button>
            <el-radio-button :label="4">小时</el-radio-button>
            <el-radio-button :label="5">30min</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchInfo.date_range"
            type="datetimerange"
            @change="dateRangeChange"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DDTHH:mm:ssZ"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="tableData"
        style="width: 100%"
        :data="tableData"
        row-key="id"
        max-height="590"
      >
        <el-table-column align="center" label="日期" prop="date" width="120" fixed="left"/>
        <el-table-column align="center" label="时间点" prop="time" width="80" fixed="left"/>
        <el-table-column align="center" label="总收入" prop="TotalIncome" min-width="140">
          <template #default="scope">
            <div>
              <span style="margin-right: 15px">{{ scope.row.TotalIncome }}</span>
              <el-button size="mini" type="text" @click="copyText(scope.row.TotalIncome)">复制</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="总成本" prop="total_cost" min-width="140"/>
        <el-table-column align="center" label="消耗" prop="cost" min-width="120"/>
        <el-table-column align="center" label="毛利" prop="GrossProfit" min-width="140">
          <template #default="scope">
            <div>
              <span style="margin-right: 15px">{{ scope.row.GrossProfit }}</span>
              <el-button size="mini" type="text" @click="copyText(scope.row.GrossProfit)">复制</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="利润率(%)" prop="ProfitMargin" min-width="140"/>
        <!--<el-table-column align="center" label="活跃ROI要求(%)" prop="ActiveROIWant" min-width="140"/>-->
        <el-table-column align="center" label="活跃ROI(%)" prop="ActiveROI" min-width="125">
          <template #default="scope">
            <div>
              <span style="margin-right: 15px">{{ scope.row.ActiveROI }}</span>
              <el-button size="mini" type="text" @click="copyText(scope.row.ActiveROI)">复制</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="留存ROI(%)" prop="RetainedROI" min-width="140"/>
        <el-table-column align="center" label="新增ROI(%)" prop="AddROI" min-width="140"/>
        <el-table-column align="center" label="CPI" prop="CPI" min-width="140"/>
        <el-table-column align="center" label="广告注册" prop="install" min-width="140"/>
        <el-table-column align="center" label="新增付费率(%)" prop="InstallPayRate" min-width="140"/>
        <el-table-column align="center" label="新增ARPPU" prop="ARPPU" min-width="140"/>
        <el-table-column align="center" label="留存付费率(%)" prop="RetainedPayRate" min-width="140"/>
        <el-table-column align="center" label="留存ARPPU" prop="RetainedARPPU" min-width="110"/>
        <el-table-column align="center" label="新增人均付费次数" prop="NoOfAddPayPer" min-width="160"/>
        <el-table-column align="center" label="留存人均付费次数" prop="NoOfRetainedPayPer" min-width="160"/>
        <el-table-column v-if="searchInfo.channel === 1" align="center" label="通话主播数(未去重)" prop="call_anchor_count"
                         min-width="160"/>
        <el-table-column v-if="searchInfo.channel === 1" align="center" label="总通话时长(h:m:s)" prop="call_duration"
                         min-width="140"/>
        <el-table-column v-if="searchInfo.channel === 1" align="center" label="总接听率(%)" prop="CallAnswerRatio"
                         min-width="140"/>
        <el-table-column align="center" label="DAU" prop="dau" min-width="140"/>
        <el-table-column align="center" label="新客转化率(%)" prop="NewCustomerConversionRatio" min-width="140"/>
        <el-table-column align="center" label="老客复购率(%)" prop="OldCustomerPaidRatio" min-width="140"/>
        <el-table-column align="center" label="其他成本占比(%)" prop="OtherCostRatio" min-width="140"/>
        <el-table-column align="center" label="30秒以下通话占比(%)" prop="InvalidCallRatio" min-width="170"/>
        <el-table-column align="center" label="总通话次数" prop="call_count" min-width="100"/>
        <el-table-column align="center" label="人均通话时长(s)" prop="SOfCallPer" min-width="130"/>
        <el-table-column align="center" label="人均通话次数" prop="CountOfCallPer" min-width="140"/>
        <el-table-column align="center" label="新增次留人数" prop="day_one_retained" min-width="140"/>
        <el-table-column align="center" label="新增次留率" prop="day_one_rate" min-width="140"/>
        <el-table-column align="center" label="新增7留人数" prop="day_seven_retained" min-width="140"/>
        <el-table-column align="center" label="新增7留率" prop="day_seven_rate" min-width="140"/>
        <el-table-column align="center" label="历史付费用户活跃数" prop="his_pay_user_active" min-width="180"/>
        <el-table-column align="center" label="LTV付费用户数" prop="ltv_paid_users" min-width="180"/>
        <el-table-column align="center" label="新增付费次留人数" prop="new_paid_day_one_retained" min-width="180"/>
        <el-table-column align="center" label="新增付费7留人数" prop="new_paid_day_seven_retained" min-width="180"/>
        <el-table-column align="center" label="新增付费次留率" prop="new_paid_day_one_rate" min-width="180"/>
        <el-table-column align="center" label="新增付费7留率" prop="new_paid_day_seven_rate" min-width="180"/>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import {getModelDailyReportList} from '@/api/model_daily_report' //  此处请自行替换地址
  import infoList from '@/mixins/infoList'
  import modelDailyReportMixins from '@/mixins/modelDailyReport'
  import dayjs from "dayjs";

  export default {
    mixins: [infoList, modelDailyReportMixins],
    data() {
      return {
        unionIdBool: null,
        searchInfo: {
          is_all: false,
          date_range: [],
          app_ids: [5,],
          channel: 1,
          type: 3
        },
        listApi: getModelDailyReportList,
      }
    },
    async created() {
      this.searchInfo.app_ids = [this.appIds[0]]
      this.unionIdBool = this.userInfo.union_id === 0
      await this.getTableData(this.nFunc, this.tableDataFormat)
    },
    methods: {
      dateRangeChange(v) {
        if (v !== null) {
          let start = v[0]
          let end = v[1]
          let startDate = dayjs(start)
          let endDate = dayjs(end)
          let lastDate = dayjs('2023-06-14 23:59:59')
          // 如过开始时间或者结束时间大于lastDate,返回错误
          if (startDate.isAfter(lastDate) || endDate.isAfter(lastDate)) {
            this.searchInfo.date_range = []
            this.$message.error('时间不能大于2023-06-14 23:59:59')
          }
        }
      },
      isAllChange(v) {
        if (v) {
          this.searchInfo.app_ids = this.appIds
        } else {
          this.searchInfo.app_ids = [this.appIds[0]]
        }
      },
      onReset() {
        this.searchInfo = {
          is_all: false,
          date_range: [],
          app_ids: [this.appIds[0]],
          channel: 1,
          type: 3
        }
      },
      onSubmit() {
        this.page = 1
        this.pageSize = 10
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      getSummaries({columns, data}) {
        // 获取表头和数据
        // :show-summary="this.searchInfo.group_range.length > 0"
        // :summary-method="getSummaries"
        let labelList = []
        let vList = []
        columns.forEach((column, i) => {
          labelList.push(column.label)
          vList.push(column.property)
        })
        console.log(JSON.stringify(labelList))
        console.log(JSON.stringify(vList))
      },
      handleDownload() {
        let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
        let res = this.getTableHeaderProp(tableColumns)
        let tHeader = res[0]
        let filterV = res[1]
        this.handleProgressLoading()
        import('@/utils/excel').then((excel) => {
          const data = this.formatJson(filterV, this.tableData)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.filename,
            autoWidth: this.autoWidth,
            bookType: this.bookType,
          })
          this.progressLoading.close()
        })
      },
      sizeChange(v) {
        this.pageSize = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      pageChange(v) {
        this.page = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      tableDataFormat() {
        this.tableData.map((item, i, data) => {
          data[i].date = this.formatDateYMD(item.date)
          data[i].time = this.formateSecondsHM(item.time)
          data[i].call_duration = this.formateSeconds(item.call_duration)
          data[i].TotalIncome = this.formatMDRTotalIncome(item)[0]
          data[i].total_cost = this.strCheckN3F2(item.total_cost)
          data[i].GrossProfit = this.formatMDRGrossProfit(item)[0]
          data[i].ProfitMargin = this.formatMDRProfitMargin(item)

          let ActiveROIWantRes = this.formatMDRActiveROIWant(item)
          data[i].ActiveROIWant = ActiveROIWantRes[0]
          data[i].ActiveROIWantFloat = ActiveROIWantRes[1]

          let ActiveROIRes = this.formatMDRActiveROI(item)
          data[i].ActiveROI = ActiveROIRes[0]
          data[i].ActiveROIFloat = ActiveROIRes[1]

          data[i].RetainedROI = this.formatMDRRetainedROI(item)
          data[i].AddROI = this.formatMDRAddROI(item)[0]
          data[i].cost = this.strCheckN3F2(item.cost)
          data[i].install = this.strCheckN3F2(item.install)
          data[i].CPI = this.formatMDRCPI(item)
          data[i].AdvertReg = this.strCheckN3F2(item.install)
          data[i].InstallPayRate = this.formatMDRInstallPayRate(item)
          data[i].ARPPU = this.formatMDRARPPU(item)
          data[i].RetainedPayRate = this.formatMDRRetainedPayRate(item)
          data[i].RetainedARPPU = this.formatMDRRetainedARPPU(item)
          data[i].NoOfAddPayPer = this.formatMDRNoOfAddPayPer(item)
          data[i].NoOfRetainedPayPer = this.formatMDRNoOfRetainedPayPer(item)
          data[i].CallAnswerRatio = this.formatMDRCallAnswerRatio(item)
          data[i].NewCustomerConversionRatio = this.formatMDRNewCustomerConversionRatio(item)
          data[i].OldCustomerPaidRatio = this.formatMDROldCustomerPaidRatio(item)
          data[i].OtherCostRatio = this.formatMDROtherCostRatio(item)
          data[i].InvalidCallRatio = this.formatMDRInvalidCallRatio(item)
          data[i].SOfCallPer = this.formatMDRSOfCallPer(item)
          data[i].CountOfCallPer = this.formatMDRCountOfCallPer(item)
          data[i].day_one_rate = this.formatMDRDayOneRate(item)
          data[i].day_seven_rate = this.formatMDRDaySevenRate(item)
          data[i].new_paid_day_one_rate = this.formatMDRNewPaidDayOneRate(item)
          data[i].new_paid_day_seven_rate = this.formatMDRNewPaidDaySevenRate(item)


        })
      },
    },
  }
</script>

<style lang="scss" scoped>

</style>
