<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="产品名称">
          <el-tooltip
              class="box-item"
              effect="dark"
              content="是否统计所有产品？"
              placement="top"
          >
            <el-switch v-model="searchInfo.is_all" @change="isAllChange"/>
          </el-tooltip>
          <el-select multiple collapse-tags v-model="searchInfo.app_ids" filterable>
            <el-option
                v-for="item in appList"
                :key="item.id"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="渠道">
          <el-select v-model="searchInfo.channel" filterable>
            <el-option label="总计数据" :value="1"/>
            <el-option label="自然量" :value="2"/>
            <el-option label="Facebook" :value="3"/>
            <el-option label="google" :value="4"/>
          </el-select>
        </el-form-item>
        <el-form-item label="颗粒度">
          <el-radio-group v-model="searchInfo.type" size="small">
            <el-radio-button :label="1">月</el-radio-button>
            <el-radio-button :label="2">周</el-radio-button>
            <el-radio-button :label="3">天</el-radio-button>
            <el-radio-button :label="4">小时</el-radio-button>
            <el-radio-button :label="5">30min</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
              v-model="searchInfo.date_range"
              type="datetimerange"
              :shortcuts="dateRangeShortcuts"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DDTHH:mm:ssZ"
              :default-time="[new Date(2022, 1, 1, 0, 0, 0),new Date(2022, 1, 1, 23, 59, 59)]"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
          ref="tableData"
          style="width: 100%"
          :data="tableData"
          row-key="id"
          max-height="590"
      >
        <el-table-column align="center" label="日期" width="180" prop="date" fixed="left"/>
        <el-table-column align="center" label="时间点" prop="time" fixed="left"/>
        <el-table-column align="center" label="总收入" prop="TotalIncome"/>
        <el-table-column align="center" label="VIP收入" prop="vip_income"/>
        <el-table-column align="center" label="金币收入" prop="diamonds_income"/>
        <el-table-column align="center" label="总付费率(%)" prop="AllPayRate" width="140"/>
        <el-table-column align="center" label="总ARPPU" prop="AllARPPU" width="140"/>
        <el-table-column align="center" label="付费率*ARPPU" prop="PayRateARPPU" width="140"/>
        <el-table-column align="center" label="新增收入" prop="new_user_income"/>
        <el-table-column align="center" label="留存收入" prop="old_user_income"/>
        <el-table-column align="center" label="新客收入" prop="new_customer_income"/>
        <el-table-column align="center" label="老客收入" prop="old_customer_income"/>
        <el-table-column align="center" label="新增付费率(%)" prop="NewPayRate" width="140"/>
        <el-table-column align="center" label="新增ARPPU" prop="NewARPPU" width="140"/>
        <el-table-column align="center" label="新增付费率*新增ARPPU" prop="NewPayRateNewARPPU" width="220"/>
        <el-table-column align="center" label="新增人均付费次数" prop="NewPayOfPer" width="160"/>
        <el-table-column align="center" label="留存付费率(%)" prop="OldPayRate" width="140"/>
        <el-table-column align="center" label="留存ARPPU" prop="OldARPPU" width="120"/>
        <el-table-column align="center" label="留存付费率*留存ARPPU" prop="OldPayRateOldARPPU" width="220"/>
        <el-table-column align="center" label="留存人均付费次数" prop="OldPayOfPer" width="160"/>
        <el-table-column align="center" label="新客转化率(%)" prop="NewCustomerPayRate" width="140"/>
        <el-table-column align="center" label="新客ARPPU" prop="NewCustomerARPPU" width="120"/>
        <el-table-column align="center" label="新客转化率*新客ARPPU" prop="NewCustomerPayRateNewCustomerARPPU"
                         width="220"/>
        <el-table-column align="center" label="老客复购率(%)" prop="OldCustomerPayRate" width="140"/>
        <el-table-column align="center" label="老客ARPPU" prop="OldCustomerARPPU" width="120"/>
        <el-table-column align="center" label="老客复购率*老客ARPPU" prop="OldCustomerPayRateOldCustomerARPPU"
                         width="220"/>
        <el-table-column align="center" label="老客人均付费次数" prop="OldCustomerPayOfPer" width="160"/>
        <el-table-column align="center" label="Gpay收入" prop="google_income" width="120"/>
        <el-table-column align="center" label="Paytm收入" prop="paytm_income" width="120"/>
        <el-table-column align="center" label="Dlocal收入" prop="dlocal_income" width="140"/>
        <el-table-column align="center" label="payssion收入" prop="payssion_income" width="140"/>
        <el-table-column align="center" label="coda收入" prop="coda_income" width="140"/>
        <el-table-column align="center" label="巴基斯坦收入" prop="gre_income" width="140"/>
        <el-table-column align="center" label="liv收入" prop="liv_pay_income" width="140"/>
        <el-table-column align="center" label="joy收入" prop="joy_pay_income" width="140"/>
        <el-table-column align="center" label="aamirPay收入" prop="aamir_pay_income" width="140"/>
        <el-table-column align="center" label="sunPay收入" prop="sun_pay_income" width="140"/>
        <el-table-column align="center" label="wowPay收入" prop="wow_pay_income" width="140"/>
        <el-table-column align="center" label="accPay收入" prop="accpay_lvy_income" width="140"/>
        <el-table-column align="center" label="adkjkPay收入" prop="adkjk_income" width="140"/>
        <el-table-column align="center" label="payU收入" prop="pay_u_income" width="140"/>
        <el-table-column align="center" label="rarPay收入" prop="rar_pay_income" width="140"/>
        <el-table-column align="center" label="qartPay收入" prop="qart_pay_income" width="140"/>
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="pageChange"
                       @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import {getRevenueReportList} from '@/api/revenueReport' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import revenueReportMixins from '@/mixins/revenueReport'

export default {
  mixins: [infoList, revenueReportMixins],
  data() {
    return {
      unionIdBool: null,
      searchInfo: {
        is_all: false,
        date_range: [],
        app_ids: [5,],
        channel: 1,
        type: 3
      },
      listApi: getRevenueReportList,
    }
  },
  async created() {
    this.searchInfo.app_ids = [this.appIds[0]]
    this.unionIdBool = this.userInfo.union_id === 0
    await this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    isAllChange(v) {
      if (v) {
        this.searchInfo.app_ids = this.appIds
      } else {
        this.searchInfo.app_ids = [this.appIds[0]]
      }
    },
    onReset() {
      this.searchInfo = {
        is_all: false,
        date_range: [],
        app_ids: [this.appIds[0]],
        channel: 1,
        type: 3
      }
    },
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    getSummaries({columns, data}) {
      // 获取表头和数据
      // :show-summary="this.searchInfo.group_range.length > 0"
      // :summary-method="getSummaries"
      let labelList = []
      let vList = []
      columns.forEach((column, i) => {
        labelList.push(column.label)
        vList.push(column.property)
      })
      console.log(labelList)
      console.log(vList)
    },
    handleDownload() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].date = this.formatDateYMD(item.date)
        data[i].time = this.formateSecondsHM(item.time)
        data[i].TotalIncome = this.formateRRTotalIncome(item)
        data[i].vip_income = this.strCheckN3F2(item.vip_income)
        data[i].diamonds_income = this.strCheckN3F2(item.diamonds_income)
        data[i].AllPayRate = this.formateRRAllPayRate(item)[0]
        data[i].AllARPPU = this.formateRRAllARPPU(item)
        data[i].PayRateARPPU = this.formateRRPayRateARPPU(item)
        data[i].new_user_income = this.strCheckN3F2(item.new_user_income)
        data[i].old_user_income = this.strCheckN3F2(item.old_user_income)
        data[i].new_customer_income = this.strCheckN3F2(item.new_customer_income)
        data[i].old_customer_income = this.strCheckN3F2(item.old_customer_income)
        data[i].NewPayRate = this.formateRRNewPayRate(item)[0]
        data[i].NewARPPU = this.formateRRNewARPPU(item)
        data[i].NewPayRateNewARPPU = this.formateRRNewPayRateNewARPPU(item)
        data[i].NewPayOfPer = this.formateRRNewPayOfPer(item)
        data[i].OldPayRate = this.formateRROldPayRate(item)[0]
        data[i].OldARPPU = this.formateRROldARPPU(item)
        data[i].OldPayRateOldARPPU = this.formateRROldPayRateOldARPPU(item)
        data[i].OldPayOfPer = this.formateRROldPayOfPer(item)
        data[i].NewCustomerPayRate = this.formateRRNewCustomerPayRate(item)[0]
        data[i].NewCustomerARPPU = this.formateRRNewCustomerARPPU(item)
        data[i].NewCustomerPayRateNewCustomerARPPU = this.formateRRNewCustomerPayRateNewCustomerARPPU(item)
        data[i].OldCustomerPayRate = this.formateRROldCustomerPayRate(item)[0]
        data[i].OldCustomerARPPU = this.formateRROldCustomerARPPU(item)
        data[i].OldCustomerPayRateOldCustomerARPPU = this.formateRROldCustomerPayRateOldCustomerARPPU(item)
        data[i].OldCustomerPayOfPer = this.formateRROldCustomerPayOfPer(item)
        data[i].google_income = this.strCheckN3F2(item.google_income)
        data[i].paytm_income = this.strCheckN3F2(item.paytm_income)
        data[i].dlocal_income = this.strCheckN3F2(item.dlocal_income)
        data[i].payssion_income = this.strCheckN3F2(item.payssion_income)
        data[i].gre_income = this.strCheckN3F2(item.gre_income)
        data[i].liv_pay_income = this.strCheckN3F2(item.liv_pay_income)
        data[i].joy_pay_income = this.strCheckN3F2(item.joy_pay_income)
        data[i].aamir_pay_income = this.strCheckN3F2(item.aamir_pay_income)
        data[i].sun_pay_income = this.strCheckN3F2(item.sun_pay_income)
        data[i].wow_pay_income = this.strCheckN3F2(item.wow_pay_income)
        data[i].accpay_lvy_income = this.strCheckN3F2(item.accpay_lvy_income)
        data[i].adkjk_income = this.strCheckN3F2(item.adkjk_income)
        data[i].pay_u_income = this.strCheckN3F2(item.pay_u_income)
        data[i].rar_pay_income = this.strCheckN3F2(item.rar_pay_income)
        data[i].part_pay_income = this.strCheckN3F2(item.part_pay_income)
      })
    }
  },
}
</script>

<style lang="scss" scoped>

</style>
