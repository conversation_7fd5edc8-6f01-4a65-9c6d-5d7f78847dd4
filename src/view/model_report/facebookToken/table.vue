<template>
  <div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
      </div>
      <el-table
          ref="multipleTable"
          style="width: 100%"
          tooltip-effect="dark"
          :data="tableData"
          row-key="id"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="left" label="用户名" prop="user_name" min-width="120" />
        <el-table-column align="left" label="用户Token" prop="token" min-width="300" show-overflow-tooltip/>
        <el-table-column align="left" label="更新时间" prop="updated_at" min-width="180">
          <template #default="scope">
            <span>{{ formatTimestamp(scope.row.updated_at) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="过期时间" prop="expiration_at" min-width="180">
          <template #default="scope">
            <span>{{ formatTimestamp(scope.row.expiration_at) }}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="操作">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateFacebookToken(scope.row)">修改</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogForm" :before-close="closeDialog" :title="dialogTitle">
      <el-form :model="formData" label-position="right" label-width="100px">
        <el-form-item label="用户名:">
          <el-input v-model="formData.user_name" clearable placeholder="请输入" maxlength="64" show-word-limit />
        </el-form-item>
        <el-form-item label="用户Token:">
          <el-input v-model="formData.token" clearable placeholder="请输入" maxlength="255" show-word-limit/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createFacebook,
  deleteFacebook,
  updateFacebook,
  findFacebook,
  getFacebookList
} from '@/api/fbToken' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import moment from "moment";
export default {
  name: 'FacebookToken',
  mixins: [infoList],
  data() {
    return {
      listApi: getFacebookList,
      dialogForm: false,
      type: '',
      dialogTitle: '',
      deleteVisible: false,
      formData: {}
    }
  },
  async created() {
    await this.getTableData()
  },
  methods: {
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteFacebookToken(row)
      })
    },
    updateFacebookToken(row) {
      this.formData.user_name = row.user_name
      this.formData.token = row.token
      this.dialogTitle = '修改'
      this.dialogForm = true
    },
    closeDialog() {
      this.dialogForm = false
      this.formData = {
        user_name: '',
        token: '',
      }
    },
    async deleteFacebookToken(row) {
      const res = await deleteFacebook({ user_name: row.user_name })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createFacebook(this.formData)
          break
        case 'update':
          res = await updateFacebook(this.formData)
          break
        default:
          res = await createFacebook(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.dialogTitle = '创建'
      this.type = 'create'
      this.dialogForm = true
    },
    formatTimestamp(timestamp) {
      return moment(timestamp * 1000).format('YYYY-MM-DD HH:mm:ss')
    }
  },
}
</script>

<style>
</style>

