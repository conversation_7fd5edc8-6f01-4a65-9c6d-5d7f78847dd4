<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item v-if="unionIdBool" label="产品名称">
          <el-tooltip
            class="box-item"
            effect="dark"
            content="是否统计所有产品？"
            placement="top"
          >
            <el-switch v-model="searchInfo.is_all" @change="isAllChange"/>
          </el-tooltip>
          <el-select multiple collapse-tags v-model="searchInfo.app_ids" filterable>
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="`${item.app_name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="颗粒度">
          <el-radio-group v-model="searchInfo.type" size="small">
            <el-radio-button :label="1">月</el-radio-button>
            <el-radio-button :label="2">周</el-radio-button>
            <el-radio-button :label="3">天</el-radio-button>
            <el-radio-button :label="4">小时</el-radio-button>
            <el-radio-button :label="5">30min</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchInfo.date_range"
            type="datetimerange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DDTHH:mm:ssZ"
            :default-time="[new Date(2022, 1, 1, 0, 0, 0),new Date(2022, 1, 1, 23, 59, 59)]"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="tableData"
        style="width: 100%"
        :data="tableData"
        row-key="id"
        fit
        max-height="590"
      >
        <el-table-column align="center" label="日期" width="120" prop="date" fixed="left"/>
        <el-table-column align="center" label="时间点" prop="time" width="80" fixed="left"/>
        <el-table-column align="center" label="活跃主播数" prop="active_anchor_count" min-width="100"/>
        <el-table-column align="center" label="工作模式主播数" prop="worker_anchor_count" min-width="140"/>
        <el-table-column align="center" label="通话主播数" prop="call_anchor_count" min-width="100"/>
        <el-table-column align="center" label="总接听率(%)" prop="TotalAnsweringRate" min-width="100"/>
        <el-table-column align="center" label="总通话次数" prop="TotalAnsweringCount" min-width="100"/>
        <el-table-column align="center" label="30秒以上通话次数" prop="valid_calls_count" min-width="160"/>
        <el-table-column align="center" label="30秒以下通话占比(%)" prop="InvalidCallRate" min-width="180"/>
        <el-table-column align="center" label="总通话时长(h:m:s)" prop="TotalAnsweringH" min-width="140"/>
        <el-table-column align="center" label="人均通话次数" prop="NumberOfCallsPer" min-width="120"/>
        <el-table-column align="center" label="人均通话时长(min)" prop="MinOfCallsPer" min-width="140"/>
        <el-table-column align="center" label="付费通话次数" prop="pay_call_answer_count" min-width="120"/>
        <el-table-column align="center" label="付费通话时长(h:m:s)" prop="pay_call_duration" min-width="150"/>
        <el-table-column align="center" label="付费次均通话时长(s)" prop="SOfPayCallsPer" min-width="180"/>
        <el-table-column align="center" label="免费通话次数" prop="free_call_answer_count" min-width="120"/>
        <el-table-column align="center" label="免费通话时长(h:m:s)" prop="free_call_duration" min-width="150"/>
        <el-table-column align="center" label="免费通话次均时长(s)" prop="SOfFreeCallsPer" min-width="180"/>
        <el-table-column align="center" label="人均接听次数" prop="NumberOfAnswerPer" min-width="120"/>
        <el-table-column align="center" label="免费人均接听次数" prop="NumberOfFreeAnswerPer" min-width="160"/>
        <el-table-column align="center" label="付费人均接听次数" prop="NumberOfPayAnswerPer" min-width="160"/>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import {getSumCallList} from '@/api/sum_call' //  此处请自行替换地址
  import infoList from '@/mixins/infoList'
  import sumCallMixins from '@/mixins/sumCall'

  export default {
    mixins: [infoList, sumCallMixins],
    data() {
      return {
        unionIdBool: null,
        searchInfo: {
          is_all: false,
          date_range: [],
          app_ids: [27,],
          channel: 1,
          type: 3
        },
        listApi: getSumCallList,
      }
    },
    async created() {
      this.searchInfo.app_ids = [this.appIds[0]]
      this.unionIdBool = this.userInfo.union_id === 0
      await this.getTableData(this.nFunc, this.tableDataFormat)
    },
    methods: {
      isAllChange(v) {
        if (v) {
          this.searchInfo.app_ids = this.appIds
        } else {
          this.searchInfo.app_ids = [this.appIds[0]]
        }
      },
      onReset() {
        this.searchInfo = {
          is_all: false,
          date_range: [],
          app_ids: [this.appIds[0]],
          channel: 1,
          type: 3
        }
      },
      onSubmit() {
        this.page = 1
        this.pageSize = 10
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      getSummaries({columns, data}) {
        // 获取表头和数据
        // :show-summary="this.searchInfo.group_range.length > 0"
        // :summary-method="getSummaries"
        let labelList = []
        let vList = []
        columns.forEach((column, i) => {
          labelList.push(column.label)
          vList.push(column.property)
        })
        console.log(JSON.stringify(labelList))
        console.log(JSON.stringify(vList))
      },
      handleDownload() {
        let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
        let res = this.getTableHeaderProp(tableColumns)
        let tHeader = res[0]
        let filterV = res[1]
        this.handleProgressLoading()
        import('@/utils/excel').then((excel) => {
          const data = this.formatJson(filterV, this.tableData)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.filename,
            autoWidth: this.autoWidth,
            bookType: this.bookType,
          })
          this.progressLoading.close()
        })
      },
      sizeChange(v) {
        this.pageSize = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      pageChange(v) {
        this.page = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      tableDataFormat() {
        this.tableData.map((item, i, data) => {
          data[i].date = this.formatDateYMD(item.date)
          data[i].time = this.formateSecondsHM(item.time)
          data[i].TotalAnsweringRate = this.formatSCTotalAnsweringRate(item)
          data[i].TotalAnsweringCount = this.formatSCTotalAnsweringCount(item)[0]
          data[i].InvalidCallRate = this.formatSCInvalidCallRate(item)
          data[i].TotalAnsweringH = this.formatSCTotalAnsweringH(item)
          data[i].NumberOfCallsPer = this.formatSCNumberOfCallsPer(item)
          data[i].MinOfCallsPer = this.formatSCMinOfCallsPer(item)
          data[i].SOfPayCallsPer = this.formatSCSOfPayCallsPer(item)
          data[i].SOfFreeCallsPer = this.formatSCSOfFreeCallsPer(item)
          data[i].NumberOfAnswerPer = this.formatSCNumberOfAnswerPer(item)
          data[i].NumberOfFreeAnswerPer = this.formatSCNumberOfFreeAnswerPer(item)
          data[i].NumberOfPayAnswerPer = this.formatSCNumberOfPayAnswerPer(item)
          data[i].pay_call_duration = this.formateSeconds(item.pay_call_duration)
          data[i].free_call_duration = this.formateSeconds(item.free_call_duration)
        })
      },
    },
  }
</script>

<style lang="scss" scoped>

</style>
