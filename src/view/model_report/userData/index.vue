<template>
  <div>
    <div class="top">
      <el-row>
        <el-col :span="6">
          <div class="flex flex-row items-center">
            <div>
              <img class="t-icon" src="@/assets/userData/1.svg" alt="">
            </div>
            <div class="r-info">
              <el-tooltip
                effect="dark"
                content="今日新增人数"
                placement="right"
              >
                <p class="t-p">{{ topForm.today_add_count }}</p>
              </el-tooltip>
              <el-tooltip
                effect="dark"
                content="昨日新增人数"
                placement="right"
              >
                <p class="b-p">昨日新增：{{ topForm.yesterday_add_count }}</p>
              </el-tooltip>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="flex flex-row items-center">
            <div>
              <img class="t-icon" src="@/assets/userData/2.svg" alt="">
            </div>
            <div class="r-info">
              <el-tooltip
                effect="dark"
                content="今日支付次数"
                placement="right"
              >
                <p class="t-p">{{ topForm.today_pay_count }}</p>
              </el-tooltip>
              <el-tooltip
                effect="dark"
                content="昨日支付次数"
                placement="right"
              >
                <p class="b-p">昨日支付: {{ topForm.yesterday_pay_count }}</p>
              </el-tooltip>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="flex flex-row items-center">
            <div>
              <img class="t-icon" src="@/assets/userData/3.svg" alt="">
            </div>
            <div class="r-info">
              <el-tooltip
                effect="dark"
                content="今日收入"
                placement="left"
              >
                <p class="t-p">{{ topForm.today_income }}</p>
              </el-tooltip>
              <el-tooltip
                effect="dark"
                content="昨日收入"
                placement="left"
              >
                <p class="b-p">昨日金额：{{ topForm.yesterday_income }}</p>
              </el-tooltip>
            </div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="flex flex-row items-center">
            <div>
              <img class="t-icon" src="@/assets/userData/4.svg" alt="">
            </div>
            <div class="r-info">
              <el-tooltip
                effect="dark"
                content="本月收入"
                placement="left"
              >
                <p class="t-p">{{ topForm.month_income }}</p>
              </el-tooltip>
              <el-tooltip
                effect="dark"
                content="上月收入"
                placement="left"
              >
                <p class="b-p">上月金额：{{ topForm.last_month_income }}</p>
              </el-tooltip>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="18">
          <div ref="chartOne" class="recent-income-echarts"></div>
        </el-col>
        <el-col :span="6">
          <div class="flex flex-col">
            <div class="flex justify-between">
              <el-tooltip effect="dark" placement="left" content="在线主播数量">
                <div class="i-item flex items-center">
                  <div>
                    <img class="l-icon" src="@/assets/userData/zxyh.svg" alt="">
                  </div>
                  <div class="r-txt">
                    <p class="t1">{{ topUserForm.online_anchor }}</p>
                    <p class="t2">在线主播</p>
                  </div>
                </div>
              </el-tooltip>
              <el-tooltip effect="dark" placement="left" content="今日付费:今天有付费的人数">
                <div class="i-item flex items-center">
                  <div>
                    <img class="l-icon" src="@/assets/userData/zxnyh.svg" alt="">
                  </div>
                  <div class="r-txt">
                    <p class="t1">{{ topUserForm.pay_user }}</p>
                    <p class="t2">今日付费</p>
                  </div>
                </div>
              </el-tooltip>
            </div>
            <div class="flex justify-between">
              <el-tooltip effect="dark" placement="left" content="今日活跃:付费用户在今日活跃的人数">
                <div class="i-item flex items-center">
                  <div>
                    <img class="l-icon" src="@/assets/userData/zxzb.svg" alt="">
                  </div>
                  <div class="r-txt">
                    <p class="t1">{{ topUserForm.pay_user_active }}</p>
                    <p class="t2">今日活跃</p>
                  </div>
                </div>
              </el-tooltip>
              <el-tooltip effect="dark" placement="left" content="今日新增:今天新增用户付费的人数">
                <div class="i-item flex items-center">
                  <div>
                    <img class="l-icon" src="@/assets/userData/sfzb.svg" alt="">
                  </div>
                  <div class="r-txt">
                    <p class="t1">{{ topUserForm.new_user_pay }}</p>
                    <p class="t2">今日新增</p>
                  </div>
                </div>
              </el-tooltip>
            </div>
            <div class="flex justify-between">
              <el-tooltip effect="dark" placement="left" content="本月付费:本月新增的用户在本月付费的金额">
                <div class="i-item flex items-center">
                  <div>
                    <img class="l-icon" src="@/assets/userData/thzyh.svg" alt="">
                  </div>
                  <div class="r-txt">
                    <p class="t1">{{ topUserForm.month_price }}</p>
                    <p class="t2">本月付费</p>
                  </div>
                </div>
              </el-tooltip>
              <el-tooltip effect="dark" placement="left" content="历史付费:在本月之前注册的在本月付费的金额">
                <div class="i-item flex items-center">
                  <div>
                    <img class="l-icon" src="@/assets/userData/ffnyh.svg" alt="">
                  </div>
                  <div class="r-txt">
                    <p class="t1">{{ topUserForm.month_ago_price }}</p>
                    <p class="t2">历史付费</p>
                  </div>
                </div>
              </el-tooltip>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <div ref="chartTwo" class="order-count-echarts"></div>
        </el-col>
        <el-col :span="12">
          <div ref="chartThree" class="usd-price-echarts"></div>
        </el-col>
      </el-row>
      <el-row>
        <el-col :span="12">
          <div class="new-add-count-echarts">
            <div ref="chartAdd" class="add-echarts"></div>
          </div>
        </el-col>
        <el-col :span="12">
          <div ref="chartFour" class="order-pay-count-echarts"></div>
        </el-col>
      </el-row>

    </div>
  </div>
</template>

<script>
import {markRaw} from 'vue'
import {topInfo, recentIncome, dayHourOrderCount, dayHourUserCount, topUserInfo} from '@/api/homePage' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import * as echarts from 'echarts'

export default {
  mixins: [infoList],
  data() {
    return {
      topUserForm: {},
      topForm: {
        today_add_count: null,
        yesterday_add_count: null,
        today_pay_count: null,
        yesterday_pay_count: null,
        today_income: null,
        yesterday_income: null,
        month_income: null,
        last_month_income: null
      },
      recentIncomeForm: {},
      dayHourForm: {},
      dayUserCountHourForm: {},
      echartsOne: null,
      echartsTwo: null,
      echartsThree: null,
      echartsFour: null,
      chartAdd: null,
    }
  },
  async created() {
    this.initApiRes()
  },
  methods: {
    initApiRes() {
      this.initTopInfo()
      this.initTopUserInfo()
      this.initRecentIncome()
      this.initDayHourOrderCount()
      this.initDayHourUserCount()
    },
    initTopInfo() {
      topInfo().then(res => {
        if (res.code === 0) {
          this.topForm = res.data || {}
        }
      })
    },
    initTopUserInfo() {
      topUserInfo().then(res => {
        if (res.code === 0) {
          this.topUserForm = res.data || {}
        }
      })
    },
    initRecentIncome() {
      recentIncome().then(res => {
        if (res.code === 0) {
          let resData = res.data || {}
          this.recentIncomeForm = resData
          let optionData = {
            title: {
              text: '用户数据'
            },
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              data: resData.legendData
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: resData.xAxisData
            },
            yAxis: {
              type: 'value'
            },
            series: resData.seriesData
          }
          this.echartsOne = markRaw(echarts.init(this.$refs.chartOne))
          this.echartsOne.setOption(optionData)
        }
      })
    },
    initDayHourOrderCount() {
      dayHourOrderCount().then(res => {
        if (res.code === 0) {
          let resData = res.data || {}
          this.dayHourForm = resData

          let orderCountOption = {
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              data: resData.orderCountMap.legendData
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: resData.orderCountMap.xAxisData
            },
            yAxis: {
              type: 'value'
            },
            series: resData.orderCountMap.seriesData
          }
          let orderPayCountOption = {
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              data: resData.orderPayCountMap.legendData
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: resData.orderPayCountMap.xAxisData
            },
            yAxis: {
              type: 'value'
            },
            series: resData.orderPayCountMap.seriesData
          }
          let usdPriceOption = {
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              data: resData.usdPriceMap.legendData
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: resData.usdPriceMap.xAxisData
            },
            yAxis: {
              type: 'value'
            },
            series: resData.usdPriceMap.seriesData
          }

          this.echartsTwo = markRaw(echarts.init(this.$refs.chartTwo)) // orderCount
          this.echartsTwo.setOption(orderCountOption)
          this.echartsThree = markRaw(echarts.init(this.$refs.chartThree)) // orderCount
          this.echartsThree.setOption(usdPriceOption)
          this.echartsFour = markRaw(echarts.init(this.$refs.chartFour)) // orderCount
          this.echartsFour.setOption(orderPayCountOption)

        }
      })
    },
    initDayHourUserCount() {
      dayHourUserCount().then(res => {
        if (res.code === 0) {
          let resData = res.data || {}
          this.dayUserCountHourForm = resData
          let userCountOption = {
            tooltip: {
              trigger: 'axis'
            },
            legend: {
              data: resData.userCountMap.legendData
            },
            grid: {
              left: '3%',
              right: '4%',
              bottom: '3%',
              containLabel: true
            },
            xAxis: {
              type: 'category',
              boundaryGap: false,
              data: resData.userCountMap.xAxisData
            },
            yAxis: {
              type: 'value'
            },
            series: resData.userCountMap.seriesData
          }
          this.chartAdd = markRaw(echarts.init(this.$refs.chartAdd)) // orderCount
          this.chartAdd.setOption(userCountOption)

        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>

.t-icon {
  width: 90px;
  height: 45px;
}

.r-info {
  margin-left: 6px;

  .t-p {
    font-size: 24px;
    font-weight: bold;
  }

  .b-p {
    font-size: 16px;
  }

}

.recent-income-echarts {
  height: 200px;
}

.order-count-echarts {
  height: 200px;
}

.usd-price-echarts {
  height: 200px;
}

.new-add-count-echarts {
  height: 200px;
}

.order-pay-count-echarts {
  height: 200px;
}

.add-echarts {
  height: 200px;
}

.i-item {
  width: 200px;
  display: flex;

  .l-icon {
    width: 30px;
    height: 30px;
  }

  .r-txt {
    margin-left: 12px;

    .t1 {
      font-size: 22px;
      font-weight: bold;
    }

    .t2 {
      font-size: 14px;
      color: #00afff;

    }
  }
}
</style>
