<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item v-if="unionIdBool" label="产品名称">
          <el-tooltip
            class="box-item"
            effect="dark"
            content="是否统计所有产品？"
            placement="top"
          >
            <el-switch v-model="searchInfo.is_all" @change="isAllChange"/>
          </el-tooltip>
          <el-select multiple collapse-tags v-model="searchInfo.app_ids" filterable>
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="`${item.app_name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item v-if="unionIdBool" label="渠道">
          <el-select v-model="searchInfo.channel" filterable>
            <el-option label="总计数据" :value="1"/>
            <el-option label="自然量" :value="2"/>
            <el-option label="Facebook" :value="3"/>
            <el-option label="google" :value="4"/>
          </el-select>
        </el-form-item>
        <el-form-item label="颗粒度">
          <el-radio-group v-model="searchInfo.type" size="small">
            <el-radio-button :label="1">月</el-radio-button>
            <el-radio-button :label="2">周</el-radio-button>
            <el-radio-button :label="3">天</el-radio-button>
            <el-radio-button :label="4">小时</el-radio-button>
            <el-radio-button :label="5">30min</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="searchInfo.date_range"
            type="datetimerange"
            :shortcuts="dateRangeShortcuts"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DDTHH:mm:ssZ"
            :default-time="[new Date(2022, 1, 1, 0, 0, 0),new Date(2022, 1, 1, 23, 59, 59)]"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
        ref="tableData"
        style="width: 100%"
        :data="tableData"
        row-key="id"
        max-height="590"
      >
        <el-table-column align="center" label="日期" prop="date" width="120" fixed="left"/>
        <el-table-column align="center" label="时间点" prop="time" width="80" fixed="left"/>
        <el-table-column align="center" label="消耗" prop="cost" min-width="140"/>
        <el-table-column align="center" label="Gpay收入" prop="google_pay_revenue" min-width="140"/>
        <el-table-column align="center" label="Paytm收入" prop="paytm_revenue" min-width="140"/>
        <el-table-column align="center" label="Dlocal收入" prop="dlocal_revenue" min-width="140"/>
        <el-table-column align="center" label="Payssion收入" prop="payssion_revenue" min-width="140"/>
        <el-table-column align="center" label="coda收入" prop="coda_pay_revenue" min-width="140"/>
        <el-table-column align="center" label="巴基斯坦支付收入" prop="gre_pay_revenue" min-width="160"/>
        <el-table-column align="center" label="live收入" prop="liv_pay_revenue" min-width="160"/>
        <el-table-column align="center" label="joy收入" prop="joy_pay_revenue" min-width="160"/>
        <el-table-column align="center" label="aamirPay收入" prop="aamir_pay_revenue" min-width="160"/>
        <el-table-column align="center" label="sunPay收入" prop="sun_pay_revenue" min-width="160"/>
        <el-table-column align="center" label="wowPay收入" prop="wow_pay_revenue" min-width="160"/>
        <el-table-column align="center" label="accPay收入" prop="accpay_lvy_revenue" min-width="160"/>
        <el-table-column align="center" label="adkjkPay收入" prop="adkjk_revenue" min-width="160"/>
        <el-table-column align="center" label="PayU收入" prop="pay_u_revenue" min-width="160"/>
        <el-table-column align="center" label="RarPay收入" prop="rar_pay_revenue" min-width="160"/>
        <el-table-column align="center" label="QartPay收入" prop="qart_pay_revenue" min-width="160"/>
        <el-table-column align="center" label="新增激活" prop="activation_devices" min-width="140"/>
        <el-table-column align="center" label="新增注册" prop="dnu" min-width="140"/>
        <el-table-column align="center" label="DAU" prop="dau" min-width="140"/>
        <el-table-column align="center" label="总付费人数" prop="ARAllPayUserCount" min-width="140"/>
        <el-table-column align="center" label="总付费次数" prop="ARAllPayCount" min-width="140"/>
        <el-table-column align="center" label="新增付费次数" prop="new_paid_count" min-width="140"/>
        <el-table-column align="center" label="新增付费人数" prop="new_paid_users" min-width="140"/>
        <el-table-column align="center" label="新增收入" prop="new_revenue" min-width="140"/>

      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import {getAppReportList} from '@/api/appReport' //  此处请自行替换地址
  import infoList from '@/mixins/infoList'
  import appReportMixins from '@/mixins/appReport'

  export default {
    mixins: [infoList, appReportMixins],
    data() {
      return {
        unionIdBool: null,
        searchInfo: {
          is_all: false,
          date_range: [],
          app_ids: [],
          channel: 1,
          type: 3
        },
        listApi: getAppReportList,
      }
    },
    async created() {
      this.searchInfo.app_ids = [this.appIds[0]]
      this.unionIdBool = this.userInfo.union_id === 0
      await this.getTableData(this.nFunc, this.tableDataFormat)
    },
    methods: {
      isAllChange(v) {
        if (v) {
          this.searchInfo.app_ids = this.appIds
        } else {
          this.searchInfo.app_ids = [this.appIds[0]]
        }
      },
      onReset() {
        this.searchInfo = {
          is_all: false,
          date_range: [],
          app_ids: [this.appIds[0]],
          channel: 1,
          type: 3
        }
      },
      onSubmit() {
        this.page = 1
        this.pageSize = 10
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      sizeChange(v) {
        this.pageSize = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      pageChange(v) {
        this.page = v
        this.getTableData(this.nFunc, this.tableDataFormat)
      },
      tableDataFormat() {
        this.tableData.map((item, i, data) => {
          data[i].date = this.formatDateYMD(item.date)
          data[i].time = this.formateSecondsHM(item.time)
          data[i].cost = this.strCheckN3F2(item.cost)
          data[i].google_pay_revenue = this.strCheckN3F2(item.google_pay_revenue)
          data[i].paytm_revenue = this.strCheckN3F2(item.paytm_revenue)
          data[i].dlocal_revenue = this.strCheckN3F2(item.dlocal_revenue)
          data[i].payssion_revenue = this.strCheckN3F2(item.payssion_revenue)
          data[i].ARAllPayUserCount = this.formatARAllPayUserCount(item)
          data[i].ARAllPayCount = this.formatARAllPayCount(item)
          data[i].new_revenue = this.strCheckN3F2(item.new_revenue)
          data[i].gre_pay_revenue = this.strCheckN3F2(item.gre_pay_revenue)
          data[i].liv_pay_revenue = this.strCheckN3F2(item.liv_pay_revenue)
          data[i].joy_pay_revenue = this.strCheckN3F2(item.joy_pay_revenue)
          data[i].aamir_pay_revenue = this.strCheckN3F2(item.aamir_pay_revenue)
          data[i].sun_pay_revenue = this.strCheckN3F2(item.sun_pay_revenue)
          data[i].wow_pay_revenue = this.strCheckN3F2(item.wow_pay_revenue)
          data[i].accpay_lvy_revenue = this.strCheckN3F2(item.accpay_lvy_revenue)
          data[i].adkjk_revenue = this.strCheckN3F2(item.adkjk_revenue)
          data[i].pay_u_revenue = this.strCheckN3F2(item.pay_u_revenue)
          data[i].rar_pay_revenue = this.strCheckN3F2(item.rar_pay_revenue)
          data[i].qart_pay_revenue = this.strCheckN3F2(item.qart_pay_revenue)
        })
      },
      getSummaries({columns, data}) {
        // 获取表头和数据
        // :show-summary="this.searchInfo.group_range.length > 0"
        // :summary-method="getSummaries"
        let labelList = []
        let vList = []
        columns.forEach((column, i) => {
          labelList.push(column.label)
          vList.push(column.property)
        })
        console.log(JSON.stringify(labelList))
        console.log(JSON.stringify(vList))
      },
      handleDownload() {
        let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
        let res = this.getTableHeaderProp(tableColumns)
        let tHeader = res[0]
        let filterV = res[1]
        this.handleProgressLoading()
        import('@/utils/excel').then((excel) => {
          const data = this.formatJson(filterV, this.tableData)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.filename,
            autoWidth: this.autoWidth,
            bookType: this.bookType,
          })
          this.progressLoading.close()
        })
      },

    },
  }
</script>

<style lang="scss" scoped>

</style>
