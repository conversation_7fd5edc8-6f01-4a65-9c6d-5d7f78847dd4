<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="日期范围">
          <el-date-picker v-model="searchInfo.date_range" type="daterange" :shortcuts="dateRangeShortcuts"
                          value-format="YYYY-MM-DD" format="YYYY-MM-DD"/>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload('tableDataRef')">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table ref="tableDataRef" class="w100" :data="tableData" row-key="id" :max-height="590" border>
        <el-table-column align="center" :width="120" label="日期" prop="date" fixed="left"/>
        <el-table-column align="center" :width="120" label="总收入" prop="totalIncome">
          <template #default="scope">
            <div class="c-p blue" @click="showTotalIncomeDialog(scope.row)">
              <span>{{ scope.row.totalIncome }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" :width="120" label="总成本" prop="totalCost">
          <template #default="scope">
            <div class="c-p blue" @click="showCostDialog(scope.row)">
              <span>{{ scope.row.totalCost }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" :width="120" label="利润" prop="profit"></el-table-column>
        <el-table-column align="center" :width="120" label="充值收入" prop="totalOrderRevenue">
          <template #default="scope">
            <div class="c-p blue" @click="showOrderRevenueDialog(scope.row)">
              <span>{{ scope.row.totalOrderRevenue }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" :width="120" label="广告收入" prop="ad_revenue">
          <template #default="scope">
            <div class="c-p blue" @click="showAdRevenueDialog(scope.row)">
              <span>{{ scope.row.ad_revenue }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" :width="120" label="投放消耗" prop="ad_cost">
          <template #default="scope">
            <div class="c-p blue" @click="showAdCostDialog(scope.row)">
              <span>{{ scope.row.ad_cost }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" :width="120" label="DAU_ROI" prop="DAU_ROI"></el-table-column>
        <el-table-column align="center" :width="120" label="新增ROI" prop="newInstallROI"></el-table-column>
        <el-table-column align="center" :width="120" label="广告占比" prop="adRate"></el-table-column>
        <el-table-column align="center" :width="120" label="新增收入" prop="totalNewUserRevenue">
          <template #default="scope">
            <div class="c-p blue" @click="showNewUserRevenueDialog(scope.row)">
              <span>{{ scope.row.totalNewUserRevenue }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" :width="120" label="留存收入" prop="totalRetentionRevenue">
          <template #default="scope">
            <div class="c-p blue" @click="showRetentionRevenueDialog(scope.row)">
              <span>{{ scope.row.totalRetentionRevenue }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" :width="120" label="毛利润率" prop="grossProfitRate"></el-table-column>


        <el-table-column align="center" :width="120" label="新增用户" prop="installs"></el-table-column>
        <el-table-column align="center" :width="120" label="DAU用户" prop="dau"></el-table-column>
        <el-table-column align="center" :width="120" label="新增付费率" prop="newPayRate"></el-table-column>
        <el-table-column align="center" :width="120" label="新增ARPPU" prop="newARPPU"></el-table-column>
        <el-table-column align="center" :width="160" label="新增人均付费次数" prop="newPayCountPer"></el-table-column>
        <el-table-column align="center" :width="120" label="留存付费率" prop="retentionPayRate"></el-table-column>
        <el-table-column align="center" :width="120" label="留存ARPPU" prop="retentionARPPU"></el-table-column>
        <el-table-column align="center" :width="160" label="留存人均付费次数" prop="retentionPayCountPer"></el-table-column>
        <!-- <el-table-column align="center" :width="120" label="老客复购率" prop="repurchaseRate"></el-table-column> -->
        <el-table-column align="center" :width="120" label="新增付费" prop="newPayUserCount"></el-table-column>
        <el-table-column align="center" :width="120" label="今日付费" prop="todayPayUserCount"></el-table-column>
        <!-- <el-table-column align="center" :width="120" label="活跃付费" prop="activePayUserCount"></el-table-column> -->
        <el-table-column align="center" :width="120" label="留存付费" prop="retentionPayUserCount"></el-table-column>


        

        <el-table-column align="center" :width="120" label="主播DAU" prop="totalAnchorDau">
          <template #default="scope">
            <div class="c-p blue" @click="showAnchorDauDialog(scope.row)">
              <span>{{ scope.row.totalAnchorDau }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" :width="120" label="电话数量" prop="totalCallCounts">
          <template #default="scope">
            <div class="c-p blue" @click="showCallCountsDialog(scope.row)">
              <span>{{ scope.row.totalCallCounts }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" :width="120" label="消息条数" prop="totalMessageCounts">
          <template #default="scope">
            <div class="c-p blue" @click="showMessageCountsDialog(scope.row)">
              <span>{{ scope.row.totalMessageCounts }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" :width="120" label="礼物个数" prop="totalGiftCounts">
          <template #default="scope">
            <div class="c-p blue" @click="showGiftCountsDialog(scope.row)">
              <span>{{ scope.row.totalGiftCounts }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" :width="120" label="钻石消耗" prop="totalDiamondDetail">
          <template #default="scope">
            <div class="c-p blue" @click="showDiamondDetailDialog(scope.row)">
              <span>{{ scope.row.totalDiamondDetail }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" :width="120" label="卡片消耗" prop="totalCardDetail">
          <template #default="scope">
            <div class="c-p blue" @click="showCardDetailDialog(scope.row)">
              <span>{{ scope.row.totalCardDetail }}</span>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          v-show="total > 0"
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="pageChange"
          @size-change="sizeChange"
        />
      </div>
    </div>
    <!--弹窗-->
    <div>
      <el-dialog v-model="payRevenueDialogVisible" title="总收入" top="2px">
        <el-button type="success" @click="tableExport('payRevenueTableDataRef')">导出</el-button>
        <el-table ref="payRevenueTableDataRef" class="w100" :data="payRevenueTableData" :max-height="590" border>
          <el-table-column align="center" :min-width="120" label="支付方式" prop="name"/>
          <el-table-column align="center" :min-width="120" label="金额" prop="income" sortable/>
          <el-table-column align="center" :min-width="120" label="占比" prop="rate"/>
        </el-table>
      </el-dialog>
      <el-dialog v-model="orderRevenueDialogVisible" title="充值收入" top="2px">
        <!--order_revenue key是商品类型,product_type-->
        <el-button type="success" @click="tableExport('orderRevenueTableDataRef')">导出</el-button>
        <el-table ref="orderRevenueTableDataRef" class="w100" :data="orderRevenueTableData" :max-height="590" border>
          <el-table-column align="center" :min-width="120" label="类型" prop="name"/>
          <el-table-column align="center" :min-width="120" label="金额" prop="income" sortable/>
          <el-table-column align="center" :min-width="120" label="占比" prop="rate"/>
        </el-table>
      </el-dialog>
      <el-dialog v-model="adRevenueDialogVisible" title="广告收入" top="2px">
        <el-form :model="adRevenueFormData">
          <el-form-item label="广告收入">
            <el-input-number v-model="adRevenueFormData.ad_revenue" class="w100"></el-input-number>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button type="primary" @click="submitUpdateAdRevenue">提交</el-button>
        </template>
      </el-dialog>
      <el-dialog v-model="adCostDialogVisible" title="投放消耗" top="2px">
        <!--ad_cost 只有总消耗,具体的消耗在AdCostReport表-->
        <el-button type="success" @click="tableExport('adCostTableDataRef')">导出</el-button>
        <el-table ref="adCostTableDataRef" class="w100" :data="adCostTableData" :max-height="590" border>
          <el-table-column align="center" :min-width="120" label="渠道" prop="media_source" sortable/>
          <el-table-column align="center" :min-width="120" label="消耗" prop="cost" sortable/>
          <el-table-column align="center" :min-width="120" label="安装" prop="install" sortable/>
          <el-table-column align="center" :min-width="120" label="付费率" prop="paid_rate_str"/>
        </el-table>
      </el-dialog>
      <el-dialog v-model="newUserRevenueDialogVisible" title="新增收入" top="2px">
        <!--new_user_revenue key是商品类型,product_type-->
        <el-button type="success" @click="tableExport('newUserRevenueTableDataRef')">导出</el-button>
        <el-table ref="newUserRevenueTableDataRef" class="w100" :data="newUserRevenueTableData" :max-height="590"
                  border>
          <el-table-column align="center" :min-width="120" label="类型" prop="name"/>
          <el-table-column align="center" :min-width="120" label="金额" prop="income" sortable/>
          <el-table-column align="center" :min-width="120" label="占比" prop="rate"/>
        </el-table>
      </el-dialog>
      <el-dialog v-model="retentionRevenueDialogVisible" title="留存收入" top="2px">
        <!--retention_revenue key是商品类型,product_type-->
        <el-button type="success" @click="tableExport('retentionRevenueTableDataRef')">导出</el-button>
        <el-table ref="retentionRevenueTableDataRef" class="w100" :data="retentionRevenueTableData" :max-height="590"
                  border>
          <el-table-column align="center" :min-width="120" label="类型" prop="name"/>
          <el-table-column align="center" :min-width="120" label="金额" prop="income" sortable/>
          <el-table-column align="center" :min-width="120" label="占比" prop="rate"/>
        </el-table>
      </el-dialog>
      <el-dialog v-model="anchorDauDialogVisible" title="主播DAU" top="2px">
        <!--anchor_dau-->
        <el-button type="success" @click="tableExport('anchorDauTableDataRef')">导出</el-button>
        <el-table ref="anchorDauTableDataRef" class="w100" :data="anchorDauTableData" :max-height="590" border>
          <el-table-column align="center" :min-width="120" label="国家" prop="id"/>
          <el-table-column align="center" :min-width="120" label="数量" prop="income" sortable/>
          <el-table-column align="center" :min-width="120" label="占比" prop="rate"/>
        </el-table>
      </el-dialog>
      <el-dialog v-model="messageCountsDialogVisible" title="消息条数" top="2px">
        <!--message_counts-->
        <el-button type="success" @click="tableExport('messageCountsTableDataRef')">导出</el-button>
        <el-table ref="messageCountsTableDataRef" class="w100" :data="messageCountsTableData" :max-height="590"
                  border>
          <el-table-column align="center" :min-width="120" label="类型" prop="name"/>
          <el-table-column align="center" :min-width="120" label="条数" prop="count" sortable/>
          <el-table-column align="center" :min-width="120" label="金币" prop="diamonds" sortable/>
        </el-table>
      </el-dialog>
      <el-dialog v-model="giftCountsDialogVisible" title="礼物个数" top="2px">
        <!--gift_counts-->
        <div class="flex justify-between items-center">
          <p>礼物个数:{{ giftCountsDictData['count'] }}, 钻石:{{ giftCountsDictData['diamonds'] }}</p>
          <el-button type="success" @click="tableExport('giftCountsTableDataRef')">导出</el-button>
        </div>
        <el-table ref="giftCountsTableDataRef" class="w100" :data="giftCountsDictData['list']" :max-height="590" border>
          <el-table-column align="center" :min-width="120" label="礼物ID" prop="id" sortable/>
          <el-table-column align="center" :min-width="120" label="个数" prop="count" sortable/>
          <el-table-column align="center" :min-width="120" label="钻石" prop="diamonds" sortable/>
          <el-table-column align="center" :min-width="120" label="个数占比" prop="count_rate"/>
          <el-table-column align="center" :min-width="120" label="钻石占比" prop="diamonds_rate"/>
        </el-table>
      </el-dialog>
      <el-dialog v-model="diamondDetailDialogVisible" title="钻石消耗" top="2px">
        <!--diamond_detail trade_record 里的trade_type-->
        <div v-for="(item,index) in diamondDetailListData" :key="index">
          <div class="flex justify-between items-center">
            <p>{{item.keyName}}: {{item.keyTotal}}</p>
            <el-button type="success" @click="tableExport(item.keyRef)">导出</el-button>
          </div>
          <div :id="item.keyRef">
            <el-table :ref="item.keyRef" class="w100" :data="item.keyData" :max-height="590" border>
              <el-table-column align="center" :min-width="120" label="类型" prop="oKeyName"/>
              <el-table-column align="center" :min-width="120" label="金额" prop="income"/>
              <el-table-column align="center" :min-width="120" label="占比" prop="rate"/>
            </el-table>
          </div>
        </div>
      </el-dialog>
      <el-dialog v-model="cardDetailDialogVisible" title="卡片消耗" top="2px">
        <!--card_detail-->
        <el-button type="success" @click="tableExport('retentionRevenueTableDataRef')">导出</el-button>
        <el-table ref="retentionRevenueTableDataRef" class="w100" :data="cardDetailTableData" :max-height="590"
                  border>
          <el-table-column align="center" :min-width="120" label="类型" prop="id"/>
          <el-table-column align="center" :min-width="120" label="金额" prop="income" sortable/>
          <el-table-column align="center" :min-width="120" label="占比" prop="rate"/>
        </el-table>
      </el-dialog>
      <el-dialog v-model="callCountsDialogVisible" title="电话数量" top="2px">
        <!--call_counts-->
        <el-button type="success" @click="tableExport('callCountsTableDataRef')">导出</el-button>
        <el-table ref="callCountsTableDataRef" class="w100" :data="callCountsTableData" :max-height="590" border>
          <el-table-column align="center" :min-width="120" label="等级" prop="id"/>
          <el-table-column align="center" :min-width="120" label="数量" prop="income" sortable/>
          <el-table-column align="center" :min-width="120" label="占比" prop="rate"/>
        </el-table>
      </el-dialog>
      <el-dialog v-model="costDialogVisible" title="总成本" top="2px">
        <!--cost-->
        <el-button type="success" @click="tableExport('costTableDataRef')">导出</el-button>
        <el-table ref="costTableDataRef" class="w100" :data="costTableData" :max-height="590" border>
          <el-table-column align="center" :min-width="120" label="类型" prop="name"/>
          <el-table-column align="center" :min-width="120" label="金额" prop="income" sortable/>
          <el-table-column align="center" :min-width="120" label="占比" prop="rate"/>
        </el-table>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {getDailyReportList, updateAdRevenue} from '@/api/dailyReport' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import {utils as xlsxUtils, write as xlsxWrite} from 'xlsx'
import FileSaver from 'file-saver'
import {getProductsType} from "@/api/products";
import {getDayRes} from "@/api/adCostReport";
import dayjs from "dayjs";

export default {
  mixins: [infoList],
  data() {
    return {
      searchInfo: {
        is_all: false,
      },
      adRevenueDialogVisible: false,
      adRevenueFormData: {},
      payRevenueDialogVisible: false,
      payRevenueTableData: [],
      orderRevenueDialogVisible: false,
      orderRevenueTableData: [],
      adCostDialogVisible: false,
      adCostTableData: [],
      newUserRevenueDialogVisible: false,
      newUserRevenueTableData: [],
      retentionRevenueDialogVisible: false,
      retentionRevenueTableData: [],
      anchorDauDialogVisible: false,
      anchorDauTableData: [],
      messageCountsDialogVisible: false,
      messageCountsTableData: [],
      giftCountsDialogVisible: false,
      giftCountsDictData: {},
      diamondDetailDialogVisible: false,
      diamondDetailListData: [],
      cardDetailDialogVisible: false,
      cardDetailTableData: [],
      callCountsDialogVisible: false,
      callCountsTableData: [],
      costDialogVisible: false,
      costTableData: [],
      productTypeDict: {},
      msgTypeDict: {
        0: 'Total',
        1: '免费消息',
        2: '付费消息',
      },
      costTypeDict: {
        0: 'Total',
        1: '消耗(AdCost)',
        2: '支付(PayCost)',
        3: '归因(AttributionCost)',
        4: '主播(AnchorCost)',
        100: '其他(OtherCost)',
      },
      diamondDetailTypeDict: {
        'charge': '充值(charge)',
        'consume': '消耗(consume)',
        'others': '其他(others)',
      },
      listApi: getDailyReportList,
    }
  },
  async created() {
    await this.getTableData(this.nFunc, this.tableDataFormat)
    this.initProductTypeList()
  },
  methods: {
    showTotalIncomeDialog(row) {
      let objMap = row.pay_revenue || {}
      let objTotal = objMap[0]['revenue'] || 0
      let objList = []
      let oKeys = Object.keys(objMap).sort().reverse()
      oKeys.forEach((key) => {
        if (key !== '0' && key !== 0) {
          let rate = this.percent2(objMap[key]['revenue'], objTotal)
          let payName = this.formatPayWayName(key)
          let detailObj = {
            id: key,
            name: payName,
            income: this.numF2(objMap[key]['revenue']),
            rate: rate
          }
          objList.push(detailObj)
        }
      })
      this.payRevenueDialogVisible = true
      this.payRevenueTableData = objList
    },
    showCostDialog(row) {
      let objMap = row.cost || {}
      let objTotal = objMap[0]
      let objList = []
      let oKeys = Object.keys(objMap)
      oKeys.forEach((key) => {
        if (key !== '0' && key !== 0) {
          let rate = this.percent2(objMap[key], objTotal)
          let name = this.costTypeDict[key]
          if (name === undefined) {
            name = 'Unknown'
          }
          let detailObj = {
            id: key,
            name: `${name}(${key})`,
            income: this.numF2(objMap[key]),
            rate: rate
          }
          objList.push(detailObj)
        }
      })
      this.costDialogVisible = true
      this.costTableData = objList
    },
    showOrderRevenueDialog(row) {
      let objMap = row.order_revenue || {}
      let objTotal = objMap[0]['revenue']
      let objList = []
      let oKeys = Object.keys(objMap)
      oKeys.forEach((key) => {
        if (key !== '0' && key !== 0) {
          let rate = this.percent2(objMap[key]['revenue'], objTotal)
          let name = this.productTypeDict[key]
          if (name === undefined) {
            name = 'Unknown'
          }
          let detailObj = {
            id: key,
            name: `${name}(${key})`,
            income: this.numF2(objMap[key]['revenue']),
            rate: rate
          }
          objList.push(detailObj)
        }
      })
      this.orderRevenueDialogVisible = true
      this.orderRevenueTableData = objList
    },
    showAdRevenueDialog(row) {
      this.adRevenueFormData = {
        id: row._id,
        ad_revenue: parseFloat(row.ad_revenue),
      }
      this.adRevenueDialogVisible = true
    },
    showNewUserRevenueDialog(row) {
      let objMap = row.new_user_revenue || {}
      let objTotal = objMap[0]['revenue'] || 0
      let objList = []
      let oKeys = Object.keys(objMap)
      oKeys.forEach((key) => {
        if (key !== '0' && key !== 0) {
          let rate = this.percent2(objMap[key]['revenue'], objTotal)
          let name = this.productTypeDict[key]
          if (name === undefined) {
            name = 'Unknown'
          }
          let detailObj = {
            id: key,
            name: `${name}(${key})`,
            income: this.numF2(objMap[key]['revenue']),
            rate: rate
          }
          objList.push(detailObj)
        }
      })
      this.newUserRevenueDialogVisible = true
      this.newUserRevenueTableData = objList
    },
    showRetentionRevenueDialog(row) {
      let objMap = row.retention_revenue || {}
      let objTotal = objMap[0]['revenue']
      let objList = []
      let oKeys = Object.keys(objMap)
      oKeys.forEach((key) => {
        if (key !== '0' && key !== 0) {
          let rate = this.percent2(objMap[key]['revenue'], objTotal)
          let name = this.productTypeDict[key]
          if (name === undefined) {
            name = 'Unknown'
          }
          let detailObj = {
            id: key,
            name: `${name}(${key})`,
            income: this.numF2(objMap[key]['revenue']),
            rate: rate
          }
          objList.push(detailObj)
        }
      })
      this.retentionRevenueDialogVisible = true
      this.retentionRevenueTableData = objList
    },
    showAnchorDauDialog(row) {
      let objMap = row.anchor_dau || {}
      let objTotal = objMap[0]
      let objList = []
      let oKeys = Object.keys(objMap)
      oKeys.forEach((key) => {
        if (key !== '0' && key !== 0) {
          let rate = this.percent2(objMap[key], objTotal)
          let detailObj = {
            id: key,
            income: this.numF2(objMap[key]),
            rate: rate
          }
          objList.push(detailObj)
        }
      })
      this.anchorDauDialogVisible = true
      this.anchorDauTableData = objList
    },
    showMessageCountsDialog(row) {
      let objMap = row.message_counts || {}
      let objTotal = objMap[0]['count']
      let objList = []
      let oKeys = Object.keys(objMap)
      oKeys.forEach((key) => {
        if (key !== '0' && key !== 0) {
          let rate = this.percent2(objMap[key]['count'], objTotal)
          let name = this.msgTypeDict[key]
          let detailObj = {
            id: key,
            name: name,
            count: objMap[key]['count'],
            diamonds: objMap[key]['diamonds'],
            rate: rate
          }
          objList.push(detailObj)
        }
      })
      this.messageCountsDialogVisible = true
      this.messageCountsTableData = objList
    },
    showCallCountsDialog(row) {
      let objMap = row.call_counts || {}
      let objTotal = objMap[0]
      let objList = []
      let oKeys = Object.keys(objMap)
      oKeys.forEach((key) => {
        if (key !== '0' && key !== 0) {
          let rate = this.percent2(objMap[key], objTotal)
          let detailObj = {
            id: `V${key}`,
            income: this.numF2(objMap[key]),
            rate: rate
          }
          objList.push(detailObj)
        }
      })
      this.callCountsDialogVisible = true
      this.callCountsTableData = objList
    },
    showCardDetailDialog(row) {
      let objMap = row.card_detail || {}
      let objTotal = objMap[0]
      let objList = []
      let oKeys = Object.keys(objMap)
      oKeys.forEach((key) => {
        if (key !== '0' && key !== 0) {
          let rate = this.percent2(objMap[key], objTotal)
          let detailObj = {
            id: key,
            income: this.numF2(objMap[key]),
            rate: rate
          }
          objList.push(detailObj)
        }
      })
      this.cardDetailDialogVisible = true
      this.cardDetailTableData = objList
    },
    showAdCostDialog(row) {
      getDayRes({date: row.date}).then(res=>{
        if (res.code===0) {
          let resList = res.data.list || []
          let nList = []
          resList.forEach(item=>{
            item.cost = this.numF2(item.cost)
            if (item.paid_rate !== 0) {
              item.paid_rate_str = item.paid_rate + '%'
            } else {
              item.paid_rate_str = item.paid_rate
            }
            nList.push(item)
          })
          this.adCostDialogVisible = true
          this.adCostTableData = nList
        }
      })
    },
    showGiftCountsDialog(row) {
      let objMap = row.gift_counts || {}
      let objTotal = objMap[0]['count']
      let objTotalDiamonds = objMap[0]['diamonds']
      let objList = []
      let oKeys = Object.keys(objMap)
      oKeys.forEach((key) => {
        if (key !== '0' && key !== 0) {
          let count_rate = this.percent2(objMap[key]['count'], objTotal)
          let diamonds_rate = this.percent2(objMap[key]['diamonds'], objTotalDiamonds)
          let detailObj = {
            id: key,
            count: objMap[key]['count'],
            diamonds: objMap[key]['diamonds'],
            count_rate,
            diamonds_rate,
          }
          objList.push(detailObj)
        }
      })
      this.giftCountsDialogVisible = true
      this.giftCountsDictData = {
        list: objList,
        count: objTotal,
        diamonds: objTotalDiamonds
      }
    },
    showDiamondDetailDialog(row) {
      let objMap = row.diamond_detail || {}
      let objList = []
      let oKeys = Object.keys(objMap)
      oKeys.forEach((key) => {
        let keyName = this.diamondDetailTypeDict[key]
        let keyTotal = objMap[key][0]
        let keyData = []
        let keyRef = `${key}Ref`
        Object.keys(objMap[key]).forEach(oKey=>{
          if (oKey !== '0' && oKey !== 0) {
            let oKeyName = this.formatTradeType(parseInt(oKey))
            if (oKeyName === undefined) {
              oKeyName = `Unknown(${oKey})`
            } else {
              oKeyName = `${oKeyName}(${oKey})`
            }
            keyData.push({
              oKeyName,
              income: objMap[key][oKey],
              rate: this.percent2(objMap[key][oKey], keyTotal)
            })
          }
        })
        let detailObj = {
          key,
          keyRef,
          keyName,
          keyTotal,
          keyData,
        }
        objList.push(detailObj)
      })
      this.diamondDetailDialogVisible = true
      this.diamondDetailListData = objList
      console.log(objList)
    },
    initProductTypeList() {
      getProductsType().then((res) => {
        let resData = res.data.list || []
        let resDict = {}
        resData.forEach((item) => {
          resDict[item.type] = item.desc
        })
        this.productTypeDict = resDict
      })
    },
    submitUpdateAdRevenue() {
      updateAdRevenue(this.adRevenueFormData).then(res=>{
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '修改成功'
          })
          this.adRevenueDialogVisible = false
          this.getTableData(this.nFunc, this.tableDataFormat)
        }
      })
    },
    onReset() {
      this.searchInfo = {}
    },
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    handleDownload(tRef) {
      let tableColumns = this.$refs[tRef].$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    tableExport(tRef) {
      let fileName = dayjs().format('YYYYMMDDHHmmss') + '.xlsx'
      let el = this.$refs[tRef].$el
      if (el === undefined) {
        el = document.getElementById(tRef)
      }
      if (el === undefined) {
        return
      }
      const wb = xlsxUtils.table_to_book(el, {raw: true})
      const wbout = xlsxWrite(wb, {
        bookType: 'xlsx',
        bookSST: true,
        type: 'array',
      })
      try {
        FileSaver.saveAs(
          new Blob([wbout], {
            type: 'application/octet-stream',
          }),
          fileName,
        )
      } catch (e) {
        console.log(e)
      }
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].totalOrderRevenue = this.numF2(item.order_revenue[0]['revenue'] || 0)
        data[i].ad_cost = this.numF2(item.ad_cost)
        data[i].totalNewUserRevenue = this.numF2(item.new_user_revenue[0]['revenue'] || 0)
        data[i].totalIncome = this.numF2(this.numF2(data[i].totalOrderRevenue) + this.numF2(data[i].ad_revenue))
        data[i].totalCost = this.numF2(item.cost[0] || 0)
        data[i].profit = this.numF2(data[i].totalIncome - data[i].totalCost)
        data[i].DAU_ROI = this.percent2(data[i].totalIncome, data[i].ad_cost)
        data[i].newInstallROI = this.percent2(data[i].totalNewUserRevenue, data[i].ad_cost)
        data[i].adRate = this.percent2(data[i].ad_revenue, data[i].ad_cost)
        data[i].totalRetentionRevenue = this.numF2(item.retention_revenue[0]['revenue'] || 0)
        data[i].grossProfitRate = this.percent2(data[i].profit, data[i].totalIncome)
        data[i].totalAnchorDau = this.numF2(item.anchor_dau[0] || 0)
        data[i].totalCallCounts = this.numF2(item.call_counts[0] || 0)
        data[i].totalMessageCounts = this.numF2(item.message_counts[0]['count'] || 0)
        data[i].totalGiftCounts = this.numF2(item.gift_counts[0]['count'] || 0)
        data[i].totalDiamondDetail = this.numF2(item.diamond_detail['consume'][0] || 0)
        data[i].totalCardDetail = this.numF2(item.card_detail[0] || 0)
        // 第一次添加字段
        data[i].newPayRate = this.percent2(item.new_user_revenue[0]['users'], data[i].installs) // 新增付费率
        data[i].newARPPU = this.div2(item.new_user_revenue[0]['revenue'], item.new_user_revenue[0]['users']) // 新增ARPPU
        data[i].newPayCountPer = this.div2(item.new_user_revenue[0]['count'], item.new_user_revenue[0]['users']) // 新增人均付费次数
        data[i].retentionPayRate = this.percent2(item.retention_revenue[0]['users'], data[i].dau - data[i].installs) // 留存付费率
        data[i].retentionARPPU = this.div2(item.retention_revenue[0]['revenue'], item.retention_revenue[0]['users']) // 留存ARPPU
        data[i].retentionPayCountPer = this.div2(item.retention_revenue[0]['count'], item.retention_revenue[0]['users']) // 留存人均付费次数
        data[i].repurchaseRate = this.percent2(item.retention_revenue[0]['users'], item.retention_paid_users - item.new_user_revenue[0]['users']) // 老客复购率
        data[i].newPayUserCount = item.new_user_revenue[0]['users'] // 新增付费
        data[i].todayPayUserCount = item.pay_revenue[0]['users'] // 今日付费
        data[i].activePayUserCount = item.retention_paid_users // 活跃付费
        data[i].retentionPayUserCount = item.retention_revenue[0]['users'] // 留存付费
      })
    },
  },
}
</script>

<style lang="scss" scoped>

.blue {
  color: #409EFF;
}

.c-p {
  cursor: pointer;
}
</style>
