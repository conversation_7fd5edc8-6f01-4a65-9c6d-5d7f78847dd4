:<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item v-if="unionIdBool" label="产品名称">
          <el-select v-model="searchInfo.app_id" filterable>
            <el-option
                v-for="item in appList"
                :key="item.id"
                :label="`${item.appName}`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="颗粒度">
          <el-radio-group v-model="searchInfo.type" size="small">
            <el-radio-button :label="1">月</el-radio-button>
            <el-radio-button :label="2">周</el-radio-button>
            <el-radio-button :label="3">天</el-radio-button>
            <el-radio-button :label="4">小时</el-radio-button>
            <el-radio-button :label="5">30min</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
              v-model="searchInfo.date_range"
              type="datetimerange"
              :shortcuts="dateRangeShortcuts"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DDTHH:mm:ssZ"
              :default-time="[new Date(2022, 1, 1, 0, 0, 0),new Date(2022, 1, 1, 23, 59, 59)]"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">搜索</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="tableData.length === 0" @click="handleDownload">导出
          </el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
          style="width: 100%"
          :data="tableData"
          row-key="id"
          @sort-change="tableSortChange"
max-height="590"
      >
        <el-table-column align="center" label="日期" width="180" fixed="left">
          <template #default="scope">{{ formatDateYMD(scope.row.date) }}</template>
        </el-table-column>
        <el-table-column align="center" label="时间点" fixed="left">
          <template #default="scope">{{ formateSecondsHM(scope.row.time) }}</template>
        </el-table-column>
        <el-table-column align="left" label="数据渠道" prop="channel" />
        <el-table-column align="left" label="渠道收入" prop="income" />
        <el-table-column align="left" label="渠道消耗" prop="advertising_cost" />
        <el-table-column align="left" label="新增" prop="install" />
        <el-table-column align="left" label="新增用户收入" prop="new_user_income" />
        <el-table-column align="left" label="总成本占比" prop="total_cost_ratio" />
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
  import {ElLoading} from 'element-plus'

  import {getAdvertisingReportList} from '@/api/advertisingReport' //  此处请自行替换地址
  import infoList from '@/mixins/infoList'

  export default {
    mixins: [infoList],
    data() {
      return {
        unionIdBool: null,
        searchInfo: {
          app_id: null,
          type: 3
        },
        listApi: getAdvertisingReportList,
      }
    },
    async created() {
      this.unionIdBool = this.userInfo.union_id === 0
      this.searchInfo.app_id = this.appId
      await this.getTableData()
    },
    methods: {
      handleProgressLoading() {
        this.progressLoading = ElLoading.service({
          lock: true,
          text: 'Loading',
          background: 'rgba(0, 0, 0, 0.7)',
        })
      },
      onReset() {
        this.searchInfo = {}
      },
      onSubmit() {
        this.page = 1
        this.pageSize = 10
        this.getTableData()
      },
      tableSortChange({column, prop, order}) {},
      handleDownload() {
        this.handleProgressLoading()
        const that = this
        import('@/utils/excel').then((excel) => {
          let tHeader = []
          let filterVal = []
          const listData = JSON.parse(JSON.stringify(this.tableData))
          // 数据处理
          listData.map(function (currentValue, index, array) {
            // currentValue -> 数组中正在处理的当前元素
            // index -> 数组中正在处理的当前元素的索引
            // array -> 指向map方法被调用的数组
            // array[index].state = that.formatBlockedState(currentValue.state)

          })

          const data = this.formatJson(filterVal, listData)
          excel.export_json_to_excel({
            header: tHeader,
            data,
            filename: this.filename,
            autoWidth: this.autoWidth,
            bookType: this.bookType,
          })
          this.progressLoading.close()
        })
      },

    },
  }
</script>

<style lang="scss" scoped>

</style>
