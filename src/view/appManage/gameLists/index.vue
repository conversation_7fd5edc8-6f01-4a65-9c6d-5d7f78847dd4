<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="游戏名称">
          <el-input v-model="searchInfo.name" placeholder="请输入游戏名称"/>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table ref="tableData" style="width: 100%" :data="tableData" row-key="id" :max-height="590" border>
        <el-table-column fixed="left" show-overflow-tooltip align="center" label="创建时间" prop="created_at_str"
                         min-width="180"/>
        <el-table-column fixed="left" show-overflow-tooltip align="center" label="游戏ID" prop="game_id"
                         min-width="120"/>
        <el-table-column show-overflow-tooltip align="center" label="游戏名称" prop="name" min-width="120"/>
        <el-table-column align="center" label="游戏图标" prop="icon" min-width="120">
          <template #default="scope">
            <el-image
                fit="contain"
                :src="scope.row.icon"
                :preview-src-list="[scope.row.icon]"
                style="width: 30px; height: 30px;"
            />
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" label="游戏logo" prop="logo" min-width="120">
          <template #default="scope">
            <el-image
                fit="contain"
                :src="scope.row.logo"
                :preview-src-list="[scope.row.logo]"
                style="width: 30px; height: 30px;"
            />
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" label="游戏图片" prop="image" min-width="120">
          <template #default="scope">
            <el-image
                fit="contain"
                :src="scope.row.image"
                :preview-src-list="[scope.row.image]"
                style="width: 30px; height: 30px;"
            />
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" label="icon4" prop="icon4" min-width="120">
          <template #default="scope">
            <el-image
                fit="contain"
                :src="scope.row.icon4"
                :preview-src-list="[scope.row.icon4]"
                style="width: 30px; height: 30px;"
            />
          </template>
        </el-table-column>
        <el-table-column show-overflow-tooltip align="center" label="游戏次数" prop="play_count" min-width="120"/>
        <el-table-column show-overflow-tooltip align="center" label="游戏图标" prop="rating" min-width="120"/>
        <el-table-column show-overflow-tooltip align="center" label="描述" prop="description" min-width="120"/>
        <el-table-column show-overflow-tooltip align="center" label="游戏url" prop="url" min-width="120"/>
        <el-table-column show-overflow-tooltip align="center" label="游戏品类" prop="category_name" min-width="120"/>
        <el-table-column show-overflow-tooltip align="center" label="品类id" prop="category_id" min-width="120"/>
        <el-table-column show-overflow-tooltip align="center" label="状态" prop="state" min-width="120">
          <template #default="scope">
            <el-tag v-if="scope.row.state === 1" type="warning">推荐</el-tag>
            <el-tag v-if="scope.row.state === 2" type="info">关闭</el-tag>
            <el-tag v-if="scope.row.state === 3" type="success">开启</el-tag>
          </template>
        </el-table-column>
        <el-table-column fixed="right" align="center" label="操作" min-width="180">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button"
                       @click="updateGameLists(scope.row)">修改
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            :total="total"
            @current-change="pageChange"
            @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" top="5px" :before-close="closeDialog" title="弹窗操作" width="30%">
      <el-form :model="formData" :rules="formDataRules" label-position="right">
        <!--
        <el-form-item label="游戏ID:" prop="game_id">
          <el-input disabled v-model.number="formData.game_id" clearable/>
        </el-form-item>
        <el-form-item label="游戏名称:" prop="name">
          <el-input disabled v-model="formData.name" clearable />
        </el-form-item>
        <el-form-item label="游戏图标:" prop="icon">
          <el-input disabled v-model="formData.icon" clearable />
        </el-form-item>
        <el-form-item label="游戏logo:" prop="logo">
          <el-input disabled v-model="formData.logo" clearable />
        </el-form-item>
        <el-form-item label="游戏图片:" prop="image">
          <el-input disabled v-model="formData.image" clearable />
        </el-form-item>
        <el-form-item label="icon4:" prop="icon4">
          <el-input disabled v-model="formData.icon4" clearable />
        </el-form-item>
        <el-form-item label="游戏次数:" prop="play_count">
          <el-input disabled v-model.number="formData.play_count" clearable/>
        </el-form-item>
        <el-form-item label="游戏图标:" prop="rating">
          <el-input-number style="width:100%" v-model="formData.rating" clearable />
        </el-form-item>
        <el-form-item label="描述:" prop="description">
          <el-input disabled v-model="formData.description" clearable />
        </el-form-item>
        <el-form-item label="游戏url:" prop="url">
          <el-input disabled v-model="formData.url" clearable />
        </el-form-item>
        <el-form-item label="游戏品类:" prop="category_name">
          <el-input disabled v-model="formData.category_name" clearable />
        </el-form-item>
        <el-form-item label="品类id:" prop="category_id">
          <el-input disabled v-model.number="formData.category_id" clearable/>
        </el-form-item>
        -->
        <el-form-item label="状态" prop="state">
          <el-select style="width: 100%;" v-model="formData.state" placeholder="请选择">
            <el-option label="推荐" :value="1"/>
            <el-option label="关闭" :value="2"/>
            <el-option label="开启" :value="3"/>
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createGameLists,
  deleteGameLists,
  deleteGameListsByIds,
  updateGameLists,
  findGameLists,
  getGameListsList
} from '@/api/gameLists' //  此处请自行替换地址
import infoList from '@/mixins/infoList'

export default {
  name: 'GameLists',
  mixins: [infoList],
  data() {
    return {
      listApi: getGameListsList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        game_id: null,
        name: null,
        icon: null,
        logo: null,
        image: null,
        icon4: null,
        play_count: null,
        description: null,
        url: null,
        category_name: null,
        category_id: null,
        state: null,
        created_time: null,
        updated_time: null,
      },
      formDataRules: {},
    }
  },
  created() {
    this.pageSize = 100
    this.getTableData(this.nFunc, this.tableDataFormat)
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteGameLists(row)
      })
    },
    async updateGameLists(row) {
      const res = await findGameLists({id: row.id})
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {}
    },
    async deleteGameLists(row) {
      const res = await deleteGameLists({id: row.id})
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createGameLists(this.formData)
          break
        case 'update':
          res = await updateGameLists(this.formData)
          break
        default:
          res = await createGameLists(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '成功'
        })
        this.closeDialog()
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
      })
    },
  },
}
</script>

<style>
</style>
