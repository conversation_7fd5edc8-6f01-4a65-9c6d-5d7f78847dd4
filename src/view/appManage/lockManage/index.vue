<template>
  <div>
    <div class="gva-table-box">
      <el-table
        style="width: 100%"
        :data="tableData"
        row-key="id"
        max-height="590"
      >
        <el-table-column align="center" label="ID" prop="id" />
        <el-table-column align="center" label="APP名称" prop="app_name" />
        <el-table-column align="center" label="合作方标记">
          <template #default="scope">
            <el-tag v-if="scope.row.partner === 0" type="success">内部包</el-tag>
            <el-tag v-else-if="scope.row.partner === 1" type="info">合作方</el-tag>
            <el-tag v-else type="danger">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作">
          <template #default="scope">
            <el-button type="text" icon="el-icon-view" @click="viewAppSwitchDetail( scope.row )">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog
      v-model="dialogVisible"
      title="产品开关"
      width="30%"
      top="10px"
    >
      <!--试用60s开关-->
      <el-form ref="switchForm" size="small" :model="switchForm" :rules="switchFormRules" label-width="120px">
        <el-form-item label="试用60S" prop="trial_60">
          <el-switch v-model="trial_60" active-text="开" inactive-text="关" @change="changeTrial60" />
        </el-form-item>
        <!--真假免费卡赠送比例-->
        <el-form-item label="真假卡比例" prop="trial_ratio">
          <el-slider v-model="trial_ratio" :min="1" :max="101" @change="changeTrialRatio" />
        </el-form-item>
        <!--首充赠送60S开关-->
        <el-form-item label="首充试用60S" prop="first_buy_switch">
          <el-switch v-model="first_buy_switch" active-text="开" inactive-text="关" @change="changeFirstBuySwitch" />
        </el-form-item>
        <!--文字消息收益开关-->
        <el-form-item label="文字消息收益" prop="message_profit_switch">
          <el-switch
            v-model="message_profit_switch"
            active-text="开"
            inactive-text="关"
            @change="changeMessageProfitSwitch"
          />
        </el-form-item>
        <!--新手礼包开关-->
        <el-form-item label="开放新手礼包" prop="new_user_pat">
          <el-switch v-model="new_user_pat" active-text="开" inactive-text="关" @change="changeNewUserPat" />
        </el-form-item>
        <!--呼叫转接比例-->
        <el-form-item label="呼叫转接比例" prop="spare_switch">
          <el-slider v-model="spare_switch" :min="1" :max="101" @change="changeSpareSwitch" />
        </el-form-item>
        <!--PayTm UPI 在各包最低版本-->
        <el-form-item label="PayTm UPI" prop="paytm_upi_version">
          <div style="display: flex; align-items: center">
            <el-switch v-model="paytm_upi_switch" active-text="开" inactive-text="关" @change="changePaytmUpiSwitchOff" />
            <el-input
              v-model="paytm_upi_version"
              style="margin-left: 10px"
              :disabled="!paytm_upi_switch"
              placeholder="最小版本,格式为:1.1.1"
              @blur="changePaytmUpiSwitch"
            />
          </div>
        </el-form-item>
        <!--第二版故事线 在各包最低版本-->
        <el-form-item label="第二版故事线" prop="qa_v2_switch">
          <div style="display: flex; align-items: center">
            <el-switch v-model="qa_v2_switch" active-text="开" inactive-text="关" @change="changeQaV2VersionOff" />
            <el-input
              v-model="qa_v2_version"
              style="margin-left: 10px"
              :disabled="!qa_v2_switch"
              placeholder="最小版本,格式为:1.1.1"
              @blur="changeQaV2Version"
            />
          </div>
        </el-form-item>
        <!--VIP 每日限制消息条数-->
        <el-form-item label="VIP消息条数" prop="vip_message">
          <div style="display: flex; align-items: center">
            <el-switch v-model="vip_message_switch" active-text="开" inactive-text="关" @change="changeVipMessageOff" />
            <el-input
              v-model="vip_message"
              style="margin-left: 10px"
              :disabled="!vip_message_switch"
              placeholder="慎重开启,只可开启在VIP模型,格式为:1.1.1"
              @blur="changeVipMessage"
            />
          </div>
        </el-form-item>
        <!--FB上报转化归因必要项 从客户端获取-->
        <el-form-item label="FB AccountId" prop="fb_account_id">
          <el-input v-model="fb_account_id" placeholder="FB上报转化归因必要项" @blur="changeFbAccountId" />
        </el-form-item>
        <!--AF上报转化归因必要项 从AF管理后台获取-->
        <el-form-item label="AF AccountId" prop="af_account_id">
          <el-input v-model="af_account_id" placeholder="AF上报转化归因必要项" @blur="changeAfAccountId" />
        </el-form-item>
        <!--AF上报转化归因必要项 从AF管理后台获取-->
        <el-form-item label="AF DevKey" prop="af_dev_key">
          <el-input v-model="af_dev_key" placeholder="AF上报转化归因必要项" @blur="changeAfDevKey" />
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getAppXesList, getAppSwitch, uptAppSwitch } from '@/api/appXes'
import infoList from '@/mixins/infoList'

export default {
  name: 'LockManage',
  mixins: [infoList],
  data() {
    return {
      detailAppId: null,
      dialogVisible: false,
      listApi: getAppXesList,
      switchForm: {},
      switchFormRules: {
        // username: [{ validator: checkUsername, trigger: 'blur' }],
        /*
          score_min: [
          { pattern: /^(\-|\+)?\d+(\.\d+)?$/, message: '请输入正确的数字', trigger: ['blur', 'change'] },
          {validator: checkScoreRange, trigger: 'blur'},
        ],
           */
      },
      // 开关
      trial_60: false,
      first_buy_switch: false,
      message_profit_switch: false,
      new_user_pat: false,
      // 滑动
      spare_switch: 1,
      trial_ratio: 1,
      // 文本框
      af_account_id: '',
      af_dev_key: '',
      fb_account_id: '',
      // 左开关右设置值
      paytm_upi_switch: false,
      paytm_upi_version: '',
      qa_v2_switch: false,
      qa_v2_version: '',
      vip_message_switch: false,
      vip_message: '',
    }
  },
  async created() {
    await this.getTableData()
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    async viewAppSwitchDetail(row) {
      this.detailAppId = row.id
      getAppSwitch({ id: this.detailAppId }).then(res => {
        this.switchForm = res.data

        // 试用开关
        this.trial_60 = this.switchForm.trial_60 !== ''
        // 赠送比例
        if (this.switchForm.trial_ratio === '') {
          this.trial_ratio = 1
        } else {
          this.trial_ratio = parseInt(this.switchForm.trial_ratio)
        }
        // 首充试用开关
        this.first_buy_switch = this.switchForm.first_buy_switch !== ''
        // 文字消息收益
        this.message_profit_switch = this.switchForm.message_profit_switch
        // 新手礼包开关
        this.new_user_pat = this.switchForm.new_user_pat === 'on'
        // 呼叫转接比例
        if (this.switchForm.spare_switch === '') {
          this.spare_switch = 1
        } else {
          this.spare_switch = parseInt(this.switchForm.spare_switch)
        }
        // PayTm UPI
        if (this.switchForm.paytm_upi_version === '') {
          this.paytm_upi_switch = false
        } else {
          this.paytm_upi_switch = true
          const x = this.switchForm.paytm_upi_version.split('-')
          this.paytm_upi_version = x[1]
        }
        // 第二版故事线
        if (this.switchForm.qa_v2_version === '') {
          this.qa_v2_switch = false
        } else {
          this.qa_v2_switch = true
          const x = this.switchForm.qa_v2_version.split('-')
          this.qa_v2_version = x[1]
        }
        // VIP每日赠送消息条数
        if (this.switchForm.vip_message === '') {
          this.vip_message_switch = false
        } else {
          this.vip_message_switch = true
          this.vip_message = this.switchForm.vip_message
        }
        // FB AccountId
        this.fb_account_id = this.switchForm.fb_account_id
        // AF AccountId
        this.af_account_id = this.switchForm.af_account_id
        // AF devKey
        this.af_dev_key = this.switchForm.af_dev_key
        // 弹窗显示
        this.dialogVisible = true
      })
    },
    updateAppSwitch(params) {
      uptAppSwitch(params).then(res => {
        this.$message.success(res.msg)
      })
    },

    changeTrial60() {
      const v = this.trial_60 ? '1' : ''
      var params = { id: this.detailAppId, key: 'trial_60', value: v }
      this.updateAppSwitch(params)
    },
    changeTrialRatio() {
      var params = { id: this.detailAppId, key: 'trial_ratio', value: `${this.trial_ratio}` }
      this.updateAppSwitch(params)
    },
    changeFirstBuySwitch() {
      const v = this.first_buy_switch ? '1' : ''
      var params = { id: this.detailAppId, key: 'first_buy_switch', value: v }
      this.updateAppSwitch(params)
    },
    // message_profit_switch
    changeMessageProfitSwitch() {
      const v = this.message_profit_switch ? '1' : ''
      var params = { id: this.detailAppId, key: 'message_profit_switch', value: v }
      this.updateAppSwitch(params)
    },
    changeNewUserPat() {
      const v = this.new_user_pat ? 'on' : 'off'
      var params = { id: this.detailAppId, key: 'new_user_pat', value: v }
      this.updateAppSwitch(params)
    },
    changeSpareSwitch() {
      var params = { id: this.detailAppId, key: 'spare_switch', value: `${this.spare_switch}` }
      this.updateAppSwitch(params)
    },
    // paytm_upi_switch
    changePaytmUpiSwitch() {
      let val = ''
      if (this.paytm_upi_switch) {
        if (this.paytm_upi_version === '') return
        val = this.detailAppId + '-' + this.paytm_upi_version
      }
      var params = { id: this.detailAppId, key: 'paytm_upi_version', value: val }
      this.updateAppSwitch(params)
    },
    changePaytmUpiSwitchOff() {
      if (this.paytm_upi_switch) return
      const val = ''
      const params = { id: this.detailAppId, key: 'paytm_upi_version', value: val }
      this.updateAppSwitch(params)
    },
    // qa_v2_version
    changeQaV2Version() {
      let val = ''
      if (this.qa_v2_switch) {
        if (this.qa_v2_version === '') return
        val = this.detailAppId + '-' + this.qa_v2_version
      }
      var params = { id: this.detailAppId, key: 'qa_v2_version', value: val }
      this.updateAppSwitch(params)
    },
    changeQaV2VersionOff() {
      if (this.qa_v2_switch) return
      const val = ''
      const params = { id: this.detailAppId, key: 'qa_v2_version', value: val }
      this.updateAppSwitch(params)
    },
    changeVipMessage() {
      let val = ''
      if (this.vip_message_switch) {
        if (this.vip_message === '') return
        val = this.vip_message
      }
      var params = { id: this.detailAppId, key: 'vip_message', value: val }
      this.updateAppSwitch(params)
    },
    changeVipMessageOff() {
      if (this.vip_message_switch) return
      const val = ''
      const params = { id: this.detailAppId, key: 'vip_message', value: val }
      this.updateAppSwitch(params)
    },
    changeFbAccountId() {
      var params = { id: this.detailAppId, key: 'fb_account_id', value: this.fb_account_id }
      this.updateAppSwitch(params)
    },
    changeAfAccountId() {
      var params = { id: this.detailAppId, key: 'af_account_id', value: this.af_account_id }
      this.updateAppSwitch(params)
    },
    changeAfDevKey() {
      if (this.af_dev_key === '') return
      var params = { id: this.detailAppId, key: 'af_dev_key', value: this.af_dev_key }
      this.updateAppSwitch(params)
    }
  }
}
</script>

<style>
</style>
