<template>
  <div>
    <!--搜索框-->
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="产品:">
          <el-select v-model="searchInfo.app_id" clearable filterable placeholder="请选择APP">
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="`${item.app_name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model.number="searchInfo.state" clearable filterable placeholder="上架状态">
            <el-option
              v-for="item in shelfStateOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签">
          <el-select v-model.number="searchInfo.labels" clearable filterable placeholder="上架状态">
            <el-option
              v-for="item in labelsOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!--列表展示-->
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
        <el-button size="mini" type="success" icon="el-icon-refresh" @click="syncData">数据同步</el-button>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" label="礼物ID" prop="id" width="120" />
        <el-table-column align="center" label="礼物名称" prop="name" min-width="120" show-overflow-tooltip />
        <el-table-column align="center" label="礼物中图" min-width="120">
          <template #default="scope">
            <el-image
              fit="contain"
              :src="scope.row.mid_pic"
              :preview-src-list="[scope.row.mid_pic]"
              style="width: 40px; height: 40px;"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="上/下架" prop="state" width="120">
          <template #default="scope">
            <el-tag v-if="scope.row.state === 2" type="success">上架</el-tag>
            <el-tag v-else type="danger">下架</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="排序值" prop="sortv" width="120" />
        <el-table-column align="center" label="礼物价格" prop="price" min-width="120" />
        <el-table-column align="center" label="平台分成" min-width="120">
          <template #default="scope">{{ xRound(scope.row.pallocate,2) }}</template>
        </el-table-column>
        <el-table-column align="center" label="APP" prop="app_id" min-width="120">
          <template #default="scope">{{ formatAppName(scope.row.app_id) }}</template>
        </el-table-column>
        <el-table-column align="center" label="标签" min-width="120">
          <template #default="scope">{{ formatGiftLabels(scope.row.labels) }}</template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="120">
          <template #default="scope">
            <el-button
              type="text"
              icon="el-icon-edit"
              size="small"
              class="table-button"
              @click="updateGifts(scope.row)"
            >修改
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <!--弹窗-->
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" :title="dialogTitle">
      <el-form ref="formData" :model="formData" :rules="formDataRules" label-position="right" label-width="100px">
        <el-form-item label="AppId:" prop="app_id">
          <el-select v-model="formData.app_id" filterable placeholder="AppId">
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="`${item.app_name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="礼物中图:" prop="mid_pic">
          <el-upload
            ref="midPic"
            :action="`${path}/files/createFiles`"
            :headers="{ 'x-token': token }"
            list-type="picture-card"
            :file-list="midPicFileList"
            :on-preview="handleMidPicPreview"
            :on-success="handleMidPicSuccess"
            :on-progress="handleProgressLoading"
            :on-exceed="handleImgExceed"
            :multiple="false"
            :limit="1"
            accept=".jpg, .jpeg, .png, .gif"
            :on-remove="handleImageRemove"
          >
            <i class="el-icon-plus" />
          </el-upload>
        </el-form-item>
        <el-form-item label="礼物名称:" prop="name">
          <el-input v-model="formData.name" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="礼物价格:" prop="price">
          <el-input v-model.number="formData.price" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="排序值:">
          <el-input v-model.number="formData.sortv" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="状态:">
          <el-switch
            v-model="formData.state"
            active-text="上架"
            inactive-text="下架"
            :active-value="2"
            :inactive-value="1"
          />
        </el-form-item>
        <el-form-item label="标签:">
          <el-cascader
            v-model="formData.labels"
            style="width:100%"
            :options="labelsOption"
            :props="{multiple:false, label:'label',value:'value',disabled:'disabled',emitPath:false}"
            filterable
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="imgPreviewDialogVisible" width="30%" title="图片预览" top="5px">
      <el-image style="width: 100%" :src="dialogImgSrc" :preview-src-list="[dialogImgSrc]"/>
    </el-dialog>
    <el-dialog v-model="syncDataDialogVisible" :before-close="closeSyncDialog" title="数据同步" width="30%" top="5px">
      <el-form ref="syncFormDataRef" :model="syncFormData" :rules="syncFormDataRules" label-position="right"
               label-width="100px">
        <el-form-item label="源AppId:" prop="source_app_id">
          <el-row :gutter="14">
            <el-col :span="20">
              <el-input-number style="width: 100%;" v-model="syncFormData.source_app_id"/>
            </el-col>
            <el-col :span="4">
              <el-button size="small" type="primary" @click="searchSyncTableData">查询</el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="目标AppId:" prop="target_app_id">
          <el-input-number style="width: 100%;" v-model="syncFormData.target_app_id"/>
        </el-form-item>
      </el-form>
      <el-table
          v-if="syncTableVisible"
          style="width: 100%"
          tooltip-effect="dark"
          :data="syncTableData"
          row-key="id"
          :height="400"
      >
        <el-table-column align="center" label="礼物ID" prop="id" width="120"/>
        <el-table-column align="center" label="礼物名称" prop="name" min-width="120" show-overflow-tooltip/>
        <el-table-column align="center" label="礼物中图" min-width="120">
          <template #default="scope">
            <el-image
                fit="contain"
                :src="scope.row.mid_pic"
                :preview-src-list="[scope.row.mid_pic]"
                style="width: 30px; height: 30px;"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="APP" prop="app_id" min-width="120">
          <template #default="scope">{{ formatAppName(scope.row.app_id) }}</template>
        </el-table-column>
      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeSyncDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterSyncDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script>
import {
  createGifts,
  updateGifts,
  findGifts,
  getGiftsList,
  syncAppGifts
} from '@/api/gifts' //  此处请自行替换地址
import infoList from '@/mixins/infoList'

const path = import.meta.env.VITE_BASE_API

export default {
  name: 'Gifts',
  mixins: [infoList],
  data() {
    var validateTargetAppId = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入'));
      } else {
        if (this.syncFormData.source_app_id === this.syncFormData.target_app_id) {
          callback(new Error('源APP和目标APP不能是一样的'));
        }
        callback();
      }
    };
    return {
      listApi: getGiftsList,
      dialogFormVisible: false,
      syncTableVisible: false,
      syncTableData: [],
      syncDataDialogVisible: false,
      syncFormData: {
        source_app_id: 5,
        target_app_id: null
      },
      syncFormDataRules: {
        source_app_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        target_app_id: [{validator: validateTargetAppId, trigger: 'blur'}],
      },
      type: '',
      dialogTitle: '',
      dialogImgSrc: '',
      imgPreviewDialogVisible: false,
      midPicFileList: [],
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        name: '',
        big_pic: '',
        mid_pic: '',
        small_pic: '',
        state: 0,
        sortv: 0,
        price: 0,
        version: '',
        platform: 0,
        utm_source: '',
        app_id: null,
        labels: '',
      },
      formDataRules: {
        app_id: [
          { required: true, message: '请选择APP', trigger: 'blur' },
        ],
        name: [
          { required: true, message: '请输入礼物名称', trigger: 'blur' },
          { min: 1, max: 64, message: '礼物名称长度在1-64字符之间', trigger: 'blur' },
        ],
        big_pic: [
          { min: 1, max: 1024, message: '礼物图片地址长度在1-1024字符之间', trigger: 'blur' },
        ],
        mid_pic: [
          { required: true, message: '请上传礼物中图', trigger: 'blur' },
          { min: 1, max: 1024, message: '礼物图片地址长度在1-1024字符之间', trigger: 'blur' },
        ],
        small_pic: [
          { min: 1, max: 1024, message: '礼物图片地址长度在1-1024字符之间', trigger: 'blur' },
        ],
        price: [
          { required: true, message: '请输入礼物价格', trigger: 'blur' },
        ],
      },
      shelfStateOptions: [
        { value: 0, label: '未选择' },
        { value: 1, label: '下架' },
        { value: 2, label: '上架' },
      ],
      searchInfo: {},
      labelsOption: [
        { value: '', label: '不限' },
        { value: '@1@', label: 'Like' },
        { value: '@2@', label: 'Super Like' },
        { value: '@3@', label: 'Favorites' },
        { value: '@201@', label: '亲密值解锁1级' },
        { value: '@202@', label: '亲密值解锁2级' },
        { value: '@203@', label: '亲密值解锁3级' },
        { value: '@204@', label: '亲密值解锁4级' },
      ],
      selectedLabels: [],
      path: path,
    }
  },
  async created() {
    await this.getTableData()
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleMidPicPreview(file) {
      this.dialogImgSrc = file.url
      this.imgPreviewDialogVisible = true
    },
    handleMidPicSuccess(res) {
      this.midPicFileList = []
      const { data } = res
      if (data.url) {
        this.formData.mid_pic = data.url
        this.midPicFileList.push(data)
      }
      this.progressLoading.close()
    },
    handleMidPicExceed() {
      this.$message.error('最多上传一张图片,如果想替换,请先删除.')
    },
    handleImageRemove() {
      this.midPicFileList = []
      this.formData.mid_pic = null
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteGifts(row)
      })
    },
    async updateGifts(row) {
      this.midPicFileList = []
      const res = await findGifts({ id: row.id })
      this.type = 'update'
      this.dialogTitle = '编辑礼物'
      if (res.code === 0) {
        const gifInfo = res.data.giftsInfo
        if (gifInfo.mid_pic !== '') {
          this.midPicFileList = [{name: '', url: gifInfo.mid_pic}]
        }
        this.formData = res.data.giftsInfo
        this.dialogFormVisible = true
      }
    },
    searchSyncTableData() {
      getGiftsList({
        app_id: this.syncFormData.source_app_id,
        state: 2,
        page: 1,
        pageSize: 10000,
      }).then(res => {
        this.syncTableVisible = true
        this.syncTableData = res.data.list || []
      })
    },
    closeSyncDialog() {
      this.syncDataDialogVisible = false
      this.syncTableVisible = false
      this.syncTableData = []
      this.syncFormData = {
        source_app_id: 5
      }
    },
    enterSyncDialog() {
      this.$refs['syncFormDataRef'].validate(async valid => {
        if (!valid) return
        let res
        res = await syncAppGifts(this.syncFormData)
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功'
          })
          this.closeSyncDialog()
          this.getTableData()
        }
      })
    },
    closeDialog() {
      this.midPicFileList = []
      this.dialogFormVisible = false
      this.formData = {
        name: '',
        big_pic: '',
        mid_pic: '',
        small_pic: '',
        state: 0,
        sortv: 0,
        price: 0,
        app_id: 0,
        labels: '',
      }
    },
    async enterDialog() {
      this.$refs['formData'].validate(async valid => {
        if (!valid) return
        let res
        switch (this.type) {
          case 'create':
            res = await createGifts(this.formData)
            break
          case 'update':
            res = await updateGifts(this.formData)
            break
          default:
            res = await createGifts(this.formData)
            break
        }
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '创建/更改成功'
          })
          this.closeDialog()
          this.getTableData()
        }
      })
    },
    openDialog() {
      this.type = 'create'
      this.dialogTitle = '新增礼物'
      this.dialogFormVisible = true
    },
    syncData() {
      this.syncDataDialogVisible = true
    },
    xRound(number, precision) {
      if (number === undefined) {
        return 0
      }

      const p = Math.pow(10, precision)
      return Math.floor(number * p + 0.5) / p
    },
    trim(aa, bb) {
      const a = aa !== '' ? aa : ' '
      const b = bb !== '' ? bb : ''
      if (b === '') {
        return ''
      }
      const la = a.length
      const lb = b.length
      let s = false
      let e = false
      if (b.indexOf(a) === 0) {
        s = true
      }
      if (b.lastIndexOf(a) === lb - la) {
        e = true
      }
      var r = b
      if (e) {
        if (r.length > 0) {
          r = r.substring(0, lb - la)
          return this.trim(a, r)
        } else {
          return ''
        }
      }
      if (s) {
        if (r.length > 0) {
          r = r.substring(la, r.length)
          return this.trim(a, r)
        } else {
          return ''
        }
      }
      return r
    },
    formatGiftLabels(labels) {
      let res = ''
      if (labels === '') {
        return res
      }
      const labelsArr = labels.split(',')
      labelsArr.forEach((item, index) => {
        this.labelsOption.forEach((item1, index1) => {
          if (item1.value === item) {
            res += item1.label + ','
          }
        })
      })

      return this.trim(',', res)
    },
  },
}
</script>

<style>
</style>
