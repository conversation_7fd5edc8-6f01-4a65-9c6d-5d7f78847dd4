<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="AppId">
          <el-select v-model="searchInfo.app_id" clearable filterable placeholder="AppId">
            <el-option
                v-for="item in appList"
                :key="item"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="类型">
          <el-select v-model="searchInfo.type" clearable placeholder="类型">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="版本号">
          <el-input v-model="searchInfo.version" placeholder="版本号" />
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
        <el-popover v-model:visible="deleteVisible" placement="top" width="160">
          <p>确定要删除吗？</p>
          <div style="text-align: right; margin-top: 8px;">
            <el-button size="mini" type="text" @click="deleteVisible = false">取消</el-button>
            <el-button size="mini" type="primary" @click="onDelete">确定</el-button>
          </div>
          <template #reference>
            <el-button icon="el-icon-delete" size="mini" style="margin-left: 10px;" :disabled="!multipleSelection.length">删除</el-button>
          </template>
        </el-popover>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="center" label="日期" width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>

        <el-table-column align="center" label="AppId" prop="app_id" width="120">
          <template #default="scope">{{ formaAppList(scope.row.app_id) }}</template>
        </el-table-column>

        <el-table-column align="center" label="类型" prop="type" width="120">
          <template #default="scope">{{ formatSettingType(scope.row.type) }}</template>
        </el-table-column>
        <el-table-column align="center" label="版本号" prop="version" width="120" />
        <el-table-column align="center" label="是否打开" prop="is_open" width="120">
          <template #default="scope">{{ formatBoolean(scope.row.is_open) }}</template>
        </el-table-column>
        <el-table-column align="center" label="按钮组">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateSystemSetting(scope.row)">变更</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作" :width="400">
      <el-form :model="formData" label-position="right" label-width="80px" :rules="rules">
        <el-form-item label="AppId:" prop="app_id">
          <el-select v-model="formData.app_id" clearable filterable placeholder="AppId">
            <el-option
                v-for="item in appList"
                :key="item"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="类型:" prop="type">
          <el-select v-model="formData.type" clearable filterable placeholder="AppId">
            <el-option
              v-for="item in typeOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="版本号:" prop="version">
          <el-input v-model="formData.version" clearable placeholder="请输入版本号（1.0.0）" />
        </el-form-item>
        <el-form-item label="是否打开:">
          <el-switch v-model="formData.is_open" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createSystemSetting,
  deleteSystemSetting,
  deleteSystemSettingByIds,
  updateSystemSetting,
  findSystemSetting,
  getSystemSettingList
} from '@/api/systemSetting' //  此处请自行替换地址
import { getAppXesList } from '@/api/appXes'
import infoList from '@/mixins/infoList'

const typeOptions = [
  {
    value: 1,
    label: '星座匹配视频',
  },
  {
    value: 2,
    label: '个人资料页视频',
  },
  {
    value: 3,
    label: 'discove页面视频',
  },
  {
    value: 4,
    label: '假通话视频',
  },
  {
    value: 5,
    label: '假通话视频模糊后的声音',
  },
  {
    value: 6,
    label: 'discove页面是否显示',
  },
]

export default {
  name: 'SystemSetting',
  mixins: [infoList],
  data() {
    return {
      listApi: getSystemSettingList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      appListOptions: [],
      typeOptions: typeOptions,
      formData: {
        app_id: 4,
        type: 1,
        version: '',
        is_open: false,
      },
      rules: {
        app_id: [
          { required: true, message: 'app_id不能为空', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '类型不能为空', trigger: 'blur' }
        ],
        version: [
          { required: true, message: '版本不能为空', trigger: 'blur' }
        ],
      }
    }
  },
  async created() {
    await this.getTableData()
    await this.getAppList()
  },
  methods: {
    formaAppList: function(bool) {
      let text
      this.appListOptions.forEach(function(element) {
        if (bool === element.id) {
          text = element.appName
          return text
        }
      })
      return text
    },
    async getAppList() {
      const res = await getAppXesList()
      this.appListOptions = res.data.list
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      if (this.searchInfo.is_open === '') {
        this.searchInfo.is_open = null
      }
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteSystemSetting(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
                this.multipleSelection.map(item => {
                  ids.push(item.id)
                })
      const res = await deleteSystemSettingByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateSystemSetting(row) {
      const res = await findSystemSetting({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.resystemSetting
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        app_id: 0,
        type: 0,
        version: '',
        is_open: false,
      }
    },
    async deleteSystemSetting(row) {
      const res = await deleteSystemSetting({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createSystemSetting(this.formData)
          break
        case 'update':
          res = await updateSystemSetting(this.formData)
          break
        default:
          res = await createSystemSetting(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    }
  },
}
</script>

<style>
</style>
