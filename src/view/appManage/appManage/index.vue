<template>
  <div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="createApp">新增</el-button>
      </div>
      <el-table
        style="width: 100%"
        :data="tableData"
        row-key="id"
        max-height="590"
      >
        <el-table-column align="center" label="ID" prop="id" />
        <el-table-column align="center" label="包名" prop="bundle_id" show-overflow-tooltip />
        <el-table-column align="center" label="应用Key" prop="app_key" min-width="170">
          <template #default="scope">
            <div class="flex-r" style="justify-content: center">
              <span>{{ scope.row.app_key }}</span>
              <el-button type="text" @click="copyText(scope.row.app_key)">复制</el-button>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="app名称" prop="app_name" />
        <el-table-column align="center" label="合作方标记">
          <template #default="scope">
            <el-tag v-if="scope.row.partner === 0" type="success">内部包</el-tag>
            <el-tag v-else-if="scope.row.partner === 1" type="info">合作方</el-tag>
            <el-tag v-else type="danger">未知</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作">
          <template #default="scope">
            <el-button type="text" icon="el-icon-view" @click="viewDetailApp(scope.row)">查看</el-button>
            <el-button type="text" icon="el-icon-edit" @click="updateApp(scope.row)">修改</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <el-dialog
      v-model="appDialoginfo.visible"
      :title="appDialoginfo.title"
      :close-on-click-modal="false"
      width="25%"
      :before-close="dialogClose"
    >
      <el-form ref="appForm" :model="appForm" :rules="appFormRules" label-width="60px">
        <el-form-item label="ID" prop="id">
          <el-input v-model.number="appForm.id" />
        </el-form-item>
        <el-form-item label="包名" prop="bundle_id">
          <el-input v-model="appForm.bundle_id" maxlength="64" show-word-limit />
        </el-form-item>
      </el-form>
      <template #footer>
        <span>
          <el-button type="primary" @click="enterAppDialog">保存</el-button>
        </span>
      </template>
    </el-dialog>
    <el-dialog
      v-model="appDetailDialoginfo.visible"
      :title="appDetailDialoginfo.title"
      :close-on-click-modal="false"
      top="10px"
      width="70%"
      :before-close="appDetailDialogClose"
    >
      <div class="app-detail-dialog">
        <el-descriptions :column="4" border>
          <el-descriptions-item align="center" :span="1" label="ID">{{ appDetailForm.id }}</el-descriptions-item>
          <el-descriptions-item align="center" :span="1" label="包名">{{ appDetailForm.bundle_id }}</el-descriptions-item>
          <el-descriptions-item align="center" :span="2" label="AppKey">
            <div class="flex-r" style="justify-content: center; align-items: center">
              <span>{{ appDetailForm.app_key }}</span>
              <el-button type="text" @click="copyText(appDetailForm.app_key)">复制</el-button>
            </div>
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="1" label="名称">
            <el-input v-model="appDetailForm.app_name" size="small" placeholder="请输入软件名称" />
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="1" label="通话扣费">
            <el-input-number v-model="appDetailForm.call_price" :controls="false" :precision="2" size="small" />
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="1" label="解锁相册扣费">
            <el-input-number v-model="appDetailForm.album_price" :controls="false" :precision="2" size="small" />
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="1" label="发消息扣费">
            <el-input-number v-model="appDetailForm.message_price" :controls="false" :precision="2" size="small" />
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="1" label="官方账户ID">
            <el-input-number v-model="appDetailForm.official" :controls="false" size="small"/>
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="1" label="官方邮箱">
            <el-input v-model="appDetailForm.official_email" size="small" placeholder="请输入官方邮箱" />
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="2" label="合作方标记">
            <el-radio-group v-model="appDetailForm.partner">
              <el-radio size="small" :label="0">内部包</el-radio>
              <el-radio size="small" :label="1">合作方</el-radio>
            </el-radio-group>
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="2" label="权益验证">
            <el-radio-group v-model="appDetailForm.stake">
              <el-radio size="small" :label="0">直接跳过</el-radio>
              <el-radio size="small" :label="1">仅验证钻石</el-radio>
              <el-radio size="small" :label="2">验证VIP+钻石</el-radio>
            </el-radio-group>
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="2" label="收益方案">
            <el-radio-group v-model="appDetailForm.revenue_scheme">
              <el-radio size="small" :label="0">用户侧</el-radio>
              <el-radio size="small" :label="1">美元方案</el-radio>
              <el-radio size="small" :label="2">金币方案</el-radio>
            </el-radio-group>
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="4" label="通话价格比例">
            <el-input-number v-model="appDetailForm.call_price_conversion" size="small" placeholder="通话价格比例" />
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="4" label="自助链接">
            <el-input v-model="appDetailForm.ai_help" size="small" placeholder="请输入客户自助服务链接" />
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="4" label="FCM推送ApiKey">
            <el-input v-model="appDetailForm.fcm_api_key" size="small" placeholder="请输入FCM推送ApiKey" />
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="1" label="接口地址">
            <el-popover placement="right" :width="400" trigger="click" :visible="appDomainFormVisible">
              <template #reference>
                <el-button size="small" type="primary" plain @click="appDomainFormVisible = !appDomainFormVisible">设置接口地址</el-button>
              </template>
              <el-form label-width="60px">
                <el-form-item label="API">
                  <el-input v-model="appDomainForm.api" size="small" placeholder="请输入API" />
                </el-form-item>
                <el-form-item label="WS">
                  <el-input v-model="appDomainForm.ws" size="small" placeholder="请输入WS" />
                </el-form-item>
                <el-form-item label="H5">
                  <el-input v-model="appDomainForm.h5" size="small" placeholder="请输入H5" />
                </el-form-item>
                <el-form-item label="IMAGE">
                  <el-input v-model="appDomainForm.image" size="small" placeholder="请输入IMAGE" />
                </el-form-item>
                <el-button size="small" type="success" @click="saveAppDomainForm">保存</el-button>
              </el-form>
            </el-popover>
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="1" label="合作开关">
            <el-popover placement="right" :width="400" trigger="click" :visible="cooperativeFormVisible">
              <template #reference>
                <el-button size="small" type="primary" plain @click="cooperativeFormVisible = !cooperativeFormVisible">设置合作开关</el-button>
              </template>
              <el-form label-width="80px">
                <el-form-item label="开关">
                  <el-switch v-model="cooperativeForm.coop_switch" size="small" />
                </el-form-item>
                <el-form-item label="应答回调">
                  <el-input v-model="cooperativeForm.chat_callback" size="small" placeholder="请输入应答回调" />
                </el-form-item>
                <el-form-item label="挂掉回调">
                  <el-input v-model="cooperativeForm.hang_callback" size="small" placeholder="请输入挂掉回调" />
                </el-form-item>
                <el-form-item label="消息回调">
                  <el-input v-model="cooperativeForm.message_callback" size="small" placeholder="请输入消息回调" />
                </el-form-item>
                <el-form-item label="声网ID">
                  <el-input v-model="cooperativeForm.ago_id" size="small" placeholder="请输入声网ID" />
                </el-form-item>
                <el-form-item v-show="appDetailForm.partner === 1" label="主播数">
                  <el-input-number v-model="cooperativeForm.view_total" size="small" placeholder="请输入主播数" />
                </el-form-item>
                <el-form-item v-show="appDetailForm.partner === 1" label="等级">
                  <el-select v-model="cooperativeForm.level" size="small" filterable clearable>
                    <el-option
                      v-for="item in 4"
                      :key="item"
                      :label="`等级${item}`"
                      :value="item"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item v-show="appDetailForm.partner === 1" label="状态">
                  <el-select v-model="cooperativeForm.status" size="small">
                    <el-option label="正常" :value="0" />
                    <el-option label="熔断" :value="1" />
                    <el-option label="关闭" :value="2" />
                  </el-select>
                </el-form-item>
                <el-button size="small" type="success" @click="saveCooperativeForm">保存</el-button>
              </el-form>
            </el-popover>
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="1" label="强制升级配置">
            <el-popover placement="left" :width="400" trigger="click" :visible="forceUpgradeFormVisible">
              <template #reference>
                <el-button size="small" type="primary" plain @click="forceUpgradeFormVisible = !forceUpgradeFormVisible">设置强制升级配置</el-button>
              </template>
              <el-form label-width="120px">
                <el-form-item label="强升开关">
                  <el-switch v-model="forceUpgradeForm.upgrade_switch" size="small" />
                </el-form-item>
                <el-form-item label="强升版本上限">
                  <el-input v-model="forceUpgradeForm.upgrade_version" size="small" placeholder="请输入强升版本上限" />
                </el-form-item>
                <el-form-item label="强升链接">
                  <el-input v-model="forceUpgradeForm.upgrade_link" size="small" placeholder="请输入强升链接" />
                </el-form-item>
                <el-form-item label="强升提示文案码">
                  <el-input v-model="forceUpgradeForm.upgrade_tip_code" size="small" placeholder="请输入强升提示文案码" />
                </el-form-item>
                <el-form-item label="强升版本下限">
                  <el-input v-model="forceUpgradeForm.lower_limit_version" size="small" placeholder="请输入强升版本下限,为空不限制" />
                </el-form-item>
                <el-button size="small" type="success" @click="saveForceUpgradeForm">保存</el-button>
              </el-form>
            </el-popover>
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="1" label="应用各类开关">
            <el-popover placement="left" :width="200" trigger="click" effect="dark" :visible="appSwitchFormVisible">
              <template #reference>
                <el-button size="small" type="primary" plain @click="appSwitchFormVisible = !appSwitchFormVisible">设置应用各类开关</el-button>
              </template>
              <el-form>
                <el-form-item label="新用户赠送60S">
                  <el-switch v-model="appSwitchForm.give60s" size="small" />
                </el-form-item>
                <el-button size="small" type="success" @click="saveAppSwitchForm">保存</el-button>
              </el-form>
            </el-popover>
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="1" label="Fb登录配置">
            <el-popover placement="right" :width="300" trigger="click" :visible="fbSignFormVisible">
              <template #reference>
                <el-button size="small" type="primary" plain @click="fbSignFormVisible = !fbSignFormVisible">设置Fb登录配置</el-button>
              </template>
              <el-form label-width="80px">
                <el-form-item label="appId">
                  <el-input v-model="fbSignForm.appId" size="small" placeholder="请输入appId" />
                </el-form-item>
                <el-form-item label="appSecret">
                  <el-input v-model="fbSignForm.appSecret" size="small" placeholder="请输入appSecret" />
                </el-form-item>
                <el-button size="small" type="success" @click="saveFbSignForm">保存</el-button>
              </el-form>
            </el-popover>
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="1" label="安卓-谷歌cred">
            <el-popover placement="right" :width="300" trigger="click" :visible="gooSignFormVisible">
              <template #reference>
                <el-button size="small" type="primary" plain @click="gooSignFormVisible = !gooSignFormVisible">设置安卓-谷歌cred</el-button>
              </template>
              <el-form label-width="60px">
                <el-form-item label="clientId">
                  <el-input v-model="gooSignForm.clientId" size="small" placeholder="请输入clientId" />
                </el-form-item>
                <el-button size="small" type="success" @click="saveGooSignForm">保存</el-button>
              </el-form>
            </el-popover>
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="1" label="苹果-谷歌cred">
            <el-popover placement="left" :width="300" trigger="click" :visible="gooIosSignFormVisible">
              <template #reference>
                <el-button size="small" type="primary" plain @click="gooIosSignFormVisible = !gooIosSignFormVisible">设置苹果-谷歌cred</el-button>
              </template>
              <el-form label-width="60px">
                <el-form-item label="clientId">
                  <el-input v-model="gooIosSignForm.clientId" size="small" placeholder="请输入clientId" />
                </el-form-item>
                <el-button size="small" type="success" @click="saveGooIosSignForm">保存</el-button>
              </el-form>
            </el-popover>

          </el-descriptions-item>
          <el-descriptions-item align="center" :span="1" label="苹果登录验证">
            <el-popover placement="left" :width="300" trigger="click" :visible="appleSignFormVisible">
              <template #reference>
                <el-button size="small" type="primary" plain @click="appleSignFormVisible = !appleSignFormVisible">设置苹果登录验证</el-button>
              </template>
              <el-form label-width="90px">
                <el-form-item label="team_id">
                  <el-input v-model="appleSignForm.team_id" size="small" placeholder="请输入team_id" />
                </el-form-item>
                <el-form-item label="client_id">
                  <el-input v-model="appleSignForm.client_id" size="small" placeholder="请输入client_id" />
                </el-form-item>
                <el-form-item label="key_id">
                  <el-input v-model="appleSignForm.key_id" size="small" placeholder="请输入key_id" />
                </el-form-item>
                <el-form-item label="signing_key">
                  <el-input v-model="appleSignForm.signing_key" size="small" placeholder="请输入signing_key" />
                </el-form-item>
                <el-button size="small" type="success" @click="saveAppleSignForm">保存</el-button>
              </el-form>
            </el-popover>
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="1" label="Adjust配置">
            <el-popover placement="right" :width="300" trigger="click" :visible="adjustFormVisible">
              <template #reference>
                <el-button size="small" type="primary" plain @click="adjustFormVisible = !adjustFormVisible">设置Adjust配置</el-button>
              </template>
              <el-form label-width="90px">
                <el-form-item label="team_id">
                  <el-input v-model="adjust.app_token" size="small" placeholder="请输入app_token" />
                </el-form-item>
                <el-form-item label="client_id">
                  <el-input v-model="adjust.event_token" size="small" placeholder="请输入event_token" />
                </el-form-item>
                <el-button size="small" type="success" @click="saveAdjustForm">保存</el-button>
              </el-form>
            </el-popover>
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="4" label="FCM推送">
            <div class="flex-c">
              <div>
                <el-switch v-model="firebaseAccountInput" size="small" active-text="可编辑" inactive-text="不可编辑" />
                <el-button type="text" style="margin-left: 15px" @click="copyText(appDetailForm.firebase_account)">复制FCM推送</el-button>
              </div>
              <el-input
                v-model="appDetailForm.firebase_account"
                :disabled="!firebaseAccountInput"
                :autosize="{ minRows: 2, maxRows: 4 }"
                type="textarea"
                placeholder="请输入FCM推送"
              />
            </div>
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="4" label="谷歌支付Json串">
            <div class="flex-c">
              <div>
                <el-switch v-model="gooPayInput" size="small" active-text="可编辑" inactive-text="不可编辑" />
                <el-button type="text" style="margin-left: 15px" @click="copyText(appDetailForm.goo_pay)">复制谷歌支付Json串</el-button>
              </div>
              <el-input
                v-model="appDetailForm.goo_pay"
                :disabled="!gooPayInput"
                :autosize="{ minRows: 2, maxRows: 8 }"
                type="textarea"
                placeholder="请输入谷歌支付json"
              />
            </div>
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="4" label="首页弹窗">
            <div class="flex-c">
              <div>
                <el-switch v-model="indexDialogInput" size="small" active-text="可编辑" inactive-text="不可编辑" />
                <el-button type="text" style="margin-left: 15px" @click="copyText(appDetailForm.index_dialog)">复制</el-button>
              </div>
              <el-input
                v-model="appDetailForm.index_dialog"
                :disabled="!indexDialogInput"
                :autosize="{ minRows: 2, maxRows: 8 }"
                type="textarea"
                placeholder="请输入"
              />
            </div>
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="4" label="资料页弹窗">
            <div class="flex-c">
              <div>
                <el-switch v-model="infoDialogInput" size="small" active-text="可编辑" inactive-text="不可编辑" />
                <el-button type="text" style="margin-left: 15px" @click="copyText(appDetailForm.info_dialog)">复制</el-button>
              </div>
              <el-input
                v-model="appDetailForm.info_dialog"
                :disabled="!infoDialogInput"
                :autosize="{ minRows: 2, maxRows: 8 }"
                type="textarea"
                placeholder="请输入"
              />
            </div>
          </el-descriptions-item>
          <el-descriptions-item align="center" :span="4" label="聊天顶部文案">
            <div class="flex-c">
              <div>
                <el-switch v-model="chatTopInput" size="small" active-text="可编辑" inactive-text="不可编辑" />
                <el-button type="text" style="margin-left: 15px" @click="copyText(appDetailForm.chat_top)">复制</el-button>
              </div>
              <el-input
                v-model="appDetailForm.chat_top"
                :disabled="!chatTopInput"
                :autosize="{ minRows: 2, maxRows: 8 }"
                type="textarea"
                placeholder="请输入"
              />
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
      <template #footer>
        <span>
          <el-button type="success" @click="enterAppDetailDialog">保存详细信息</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { createAppXes, updateAppXes, getAppXesList } from '@/api/appXes' //  此处请自行替换地址
import infoList from '@/mixins/infoList'

export default {
  name: 'AppXes',
  mixins: [infoList],
  data() {
    return {
      listApi: getAppXesList,
      dialogFormVisible: false,
      jsonItemFormVisible: false,
      jsonItemForm: {},
      dialogType: {
        create: 'create',
        update: 'update',
      },
      appDialoginfo: {
        visible: false,
        type: '',
        title: '',
      },
      appDetailDialoginfo: {
        visible: false,
        type: '',
        title: '',
      },
      appForm: {
        id: null,
        bundle_id: '',
      },
      cooperativeFormVisible: false,
      cooperativeForm: {
        coop_switch: null,
        chat_callback: '',
        hang_callback: '',
        message_callback: '',
        ago_id: '',
        status: null,
        view_total: null,
        level: null,
      },
      forceUpgradeFormVisible: false,
      forceUpgradeForm: {
        upgrade_switch: null,
        upgrade_version: '',
        upgrade_link: '',
        upgrade_tip_code: '',
        lower_limit_version: '',
      },
      appSwitchFormVisible: false,
      appSwitchForm: {
        give60s: null,
      },
      appleSignFormVisible: false,
      appleSignForm: {
        team_id: '',
        client_id: '',
        key_id: '',
        signing_key: '',
      },
      gooIosSignFormVisible: false,
      gooIosSignForm: {
        clientId: ''
      },
      gooSignFormVisible: false,
      gooSignForm: {
        clientId: ''
      },
      fbSignFormVisible: false,
      fbSignForm: {
        appId: '',
        appSecret: '',
      },
      adjustFormVisible:false,
      adjustForm:{
        app_token:'',
        event_token:''
      },
      appDomainFormVisible: false,
      appDomainForm: {
        api: '',
        ws: '',
        h5: '',
        image: '',
      },
      gooPayInput: false,
      indexDialogInput: false,
      infoDialogInput: false,
      chatTopInput: false,
      firebaseAccountInput: false,
      adjust:{
          app_token:'',
          event_token:''
      },
      appDetailForm: {
        id: null,
        bundle_id: null,
        app_name: null,
        app_domain: null,
        app_key: null,
        call_price: null,
        album_price: null,
        message_price: null,
        ai_help: null,
        fcm_api_key: null,
        goo_pay: null,
        fb_sign: null,
        goo_sign: null,
        goo_ios_sign: null,
        apple_sign: null,
        app_switch: null,
        official: null,
        official_email: null,
        force_upgrade: null,
        partner: null,
        cooperative: null,
        stake: null,
        firebase_account: null,
        revenue_scheme: null,
        adjust:{
          app_token:'',
          event_token:''
        },
      },
      appDetailFormRules: {},
      appFormRules: {
        id: [
          { required: true, message: '请输入ID', trigger: 'blur' },
          { type: 'number', message: '输入的格式不正确,请输入数字', trigger: 'blur' },
        ],
        bundle_id: [
          { required: true, message: '请输入包名', trigger: 'blur' },
          { min: 1, max: 64, message: '包名长度在1-64字符之间', trigger: 'blur' },
        ],
      },
      formData: {
        bundleId: '',
        appKey: '',
        appName: '',
        appDomain: '',
        callPrice: 0,
        albumPrice: 0,
        messagePrice: 0,
        aiHelp: '',
        fcmApiKey: '',
        gooPay: '',
        fbSign: '',
        gooSign: '',
        gooIosSign: '',
        appleSign: '',
        appSwitch: '',
        official: 0,
        officialEmail: '',
        forceUpgrade: '',
        partner: 0,
        cooperative: '',
      }
    }
  },
  async created() {
    this.pageSize = 100
    await this.getTableData()
  },
  methods: {
    saveAdjustForm(){
      if (this.appDetailForm.adjust === null) {
          this.appDetailForm.adjust = {}
      }
      this.appDetailForm.adjust.app_token = this.adjust.app_token
      this.appDetailForm.adjust.event_token = this.adjust.event_token
      this.adjustFormVisible = false
      this.$message.success('保存成功')
    },
    saveAppDomainForm() {
      this.appDetailForm.app_domain = this.appDomainForm
      this.appDomainFormVisible = false
      this.$message.success('保存成功')
    },
    saveCooperativeForm() {
      this.appDetailForm.cooperative = this.cooperativeForm
      this.cooperativeFormVisible = false
      this.$message.success('保存成功')
    },
    saveForceUpgradeForm() {
      this.appDetailForm.force_upgrade = this.forceUpgradeForm
      this.forceUpgradeFormVisible = false
      this.$message.success('保存成功')
    },
    saveAppSwitchForm() {
      this.appDetailForm.app_switch = this.appSwitchForm
      this.appSwitchFormVisible = false
      this.$message.success('保存成功')
    },
    saveFbSignForm() {
      this.appDetailForm.fb_sign = this.fbSignForm
      this.fbSignFormVisible = false
      this.$message.success('保存成功')
    },
    saveGooSignForm() {
      this.appDetailForm.goo_sign = this.gooSignForm
      this.gooSignFormVisible = false
      this.$message.success('保存成功')
    },
    saveGooIosSignForm() {
      this.appDetailForm.goo_ios_sign = this.gooIosSignForm
      this.gooIosSignFormVisible = false
      this.$message.success('保存成功')
    },
    saveAppleSignForm() {
      this.appDetailForm.apple_sign = this.appleSignForm
      this.appleSignFormVisible = false
      this.$message.success('保存成功')
    },
    dialogClose() {
      this.$refs.appForm.resetFields()
      this.appDialoginfo.visible = false
    },
    appDetailDialogClose() {
      this.appDetailDialoginfo.visible = false
    },
    enterAppDetailDialog() {
      updateAppXes(this.appDetailForm).then(res => {
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: res.msg
          })
          this.appDetailDialoginfo.visible = false
          // this.$refs.appForm.resetFields()
          this.getTableData()
        }
      })
    },
    enterAppDialog() {
      this.$refs['appForm'].validate((valid) => {
        if (valid) {
          switch (this.appDialoginfo.type) {
            case this.dialogType.create:
              createAppXes(this.appForm).then(res => {
                if (res.code === 0) {
                  this.$message({
                    type: 'success',
                    message: res.msg
                  })
                  this.appDialoginfo.visible = false
                  this.$refs.appForm.resetFields()
                  this.getTableData()
                }
              })
              break
            case this.dialogType.update:
              updateAppXes(this.appForm).then(res => {
                if (res.code === 0) {
                  this.$message({
                    type: 'success',
                    message: res.msg
                  })
                  this.appDialoginfo.visible = false
                  this.$refs.appForm.resetFields()
                  this.getTableData()
                }
              })
              break
          }
        }
      })
    },
    onReset() {
      this.searchInfo = {}
    },
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    createApp() {
      this.appDialoginfo.type = this.dialogType.create
      this.appDialoginfo.title = '创建APP'
      this.appDialoginfo.visible = true
    },
    updateApp(row) {
      this.appForm = row
      this.appDialoginfo.type = this.dialogType.update
      this.appDialoginfo.title = '修改APP'
      this.appDialoginfo.visible = true
    },
    viewDetailApp(row) {
      this.appDetailForm = row
      this.appDetailDialoginfo.type = this.dialogType.update
      this.appDetailDialoginfo.title = '修改APP详细信息'
      this.appDetailDialoginfo.visible = true
      if (row.app_domain !== null) { this.appDomainForm = row.app_domain }
      if (row.fb_sign !== null) { this.fbSignForm = row.fb_sign }
      if (row.goo_sign !== null) { this.gooSignForm = row.goo_sign }
      if (row.goo_ios_sign !== null) { this.gooIosSignForm = row.goo_ios_sign }
      if (row.apple_sign !== null) { this.appleSignForm = row.apple_sign }
      if (row.app_switch !== null) { this.appSwitchForm = row.app_switch }
      if (row.force_upgrade !== null) { this.forceUpgradeForm = row.force_upgrade }
      if (row.cooperative !== null) { this.cooperativeForm = row.cooperative }
      if (row.adjust !== null){
        this.adjust = row.adjust
      }else{
        this.adjust.app_token = ''
        this.adjust.event_token = ''
      }
    },
  },
}
</script>

<style lang="scss">

  .flex-r {
    display: flex;
    flex-direction: row;
  }
  .flex-c {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
  }
  .el-popover {
    .el-form{
      .el-button--success {
        float: right !important;
      }
    }
  }
  .app-detail-dialog {
    .el-descriptions__cell{
      padding: 4px 10px !important;
    }
    .el-descriptions__label{
      width: 140px !important;
    }
    .el-descriptions__content {
      border-top: none !important;
      border-bottom: none !important;
    }
    .el-input-number--small {
      width: 100% !important;
    }

    .el-row {
      padding: 0;
    }

    .el-radio {
      margin-right: 12px;
    }

    .el-radio__label {
      padding-left: 6px;
    }
  }
</style>
