<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="国家码">
          <el-input v-model="searchInfo.country" class="cs-search-input" placeholder="CN" />
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
      </div>
      <el-table ref="tableData" style="width: 100%" :data="tableData" row-key="id" :max-height="590">
        <el-table-column align="center" label="ID" prop="id" min-width="180" />
        <el-table-column align="center" label="日期" prop="updated_at_str" min-width="180" />
        <el-table-column align="center" label="组名称" prop="name" min-width="120" />
        <el-table-column align="center" label="组内容" prop="config_abbreviation" min-width="120" />
        <el-table-column align="center" label="操作" min-width="180">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateAreaGroups(scope.row)">修改</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" top="5px" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" :rules="formDataRules" label-position="right" label-width="120px">
        <el-form-item label="组名称:" prop="name">
          <el-input v-model="formData.name" clearable />
        </el-form-item>
        <el-form-item label="配置:">
          <el-cascader
            v-model="formData.config"
            style="width: 100%;"
            filterable
            :options="countryAndStates"
            :props="props"
            collapse-tags
            clearable
          />
        </el-form-item>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { createAreaGroups, deleteAreaGroups, updateAreaGroups, findAreaGroups, getAreaGroupsList } from '@/api/areaGroups'
import { allCountryStates } from '@/api/countryStates'
import infoList from '@/mixins/infoList'

export default {
  name: 'AreaGroups',
  mixins: [infoList],
  data() {
    return {
      listApi: getAreaGroupsList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        id: 0,
        name: '',
        config: [],
      },
      formDataRules: {
        name: [{ required: true, message: '请完善该字段.', trigger: 'blur' }],
      },
      selectedCountry: [],
      countryAndStates: [],
      props: {
        expandTrigger: 'click',
        multiple: true,
      }
    }
  },
  created() {
    this.getTableData(this.nFunc, this.tableDataFormat)
    this.allCountryStates()
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteAreaGroups(row)
      })
    },
    async updateAreaGroups(row) {
      const res = await findAreaGroups({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.areaGroupsInfo
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {}
    },
    async deleteAreaGroups(row) {
      const res = await deleteAreaGroups({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createAreaGroups(this.formData)
          break
        case 'update':
          res = await updateAreaGroups(this.formData)
          break
        default:
          res = await createAreaGroups(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '成功'
        })
        this.closeDialog()
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    exportExcel() {
      const tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      const res = this.getTableHeaderProp(tableColumns)
      const tHeader = res[0]
      const filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].updated_at_str = this.formatDate(item.updated_at)
        data[i].config_abbreviation = this.formatConfigAbbreviation(item.config)
      })
    },
    allCountryStates() {
      allCountryStates({}).then(res => {
        if (res.code === 0) {
          this.countryAndStates = res.data.list
        }
      })
    },
    formatConfigAbbreviation(arr) {
      const left = '['
      const right = ']'
      let mid = []
      arr.forEach((item, index) => {
        mid.push(item[0])
      })
      mid = mid.filter((item, index) => {
        return mid.indexOf(item, 0) === index
      })
      return left + mid.join(',') + right
    }
  },
}
</script>

<style>
</style>
