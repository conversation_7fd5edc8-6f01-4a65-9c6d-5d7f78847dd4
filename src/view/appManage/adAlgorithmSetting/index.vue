<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="广告平台分类">
          <el-select v-model="searchInfo.ad_platform" clearable>
            <el-option
                v-for="item in adPlatformTypeOption"
                :key="item"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="广告类型">
          <el-select v-model="searchInfo.ad_type" clearable>
            <el-option
                v-for="item in adTypeOption"
                :key="item"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" type="success" icon="el-icon-plus" @click="openDialog">新增</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table ref="tableData" border style="width: 100%" :data="tableData" row-key="id" :max-height="590">
        <el-table-column align="center" label="日期" prop="created_at_str" min-width="180"/>
        <el-table-column align="center" label="广告平台分类" prop="ad_platform_str" min-width="140">
          <template #default="scope">
            {{adPlatformTypeOptionDict[scope.row.ad_platform]}}
          </template>
        </el-table-column>
        <el-table-column align="center" label="广告类型" prop="ad_type_str" min-width="120">
          <template #default="scope">
            {{adTypeOptionDict[scope.row.ad_type]}}
          </template>
        </el-table-column>
        <el-table-column align="center" label="每日广告次数限制" prop="day_ad_limit" min-width="200" />
        <el-table-column align="center" label="广告CD" prop="ad_cd" min-width="120" />
        <el-table-column align="center" label="操作" min-width="180">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateAdAlgorithmSetting(scope.row)">修改</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            :total="total"
            @current-change="pageChange"
            @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" top="5px" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" :rules="formDataRules" label-position="right">
        <el-form-item label="广告平台分类:" prop="ad_platform">
          <el-select style="width: 100%;" v-model="formData.ad_platform">
            <el-option
                v-for="item in adPlatformTypeOption"
                :key="item"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="广告类型:" prop="ad_type">
          <el-select style="width: 100%;" v-model="formData.ad_type" clearable>
            <el-option
                v-for="item in adTypeOption"
                :key="item"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="每日广告次数限制:" prop="day_ad_limit">
          <el-input-number style="width: 100%;" v-model="formData.day_ad_limit" clearable/>
        </el-form-item>
        <el-form-item label="广告CD:" prop="ad_cd">
          <el-input-number style="width: 100%;" v-model="formData.ad_cd" clearable/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { createAdAlgorithmSetting, deleteAdAlgorithmSetting, getAdTypeList, getAdPlatformTypeList, deleteAdAlgorithmSettingByIds, updateAdAlgorithmSetting, findAdAlgorithmSetting, getAdAlgorithmSettingList} from '@/api/adAlgorithmSetting' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
export default {
  name: 'AdAlgorithmSetting',
  mixins: [infoList],
  data() {
    return {
      listApi: getAdAlgorithmSettingList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      adTypeOption: [],
      adTypeOptionDict: {},
      adPlatformTypeOptionDict: {},
      adPlatformTypeOption: [],
      formData: {
        ad_platform: null,
        ad_type: null,
        day_ad_limit: null,
        ad_cd: null,
      },
      formDataRules: {
        ad_platform: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        ad_type: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        day_ad_limit: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        ad_cd: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
    }
  },
  created() {
    this.getTableData(this.nFunc, this.tableDataFormat)
    this.initAdTypeList()
    this.initAdPlatformType()
  },
  methods: {
    initAdTypeList() {
      getAdTypeList().then(res=>{
        this.adTypeOption = res.data || []
        let adDict = {}
        this.adTypeOption.forEach(item=>{
          adDict[item.value] = item.label
        })
        this.adTypeOptionDict = adDict
      })
    },
    initAdPlatformType() {
      getAdPlatformTypeList().then(res=>{
        this.adPlatformTypeOption = res.data || []
        let pDict = {}
        this.adPlatformTypeOption.forEach(i=>{
          pDict[i.value] = i.label
        })
        this.adPlatformTypeOptionDict = pDict
      })
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteAdAlgorithmSetting(row)
      })
    },
    async updateAdAlgorithmSetting(row) {
      const res = await findAdAlgorithmSetting({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {}
    },
    async deleteAdAlgorithmSetting(row) {
      const res = await deleteAdAlgorithmSetting({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createAdAlgorithmSetting(this.formData)
          break
        case 'update':
          res = await updateAdAlgorithmSetting(this.formData)
          break
        default:
          res = await createAdAlgorithmSetting(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '成功'
        })
        this.closeDialog()
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
      })
    },
  },
}
</script>

<style>
</style>
