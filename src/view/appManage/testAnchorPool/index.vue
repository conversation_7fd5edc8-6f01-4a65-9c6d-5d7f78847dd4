<template>
  <div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
        <el-button size="mini" type="primary" icon="el-icon-aim" :loading="isCheckLoading" @click="checkAnchorPool">检测</el-button>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="center" label="按钮组" width="120" fixed="left">
          <template #default="scope">
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除
            </el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="用户ID" prop="id" width="120" />
        <el-table-column align="center" label="AppId" prop="appId" width="120">
          <template #default="scope">{{ formaAppList(scope.row.appId) }}</template>
        </el-table-column>
        <el-table-column align="center" label="用户名" prop="username" width="120" />
        <el-table-column align="center" label="密码" prop="password" width="120" />
        <el-table-column align="center" label="昵称" prop="nickname" width="120" />
        <el-table-column align="center" label="头像" min-width="50">
          <template #default="scope">
            <CustomPic style="margin-top:8px" :pic-src="scope.row.avatar" />
          </template>
        </el-table-column>

        <el-table-column align="center" label="性别" prop="status" width="120">
          <template #default="scope">{{ formatSex(scope.row.gender) }}</template>
        </el-table-column>

        <el-table-column align="center" label="注册日期" width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>

        <el-table-column align="center" label="身份" prop="role" width="120">
          <template #default="scope">{{ formatRole(scope.row.role) }}</template>
        </el-table-column>

        <el-table-column align="center" label="工作状态" prop="role" width="120">
          <template #default="scope">{{ formatWorkingCondition(scope.row.workingCondition) }}</template>
        </el-table-column>

        <el-table-column align="center" label="级别" prop="levels" width="120" />

        <el-table-column align="center" label="是否VIP" prop="vipExpireAt" width="120">
          <template #default="scope">{{ (Date.parse(scope.row.vipExpireAt) > new Date())?'VIP':'非VIP' }}
          </template>
        </el-table-column>

        <el-table-column align="center" label="VIP过期时间" width="180">
          <template #default="scope">{{ formatDate(scope.row.vipExpireAt) }}</template>
        </el-table-column>

        <el-table-column align="center" label="用户状态" width="180">
          <template #default="scope">{{ formatState(scope.row.state) }}</template>
        </el-table-column>

        <el-table-column align="center" label="最后登录IP" prop="lastLoginIp" width="120" />

        <el-table-column align="center" label="最后登录时间" width="180">
          <template #default="scope">{{ formatDate(scope.row.lastLoginAt) }}</template>
        </el-table-column>

        <el-table-column align="center" label="账号类型" width="120">
          <template #default="scope">{{ formatLoginType(scope.row.loginType) }}</template>
        </el-table-column>
        <el-table-column align="center" label="在线状态" width="120">
          <template #default="scope">{{ formatOnline(scope.row.online) }}</template>
        </el-table-column>
        <el-table-column align="center" label="用户余额" prop="diamonds" width="120" />
        <el-table-column align="center" label="渠道" prop="utmSource" width="120" />
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作" top="1%" width="397px">
      <el-form :model="formData" label-width="85px" :inline="true" style="max-height: 80vh;overflow-y: auto;">
        <el-form-item label="用户ID:" style="width:80%">
          <el-input v-model="formData.userId" clearable placeholder="请输入主播ID" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createUsers,
  deleteUsers,
  getUsersList,
  getUsersCheck,
} from '@/api/test_anchor_pool' //  此处请自行替换地址
import { getAppXesList } from '@/api/appXes'
import infoList from '@/mixins/infoList'
import CustomPic from '@/components/customPic/index.vue'

const roleOptions = [
  {
    value: 1,
    label: '用户',
  },
  {
    value: 2,
    label: '主播',
  },
  {
    value: 3,
    label: '客服',
  },
  {
    value: 4,
    label: '测试',
  },
]

const countryCodeOptions = [
  {
    value: 'IN',
    label: 'India',
  },
  {
    value: 'SA',
    label: 'Saudi Arabia',
  },
  {
    value: 'PH',
    label: 'Philippines',
  },
  {
    value: 'BR',
    label: 'Brazil',
  },
  {
    value: 'ID',
    label: 'Indonesia',
  },
  {
    value: 'IQ',
    label: 'Iraq',
  },
  {
    value: 'MY',
    label: 'Malaysia',
  },
  {
    value: 'KW',
    label: 'Kuwait',
  },
  {
    value: 'MX',
    label: 'Mexico',
  },
  {
    value: 'MA',
    label: 'Morocco',
  },
  {
    value: 'PK',
    label: 'Pakistan',
  },
  {
    value: 'RU',
    label: 'Russian',
  },
  {
    value: 'TH',
    label: 'Thailand',
  },
  {
    value: 'TR',
    label: 'Turkey',
  },
  {
    value: 'UK',
    label: 'United Kingdom',
  },
  {
    value: 'UA',
    label: 'Ukraine',
  },
  {
    value: 'VN',
    label: 'Viet Nam',
  },
]

const genderOptions = [
  {
    value: 1,
    label: '未知',
  },
  {
    value: 2,
    label: '女',
  },
  {
    value: 3,
    label: '男',
  },
]

export default {
  name: 'TestAnchorPool',
  components: { CustomPic },
  mixins: [infoList],
  data() {
    return {
      isCheckLoading: false,
      listApi: getUsersList,
      appListOptions: [],
      countryCodeOptions: countryCodeOptions,
      roleOptions: roleOptions,
      genderOptions: genderOptions,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        userId: 0,
      }
    }
  },
  async created() {
    await this.getTableData()
    await this.getAppList()
  },
  methods: {
    formaAppList: function(bool) {
      let text
      this.appListOptions.forEach(function(element) {
        if (bool === element.id) {
          text = element.appName
          return text
        }
      })
      return text
    },
    async checkAnchorPool() {
      this.isCheckLoading = true
      const res = await getUsersCheck({})
      if (res.code === 0) {
        this.isCheckLoading = false
        this.getTableData()
      }
    },
    async getAppList() {
      const res = await getAppXesList()
      this.appListOptions = res.data.list
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteUsers(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
        this.multipleSelection.map(item => {
          ids.push(item.id)
        })
      const res = await deleteUsersByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        userId: 0
      }
    },
    async deleteUsers(row) {
      const res = await deleteUsers({ userId: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createUsers({ userId: parseInt(this.formData.userId) })
          break
        default:
          res = await createUsers({ userId: parseInt(this.formData.userId) })
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    }
  },
}
</script>

<style>
</style>
