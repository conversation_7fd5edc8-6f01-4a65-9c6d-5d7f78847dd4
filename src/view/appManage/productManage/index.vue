<template>
  <div>
    <!--搜索框-->
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="产品:">
          <el-select v-model="searchInfo.app_id" clearable filterable placeholder="请选择APP">
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="`${item.app_name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="产品类型:">
          <el-select v-model="searchInfo.type" clearable filterable placeholder="请选择产品类型">
            <el-option
              v-for="item in productType"
              :key="item.type"
              :label="`${item.desc}(${item.type})`"
              :value="item.type"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model.number="searchInfo.state" clearable filterable placeholder="上架状态">
            <el-option
              v-for="item in shelfStateOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="国家/地区">
          <el-select v-model="searchInfo.country_code" clearable filterable placeholder="国家/地区">
            <el-option
              v-for="item in areaGroupTreeOptionsMy"
              :key="item.id"
              :label="`${item.name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item class="gva-btn-list">
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button v-show="tableData.length > 0" size="mini" icon="el-icon-download" @click="handleDownload">导出</el-button>
          <el-button size="mini" icon="el-icon-download" @click="handleDownloadTemplate">模版</el-button>
        </el-form-item>
        <el-form-item>
          <el-upload
            :action="`${path}/products/importProducts`"
            :headers="{'x-token':token}"
            :on-success="loadExcel"
            :show-file-list="false"
          >
            <el-button size="mini" icon="el-icon-plus">导入</el-button>
          </el-upload>
        </el-form-item>
      </el-form>
    </div>
    <!--列表显示-->
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
        <el-button size="mini" type="success" icon="el-icon-refresh" @click="syncData">数据同步</el-button>
      </div>
      <el-table
        ref="tableData"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
      >
        <el-table-column align="center" label="操作" prop="operation" width="160">
          <template #default="scope">
            <el-button
              type="text"
              icon="el-icon-edit"
              size="small"
              class="table-button"
              @click="updateProducts(scope.row)"
            >修改
            </el-button>
            <!--<el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>-->
          </template>
        </el-table-column>
        <el-table-column align="center" label="ID" prop="id" width="80" />
        <el-table-column align="center" label="日期" prop="created_at" min-width="160" />
        <el-table-column align="center" label="APP" prop="app_name" min-width="120" />
        <el-table-column align="center" label="产品名称" prop="name" min-width="120" show-overflow-tooltip />
        <el-table-column align="center" label="子名称" min-width="120" prop="sub_name" show-overflow-tooltip/>
        <el-table-column align="center" label="产品类型" prop="type" min-width="120" />
        <el-table-column align="center" label="谷歌的商品ID" prop="google" min-width="120" />
        <el-table-column align="center" label="谷歌商品价格" prop="goo_price" min-width="120" />
        <!--<el-table-column align="center" label="PayTm价格" prop="paytm" min-width="120" />-->
        <el-table-column align="center" label="钻石数" prop="diamonds" min-width="120" />
        <el-table-column align="center" label="奖励钻石数" prop="bonus" min-width="120" />
        <el-table-column align="center" label="排序值" prop="sort" min-width="120" />
        <el-table-column align="center" label="状态" prop="state" min-width="120" />
        <el-table-column align="center" label="会员天数" prop="days" min-width="120" />
        <el-table-column align="center" label="支付描述" prop="describe" min-width="120" />
        <el-table-column align="center" label="优惠终止" prop="expire" min-width="120" />
        <el-table-column align="center" label="钻石兑换VIP值" prop="exchange" min-width="120" />
        <el-table-column align="center" label="Popular" prop="hot" min-width="120" />
        <el-table-column align="center" label="折扣" prop="discount" min-width="120" />
        <el-table-column align="center" label="分期赠送方案ID" prop="stage_id" min-width="120" />
        <el-table-column align="center" label="苹果的商品ID" prop="apple" min-width="120" />
        <el-table-column align="center" label="新手礼包等级" prop="sign" min-width="120" />
        <el-table-column align="center" label="封面图" prop="cover" min-width="120">
          <template #default="scope">
            <el-image
              fit="contain"
              :src="scope.row.cover"
              :preview-src-list="[scope.row.cover]"
              style="width: 40px; height: 40px;"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="别名" prop="alias" min-width="120" />
        <el-table-column align="center" label="支付列表展示" prop="paid_type" width="120" />
        <el-table-column align="center" label="国家/地区" prop="country_code" width="120" />
        <el-table-column align="center" label="内购ID(安卓)" prop="google_sku_id" width="120" />
        <!--<el-table-column align="center" label="内购ID(苹果)" prop="apple_sku_id" width="120" />
        <el-table-column align="center" label="内购类型" prop="sku_type" width="120" />
        <el-table-column align="center" label="内购定价" prop="sku_price" width="120" />-->
        <el-table-column align="center" label="ABTest模版" prop="ab_test" width="120" />
        <el-table-column align="center" label="赠送描述" prop="bonus_describe" width="120" />
        <el-table-column align="center" label="渠道" prop="utm_source" width="120" />
        <el-table-column align="center" label="标签" min-width="120">
          <template #default="scope">{{ formatProductLabels(scope.row.labels) }}</template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <!--弹窗-->
    <el-dialog
      v-model="dialogFormVisible"
      top="10px"
      :before-close="closeDialog"
      title="弹窗操作"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
    >
      <el-form
        ref="formData"
        :model="formData"
        :rules="formDataRules"
        label-position="right"
        label-width="120px"
        inline
      >
        <el-form-item label="APP:" prop="app_id">
          <el-select v-model="formData.app_id" clearable filterable @change="bindAppSkuConfig">
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="item.app_name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="产品名称:" prop="name">
          <el-input v-model="formData.name" :maxlength="32" clearable show-word-limit/>
        </el-form-item>
        <el-form-item label="商品子名称:" prop="sub_name">
          <el-input v-model="formData.sub_name" :maxlength="128" clearable show-word-limit/>
        </el-form-item>
        <el-form-item label="产品别名:" prop="alias">
          <el-input v-model="formData.alias" maxlength="64" show-word-limit clearable />
        </el-form-item>
        <el-form-item label="产品类型:" prop="type">
          <el-select v-model="formData.type" clearable filterable>
            <el-option
              v-for="item in productType"
              :key="item.type"
              :label="item.desc"
              :value="item.type"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="产品关联:" prop="google_sku_id">
          <el-select v-model="formData.google_sku_id" @change="bindGoogleSkuId">
            <el-option
              v-for="item in skuConfig"
              :key="item.id"
              :label="`[$${item.sku_price}]${item.title}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="谷歌产品ID:" prop="google">
          <el-input v-model="formData.google" maxlength="32" disabled show-word-limit clearable />
        </el-form-item>
        <el-form-item label="美元价格:" prop="goo_price">
          <el-input-number v-model="formData.goo_price" :min="0" :precision="2" disabled :controls="false" clearable />
        </el-form-item>
        <el-form-item label="钻石数:" prop="diamonds">
          <el-input-number v-model="formData.diamonds" :min="0" :controls="false" clearable />
        </el-form-item>
        <el-form-item label="赠送钻石数:" prop="bonus">
          <el-input-number v-model="formData.bonus" :min="0" :controls="false" clearable />
        </el-form-item>
        <el-form-item label="排序值:" prop="sort">
          <el-input-number v-model="formData.sort" :min="1" :controls="false" clearable />
        </el-form-item>
        <el-form-item label="状态:" prop="state">
          <el-select v-model="formData.state">
            <el-option
              v-for="item in realStateOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="会员天数:" prop="days">
          <el-input-number v-model="formData.days" :min="0" :controls="false" />
        </el-form-item>
        <el-form-item label="支付描述:" prop="describe">
          <el-input v-model="formData.describe" clearable />
        </el-form-item>
        <el-form-item label="钻石兑换VIP值:" prop="exchange">
          <el-input-number v-model="formData.exchange" :min="0" :controls="false" />
        </el-form-item>
        <el-form-item label="角标:" prop="hot">
          <el-select v-model="formData.hot" clearable>
            <el-option
              v-for="item in hotOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="打折文案:" prop="discount">
          <el-input v-model="formData.discount" clearable />
        </el-form-item>
        <el-form-item label="赠送方案:" prop="stage_id">
          <el-select v-model="formData.stage_id" clearable filterable>
            <el-option
              v-for="item in stagesDiamonds"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="新手礼包等级:">
          <el-input v-model="formData.sign" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="国家/地区:" prop="country_code">
          <el-select v-model="formData.country_code">
            <el-option
              v-for="item in areaGroupTreeOptions"
              :key="item.id+''"
              :label="`${item.name}(${item.id})`"
              :value="item.id+''"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="ABTest模版:">
          <el-select v-model="formData.ab_test" filterable placeholder="ABTest模版">
            <el-option
              v-for="item in abTemplates"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!--<el-form-item label="封面图:">
          <el-input v-model="formData.cover" clearable placeholder="请输入" />
        </el-form-item>-->
        <el-form-item label="赠送描述:">
          <el-input v-model="formData.bonus_describe" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="渠道:">
          <el-select v-model="formData.utm_source" filterable placeholder="分渠道定价">
            <el-option
              v-for="item in sourceOption"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="标签:">
          <el-cascader
              v-model="selectedLabels"
              style="width:100%"
              :options="labelsOption"
              :props="{multiple:true, label:'label',value:'value',disabled:'disabled',emitPath:false}"
              filterable
          />
        </el-form-item>
        <el-form-item label="封面图">
          <el-upload
              ref="handleImage"
              :action="`${path}/files/createFiles`"
              :headers="{ 'x-token': token }"
              list-type="picture-card"
              :file-list="coverFileList"
              :on-success="handleImageSuccess"
              :on-progress="handleProgressLoading"
              :multiple="false"
              :limit="1"
              accept=".jpg, .jpeg, .png, .gif"
              :on-remove="handleImageRemove"
          >
            <i class="el-icon-plus"/>
          </el-upload>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="syncDataDialogVisible" :before-close="closeSyncDialog" title="数据同步" width="30%" top="5px">
      <el-form ref="syncFormDataRef" :model="syncFormData" :rules="syncFormDataRules" label-position="right"
               label-width="100px">
        <el-form-item label="源AppId:" prop="source_app_id">
          <el-row :gutter="14">
            <el-col :span="20">
              <el-input-number style="width: 100%;" v-model="syncFormData.source_app_id"/>
            </el-col>
            <el-col :span="4">
              <el-button size="small" type="primary" @click="searchSyncTableData">查询</el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="目标AppId:" prop="target_app_id">
          <el-input-number style="width: 100%;" v-model="syncFormData.target_app_id"/>
        </el-form-item>
      </el-form>
      <el-table
          size="small"
          border
          v-if="syncTableVisible"
          style="width: 100%"
          tooltip-effect="dark"
          :data="syncTableData"
          row-key="id"
          :height="400"
      >
        <el-table-column align="center" label="APP" prop="app_id" min-width="120">
          <template #default="scope">{{ formatAppName(scope.row.app_id) }}</template>
        </el-table-column>
        <el-table-column align="center" label="产品名称" prop="name" min-width="120" show-overflow-tooltip/>
        <el-table-column align="center" label="产品类型" prop="type" min-width="120" show-overflow-tooltip/>
        <el-table-column align="center" label="谷歌的商品ID" prop="google" min-width="140" show-overflow-tooltip/>
        <el-table-column align="center" label="谷歌的商品价格" prop="goo_price" min-width="140" show-overflow-tooltip/>

      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeSyncDialog">取 消</el-button>
          <el-button :disabled="syncOkDisabled" size="small" type="primary" @click="enterSyncDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
const path = import.meta.env.VITE_BASE_API
import {
  createProducts,
  deleteProducts,
  deleteProductsByIds,
  findProducts,
  getAppSkuList,
  getProductsList,
  getProductsType,
  getSkuList,
  syncProducts,
  updateProducts,
} from '@/api/products' //  此处请自行替换地址
import {getStagesDiamondsList} from '@/api/stagesDiamonds'
import {getAreaGroupsConfig} from '@/api/areaGroups'
import infoList from '@/mixins/infoList'

export default {
  name: 'Products',
  mixins: [infoList],
  data() {
    var validateTargetAppId = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入'));
      } else {
        if (this.syncFormData.source_app_id === this.syncFormData.target_app_id) {
          callback(new Error('源APP和目标APP不能是一样的'));
        }
        callback();
      }
    };
    return {
      listApi: getProductsList,
      dialogFormVisible: false,
      syncTableVisible: false,
      syncOkDisabled: false,
      syncTableData: [],
      syncDataDialogVisible: false,
      syncFormData: {
        source_app_id: 5,
        target_app_id: null
      },
      syncFormDataRules: {
        source_app_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        target_app_id: [{validator: validateTargetAppId, trigger: 'blur'}],
      },
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      coverFileList: [],
      formData: {
        app_id: null,
        name: '',
        sub_name: '',
        type: null,
        google: '',
        goo_price: 0,
        paytm: 0,
        diamonds: 0,
        bonus: 0,
        sort: 1,
        state: 1,
        days: 0,
        describe: '',
        expire: null,
        exchange: 0,
        hot: null,
        discount: '',
        stage_id: null,
        apple: '',
        sign: '',
        cover: '',
        alias: '',
        paid_type: 2,
        country_code: '',
        google_sku_id: 0,
        apple_sku_id: 0,
        sku_type: 1,
        sku_price: 0,
        ab_test: '',
        utm_source: '',
        labels: '',
        bonus_describe: '',
      },
      formDataRules: {
        app_id: [
          { required: true, message: '请选择APP', trigger: 'blur' },
        ],
        alias: [
          { min: 0, max: 32, message: '产品别名ID长度在0-32字符之间', trigger: 'blur' },
        ],
        name: [
          { required: true, message: '请输入产品名称', trigger: 'blur' },
        ],
        type: [
          { required: true, message: '请选择产品类型', trigger: 'blur' },
        ],
        google: [
          { min: 0, max: 32, message: '谷歌产品ID长度在0-32字符之间', trigger: 'blur' },
        ],
        goo_price: [
          { required: true, message: '请输入产品价格', trigger: 'blur' },
          { type: 'number', min: 0, message: '美元价格最小为0', trigger: 'blur' },
        ],
        paytm: [
          { required: true, message: '请输入产品价格', trigger: 'blur' },
          { type: 'number', min: 0, message: '卢比价格最小为0', trigger: 'blur' },
        ],
        diamonds: [
          { type: 'number', min: 0, message: '赠送钻石数最小为0', trigger: 'blur' },
        ],
        bonus: [],
        sort: [
          { required: true, message: '请输入排序值', trigger: 'blur' },
          { type: 'number', min: 1, message: '排序值最小为1', trigger: 'blur' },
        ],
        state: [
          { required: true, message: '请选择上下架状态', trigger: 'blur' },
        ],
        days: [
          { type: 'number', min: 0, message: '会员天数最小为0', trigger: 'blur' },
        ],
        describe: [
          { min: 0, max: 32, message: '支付描述长度在0-32字符之间', trigger: 'blur' },
        ],
        exchange: [
          { type: 'number', min: 0, message: '钻石兑换VIP值最小为0', trigger: 'blur' },
        ],
        hot: [],

      },
      productType: [],
      skuConfig: [],
      shelfStateOptions: [
        { value: 0, label: '未选择' },
        { value: 1, label: '已上架' },
        { value: 2, label: '已下架' },
      ],
      realStateOptions: [
        { value: 1, label: '上架' },
        { value: 2, label: '下架' },
      ],
      stagesDiamonds: [],
      hotOptions: [
        { value: 1, label: 'hot' },
        { value: 2, label: 'most popular' },
      ],
      paidTypeOptions: [
        { value: 1, label: '支付前展示支付后隐藏' },
        { value: 2, label: '支付前支付后都显示' },
        { value: 3, label: '支付前隐藏支付后显示' },
      ],
      countryTreeOptions: [
        { value: '', label: '不限' },
        { value: 'IN', label: '印度(IN)' },
        { value: 'PK', label: '巴基斯坦(PK)' },
        { value: 'MY', label: '马拉西亚(MY)' },
        { value: 'ID', label: '印度尼西亚(ID)' },
        { value: 'UA', label: '乌克兰(UA)' },
        { value: 'VN', label: '越南(VN)' },
        { value: 'SA', label: '沙特(SA)' },
        { value: 'PH', label: '菲律宾(PH)' },
        { value: 'BR', label: '巴西(BR)' },
        { value: 'TH', label: '泰国(TH)' },
        { value: 'TR', label: '土耳其(TR)' },
        { value: 'US', label: '美国(US)' },
        { value: 'IQ', label: '伊拉克(IQ)' },
        { value: 'KW', label: '科威特(KW)' },
        { value: 'MX', label: '墨西哥(MX)' },
        { value: 'MA', label: '摩洛哥(MA)' },
        { value: 'RU', label: '俄罗斯(RU)' },
        { value: 'UK', label: '英国(UK)' },
        { value: 'SG', label: '新加坡(SG)' },
      ],
      areaGroupTreeOptions: [],
      areaGroupTreeOptionsMy: [{name: 'T1', id: 1},{name: 'T2', id: 2},{name: 'T3', id: 3}],
      selectedLabels: [],
      abTemplates: [
        { value: 'default', label: 'default' },
      ],
      sourceOption: [
        { value: '', label: '不限' },
        { value: 'organic', label: 'Organic' },
        { value: 'non-organic', label: 'Non-Organic' },
      ],
      labelsOption: [
        { value: '@1@', label: '付费意愿' },
        { value: '@2@', label: 'Message按次展现' },
        { value: '@3@', label: '特惠套餐' },
      ],
      path: path,
    }
  },
  async created() {
    this.page = 1
    this.pageSize = 100
    await this.getTableData(this.nFunc, this.tableDataFormat)
    await this.getProductType()
    await this.getStagesDiamonds()
    this.getAreaGroupsConfig()
  },
  methods: {
    handleImageRemove(file, fileList) {
      this.formData.cover = ''
      this.coverFileList = []
    },
    handleImageSuccess(res) {
      this.coverFileList = []
      const {data} = res
      if (data.url) {
        this.formData.cover = data.url
        this.coverFileList.push(data)
      }
      this.progressLoading.close()
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at = this.formatDate(item.created_at)
        data[i].app_name = this.formatAppName(item.app_id)
        data[i].type = this.formatProductType(item.type)
        data[i].state = item.state === 1 ? '已上架' : '已下架'
        data[i].paid_type = this.formatProductPaidType(item.paid_type)
        data[i].country_code = this.formatProductAreaGroup(item.country_code)
      })
    },
    handleDownload() {
      const tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      const res = this.getTableHeaderProp(tableColumns)
      const tHeader = res[0]
      const filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    handleDownloadTemplate() {
      import('@/utils/excel').then((excel) => {
        const tHeader = [
          'app_id(int)所属APP',
          'skus.title(string)购买名称',
          'skus.sku_type(int)产品类型(1:安卓2:苹果)',
          'skus.sku(string)产品ID',
          'skus.sku_price(float)产品价格',
          'p.name(string)产品名称',
          'p.alias(string)产品别名',
          'p.type(int)类型(1VIP内购 2钻石内购 3VIP订阅 4弹窗VIP 5弹窗钻石 6新手礼包)',
          'p.diamonds(int)钻石数(大于等于0)',
          'p.bonus(int)赠送钻石数(大于等于0)',
          'p.sort(int)排序值(越大越靠前)',
          'p.state(int)上架状态(1上架 2下架)',
          'p.days(int)会员天数(大于等于0)',
          'p.describe(string)产品描述(不超过32字符)',
          'p.exchange(int)兑换VIP所需钻石(大于等于0)',
          'p.hot(int)角标(1hot, 2most popular)',
          'p.discount(string)打折文案',
          'p.sign(int)新手礼包等级(1一级 2二级)',
          'p.areaGroup(int)地区组ID(可不传,不支持多个组,来源:地区组管理)',
          'p.ab_test(string)ABTest模版',
          'p.paid_type(int)列表展示(可不传,1支付前展示支付后隐藏 2支付前支付后都显示 3支付前隐藏支付后显示)',
          'p.cover(string)(封面图 值为:https://s3.oklove.top/image/pk1.webp)',
          'p.bonus_describe(string)(赠送描述)',
          'p.umt_source(string)(渠道 organic/non-organic)',
          'p.labels(string)(标签 值为:@1@,@2@)',
        ]
        excel.export_json_to_excel({
          header: tHeader,
          data: [],
          filename: '产品导入模版',
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
      })
    },
    loadExcel() {
      this.page = 1
      this.pageSize = 100
      this.getTableData(() => {
      }, this.tableDataFormat)
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 100
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteProducts(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
      this.multipleSelection.map(item => {
        ids.push(item.id)
      })
      const res = await deleteProductsByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    async getProductType() {
      const res = await getProductsType()
      if (res.code === 0) {
        this.productType = res.data.list
      }
    },
    async getStagesDiamonds() {
      const res = await getStagesDiamondsList()
      if (res.code === 0) {
        this.stagesDiamonds = res.data.list
      }
    },
    async updateProducts(row) {
      this.coverFileList = []
      const res = await findProducts({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.productsInfo
        if (this.formData.cover !== '') {
          this.coverFileList.push({name: '', url: this.formData.cover})
        }
        if (this.formData.labels !== '') {
          this.selectedLabels = this.formData.labels.split(',')
        } else {
          this.selectedLabels = []
        }
        this.formData.goo_price = parseFloat(this.formData.goo_price)
        this.formData.paytm = parseFloat(this.formData.paytm)
        this.formData.sku_price = parseFloat(this.formData.sku_price)
        this.dialogFormVisible = true
      }
      const sku = await getSkuList({id: row.id})
      if (sku.code === 0) {
        this.skuConfig = sku.data.list
      }
    },
    searchSyncTableData() {
      getProductsList({
        app_id: this.syncFormData.source_app_id,
        state: 1,
        page: 1,
        pageSize: 10000,
      }).then(res => {
        this.syncTableVisible = true
        this.syncTableData = res.data.list || []

        this.syncTableData.map((item, i, data) => {
          data[i].created_at = this.formatDate(item.created_at)
          data[i].app_name = this.formatAppName(item.app_id)
          data[i].type = this.formatProductType(item.type)
          data[i].state = item.state === 1 ? '已上架' : '已下架'
        })

      })
    },
    closeSyncDialog() {
      this.syncDataDialogVisible = false
      this.syncTableVisible = false
      this.syncTableData = []
      this.syncFormData = {
        source_app_id: 5
      }
    },
    enterSyncDialog() {
      this.$refs['syncFormDataRef'].validate(async valid => {
        if (!valid) return
        let res
        this.syncOkDisabled = true
        res = await syncProducts(this.syncFormData)
        if (res.code === 0) {
          this.syncOkDisabled = false
          this.$message({
            type: 'success',
            message: '成功'
          })
          this.closeSyncDialog()
          this.getTableData(this.nFunc, this.tableDataFormat)
        }
      })
    },
    syncData() {
      this.syncDataDialogVisible = true
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.$refs['formData'].resetFields()
      this.formData = {
        app_id: 0,
        name: '',
        sub_name: '',
        type: null,
        google: '',
        goo_price: 0,
        paytm: 0,
        diamonds: 0,
        bonus: 0,
        sort: 1,
        state: 1,
        days: 0,
        describe: '',
        expire: null,
        exchange: 0,
        hot: null,
        discount: '',
        stage_id: null,
        apple: '',
        sign: '',
        cover: '',
        alias: '',
        paid_type: 2,
        country_code: '',
        google_sku_id: 0,
        apple_sku_id: 0,
        sku_type: 1,
        sku_price: 0,
        ab_test: '',
        bonus_describe: '',
        utm_source: '',
        labels: '',
      }
      this.skuConfig = []
      this.selectedLabels = []
    },
    async deleteProducts(row) {
      const res = await deleteProducts({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    async enterDialog() {
      this.$refs['formData'].validate(async valid => {
        if (!valid) return

        let labelsVal = ''
        for (const element of this.selectedLabels) {
          labelsVal += element + ','
        }
        this.formData.labels = this.trim(',', labelsVal)
        let res
        switch (this.type) {
          case 'create':
            res = await createProducts(this.formData)
            break
          case 'update':
            res = await updateProducts(this.formData)
            break
          default:
            res = await createProducts(this.formData)
            break
        }
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '创建/更改成功'
          })
          this.closeDialog()
          this.getTableData(this.nFunc, this.tableDataFormat)
        }
      })
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    formatProductPaidType(paidType) {
      let text
      this.paidTypeOptions.forEach((item, index) => {
        if (paidType === item.value) {
          text = item.label
        }
      })
      return text
    },
    bindGoogleSkuId(val) {
      this.skuConfig.forEach((item, index) => {
        if (val === item.id) {
          this.formData.google = item.sku
          this.formData.goo_price = parseFloat(item.sku_price)
        }
      })
    },
    bindAppSkuConfig(val) {
      getAppSkuList({ app_id: val }).then(res => {
        if (res.code === 0) {
          this.skuConfig = res.data.list || []
        }
      })
    },
    trim(aa, bb) {
      const a = aa !== '' ? aa : ' '
      const b = bb !== '' ? bb : ''
      if (b === '') {
        return ''
      }
      const la = a.length
      const lb = b.length
      let s = false
      let e = false
      if (b.indexOf(a) === 0) {
        s = true
      }
      if (b.lastIndexOf(a) === lb - la) {
        e = true
      }
      var r = b
      if (e) {
        if (r.length > 0) {
          r = r.substring(0, lb - la)
          return this.trim(a, r)
        } else {
          return ''
        }
      }
      if (s) {
        if (r.length > 0) {
          r = r.substring(la, r.length)
          return this.trim(a, r)
        } else {
          return ''
        }
      }
      return r
    },
    formatProductLabels(labels) {
      let res = ''
      if (labels === '') {
        return res
      }
      const labelsArr = labels.split(',')
      labelsArr.forEach((item, index) => {
        this.labelsOption.forEach((item1, index1) => {
          if (item1.value === item) {
            res += item1.label + ','
          }
        })
      })

      return this.trim(',', res)
    },
    getAreaGroupsConfig() {
      getAreaGroupsConfig({}).then(res => {
        if (res.code === 0) {
          this.areaGroupTreeOptions = res.data.list
        }
      })
    },
    formatProductAreaGroup(areaId) {
      let ret = ''
      if (areaId === '') {
        return ret
      }

      this.areaGroupTreeOptions.forEach((item, index) => {
        const tmpId = item.id + ''
        if (areaId === tmpId) {
          ret = item.name
        }
      })

      return ret
    }
  },
}
</script>

<style>
</style>
