<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="语言名称">
          <el-input v-model="searchInfo.name" placeholder="请输入语言名称" />
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" type="success" icon="el-icon-plus" @click="openDialog">新增</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table
          ref="multipleTable"
          style="width: 100%"
          tooltip-effect="dark"
          :data="tableData"
          row-key="id"
      >
        <el-table-column align="left" label="语言名称" prop="name" min-width="120" />
        <el-table-column align="left" label="英语名称" prop="en_name" min-width="120" />
        <el-table-column align="left" label="语言Code" prop="code" min-width="120" />
        <el-table-column align="left" label="语言Icon" prop="icon" min-width="120">
          <template #default="scope">
            <el-image style="width: 30px; height: 30px" :src="scope.row.icon" fit="contain" :preview-src-list="[scope.row.icon]" />
          </template>
        </el-table-column>
        <el-table-column align="left" label="排序" prop="sort" min-width="120" />
        <el-table-column align="left" label="操作">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateLanguages(scope.row)">修改</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            :total="total"
            @current-change="handleCurrentChange"
            @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog top="10px" v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作">
      <el-form ref="formData" :model="formData" :rules="formDataRules" label-position="right" label-width="110px">
        <el-form-item label="语言名称:" prop="name">
          <el-input v-model="formData.name" maxlength="32" show-word-limit clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="英语名称:" prop="en_name">
          <el-input v-model="formData.en_name" maxlength="64" show-word-limit clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="语言Code:" prop="code">
          <el-input v-model="formData.code" maxlength="16" show-word-limit clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="语言Icon:" prop="icon">
          <el-upload
              ref="handleIcon"
              :action="`${path}/files/createFiles`"
              :headers="{ 'x-token': token }"
              list-type="picture-card"
              :file-list="iconFileList"
              :on-success="iconUploadSuccess"
              :on-preview="imgPreview"
              :on-progress="handleProgressLoading"
              :on-exceed="handleImgExceed"
              :multiple="false"
              :limit="1"
              accept=".jpg, .jpeg, .png, .gif"
              :on-remove="iconUploadRemove"
          >
            <i class="el-icon-plus" />
          </el-upload>
        </el-form-item>
        <el-form-item label="排序:" prop="sort">
          <el-input-number v-model="formData.sort" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="语言描述:" prop="describe">
          <el-input v-model="formData.describe" :rows="2" type="textarea" maxlength="1024" show-word-limit clearable placeholder="请输入" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>

      <el-dialog v-model="previewImageVisible" append-to-body title="图片预览" width="50%" top="1%" center>
        <img :src="previewImagePath" style="max-height: 80vh;overflow-y: auto;">
      </el-dialog>


    </el-dialog>

  </div>
</template>

<script>
import {
  createLanguages,
  deleteLanguages,
  deleteLanguagesByIds,
  updateLanguages,
  findLanguages,
  getLanguagesList
} from '@/api/languages' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
import {createApi, updateApi} from "@/api/api";
const path = import.meta.env.VITE_BASE_API
export default {
  name: 'Languages',
  mixins: [infoList],
  data() {
    return {
      listApi: getLanguagesList,
      dialogFormVisible: false,
      type: '',
      previewImagePath: '',
      path,
      deleteVisible: false,
      previewImageVisible: false,
      iconFileList: [],
      formData: {
        name: '',
        en_name: '',
        code: '',
        icon: '',
        sort: 0,
        describe: ''
      },
      formDataRules: {
        name: [{ required: true, message: '请输入', trigger: 'blur' }],
        en_name: [{ required: true, message: '请输入', trigger: 'blur' }],
        code: [{ required: true, message: '请输入', trigger: 'blur' }],
        icon: [{ required: true, message: '请上传', trigger: 'blur' }],
        sort: [{ required: true, message: '请输入', trigger: 'blur' }],
        describe: [{ required: true, message: '请输入', trigger: 'blur' }]
      }
    }
  },
  async created() {
    this.pageSize = 100
    await this.getTableData()
  },
  methods: {
    iconUploadSuccess(response, file, fileList) {
      this.iconFileList = []
      const {data} = response
      if (data.url) {
        this.formData.icon = data.url
        this.iconFileList.push(data)
      }
      this.progressLoading.close()
    },
    iconUploadRemove() {
      this.formData.icon = ''
      this.iconFileList = []
    },
    imgPreview(file){
      this.previewImagePath = file.url
      // 打开预览图片的对话框
      this.previewImageVisible = true
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteLanguages(row)
      })
    },
    async updateLanguages(row) {
      const res = await findLanguages({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data
        if (this.formData.icon !== "") {
          this.iconFileList = [{name:'icon.png', url: this.formData.icon}]
        }
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        name: '',
        en_name: '',
        code: '',
        icon: '',
        sort: 0,
        describe: ''
      }
    },
    async deleteLanguages(row) {
      const res = await deleteLanguages({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      this.$refs.formData.validate(async valid => {
        if (valid) {
          let res
          switch (this.type) {
            case 'create':
              res = await createLanguages(this.formData)
              break
            case 'update':
              res = await updateLanguages(this.formData)
              break
            default:
              res = await createLanguages(this.formData)
              break
          }
          if (res.code === 0) {
            this.$message({
              type: 'success',
              message: '创建/更改成功'
            })
            this.closeDialog()
            this.getTableData()
          }
        }
      })
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    }
  },
}
</script>

<style>
</style>

