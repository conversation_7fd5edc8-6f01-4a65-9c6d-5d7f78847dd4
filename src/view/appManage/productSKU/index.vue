<template>
  <div>
    <!--搜索-->
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="产品:">
          <el-select v-model="searchInfo.app_id" clearable filterable placeholder="请选择APP">
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="`${item.app_name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model.number="searchInfo.sku_type" clearable filterable placeholder="操作系统">
            <el-option
              v-for="item in skuTypeOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!--列表展示-->
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
        <el-button size="mini" type="success" icon="el-icon-refresh" @click="syncData">数据同步</el-button>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column align="left" label="日期" min-width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column align="left" label="标题" prop="title" min-width="120" />
        <el-table-column align="left" label="所属APP" prop="app_id" min-width="120">
          <template #default="scope">{{ formatAppName(scope.row.app_id) }}</template>
        </el-table-column>
        <el-table-column align="left" label="内购产品ID" prop="sku" min-width="120" />
        <el-table-column align="left" label="内购产品类型" prop="sku_type" min-width="120">
          <template #default="scope">{{ formatSkuType(scope.row.sku_type) }}</template>
        </el-table-column>
        <el-table-column align="left" label="内购产品定价(美元)" prop="sku_price" min-width="120" />
        <el-table-column align="left" label="操作">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateProductSkus(scope.row)">修改</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <!--弹窗-->
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" label-position="middle" label-width="140px">
        <el-form-item label="标题:">
          <el-input v-model="formData.title" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="所属APP:" prop="app_id">
          <el-select v-model="formData.app_id" filterable placeholder="AppId">
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="`${item.app_name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="内购产品ID:">
          <el-input v-model="formData.sku" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="内购产品类型:" prop="sku_type">
          <el-select v-model="formData.sku_type" filterable placeholder="AppId">
            <el-option
              v-for="item in skuTypeOptions"
              :key="item.value"
              :label="`${item.label}`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="内购产品定价:美元:">
          <el-input-number v-model="formData.sku_price" :min="0" :precision="2" clearable placeholder="请输入"/>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="syncDataDialogVisible" :before-close="closeSyncDialog" title="数据同步" width="30%" top="5px">
      <el-form ref="syncFormDataRef" :model="syncFormData" :rules="syncFormDataRules" label-position="right"
               label-width="100px">
        <el-form-item label="源AppId:" prop="source_app_id">
          <el-row :gutter="14">
            <el-col :span="20">
              <el-input-number style="width: 100%;" v-model="syncFormData.source_app_id"/>
            </el-col>
            <el-col :span="4">
              <el-button size="small" type="primary" @click="searchSyncTableData">查询</el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item label="目标AppId:" prop="target_app_id">
          <el-input-number style="width: 100%;" v-model="syncFormData.target_app_id"/>
        </el-form-item>
      </el-form>
      <el-table
          border
          v-if="syncTableVisible"
          style="width: 100%"
          tooltip-effect="dark"
          :data="syncTableData"
          row-key="id"
          :height="400"
      >
        <el-table-column align="left" label="标题" prop="title" min-width="120" show-overflow-tooltip/>
        <el-table-column align="left" label="所属APP" prop="app_id" min-width="120" show-overflow-tooltip>
          <template #default="scope">{{ formatAppName(scope.row.app_id) }}</template>
        </el-table-column>
        <el-table-column align="left" label="内购产品ID" prop="sku" min-width="120" show-overflow-tooltip/>
        <el-table-column align="left" label="内购产品类型" prop="sku_type" min-width="120" show-overflow-tooltip>
          <template #default="scope">{{ formatSkuType(scope.row.sku_type) }}</template>
        </el-table-column>
        <el-table-column align="left" label="内购产品定价(美元)" prop="sku_price" min-width="160"/>
      </el-table>

      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeSyncDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterSyncDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createProductSkus,
  deleteProductSkus,
  deleteProductSkusByIds,
  updateProductSkus,
  findProductSkus,
  syncProductSkus,
  getProductSkusList
} from '@/api/productSkus' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
export default {
  name: 'ProductSkus',
  mixins: [infoList],
  data() {
    var validateTargetAppId = (rule, value, callback) => {
      if (!value) {
        callback(new Error('请输入'));
      } else {
        if (this.syncFormData.source_app_id === this.syncFormData.target_app_id) {
          callback(new Error('源APP和目标APP不能是一样的'));
        }
        callback();
      }
    };
    return {
      listApi: getProductSkusList,
      dialogFormVisible: false,
      syncTableVisible: false,
      syncTableData: [],
      syncDataDialogVisible: false,
      syncFormData: {
        source_app_id: 5,
        target_app_id: null
      },
      syncFormDataRules: {
        source_app_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        target_app_id: [{validator: validateTargetAppId, trigger: 'blur'}],
      },
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        title: '',
        app_id: 0,
        sku: '',
        sku_type: 0,
        sku_price: 0,
      },
      skuTypeOptions: [
        { value: 1, label: '安卓' },
        { value: 2, label: '苹果' },
      ]
    }
  },
  async created() {
    await this.getTableData()
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteProductSkus(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
      this.multipleSelection.map(item => {
        ids.push(item.id)
      })
      const res = await deleteProductSkusByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateProductSkus(row) {
      const res = await findProductSkus({id: row.id})
      this.type = 'update'
      if (res.code === 0) {
        const resData = res.data.productSkusInfo
        resData.sku_price = parseFloat(resData.sku_price)
        this.formData = resData
        this.dialogFormVisible = true
      }
    },
    searchSyncTableData() {
      getProductSkusList({
        app_id: this.syncFormData.source_app_id,
        page: 1,
        pageSize: 10000,
      }).then(res => {
        this.syncTableVisible = true
        this.syncTableData = res.data.list || []
      })
    },
    closeSyncDialog() {
      this.syncDataDialogVisible = false
      this.syncTableVisible = false
      this.syncTableData = []
      this.syncFormData = {
        source_app_id: 5
      }
    },
    enterSyncDialog() {
      this.$refs['syncFormDataRef'].validate(async valid => {
        if (!valid) return
        let res
        res = await syncProductSkus(this.syncFormData)
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功'
          })
          this.closeSyncDialog()
          this.getTableData()
        }
      })
    },
    syncData() {
      this.syncDataDialogVisible = true
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        title: '',
        app_id: 0,
        sku: '',
        sku_type: 0,
        created_time: 0,
        updated_time: 0,
      }
    },
    async deleteProductSkus(row) {
      const res = await deleteProductSkus({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createProductSkus(this.formData)
          break
        case 'update':
          res = await updateProductSkus(this.formData)
          break
        default:
          res = await createProductSkus(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    formatSkuType(skuType) {
      let text
      this.skuTypeOptions.forEach((item, index) => {
        if (skuType === item.value) {
          text = item.label
        }
      })
      return text
    }
  },
}
</script>

<style>
</style>

