<template>
  <div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
        <el-popover v-model:visible="deleteVisible" placement="top" width="160">
          <p>确定要删除吗？</p>
          <div style="text-align: right; margin-top: 8px;">
            <el-button size="mini" type="text" @click="deleteVisible = false">取消</el-button>
            <el-button size="mini" type="primary" @click="onDelete">确定</el-button>
          </div>
          <template #reference>
            <el-button icon="el-icon-delete" size="mini" style="margin-left: 10px;" :disabled="!multipleSelection.length">删除</el-button>
          </template>
        </el-popover>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="center" label="任务名称" prop="name" width="180" />
        <el-table-column align="center" label="所属APP" prop="app_id" width="120">
          <template #default="scope">{{ formatAppList(scope.row.app_id) }}</template>
        </el-table-column>
        <el-table-column align="center" label="创建日期" width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="模块" prop="task_category" width="120">
          <template #default="scope">{{ formaTasksPartner(scope.row.task_category) }}</template>
        </el-table-column>
        <el-table-column align="center" label="任务排序" prop="rank" width="120" />
        <el-table-column align="center" label="任务标题" prop="title" width="120" />
        <el-table-column align="center" label="任务图标" min-width="80" style="line-height: 50px">
          <template #default="scope">
            <el-image
              v-show="scope.row.icon"
              style="width: 40px; height: 40px;line-height: 40px;overflow:visible"
              :src="scope.row.icon"
              :lazy="true"
              :preview-src-list="[scope.row.icon]"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="任务中图" min-width="80" style="line-height: 50px">
          <template #default="scope">
            <el-image
              v-show="scope.row.mid_icon"
              style="width: 40px; height: 40px;line-height: 40px;overflow:visible"
              :src="scope.row.mid_icon"
              :lazy="true"
              :preview-src-list="[scope.row.mid_icon]"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="任务大图" min-width="80" style="line-height: 50px">
          <template #default="scope">
            <el-image
              v-show="scope.row.big_icon"
              style="width: 40px; height: 40px;line-height: 40px;overflow:visible"
              :src="scope.row.big_icon"
              :lazy="true"
              :preview-src-list="[scope.row.big_icon]"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="上线" width="180">
          <template #default="scope">{{ formatDate(scope.row.online_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="下线" width="180">
          <template #default="scope">{{ formatDate(scope.row.offline_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="状态" prop="state" width="120">
          <template #default="scope">{{ formaTasksStatusPartner(scope.row.state) }}</template>
        </el-table-column>
        <el-table-column align="center" label="奖品数量" prop="reward_amount" width="120" />
        <el-table-column align="center" label="奖品ID" prop="reward_id" width="150">
          <template #default="scope">{{ formaTasksRewardPartner(scope.row.reward_id) }}</template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateTasks(scope.row)">变更</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作" top="1%">
      <el-form :model="formData" label-position="right" :inline="true" label-width="80px" style="max-height: 80vh;overflow-y: auto;">
        <el-form-item label="模块" style="width:30%">
          <el-select v-model="formData.task_category" clearable placeholder="模块">
            <el-option
              v-for="item in tasksOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="任务排序:" style="width:30%">
          <el-input v-model.number="formData.rank" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="任务名称:" style="width:30%">
          <el-input v-model="formData.name" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="任务标题:" style="width:30%">
          <el-input v-model="formData.title" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="任务描述:" style="width:30%">
          <el-input v-model="formData.description" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="所属任务:" style="width:30%">
          <el-cascader
            v-model.number="formData.parent_id"
            style="width:100%"
            :options="tasksOption"
            :props="{label: 'title',value: 'id',checkStrictly: true,emitPath:false,disabled:'disabled'}"
            :show-all-levels="false"
            filterable
          />
        </el-form-item>

        <el-form-item label="上线:" style="width:30%">
          <el-date-picker v-model="formData.online_at" type="date" style="width:100%" placeholder="选择日期" clearable />
        </el-form-item>
        <el-form-item label="下线:" style="width:30%">
          <el-date-picker v-model="formData.offline_at" type="date" style="width:100%" placeholder="选择日期" clearable />
        </el-form-item>
        <el-form-item label="状态" style="width:30%">
          <el-select v-model="formData.state" clearable placeholder="状态">
            <el-option
              v-for="item in tasksStatusOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="奖品ID" style="width:30%">
          <el-select v-model="formData.reward_id" clearable placeholder="状态">
            <el-option
              v-for="item in tasksRewardOptions"
              :key="item.value"
              :label="`${item.label}`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="奖品数量:" style="width:30%">
          <el-input v-model.number="formData.reward_amount" clearable placeholder="请输入" />
        </el-form-item>

        <el-form-item v-if="formData.parent_id == 0" label="所属APP:" prop="app_id">
          <el-select v-model="formData.app_id" clearable placeholder="app_id">
            <el-option
                v-for="item in appList"
                :key="item"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="任务图标:" style="width:100%">
          <el-upload
            :action="`${path}/files/createFiles`"
            :headers="{ 'x-token': token }"
            list-type="picture-card"
            :file-list="iconFileList"
            :on-preview="handlePreview"
            :on-success="handleIconSuccess"
            :on-progress="handleProgressLoading"
            :multiple="false"
            :limit="1"
            accept=".jpg, .jpeg, .png, .gif"
            :on-remove="handleIconRemove"
          >
            <i class="el-icon-plus" />
          </el-upload>
        </el-form-item>

        <el-form-item label="任务中图:" style="width:100%">
          <el-upload
            :action="`${path}/files/createFiles`"
            :headers="{ 'x-token': token }"
            list-type="picture-card"
            :file-list="midIconFileList"
            :on-preview="handlePreview"
            :on-success="handleMidIconSuccess"
            :on-progress="handleProgressLoading"
            :multiple="false"
            :limit="1"
            accept=".jpg, .jpeg, .png, .gif"
            :on-remove="handleMidIconRemove"
          >
            <i class="el-icon-plus" />
          </el-upload>
        </el-form-item>

        <el-form-item label="任务大图:" style="width:100%">
          <el-upload
            :action="`${path}/files/createFiles`"
            :headers="{ 'x-token': token }"
            list-type="picture-card"
            :file-list="bigIconFileList"
            :on-preview="handlePreview"
            :on-success="handleBigIconSuccess"
            :on-progress="handleProgressLoading"
            :multiple="false"
            :limit="1"
            accept=".jpg, .jpeg, .png, .gif"
            :on-remove="handleBigIconRemove"
          >
            <i class="el-icon-plus" />
          </el-upload>
        </el-form-item>

      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog v-model="previewImageVisible" title="图片预览" width="50%" center>
      <img :src="previewImagePath" class="avatar video-avatar">
    </el-dialog>
  </div>
</template>

<script>
import {
  createTasks,
  deleteTasks,
  deleteTasksByIds,
  updateTasks,
  findTasks,
  getTasksList
} from '@/api/tasks' //  此处请自行替换地址
import {getAppXesList} from '@/api/appXes'
const path = import.meta.env.VITE_BASE_API
import infoList from '@/mixins/infoList'
import { ElLoading } from 'element-plus'
import { mapGetters } from 'vuex'

const tasksOptions = [
  {
    value: 1,
    label: '签到任务',
  },
  {
    value: 2,
    label: '任务中心',
  },
]

const tasksRewardOptions = [
  {
    value: 1,
    label: '免费通话卡（真）',
  },
  {
    value: 2,
    label: '免费通话卡（假）',
  },
  {
    value: 3,
    label: '金币',
  },
  {
    value: 4,
    label: 'VIP',
  },
]

const tasksStatusOptions = [
  {
    value: 1,
    label: '未上架',
  },
  {
    value: 2,
    label: '上架中',
  },
]

export default {
  name: 'Tasks',
  mixins: [infoList],
  data() {
    return {
      appListOptions: [],
      listApi: getTasksList,
      tasksOptions: tasksOptions,
      tasksStatusOptions: tasksStatusOptions,
      tasksRewardOptions: tasksRewardOptions,
      previewImagePath: '',
      previewImageVisible: false,
      dialogFormVisible: false,
      deleteVisible: false,
      path: path,
      type: '',
      tasksOption: [],
      multipleSelection: [],
      iconFileList: [],
      midIconFileList: [],
      bigIconFileList: [],
      formData: {
        task_category: 0,
        rank: 0,
        name: '',
        title: '',
        task_range: 0,
        description: '',
        icon: '',
        mid_icon: '',
        big_icon: '',
        parent_id: 0,
        online_at: new Date(),
        offline_at: new Date(),
        state: 0,
        reward_id: 0,
        reward_amount: 0,
        app_id:0,
      }
    }
  },
  async created() {
    this.pageSize = 1000
    await this.getTableData()
    await this.getAppList()
  },
  methods: {
    formatAppList: function(bool) {
      let text
      this.appListOptions.forEach(function(element) {
        if (bool === element.id) {
          text = element.appName
          return text
        }
      })
      return text
    },
    async getAppList() {
      const res = await getAppXesList()
      this.appListOptions = res.data.list
    },
    setOptions() {
      this.tasksOption = [
        {
          id: '0',
          title: '根目录'
        }
      ]
      this.setTasksOptions(this.tableData, this.tasksOption, false)
    },
    setTasksOptions(menuData, optionsData, disabled) {
      menuData &&
        menuData.forEach(item => {
          if (item.children && item.children.length) {
            const option = {
              title: item.title,
              id: item.id,
              disabled: parseInt(item.id) === parseInt(this.formData.id),
              children: []
            }
            this.setTasksOptions(
              item.children,
              option.children,
              parseInt(item.id) === parseInt(this.formData.id)
            )
            optionsData.push(option)
          } else {
            const option = {
              title: item.title,
              disabled: parseInt(item.id) === parseInt(this.formData.id),
              id: item.id,
            }
            optionsData.push(option)
          }
        })
    },
    handleProgressLoading() {
      this.progressLoading = ElLoading.service({
        lock: true,
        text: 'Loading',
        background: 'rgba(0, 0, 0, 0.7)',
      })
    },
    handlePreview(file) {
      // 把url地址赋值到本地
      this.previewImagePath = file.url
      // 打开预览图片的对话框
      this.previewImageVisible = true
    },
    handleIconSuccess(res) {
      const that = this
      that.iconFileList = []
      const { data } = res
      if (data.url) {
        that.formData.icon = data.url
        that.iconFileList.push(data)
      }
      this.progressLoading.close()
    },
    handleMidIconSuccess(res) {
      const that = this
      that.midIconFileList = []
      const { data } = res
      if (data.url) {
        that.formData.mid_icon = data.url
        that.midIconFileList.push(data)
      }
      this.progressLoading.close()
    },

    handleBigIconSuccess(res) {
      const that = this
      that.bigIconFileList = []
      const { data } = res
      if (data.url) {
        that.formData.big_icon = data.url
        that.bigIconFileList.push(data)
      }
      this.progressLoading.close()
    },

    handleIconRemove(file, fileList) {
      this.formData.icon = ''
      this.iconFileList = []
    },

    handleMidIconRemove() {
      this.formData.mid_icon = ''
      this.midIconFileList = []
    },

    handleBigIconRemove() {
      this.formData.big_icon = ''
      this.bigIconFileList = []
    },

    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteTasks(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
        this.multipleSelection.map(item => {
          ids.push(item.id)
        })
      const res = await deleteTasksByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateTasks(row) {
      const that = this
      that.setOptions()
      const res = await findTasks({ id: row.id })
      that.type = 'update'
      that.iconFileList = []
      that.midIconFileList = []
      that.bigIconFileList = []
      if (res.code === 0) {
        that.formData = res.data.retasks
        if (that.formData.icon !== '') {
          that.iconFileList.push({ name: '', url: that.formData.icon })
        }
        if (that.formData.mid_icon !== '') {
          that.midIconFileList.push({ name: '', url: that.formData.mid_icon })
        }
        if (that.formData.big_icon !== '') {
          that.bigIconFileList.push({ name: '', url: that.formData.big_icon })
        }
        that.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.iconFileList = []
      this.midIconFileList = []
      this.bigIconFileList = []
      this.formData = {
        task_category: 0,
        rank: 0,
        name: '',
        title: '',
        task_range: 0,
        description: '',
        icon: '',
        mid_icon: '',
        big_icon: '',
        parent_id: 0,
        online_at: new Date(),
        offline_at: new Date(),
        state: 0,
        reward_id: 0,
        reward_amount: 0,
      }
    },
    async deleteTasks(row) {
      const res = await deleteTasks({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createTasks(this.formData)
          break
        case 'update':
          res = await updateTasks(this.formData)
          break
        default:
          res = await createTasks(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
      this.setOptions()
    }
  },
}
</script>

<style>
</style>

