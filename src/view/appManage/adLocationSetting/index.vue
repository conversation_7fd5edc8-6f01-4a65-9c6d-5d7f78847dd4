<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo">
        <el-form-item label="AppID">
          <el-select v-model="searchInfo.app_id" clearable filterable placeholder="请选择APP">
            <el-option
                v-for="item in appList"
                :key="item.id"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="广告类型">
          <el-select v-model="searchInfo.ad_type" clearable>
            <el-option
                v-for="item in adTypeOption"
                :key="item"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="广告位ID">
          <el-input v-model="searchInfo.ad_id" placeholder="请输入广告位ID" />
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
          <el-button size="mini" type="success" icon="el-icon-plus" @click="openDialog">新增</el-button>
          <el-button size="mini" icon="el-icon-download" :disabled="total === 0" @click="exportExcel">导出</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <el-table ref="tableData" style="width: 100%" border :data="tableData" row-key="id" :max-height="590">
        <el-table-column align="center" label="日期" prop="created_at_str" min-width="180"/>
        <el-table-column align="center" label="AppID" prop="app_id" min-width="120">
          <template #default="scope">
            {{formatAppName(scope.row.app_id)}}
          </template>
        </el-table-column>
        <el-table-column align="center" label="广告类型" prop="ad_type_str" min-width="120">
          <template #default="scope">
            {{adTypeOptionDict[scope.row.ad_type]}}
          </template>
        </el-table-column>
        <el-table-column align="center" label="广告位ID" prop="ad_id" min-width="120" />
        <el-table-column align="center" label="备注" prop="remark" min-width="120" />
        <el-table-column align="center" label="最小版本" prop="min_version" min-width="120" />
        <el-table-column align="center" label="最大版本" prop="max_version" min-width="120" />
        <el-table-column align="center" label="操作" min-width="180">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateAdLocationSetting(scope.row)">修改</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
            layout="total, sizes, prev, pager, next, jumper"
            :current-page="page"
            :page-size="pageSize"
            :page-sizes="pageSizeList"
            :total="total"
            @current-change="pageChange"
            @size-change="sizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" top="5px" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" :rules="formDataRules" label-position="right" label-width="120px">
        <el-form-item label="AppID:" prop="app_id">
          <el-select v-model="formData.app_id" style="width: 100%" filterable placeholder="请选择APP">
            <el-option
                v-for="item in appList"
                :key="item.id"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="广告类型:" prop="ad_type">
          <el-select v-model="formData.ad_type" style="width: 100%" clearable>
            <el-option
                v-for="item in adTypeOption"
                :key="item"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="广告位ID:" prop="ad_id">
          <el-input v-model="formData.ad_id" clearable />
        </el-form-item>
        <el-form-item label="备注:" prop="remark">
          <el-input v-model="formData.remark" clearable />
        </el-form-item>
        <el-form-item label="最小版本:" prop="min_version">
          <el-input v-model="formData.min_version" clearable />
        </el-form-item>
        <el-form-item label="最大版本:" prop="max_version">
          <el-input v-model="formData.max_version" clearable />
        </el-form-item>
        
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { createAdLocationSetting, deleteAdLocationSetting, getAdTypeList, deleteAdLocationSettingByIds, updateAdLocationSetting, findAdLocationSetting, getAdLocationSettingList} from '@/api/adLocationSetting' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
export default {
  name: 'AdLocationSetting',
  mixins: [infoList],
  data() {
    return {
      listApi: getAdLocationSettingList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      adTypeOption: [],
      adTypeOptionDict: {},
      formData: {
        app_id: null,
        ad_type: null,
        ad_id: null,
        remark: null,
        max_version:null,
        max_version:null,
      },
      formDataRules: {
        app_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        ad_type: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        ad_id: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        max_version: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        min_version: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
        // remark: [{required: true, message: '请完善该字段.', trigger: 'blur'}],
      },
    }
  },
  created() {
    this.getTableData(this.nFunc, this.tableDataFormat)
    this.initAdTypeList()
  },
  methods: {
    initAdTypeList() {
      getAdTypeList().then(res=>{
        this.adTypeOption = res.data || []
        let aDict = {}
        this.adTypeOption.forEach(i=>{
          aDict[i.value] = i.label
        })
        this.adTypeOptionDict = aDict
      })
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteAdLocationSetting(row)
      })
    },
    async updateAdLocationSetting(row) {
      const res = await findAdLocationSetting({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {}
    },
    async deleteAdLocationSetting(row) {
      const res = await deleteAdLocationSetting({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createAdLocationSetting(this.formData)
          break
        case 'update':
          res = await updateAdLocationSetting(this.formData)
          break
        default:
          res = await createAdLocationSetting(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '成功'
        })
        this.closeDialog()
        this.getTableData(this.nFunc, this.tableDataFormat)
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    exportExcel() {
      let tableColumns = this.$refs.tableData.$refs.tableHeader.columns
      let res = this.getTableHeaderProp(tableColumns)
      let tHeader = res[0]
      let filterV = res[1]
      this.handleProgressLoading()
      import('@/utils/excel').then((excel) => {
        const data = this.formatJson(filterV, this.tableData)
        excel.export_json_to_excel({
          header: tHeader,
          data,
          filename: this.filename,
          autoWidth: this.autoWidth,
          bookType: this.bookType,
        })
        this.progressLoading.close()
      })
    },
    sizeChange(v) {
      this.pageSize = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    pageChange(v) {
      this.page = v
      this.getTableData(this.nFunc, this.tableDataFormat)
    },
    tableDataFormat() {
      this.tableData.map((item, i, data) => {
        data[i].created_at_str = this.formatDate(item.created_at)
      })
    },
  },
}
</script>

<style>
</style>
