<template>
  <div>
    <el-card shadow="hover">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" @keyup.enter="onSubmit">
            <el-form-item label="ID" prop="user_id">
               <el-input v-model="searchInfo.user_id" placeholder="请输入" />
            </el-form-item>
            <el-form-item label="类型" prop="feedback_type">
               <el-select v-model="searchInfo.feedback_type" clearable filterable placeholder="请选择类型">
                <el-option
                  v-for="item in feedBackType"
                  :key="item.value"
                  :label="item.label_zh"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="状态" prop="feedback_state">
               <el-select v-model="searchInfo.feedback_state" clearable filterable placeholder="请选择状态">
                <el-option
                  v-for="item in feedBackState"
                  :key="item.value"
                  :label="item.label_zh"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
            <el-button icon="refresh" @click="onReset">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table ref="tableRef" :data="tableData" row-key="id" border @sort-change="sortChange" show-overflow-tooltip>
                <el-table-column align="center" label="ID" prop="user_id" :width="100" fixed="left" />
                <el-table-column align="center" label="类型" prop="feedback_type" :min-width="120" />
                <el-table-column align="center" label="内容" prop="content" :min-width="200" show-overflow-tooltip/>
                <el-table-column align="center" label="截图/视频" prop="res_url" :min-width="200">
                  <template #default="{row}">
                    <div class="imageFlex" >
                        <el-image 
                          v-for="item in row.res_url"
                          style="width: 200px;height: 100px" 
                          :src="item" 
                          :preview-src-list="row.res_url"
                          fit="scale-down">
                            <template #error>
                              <div class="image-slot">
                                <el-icon><Picture /></el-icon>
                              </div>
                            </template>
                        </el-image>
                    </div>                 
                  </template>
                </el-table-column>
                <el-table-column align="center" label="通话频道" prop="call_project" :min-width="200" show-overflow-tooltip/>
                <el-table-column align="center" label="状态" prop="feedback_state" :min-width="120" />
                <el-table-column align="center" label="操作" :width="160" fixed="right">
                    <template #default="scope">
                    <el-button type="success" link @click="solveRow(scope.row)">解决</el-button>
                    </template>
                </el-table-column>
                </el-table>
                <div class="gva-pagination">
                  <el-pagination
                  layout="total, sizes, prev, pager, next, jumper"
                  :current-page="page"
                  :page-size="pageSize"
                  :page-sizes="pageSizes"
                  :total="total"
                  @current-change="pageChange"
                  @size-change="pageSizeChange"
                />
                </div>
                
  </el-card>
    <!-- <el-dialog v-model="dialogShow" :before-close="closeDialog" :title="dTitle" top="10px" destroy-on-close>
      <el-form :model="formData" label-position="right" ref="elFormRef" :rules="rule" label-width="80px">
        <el-form-item label="ID:"  prop="user_id" >
          <el-input v-model="formData.user_id" clearable  placeholder="请输入" />
        </el-form-item>
        <el-form-item label="类型:"  prop="feedback_type" >
          <el-input v-model="formData.feedback_type" clearable  placeholder="请输入" />
        </el-form-item>
        <el-form-item label="状态:"  prop="feedback_state" >
          <el-input v-model="formData.feedback_state" clearable  placeholder="请输入" />
        </el-form-item>
        <el-form-item label="内容:"  prop="content" >
          <el-input v-model="formData.content" clearable  placeholder="请输入" />
        </el-form-item>
        <el-form-item label="截图/视频:"  prop="res_url" >
          <el-input v-model="formData.res_url" clearable  placeholder="请输入" />
        </el-form-item>
        <el-form-item label="通话频道:"  prop="call_p'r'oject" >
          <el-input v-model="formData.call_project" clearable  placeholder="请输入" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeDialog">取 消</el-button>
          <el-button type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog> -->
  </div>
</template>

<script setup>
import {
  getPage,
  GetFeedBackType,
  GetFeedBackState,
  updateState
} from '@/api/feedBack.js'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ref, reactive, onBeforeMount } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Picture } from '@element-plus/icons'


const router = useRouter()
const route = useRoute()
const dialogShow = ref(false)
const dTitle = ref('')
const dType = ref('')
const elFormRef = ref()
const elSearchFormRef = ref()
const page = ref(1)
const total = ref(0)
const pageSize = ref(20)
const pageSizes = ref([20,30,40])
const tableData = ref([])
const searchInfo = ref({})
const formData = ref({user_id: '',feedback_type: '',feedback_state: '',content: '',res_url: '',call_project: '',})
const rule = reactive({})
const feedBackState = ref([])
const feedBackType = ref([])

const sortChange = ({ prop, order }) => {
  searchInfo.value.sort = prop
  searchInfo.value.order = order === 'ascending'
  getTableData()
}

const onReset = () => {
  searchInfo.value = {}
  getTableData()
}

const onSubmit = () => {
  elSearchFormRef.value?.validate(async(valid) => {
    if (!valid) return
    page.value = 1
    getTableData()
  })
}

//切换每页显示行数
const pageSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

//切换页
const pageChange = (val) => {
  page.value = val
  getTableData()
}

//获取列表全部数据
const getTableData = async() => {
  await getFeedBackType()
  await getFeedBackState()
  const table = await getPage({ page: page.value, pageSize: pageSize.value, ...searchInfo.value })
  if (table.code === 0) {
    let resDataList = table.data.list || []
    let newList = []
    resDataList.forEach(item => {
      newList.push(item)
    })
    
    tableData.value = newList
    total.value = table.data.total
    tableData.value.forEach((item,index) => {
      //转换反馈类型
      feedBackType.value.forEach(type => {
        if(item.feedback_type === type.value){
          item.feedback_type = type.label_zh
        }
      })
      //转换反馈状态
      feedBackState.value.forEach(state => {
        if(item.feedback_state === state.value){
          item.feedback_state = state.label_zh
        }
      })
    })

  }
}

//获取所有反馈类型
const getFeedBackType = async() => {
  const alltype = await GetFeedBackType()
  feedBackType.value = alltype.data
}

//获取所有反馈状态
const getFeedBackState = async() => {
  const allState = await GetFeedBackState()
  feedBackState.value = allState.data
}

//编辑状态
const solveRow = async(row) => {
  const data = {
    id:row.id,
    feedback_state:row.feedback_state === '未解决' ? 2 : 1
  } 
 const res = await updateState(data)
 if(res.code === 0){
   ElMessage.success(res.msg)
   getTableData()
 }
}


const closeDialog = () => {
  dialogShow.value = false
  formData.value = {user_id: '',feedback_type: '',feedback_state: '',content: '',res_url: '',call_project: '',}
}

// const enterDialog = async () => {
//   elFormRef.value?.validate( async (valid) => {
//     if (!valid) return
//     let res
//     switch (dType.value) {
//       case 'create':
//         res = await createAdAdsets(formData.value)
//         break
//       case 'update':
//         res = await updateAdAdsets(formData.value)
//         break
//       default:
//         res = await createAdAdsets(formData.value)
//         break
//     }
//     if (res.code === 0) {
//       ElMessage({type: 'success', message: '创建/更改成功'})
//       closeDialog()
//       getTableData()
//     }
//   })
// }


onBeforeMount(()=>{
  getTableData()
  getFeedBackState()
})
</script>

<style scoped>
.gva-pagination{
  display: flex;
  justify-content: flex-end;
}
.imageFlex{
  display: flex;
}
.image-slot {
display: flex;
justify-content: center;
align-items: center;
width: 100%;
height: 100%;
background: var(--el-fill-color-light);
color: var(--el-text-color-secondary);
}
.image-slot .el-icon {
  font-size: 30px;
}
</style>
