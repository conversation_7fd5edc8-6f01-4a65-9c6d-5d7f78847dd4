<template>
  <div>
    <!--Search-->
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item label="产品:">
          <el-select v-model="searchInfo.app_id" clearable filterable placeholder="请选择APP">
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="`${item.app_name}(${item.id})`"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model.number="searchInfo.state" clearable filterable placeholder="上架状态">
            <el-option
                v-for="item in shelfStateOptions"
                :key="item.value"
                :label="`${item.label}(${item.value})`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="支付方式">
          <el-select v-model.number="searchInfo.pay_id" clearable filterable placeholder="支付方式">
            <el-option
                v-for="item in payWays"
                :key="item.value"
                :label="`${item.internal_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="国家">
          <el-select v-model="searchInfo.country_code" clearable filterable>
            <el-option
                v-for="item in countryTreeOptions"
                :key="item.value"
                :label="`${item.label}`"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="ABTest">
          <el-select v-model.number="searchInfo.ab_test" clearable filterable placeholder="AB Test">
            <el-option
                v-for="item in abTemplates"
                :key="item.value"
                :label="item.label"
                :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <!--Table-->
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
        <el-button size="mini" type="danger" icon="el-icon-refresh" @click="delCache">删除支付列表缓存</el-button>
      </div>
      <el-table
          ref="multipleTable"
          style="width: 100%"
          tooltip-effect="dark"
          :data="tableData"
          @sort-change="tableSort"
          row-key="id"
          @selection-change="handleSelectionChange"
      >
        <el-table-column align="left" label="日期" min-width="180">
          <template #default="scope">{{ formatDate(scope.row.updated_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="APP" prop="app_id" sortable="custom" min-width="120">
          <template #default="scope">{{ formatAppName(scope.row.app_id) }}</template>
        </el-table-column>
        <el-table-column align="left" label="ABTest模版" prop="ab_test" min-width="120" />
        <el-table-column align="left" label="支付方式" prop="pay_id" min-width="120">
          <template #default="scope">{{ formatPayWay(scope.row.pay_id) }}</template>
        </el-table-column>
        <el-table-column align="left" label="显示名称" prop="pay_name" min-width="120" />
        <el-table-column align="left" label="显示描述" prop="pay_text" min-width="120" />
        <el-table-column align="center" label="显示图标" min-width="120">
          <template #default="scope">
            <el-image
              fit="contain"
              :src="scope.row.pay_icon"
              :preview-src-list="[scope.row.pay_icon]"
              style="width: 40px; height: 40px;"
            />
          </template>
        </el-table-column>
        <el-table-column align="left" label="排序" prop="sort" min-width="120" />
        <el-table-column align="left" label="最低版本" prop="version" min-width="120" />
        <el-table-column align="left" label="状态" min-width="120">
          <template #default="scope">{{ formatPaymentState(scope.row.state) }}</template>
        </el-table-column>
        <el-table-column align="left" label="操作">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateAppPayments(scope.row)">修改</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="pageSizeList"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
    <!--Dialog-->
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" label-position="right" label-width="160px">
        <el-form-item label="AppId:" prop="app_id">
          <el-select v-model="formData.app_id" filterable placeholder="AppId">
            <el-option
              v-for="item in appList"
              :key="item.id"
              :label="`${item.app_name}(${item.id})`"
              :value="item.id"
              :disabled="type==='update'"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="ABTest模版:">
          <el-select v-model="formData.ab_test" filterable placeholder="ABTest模版">
            <el-option
              v-for="item in abTemplates"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="支付方式:">
          <el-select v-model="formData.pay_id" filterable placeholder="支付方式">
            <el-option
              v-for="item in payWays"
              :key="item.id"
              :label="`${item.internal_name}(${item.id})`"
              :value="item.id"
              :disabled="type==='update'"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="显示名称:">
          <el-input v-model="formData.pay_name" clearable placeholder="如果非空,覆盖原本名称" />
        </el-form-item>
        <el-form-item label="显示描述:">
          <el-input v-model="formData.pay_text" clearable placeholder="如果非空,覆盖原本描述" />
        </el-form-item>
        <el-form-item label="图标:" prop="pay_icon">
          <el-upload
            ref="pay_icon"
            :action="`${path}/files/createFiles`"
            :headers="{ 'x-token': token }"
            list-type="picture-card"
            :file-list="midPicFileList"
            :on-preview="handleMidPicPreview"
            :on-success="handleMidPicSuccess"
            :on-progress="handleProgressLoading"
            :on-exceed="handleImgExceed"
            :multiple="false"
            :limit="1"
            accept=".jpg, .jpeg, .png, .gif"
            :on-remove="handleImageRemove"
          >
            <i class="el-icon-plus" />
          </el-upload>
        </el-form-item>
        <el-form-item label="排序:">
          <el-input v-model.number="formData.sort" clearable placeholder="请输入,排序值越大越靠前" />
        </el-form-item>
        <el-form-item label="最低版本:">
          <el-input v-model="formData.version" clearable placeholder="请输入最低版本,默认为空" />
        </el-form-item>
        <el-form-item label="状态:">
          <el-select v-model="formData.state" filterable placeholder="状态:分开关">
            <el-option
              v-for="item in shelfStateOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createAppPayments,
  deleteAppPayments,
  deleteAppPaymentsByIds,
  updateAppPayments,
  findAppPayments,
  getAppPaymentsList
} from '@/api/appPayments' //  此处请自行替换地址
import {
  getPayWaysList,
  deletePayWayCache,
} from '@/api/payWays'
import infoList from '@/mixins/infoList'
const path = import.meta.env.VITE_BASE_API
export default {
  name: 'AppPayments',
  mixins: [infoList],
  data() {
    return {
      listApi: getAppPaymentsList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      midPicFileList: [],
      formData: {
        app_id: 0,
        ab_test: '',
        pay_id: 0,
        pay_internal_name: '',
        pay_name: '',
        pay_text: '',
        pay_icon: '',
        sort: 0,
        version: '',
        state: 0,
        created_time: 0,
        updated_time: 0,
      },
      shelfStateOptions: [
        {value: 0, label: '未选择'},
        {value: 1, label: '可用'},
        {value: 2, label: '不可用'},
      ],
      abTemplates: [
        {value: 'default', label: 'default'},
      ],
      payWays: [],
      countryTreeOptions: [{label: "全部(ALL)", value: "ALL"}, {label: "阿富汗(AF)", value: "AF"}, {
        label: "奥兰(AX)",
        value: "AX"
      }, {label: "阿尔巴尼亚(AL)", value: "AL"}, {label: "阿尔及利亚(DZ)", value: "DZ"}, {
        label: "美属萨摩亚(AS)",
        value: "AS"
      }, {label: "安道尔(AD)", value: "AD"}, {label: "安哥拉(AO)", value: "AO"}, {
        label: "安圭拉(AI)",
        value: "AI"
      }, {label: "南极洲(AQ)", value: "AQ"}, {label: "安地卡及巴布达(AG)", value: "AG"}, {
        label: "阿根廷(AR)",
        value: "AR"
      }, {label: "亚美尼亚(AM)", value: "AM"}, {label: "阿鲁巴(AW)", value: "AW"}, {
        label: "澳大利亚(AU)",
        value: "AU"
      }, {label: "奥地利(AT)", value: "AT"}, {label: "阿塞拜疆(AZ)", value: "AZ"}, {
        label: "巴哈马(BS)",
        value: "BS"
      }, {label: "巴林(BH)", value: "BH"}, {label: "孟加拉国(BD)", value: "BD"}, {
        label: "巴巴多斯(BB)",
        value: "BB"
      }, {label: "白俄罗斯(BY)", value: "BY"}, {label: "比利时(BE)", value: "BE"}, {
        label: "伯利兹(BZ)",
        value: "BZ"
      }, {label: "贝宁(BJ)", value: "BJ"}, {label: "百慕大(BM)", value: "BM"}, {
        label: "不丹(BT)",
        value: "BT"
      }, {label: "玻利维亚(BO)", value: "BO"}, {label: "荷兰加勒比区(BQ)", value: "BQ"}, {
        label: "波黑(BA)",
        value: "BA"
      }, {label: "博茨瓦纳(BW)", value: "BW"}, {label: "布韦岛(BV)", value: "BV"}, {
        label: "巴西(BR)",
        value: "BR"
      }, {label: "英属印度洋领地(IO)", value: "IO"}, {label: "文莱(BN)", value: "BN"}, {
        label: "保加利亚(BG)",
        value: "BG"
      }, {label: "布吉纳法索(BF)", value: "BF"}, {label: "布隆迪(BI)", value: "BI"}, {
        label: "佛得角(CV)",
        value: "CV"
      }, {label: "柬埔寨(KH)", value: "KH"}, {label: "喀麦隆(CM)", value: "CM"}, {
        label: "加拿大(CA)",
        value: "CA"
      }, {label: "开曼群岛(KY)", value: "KY"}, {label: "中非(CF)", value: "CF"}, {
        label: "乍得(TD)",
        value: "TD"
      }, {label: "智利(CL)", value: "CL"}, {label: "中国(CN)", value: "CN"}, {
        label: "圣诞岛(CX)",
        value: "CX"
      }, {label: "科科斯（基林）群岛(CC)", value: "CC"}, {label: "哥伦比亚(CO)", value: "CO"}, {
        label: "科摩罗(KM)",
        value: "KM"
      }, {label: "刚果共和国(CG)", value: "CG"}, {label: "刚果民主共和国(CD)", value: "CD"}, {
        label: "库克群岛(CK)",
        value: "CK"
      }, {label: "哥斯达黎加(CR)", value: "CR"}, {label: "科特迪瓦(CI)", value: "CI"}, {
        label: "克罗地亚(HR)",
        value: "HR"
      }, {label: "古巴(CU)", value: "CU"}, {label: "库拉索(CW)", value: "CW"}, {
        label: "赛普勒斯(CY)",
        value: "CY"
      }, {label: "捷克(CZ)", value: "CZ"}, {label: "丹麦(DK)", value: "DK"}, {
        label: "吉布提(DJ)",
        value: "DJ"
      }, {label: "多米尼克(DM)", value: "DM"}, {label: "多米尼加(DO)", value: "DO"}, {
        label: "厄瓜多尔(EC)",
        value: "EC"
      }, {label: "埃及(EG)", value: "EG"}, {label: "萨尔瓦多(SV)", value: "SV"}, {
        label: "赤道几内亚(GQ)",
        value: "GQ"
      }, {label: "厄立特里亚(ER)", value: "ER"}, {label: "爱沙尼亚(EE)", value: "EE"}, {
        label: "斯威士兰(SZ)",
        value: "SZ"
      }, {label: "衣索比亚(ET)", value: "ET"}, {label: "福克兰群岛(FK)", value: "FK"}, {
        label: "法罗群岛(FO)",
        value: "FO"
      }, {label: "斐济(FJ)", value: "FJ"}, {label: "芬兰(FI)", value: "FI"}, {
        label: "法国(FR)",
        value: "FR"
      }, {label: "法属圭亚那(GF)", value: "GF"}, {
        label: "法属玻里尼西亚(PF)",
        value: "PF"
      }, {label: "法属南部和南极领地(TF)", value: "TF"}, {label: "加彭(GA)", value: "GA"}, {
        label: "冈比亚(GM)",
        value: "GM"
      }, {label: "格鲁吉亚(GE)", value: "GE"}, {label: "德国(DE)", value: "DE"}, {
        label: "加纳(GH)",
        value: "GH"
      }, {label: "直布罗陀(GI)", value: "GI"}, {label: "希腊(GR)", value: "GR"}, {
        label: "格陵兰(GL)",
        value: "GL"
      }, {label: "格瑞那达(GD)", value: "GD"}, {label: "瓜德罗普(GP)", value: "GP"}, {
        label: "关岛(GU)",
        value: "GU"
      }, {label: "危地马拉(GT)", value: "GT"}, {label: "根西(GG)", value: "GG"}, {
        label: "几内亚(GN)",
        value: "GN"
      }, {label: "几内亚比绍(GW)", value: "GW"}, {label: "圭亚那(GY)", value: "GY"}, {
        label: "海地(HT)",
        value: "HT"
      }, {label: "赫德岛和麦克唐纳群岛(HM)", value: "HM"}, {label: "梵蒂冈(VA)", value: "VA"}, {
        label: "洪都拉斯(HN)",
        value: "HN"
      }, {label: "香港(HK)", value: "HK"}, {label: "匈牙利(HU)", value: "HU"}, {
        label: "冰岛(IS)",
        value: "IS"
      }, {label: "印度(IN)", value: "IN"}, {label: "印尼(ID)", value: "ID"}, {
        label: "伊朗(IR)",
        value: "IR"
      }, {label: "伊拉克(IQ)", value: "IQ"}, {label: "爱尔兰(IE)", value: "IE"}, {
        label: "马恩岛(IM)",
        value: "IM"
      }, {label: "以色列(IL)", value: "IL"}, {label: "义大利(IT)", value: "IT"}, {
        label: "牙买加(JM)",
        value: "JM"
      }, {label: "日本(JP)", value: "JP"}, {label: "泽西(JE)", value: "JE"}, {
        label: "约旦(JO)",
        value: "JO"
      }, {label: "哈萨克斯坦(KZ)", value: "KZ"}, {label: "肯尼亚(KE)", value: "KE"}, {
        label: "基里巴斯(KI)",
        value: "KI"
      }, {label: "朝鲜(KP)", value: "KP"}, {label: "韩国(KR)", value: "KR"}, {
        label: "科威特(KW)",
        value: "KW"
      }, {label: "吉尔吉斯斯坦(KG)", value: "KG"}, {label: "老挝(LA)", value: "LA"}, {
        label: "拉脱维亚(LV)",
        value: "LV"
      }, {label: "黎巴嫩(LB)", value: "LB"}, {label: "赖索托(LS)", value: "LS"}, {
        label: "利比里亚(LR)",
        value: "LR"
      }, {label: "利比亚(LY)", value: "LY"}, {label: "列支敦斯登(LI)", value: "LI"}, {
        label: "立陶宛(LT)",
        value: "LT"
      }, {label: "卢森堡(LU)", value: "LU"}, {label: "澳门(MO)", value: "MO"}, {
        label: "马达加斯加(MG)",
        value: "MG"
      }, {label: "马拉维(MW)", value: "MW"}, {label: "马来西亚(MY)", value: "MY"}, {
        label: "马尔地夫(MV)",
        value: "MV"
      }, {label: "马里(ML)", value: "ML"}, {label: "马尔他(MT)", value: "MT"}, {
        label: "马绍尔群岛(MH)",
        value: "MH"
      }, {label: "马提尼克(MQ)", value: "MQ"}, {label: "毛里塔尼亚(MR)", value: "MR"}, {
        label: "模里西斯(MU)",
        value: "MU"
      }, {label: "马约特(YT)", value: "YT"}, {label: "墨西哥(MX)", value: "MX"}, {
        label: "密克罗尼西亚联邦(FM)",
        value: "FM"
      }, {label: "摩尔多瓦(MD)", value: "MD"}, {label: "摩纳哥(MC)", value: "MC"}, {
        label: "蒙古(MN)",
        value: "MN"
      }, {label: "蒙特内哥罗(ME)", value: "ME"}, {label: "蒙特塞拉特(MS)", value: "MS"}, {
        label: "摩洛哥(MA)",
        value: "MA"
      }, {label: "莫桑比克(MZ)", value: "MZ"}, {label: "缅甸(MM)", value: "MM"}, {
        label: "纳米比亚(NA)",
        value: "NA"
      }, {label: "瑙鲁(NR)", value: "NR"}, {label: "尼泊尔(NP)", value: "NP"}, {
        label: "荷兰(NL)",
        value: "NL"
      }, {label: "新喀里多尼亚(NC)", value: "NC"}, {label: "新西兰(NZ)", value: "NZ"}, {
        label: "尼加拉瓜(NI)",
        value: "NI"
      }, {label: "尼日尔(NE)", value: "NE"}, {label: "奈及利亚(NG)", value: "NG"}, {
        label: "纽埃(NU)",
        value: "NU"
      }, {label: "诺福克岛(NF)", value: "NF"}, {label: "北马其顿(MK)", value: "MK"}, {
        label: "北马里亚纳群岛(MP)",
        value: "MP"
      }, {label: "挪威(NO)", value: "NO"}, {label: "阿曼(OM)", value: "OM"}, {
        label: "巴基斯坦(PK)",
        value: "PK"
      }, {label: "帛琉(PW)", value: "PW"}, {label: "巴勒斯坦(PS)", value: "PS"}, {
        label: "巴拿马(PA)",
        value: "PA"
      }, {label: "巴布亚新几内亚(PG)", value: "PG"}, {label: "巴拉圭(PY)", value: "PY"}, {
        label: "秘鲁(PE)",
        value: "PE"
      }, {label: "菲律宾(PH)", value: "PH"}, {label: "皮特凯恩群岛(PN)", value: "PN"}, {
        label: "波兰(PL)",
        value: "PL"
      }, {label: "葡萄牙(PT)", value: "PT"}, {label: "波多黎各(PR)", value: "PR"}, {
        label: "卡塔尔(QA)",
        value: "QA"
      }, {label: "留尼旺(RE)", value: "RE"}, {label: "罗马尼亚(RO)", value: "RO"}, {
        label: "俄罗斯(RU)",
        value: "RU"
      }, {label: "卢旺达(RW)", value: "RW"}, {
        label: "圣巴泰勒米(BL)",
        value: "BL"
      }, {label: "圣赫勒拿、阿森松和特里斯坦-达库尼亚(SH)", value: "SH"}, {
        label: "圣基茨和尼维斯(KN)",
        value: "KN"
      }, {label: "圣卢西亚(LC)", value: "LC"}, {label: "法属圣马丁(MF)", value: "MF"}, {
        label: "圣皮埃尔和密克隆(PM)",
        value: "PM"
      }, {label: "圣文森特和格林纳丁斯(VC)", value: "VC"}, {label: "萨摩亚(WS)", value: "WS"}, {
        label: "圣马力诺(SM)",
        value: "SM"
      }, {label: "圣多美和普林西比(ST)", value: "ST"}, {label: "沙乌地阿拉伯(SA)", value: "SA"}, {
        label: "塞内加尔(SN)",
        value: "SN"
      }, {label: "塞尔维亚(RS)", value: "RS"}, {label: "塞舌尔(SC)", value: "SC"}, {
        label: "塞拉利昂(SL)",
        value: "SL"
      }, {label: "新加坡(SG)", value: "SG"}, {label: "荷属圣马丁(SX)", value: "SX"}, {
        label: "斯洛伐克(SK)",
        value: "SK"
      }, {label: "斯洛维尼亚(SI)", value: "SI"}, {label: "所罗门群岛(SB)", value: "SB"}, {
        label: "索马利亚(SO)",
        value: "SO"
      }, {label: "南非(ZA)", value: "ZA"}, {label: "南乔治亚和南桑威奇群岛(GS)", value: "GS"}, {
        label: "南苏丹(SS)",
        value: "SS"
      }, {label: "西班牙(ES)", value: "ES"}, {label: "斯里兰卡(LK)", value: "LK"}, {
        label: "苏丹(SD)",
        value: "SD"
      }, {label: "苏里南(SR)", value: "SR"}, {label: "斯瓦尔巴和扬马延(SJ)", value: "SJ"}, {
        label: "瑞典(SE)",
        value: "SE"
      }, {label: "瑞士(CH)", value: "CH"}, {label: "叙利亚(SY)", value: "SY"}, {
        label: "中国台湾省(TW)",
        value: "TW"
      }, {label: "塔吉克斯坦(TJ)", value: "TJ"}, {label: "坦桑尼亚(TZ)", value: "TZ"}, {
        label: "泰国(TH)",
        value: "TH"
      }, {label: "东帝汶(TL)", value: "TL"}, {label: "多哥(TG)", value: "TG"}, {
        label: "托克劳(TK)",
        value: "TK"
      }, {label: "汤加(TO)", value: "TO"}, {label: "千里达及托巴哥(TT)", value: "TT"}, {
        label: "突尼西亚(TN)",
        value: "TN"
      }, {label: "土耳其(TR)", value: "TR"}, {label: "土库曼斯坦(TM)", value: "TM"}, {
        label: "特克斯和凯科斯群岛(TC)",
        value: "TC"
      }, {label: "图瓦卢(TV)", value: "TV"}, {label: "乌干达(UG)", value: "UG"}, {
        label: "乌克兰(UA)",
        value: "UA"
      }, {label: "阿联酋(AE)", value: "AE"}, {label: "英国(GB)", value: "GB"}, {
        label: "美国(US)",
        value: "US"
      }, {label: "美国本土外小岛屿(UM)", value: "UM"}, {label: "乌拉圭(UY)", value: "UY"}, {
        label: "乌兹别克斯坦(UZ)",
        value: "UZ"
      }, {label: "瓦努阿图(VU)", value: "VU"}, {label: "委内瑞拉(VE)", value: "VE"}, {
        label: "越南(VN)",
        value: "VN"
      }, {label: "英属维尔京群岛(VG)", value: "VG"}, {
        label: "美属维尔京群岛(VI)",
        value: "VI"
      }, {label: "瓦利斯和富图纳(WF)", value: "WF"}, {label: "西撒哈拉(EH)", value: "EH"}, {
        label: "叶门(YE)",
        value: "YE"
      }, {label: "尚比亚(ZM)", value: "ZM"}, {label: "辛巴威(ZW)", value: "ZW"}],
      path: path
    }
  },
  async created() {
    await this.getTableData()
    await this.getPayWay()
  },
  methods: {
    tableSort(data) {
      let columnData = data.column
      let orderData = data.order
      let propData = data.prop
      if (propData !== null && orderData !== null) {
        switch (propData) {
          case 'app_id':
            this.searchInfo.order_by = `a.app_id`
            break
          default:
            this.searchInfo.order_by = propData
        }
        this.searchInfo.order_desc = orderData !== 'ascending'
      } else {
        this.searchInfo.order_by = null
        this.searchInfo.order_desc = false
      }
      this.getTableData()
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteAppPayments(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
      this.multipleSelection.map(item => {
        ids.push(item.id)
      })
      const res = await deleteAppPaymentsByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateAppPayments(row) {
      this.midPicFileList = []
      const res = await findAppPayments({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.appPayments
        if (this.formData.pay_icon !== '') {
          this.midPicFileList = [{ name: '', url: this.formData.pay_icon }]
        }
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        app_id: 0,
        ab_test: '',
        pay_id: 0,
        pay_internal_name: '',
        pay_name: '',
        pay_text: '',
        sort: 0,
        version: '',
        state: 0,
        created_time: 0,
        updated_time: 0,
      }
      this.midPicFileList = []
    },
    async deleteAppPayments(row) {
      const res = await deleteAppPayments({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createAppPayments(this.formData)
          break
        case 'update':
          res = await updateAppPayments(this.formData)
          break
        default:
          res = await createAppPayments(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    },
    delCache() {
      deletePayWayCache().then(res => {
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功'
          })
        }
      })
    },
    async getPayWay() {
      const res = await getPayWaysList()
      if (res.code === 0) {
        this.payWays = res.data.list
      }
    },
    formatPayWay(payWay) {
      for (const x of this.payWays) {
        if (x.id === payWay) {
          return x.internal_name + '(' + x.id + ')'
        }
      }
      return ''
    },
    formatPaymentState(rowState) {
      for (const x of this.shelfStateOptions) {
        if (x.value === rowState) {
          return x.label
        }
      }
      return ''
    },
    handleImageRemove() {
      this.midPicFileList = []
      this.formData.pay_icon = null
    },
    handleMidPicSuccess(res) {
      this.midPicFileList = []
      const { data } = res
      if (data.url) {
        this.formData.pay_icon = data.url
        this.midPicFileList.push(data)
      }
      this.progressLoading.close()
    },
    handleMidPicPreview(file) {
      this.dialogImgSrc = file.url
      this.imgPreviewDialogVisible = true
    },
  },
}
</script>

<style>
</style>

