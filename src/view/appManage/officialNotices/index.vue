<template>
  <div>
    <div class="gva-search-box">
      <el-form :inline="true" :model="searchInfo" class="demo-form-inline">
        <el-form-item>
          <el-button size="mini" type="primary" icon="el-icon-search" @click="onSubmit">查询</el-button>
          <el-button size="mini" icon="el-icon-refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
        <el-popover v-model:visible="deleteVisible" placement="top" width="160">
          <p>确定要删除吗？</p>
          <div style="text-align: right; margin-top: 8px;">
            <el-button size="mini" type="text" @click="deleteVisible = false">取消</el-button>
            <el-button size="mini" type="primary" @click="onDelete">确定</el-button>
          </div>
          <template #reference>
            <el-button
              icon="el-icon-delete"
              size="mini"
              style="margin-left: 10px;"
              :disabled="!multipleSelection.length"
            >删除
            </el-button>
          </template>
        </el-popover>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="center" label="日期" width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="公告标题" prop="title" width="120" />
        <el-table-column align="center" label="AppId" prop="app_id" width="120" />
        <el-table-column align="center" label="版本列表" prop="version" width="120" />
        <el-table-column align="center" label="目标用户" prop="target_id" width="120" />
        <el-table-column align="center" label="频率" width="180">
          <template #default="scope">{{ formaOfficialNoticesFrequencyPartner(scope.row.frequency) }}</template>
        </el-table-column>
        <el-table-column align="center" label="状态" width="60">
          <template #default="scope">
            <el-switch
              v-model="scope.row.state"
              active-color="#13ce66"
              inactive-color="#ff4949"
              :active-value="1"
              :inactive-value="2"
              @change="stateChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column align="center" label="按钮组">
          <template #default="scope">
            <el-button
              type="text"
              icon="el-icon-edit"
              size="small"
              class="table-button"
              @click="updateOfficialNotices(scope.row)"
            >变更
            </el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作">
      <el-form :model="formData" label-position="right" label-width="80px">
        <el-form-item label="公告标题:">
          <el-input v-model="formData.title" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="目标用户:">
          <el-input v-model="formData.target_id" clearable placeholder="请输入" @change="targetChange" />
        </el-form-item>
        <el-form-item label="AppId:">
          <el-select
            v-model="formData.app_id"
            clearable
            filterable
            multiple
            placeholder="AppId"
            :disabled="appIdDisabled"
            @change="targetChange"
          >
            <el-option
                v-for="item in appList"
                :key="item"
                :label="`${item.app_name}(${item.id})`"
                :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="版本列表:">
          <el-input v-model="formData.version" clearable placeholder="请输入" :disabled="frequencyDisabled" />
        </el-form-item>
        <el-form-item label="频率">
          <el-select v-model.number="formData.frequency" clearable placeholder="请输入">
            <el-option
              v-for="item in frequencyOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="状态:">
          <el-select v-model.number="formData.state" clearable placeholder="请输入">
            <el-option
              v-for="item in officialNoticesStatusOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="公告内容:">
          <el-input
            v-model="formData.content"
            clearable
            placeholder="请输入"
            type="textarea"
            :autosize="{ minRows: 6, maxRows: 18 }"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createOfficialNotices,
  deleteOfficialNotices,
  deleteOfficialNoticesByIds,
  updateOfficialNotices,
  findOfficialNotices,
  getOfficialNoticesList
} from '@/api/officialNotices' //  此处请自行替换地址
import { getAppXesList } from '@/api/appXes'
import infoList from '@/mixins/infoList'

//  频率: 1 2 3
const frequencyOptions = [
  {
    value: 1,
    label: '首次进入',
  },
  {
    value: 2,
    label: '每日一次',
  },
  {
    value: 3,
    label: '每次进入app',
  },
]

const officialNoticesStatusOptions = [
  {
    value: 1,
    label: '上架',
  },
  {
    value: 2,
    label: '下架',
  },
]

export default {
  name: 'OfficialNotices',
  mixins: [infoList],
  data() {
    return {
      appListOptions: [],
      officialNoticesStatusOptions: officialNoticesStatusOptions,
      frequencyOptions: frequencyOptions,
      listApi: getOfficialNoticesList,
      dialogFormVisible: false,
      appIdDisabled: false,
      frequencyDisabled: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        title: '',
        state: 0,
        version: '',
        app_id: [],
        target_id: '',
        frequency: 0,
        content: '',
        created_time: 0,
        updated_time: 0,
      },
    }
  },
  async created() {
    await this.getAppList()
    await this.getTableData()
    this.targetChange()
  },
  methods: {
    async stateChange(row) {
      await updateOfficialNotices(row)
    },
    targetChange: function() {
      if (this.formData.target_id !== '') {
        this.appIdDisabled = true
      } else {
        this.appIdDisabled = false
      }

      if (this.appIdDisabled === false && this.formData.app_id?.length <= 1) {
        this.frequencyDisabled = false
      } else {
        this.frequencyDisabled = true
      }
    },
    formaAppList: function(bool) {
      let text
      this.appListOptions.forEach(function(element) {
        if (bool === element.id) {
          text = element.appName
          return text
        }
      })
      return text
    },
    async getAppList() {
      const res = await getAppXesList()
      this.appListOptions = res.data.list
    },
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteOfficialNotices(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
        this.multipleSelection.map(item => {
          ids.push(item.id)
        })
      const res = await deleteOfficialNoticesByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateOfficialNotices(row) {
      const res = await findOfficialNotices({ id: row.id })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.reofficialNotices

        if (this.formData.app_id) {
          this.formData.app_id = this.formData.app_id.split(',').map(Number)
        }

        this.targetChange()

        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        title: '',
        state: 0,
        app_id: '',
        version: '',
        target_id: '',
        frequency: 0,
        content: '',
        created_time: 0,
        updated_time: 0,
      }
    },
    async deleteOfficialNotices(row) {
      const res = await deleteOfficialNotices({ id: row.id })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      if (this.formData.app_id) {
        this.formData.app_id = this.formData.app_id.join(',')
      }
      switch (this.type) {
        case 'create':
          res = await createOfficialNotices(this.formData)
          break
        case 'update':
          res = await updateOfficialNotices(this.formData)
          break
        default:
          res = await createOfficialNotices(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    }
  },
}
</script>

<style>
</style>
