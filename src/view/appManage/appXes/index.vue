<template>
  <div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button size="mini" type="primary" icon="el-icon-plus" @click="openDialog">新增</el-button>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        max-height="590"
      >
        <el-table-column align="center" label="按钮组" width="120" fixed="left">
          <template #default="scope">
            <el-button type="text" icon="el-icon-edit" size="small" class="table-button" @click="updateAppXes(scope.row)">变更</el-button>
            <el-button type="text" icon="el-icon-delete" size="mini" @click="deleteRow(scope.row)">删除</el-button>
          </template>
        </el-table-column>
        <el-table-column align="center" label="日期" width="180">
          <template #default="scope">{{ formatDate(scope.row.created_at) }}</template>
        </el-table-column>
        <el-table-column align="center" label="包名" prop="bundleId" width="200" />
        <el-table-column align="center" label="应用Key" prop="appKey" width="300" />
        <el-table-column align="center" label="app名称" prop="appName" width="120" />
        <el-table-column align="center" label="通话每分钟扣费钻石数" prop="callPrice" width="120" />
        <el-table-column align="center" label="解锁相册扣费钻石数" prop="albumPrice" width="120" />
        <el-table-column align="center" label="发送消息扣费钻石数" prop="messagePrice" width="120" />
        <el-table-column align="center" label="官方账户ID" prop="official" width="120" />
        <el-table-column align="center" label="官方邮箱" prop="officialEmail" width="120" />
        <el-table-column align="center" label="合作方标记" prop="partner" width="120">
          <template #default="scope">{{ formaPartner(scope.row.partner) }}</template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination v-show="total > 0"
                       layout="total, sizes, prev, pager, next, jumper"
                       :current-page="page"
                       :page-size="pageSize"
                       :page-sizes="pageSizeList"
                       :total="total"
                       @current-change="handleCurrentChange"
                       @size-change="handleSizeChange"
        />
      </div>
    </div>
    <el-dialog v-model="dialogFormVisible" :before-close="closeDialog" title="弹窗操作" top="1%" width="60%">
      <el-form :model="formData" label-position="top" :inline="true" label-width="80px" style="max-height: 80vh;overflow-y: auto;">
        <el-form-item label="包名:" style="width:30%">
          <el-input v-model="formData.bundleId" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="应用Key:" style="width:30%">
          <el-input v-model="formData.appKey" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="app名称:" style="width:30%">
          <el-input v-model="formData.appName" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="通话每分钟扣费钻石数:" style="width:30%">
          <el-input v-model.number="formData.callPrice" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="解锁相册扣费钻石数:" style="width:30%">
          <el-input v-model.number="formData.albumPrice" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="发送消息扣费钻石数:" style="width:30%">
          <el-input v-model.number="formData.messagePrice" clearable placeholder="请输入" />
        </el-form-item>
        <el-form-item label="接口地址配置 api,h5,ws,image:" style="width:50%">
          <el-input v-model="formData.appDomain" clearable placeholder="请输入" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item label="客户自助服务链接:" style="width:50%">
          <el-input v-model="formData.aiHelp" clearable placeholder="请输入" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item label="FCM推送配置ApiKey:" style="width:50%">
          <el-input v-model="formData.fcmApiKey" clearable placeholder="请输入" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item label="谷歌支付Json串:" style="width:50%">
          <el-input v-model="formData.gooPay" clearable placeholder="请输入" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item label="Facebook登录配置 clientId,clientSecret:" style="width:50%">
          <el-input v-model="formData.fbSign" clearable placeholder="请输入" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item label="安卓GOOGLE服务端访问google api所需账户 cred:" style="width:50%">
          <el-input v-model="formData.gooSign" clearable placeholder="请输入" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item label="苹果GOOGLE服务端访问google api所需账户 cred:" style="width:50%">
          <el-input v-model="formData.gooIosSign" clearable placeholder="请输入" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item label="苹果登录验证所需配置 team_id,client_id,key_id,signing_key:" style="width:50%">
          <el-input v-model="formData.appleSign" clearable placeholder="请输入" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item label="应用各类开关:" style="width:50%">
          <el-input v-model="formData.appSwitch" clearable placeholder="请输入" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item label="官方账户ID:" style="width:50%">
          <el-input v-model.number="formData.official" clearable placeholder="请输入" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item label="官方邮箱:" style="width:50%">
          <el-input v-model="formData.officialEmail" clearable placeholder="请输入" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item label="强制升级配置:" style="width:50%">
          <el-input v-model="formData.forceUpgrade" clearable placeholder="请输入" type="textarea" :rows="4" />
        </el-form-item>
        <el-form-item label="合作方标记" style="width:50%">
          <el-input v-model.number="formData.partner" clearable placeholder="请输入" type="textarea" :rows="4" />
        </el-form-item>

        <el-form-item label="合作方标记" style="width:50%">
          <el-select v-model="formData.partner" clearable placeholder="合作方标记">
            <el-option
              v-for="item in partnerOptions"
              :key="item.value"
              :label="`${item.label}(${item.value})`"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <el-form-item label="合作开关:" style="width:50%">
          <el-input v-model="formData.cooperative" clearable placeholder="请输入" type="textarea" :rows="4" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="small" @click="closeDialog">取 消</el-button>
          <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import {
  createAppXes,
  deleteAppXes,
  deleteAppXesByIds,
  updateAppXes,
  findAppXes,
  getAppXesList
} from '@/api/appXes' //  此处请自行替换地址
import infoList from '@/mixins/infoList'
const partnerOptions = [
  {
    value: 0,
    label: '内部包',
  },
  {
    value: 1,
    label: '合作方',
  },
]
export default {
  name: 'AppXes',
  mixins: [infoList],
  data() {
    return {
      partnerOptions: partnerOptions,
      listApi: getAppXesList,
      dialogFormVisible: false,
      type: '',
      deleteVisible: false,
      multipleSelection: [],
      formData: {
        bundleId: '',
        appKey: '',
        appName: '',
        appDomain: '',
        callPrice: 0,
        albumPrice: 0,
        messagePrice: 0,
        aiHelp: '',
        fcmApiKey: '',
        gooPay: '',
        fbSign: '',
        gooSign: '',
        gooIosSign: '',
        appleSign: '',
        appSwitch: '',
        official: 0,
        officialEmail: '',
        forceUpgrade: '',
        partner: 0,
        cooperative: '',
      }
    }
  },
  async created() {
    await this.getTableData()
  },
  methods: {
    onReset() {
      this.searchInfo = {}
    },
    // 条件搜索前端看此方法
    onSubmit() {
      this.page = 1
      this.pageSize = 10
      this.getTableData()
    },
    deleteRow(row) {
      this.$confirm('确定要删除吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.deleteAppXes(row)
      })
    },
    async onDelete() {
      const ids = []
      if (this.multipleSelection.length === 0) {
        this.$message({
          type: 'warning',
          message: '请选择要删除的数据'
        })
        return
      }
      this.multipleSelection &&
                this.multipleSelection.map(item => {
                  ids.push(item.ID)
                })
      const res = await deleteAppXesByIds({ ids })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === ids.length && this.page > 1) {
          this.page--
        }
        this.deleteVisible = false
        this.getTableData()
      }
    },
    async updateAppXes(row) {
      const res = await findAppXes({ ID: row.ID })
      this.type = 'update'
      if (res.code === 0) {
        this.formData = res.data.reappXes
        this.dialogFormVisible = true
      }
    },
    closeDialog() {
      this.dialogFormVisible = false
      this.formData = {
        bundleId: '',
        appKey: '',
        appName: '',
        appDomain: '',
        callPrice: 0,
        albumPrice: 0,
        messagePrice: 0,
        aiHelp: '',
        fcmApiKey: '',
        gooPay: '',
        fbSign: '',
        gooSign: '',
        gooIosSign: '',
        appleSign: '',
        appSwitch: '',
        official: 0,
        officialEmail: '',
        forceUpgrade: '',
        partner: 0,
        cooperative: '',
      }
    },
    async deleteAppXes(row) {
      const res = await deleteAppXes({ ID: row.ID })
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '删除成功'
        })
        if (this.tableData.length === 1 && this.page > 1) {
          this.page--
        }
        this.getTableData()
      }
    },
    async enterDialog() {
      let res
      switch (this.type) {
        case 'create':
          res = await createAppXes(this.formData)
          break
        case 'update':
          res = await updateAppXes(this.formData)
          break
        default:
          res = await createAppXes(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功'
        })
        this.closeDialog()
        this.getTableData()
      }
    },
    openDialog() {
      this.type = 'create'
      this.dialogFormVisible = true
    }
  },
}
</script>

<style>
</style>
