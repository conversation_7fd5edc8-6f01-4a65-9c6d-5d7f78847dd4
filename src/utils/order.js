import { store } from '@/store'

export const getUsdPrice = (price, payment) => {
  let payWaysDict = store.getters['user/payWaysDict']
  if (payWaysDict === undefined) {
    console.error("所有支付选项payWaysDict === undefined")
    return price
  }
  let toUsdRate = payWaysDict[payment]
  if (toUsdRate === undefined) {
    console.error(`当前支付方式(${payment})所对应的兑换美元费率不存在`)
    return price
  }

  let toUsdRateFloat = parseFloat(toUsdRate)
  let priceFloat = parseFloat(price)
  let realPrice, realPriceStr, realPrice2Float
  realPrice = priceFloat * toUsdRateFloat
  realPriceStr = realPrice.toFixed(2)
  realPrice2Float = parseFloat(realPriceStr)
  return realPrice2Float
}
