export const numberTo3 = (value) => {
    if (!value) return 0
    return parseFloat(parseFloat(value).toFixed(2))


    let nValue = parseFloat(value).toFixed(2)
    // 获取整数部分
    const intPart = Math.trunc(nValue)
    // 整数部分处理，增加,
    const intPartFormat = intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
    // 预定义小数部分
    let floatPart = ''
    // 将数值截取为小数部分和整数部分
    const valueArray = nValue.toString().split('.')
    if (valueArray.length === 2) { // 有小数部分
        floatPart = valueArray[1].toString() // 取得小数部分
        return intPartFormat + '.' + floatPart
    }
    return intPartFormat + floatPart
}
export const delcommafy = (num) => {
    if ((num + "").trim() === "") {
        return "";
    }
    num = num.replace(/,/gi, "");
    return num
}
export const numberTo3Int = (value) => {
    if (!value) return 0
    let nValue = parseInt(value)
    // 获取整数部分
    const intPart = Math.trunc(nValue)
    // 整数部分处理，增加,
    return intPart.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,')
}

export const calcPercent = (a, b) => {
    let rate = 0.0
    let aNew = parseFloat(a)
    let bNew = parseFloat(b)
    if (!isNaN(aNew) && !isNaN(bNew) && bNew !== 0) {
        rate = aNew / bNew
    }
    let rateStr = rate.toFixed(4)
    let rateFloat = parseFloat(rateStr) * 100
    let rateFloatStr = rateFloat.toFixed(2)
    return [`${rateFloatStr}%`, rateFloat]
}

export const div2 = (a, b) => {
    let rate = 0
    let aNew = parseFloat(a)
    let bNew = parseFloat(b)
    if (!isNaN(aNew) && !isNaN(bNew) && bNew !== 0) {
        rate = aNew / bNew
    }
    return parseFloat(rate.toFixed(2))
}
