<template>
  <div>
    <el-dialog destroy-on-close v-model="dialogFormVisible" :title="`修改信息:${user_id}`" top="2px" :before-close="closeInfoDialog">
      <el-form ref="usersForm" :model="formData" label-position="left">
        <!--头像-->
        <el-row>
          <el-col :span="24">
            <el-form-item label="头像">
              <el-upload
                ref="handleImage"
                :action="`${path}/files/createFiles`"
                :headers="{ 'x-token': token }"
                list-type="picture-card"
                :file-list="avatarFileList"
                :on-preview="handleImagePreview"
                :on-success="handleImageSuccess"
                :on-progress="handleProgressLoading"
                :multiple="false"
                :limit="1"
                accept=".jpg, .jpeg, .png, .gif"
                :on-remove="handleImageRemove"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <!--头像-->

        <!--照片墙-->
        <el-row>
          <el-col :span="24">
            <el-form-item label="照片墙">
              <draggable
                v-model="formData.photoAlbums"
                class="el-upload-list el-upload-list--picture-card"
                item-key="id"
                @start="isDragging = true"
                @end="isDragging = false"
                @update="updatePhotoAlbumsList"
              >
                <template #item="{element}">
                  <div class="el-upload-list__items" style="width: 146px;"  :class="{'checkBoxs':!element.audit_tags}">
                    <div class="el-upload-list__item">
                      <img :src="element.url" class="el-upload-list__item-thumbnail" alt="img" />
                      <span class="el-upload-list__item-actions">
                        <span class="el-upload-list__item-preview" @click="handleImagePreview(element)">
                          <i class="el-icon-zoom-in" />
                        </span>
                        <span class="el-upload-list__item-delete" @click="handlePhotoAlbumsRemove(element)">
                          <i class="el-icon-delete" />
                        </span>
                        <!-- <el-checkbox v-model="element.recover" label="推荐"/> -->
                      </span>
                    </div>
                    <div v-if="element.audit_tags && element.audit_tags.length > 0">
                      <div class="extra-item">
                        <span style="margin-right: 6px;">推荐:</span><el-checkbox @change="imgRecoverChange(element)" v-model="element.recover" label="" />
                      </div>
                      <div class="extra-item extra-item1" >
                        <span style="margin-right: 6px;width: 60px;align-self: start;">类型:</span>
                        <el-checkbox-group v-model="element.imgType">
                          <el-checkbox
                            v-for="types in element.audit_tags"
                            @change="(value) => changeChekBox(element, value, 1, types)"
                            :key="types.id"
                            :value="types.id"
                            :label="types.id"
                            >{{ types.name }}</el-checkbox
                          >
                        </el-checkbox-group>
                      </div>
                    </div>
                  </div>
                </template>
              </draggable>
              <el-upload
                ref="handlePhotoAlbums"
                :action="`${path}/files/createFiles`"
                :headers="{ 'x-token': token }"
                list-type="picture-card"
                :show-file-list="false"
                :on-success="handlePhotoAlbumsSuccess"
                :on-progress="handleProgressLoading"
                :multiple="true"
                accept=".jpg, .jpeg, .png, .gif"
              >
                <i class="el-icon-plus" />
                <!-- 使用 #file 插槽来扩展默认的图片项 -->
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <!--照片墙--> 

        <!--视频墙-->
        <el-row>
          <el-col :span="24">
            <el-form-item label="视频墙">
              <draggable v-model="formData.albumVideo" class="el-upload-list el-upload-list--picture-card" item-key="id" @start="isDragging = true" @end="isDragging = false">
                <template #item="{element}">
                  <div class="el-upload-list__items" style="min-height: 290px;" :class="{'checkBoxs':!element.audit_tags}">

                  <div class="el-upload-list__item">
                    <img :src="element.url" class="el-upload-list__item-thumbnail" style="display: block;" alt="img" />
                    <span class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handleVideoVisible(element)">
                        <i class="el-icon-zoom-in" />
                      </span>
                      <span class="el-upload-list__item-delete" @click="handleAlbumVideoRemove(element)">
                        <i class="el-icon-delete" />
                      </span>
                      <!-- <el-checkbox v-model="element.recover" label="推荐" @change="videoRecoverChange(element)" /> -->
                    </span>
                  </div>

                  <div v-if="element.audit_tags && element.audit_tags.length > 0">
                      <div class="extra-item">
                        <span style="margin-right: 6px;">推荐:</span><el-checkbox @change="videoRecoverChange(element)" v-model="element.recover" label="" />
                      </div>
                      <div class="extra-item extra-item1">
                        <span style="margin-right: 6px;">类型:</span>
                        <el-checkbox-group v-model="element.imgType" >
                          <el-checkbox v-for="types in element.audit_tags" @change="(value) => changeChekBox(element, value, 2,types)" :key="types.id" :value="types.id" :label="types.id">{{ types.name }}</el-checkbox>
                        </el-checkbox-group>
                      </div>
                    </div>
                  </div>
                </template>
              </draggable>
              <el-upload
                ref="handleAlbumVideo"
                :action="`${path}/files/createVideo`"
                :headers="{ 'x-token': token }"
                list-type="picture-card"
                :on-success="handleAlbumVideoSuccess"
                :on-progress="handleProgressLoading"
                :show-file-list="false"
                :multiple="true"
                accept=".mp4"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <!--视频墙-->

        <!--匹配假视频-->
        <el-row>
          <el-col :span="24">
            <el-form-item label="匹配假视频" class="m-photo-box">
              <el-upload
                ref="handleConstellationVideo"
                :action="`${path}/files/createVideo`"
                :headers="{ 'x-token': token }"
                list-type="picture-card"
                :file-list="formData.constellationVideo"
                :on-success="handleConstellationVideoSuccess"
                :on-preview="handleVideoVisible"
                :on-progress="handleProgressLoading"
                :on-remove="handleConstellationVideoRemove"
                :multiple="false"
                :limit="1"
                accept=".mp4"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <!--匹配假视频-->

        <!--转接假视频-->
        <el-row>
          <el-col :span="24">
            <el-form-item label="转接假视频" class="m-photo-box">
              <draggable v-model="formData.callVideo" class="el-upload-list el-upload-list--picture-card" item-key="id" @start="isDragging = true" @end="isDragging = false">
                <template #item="{element}">
                  <div class="el-upload-list__item">
                    <img :src="element.url" class="el-upload-list__item-thumbnail" />
                    <span class="el-upload-list__item-actions">
                      <span class="el-upload-list__item-preview" @click="handleVideoVisible(element)">
                        <i class="el-icon-zoom-in" />
                      </span>
                      <span class="el-upload-list__item-delete" @click="handleCallVideoRemove(element)">
                        <i class="el-icon-delete" />
                      </span>
                    </span>
                  </div>
                </template>
              </draggable>
              <el-upload
                ref="handleCallVideo"
                :action="`${path}/files/createVideo`"
                :headers="{ 'x-token': token }"
                list-type="picture-card"
                :on-success="handleCallVideoSuccess"
                :on-progress="handleProgressLoading"
                :on-preview="handleVideoVisible"
                :show-file-list="false"
                :multiple="false"
                accept=".mp4"
              >
                <i class="el-icon-plus" />
              </el-upload>
            </el-form-item>
          </el-col>
        </el-row>
        <!--转接假视频-->

        <!--基础资料-->
        <el-row :gutter="20" style="padding: 0">
          <el-col :span="4">
            <el-form-item label="昵称">
              <el-input v-model="formData.nickname" clearable placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="AppID">
              <el-input disabled v-model="formData.appId" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别">
              <el-radio-group v-model="formData.gender">
                <el-radio v-for="item in genderOptions" :key="item.value" :label="item.value">
                  {{ item.label }}
                </el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="padding: 0" :gutter="10">
          <el-col :span="6">
            <el-form-item label="生日">
              <el-date-picker v-model="formData.birthday" type="date" placeholder="选择日期" />
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label="身高">
              <el-input v-model="formData.height" placeholder="请输入身高数值" />
            </el-form-item>
          </el-col>
          <el-col :span="3">
            <el-form-item label="体重">
              <el-input v-model="formData.weight" placeholder="请输入体重数值" />
            </el-form-item>
          </el-col>
          <el-col :span="4">
            <el-form-item label="语言">
              <el-select v-model.number="formData.langTags" clearable filterable placeholder="语言">
                <el-option v-for="item in anchorLangOptions" :key="item.value" :label="`${item.label}(${item.value})`" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="标签">
              <el-select v-model="formData.labels" multiple filterable allow-create clearable collapse-tags default-first-option placeholder="请选择标签">
                <el-option-group v-for="group in personalityLabelsOptions" :key="group.id" :label="group.name">
                  <el-option v-for="item in group.children" :key="item.id" :label="item.name" :value="item.id" />
                </el-option-group>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="padding: 0">
          <el-col :span="24">
            <el-form-item label="个性签名" style="width: 80%;">
              <el-input v-model="formData.introduction" type="textarea" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-divider content-position="left" style="margin: 10px 0; width: 80%">补充资料</el-divider>
        <el-row style="padding: 0">
          <el-col :span="6">
            <el-form-item label="工会">
              <el-select v-model="formData.unionId" clearable filterable remote reserve-keyword placeholder="所属工会">
                <el-option v-for="item in uniosList" :key="item.id" :label="`${item.name}(${item.id})`" :value="item.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="主播等级">
              <el-select v-model="formData.levels" clearable filterable placeholder="主播等级">
                <el-option v-for="item in levelsOptions" :key="item.value" :label="`${item.label}(${item.value})`" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="主播类型">
              <el-select v-model.number="formData.anchorFake" @change="onChangeUserFake(e)" clearable filterable placeholder="主播类型">
                <el-option v-for="item in anchorFakeOptions" :key="item.value" :label="`${item.label}(${item.value})`" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="类型">
              <el-select v-model="formData.fake" clearable filterable placeholder="类型">
                <el-option v-for="item in fakeOptions" :key="item.value" :label="`${item.label}(${item.value})`" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-form-item label="国籍">
              <el-select v-model="formData.countryCode" clearable filterable placeholder="国籍">
                <el-option v-for="item in countryCodeOptions" :key="item.value" :label="`${item.label}(${item.value})`" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row style="padding: 0">
          <el-col :span="24">
            <el-form-item label="推荐标签">
              <draggable v-model="formData.userTags" class="el-upload-list el-upload-list--picture-card" item-key="id" @start="isDragging = true" @end="isDragging = false">
                <template #item="{element}">
                  <span style="margin-right: 5px;">
                    <el-check-tag :checked="element.checked" @change="onChangeUserTags(element)">{{ element.tag_name }}</el-check-tag>
                  </span>
                </template>
              </draggable>
            </el-form-item>
          </el-col>
          <el-col>
            <el-form-item label="敏感值" style="width:30%">
              <el-input-number v-model="formData.avoid" :min="0" :max="100" />
            </el-form-item>
          </el-col>
        </el-row>
        <!--基础资料-->
      </el-form>
      <template #footer>
        <div style="display: flex; justify-content: space-between">
          <div>
            <el-button size="small" type="danger" @click="cancelAuth(formData)">取消认证</el-button>
            <el-button v-if="formData.recovery === 0" size="small" type="danger" @click="showBanAnchorDialog(formData.id)">封禁 </el-button>
            <el-popconfirm v-if="formData.recovery === 1" confirm-button-text="确定" cancel-button-text="取消" @confirm="unsealAnchor" title="确定要解封吗?">
              <template #reference>
                <el-button size="small" type="success">解封</el-button>
              </template>
            </el-popconfirm>
          </div>
          <div>
            <el-button size="small" @click="closeDialog">取 消</el-button>
            <el-button size="small" type="primary" @click="enterDialog">确 定</el-button>
          </div>
        </div>
      </template>
      <el-dialog v-model="banDialogFormVisible" top="5px" :before-close="closeBanDialog" title="主播封禁弹窗" append-to-body>
        <el-form ref="formBanDataRef" :model="formBanData" :rules="formBanDataRules" label-position="right" label-width="120px">
          <el-form-item label="主播ID:">
            <el-input disabled v-model="formBanData.ban_id" clearable />
          </el-form-item>
          <el-form-item label="可提现余额:">
            <el-input disabled v-model="formBanData.balance" clearable />
          </el-form-item>
          <el-form-item label="扣除比例:">
            <el-input-number class="w100" v-model="formBanData.deduction_ratio" :min="0" :max="100" :precision="0" />
          </el-form-item>
          <el-form-item label="封禁天数:">
            <el-input-number class="w100" v-model="formBanData.ban_days" :min="0" :precision="0" />
          </el-form-item>
          <el-form-item label="封禁原因:" prop="ban_reason">
            <el-input type="textarea" v-model="formBanData.ban_reason" :autosize="{ minRows: 6, maxRows: 10 }" />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button size="small" type="primary" @click="enterBanDialog">确 定</el-button>
          </div>
        </template>
      </el-dialog>
      <el-dialog v-model="previewImageVisible" title="图片预览" width="50%" top="1%" center append-to-body>
        <img :src="previewImagePath" class="avatar video-avatar" style="max-height: 80vh;overflow-y: auto;" />
      </el-dialog>
      <el-dialog v-model="videoVisible" title="Notice" width="397px" top="0" append-to-body destroy-on-close center>
        <video :src="videoUrl" class="avatar video-avatar" controls="controls">
          您的浏览器不支持视频播放
        </video>
      </el-dialog>
    </el-dialog>
  </div>
</template>

<script>
import infoList from '@/mixins/infoList'
import draggable from 'vuedraggable'
import { getPersonalityLabelsTree } from '@/api/personality_labels'
import { cancelAuditUsers, createUsers, findOneUserWallet, findUsers, unsealUser, updateUsers, updateUsersFake } from '@/api/users'
import { banAnchor } from '@/api/banRecords'
import { coverDetailsDelete, coverDetailsSave, getAllCoverUrl, updateAuditTags } from '@/api/anchorCoverRecommendationsDetails'
import { deleteUserRecommendVideoRd, findAllVideoUrl, saveUserRecommendVideoRd } from '@/api/userRecommendVideoRd'

const path = import.meta.env.VITE_BASE_API

export default {
  components: { draggable },
  mixins: [infoList],
  props: {
    user_id: {
      type: Number,
      default: 0,
    },
  },
  async created() {
    console.log('created', this.user_id)
    await this.getPersonalityLabelsList()
  },
  data() {
    return {
      checkedCities: ['Shanghai', 'Beijing'],
      cities: ['Shanghai', 'Beijing', 'Guangzhou', 'Shenzhen'],
      dialogFormVisible: false,
      type: '',
      userId: '',
      formData: {},
      formBanData: {},
      formBanDataRules: {
        ban_reason: [{ required: true, message: '请完善该字段.', trigger: 'blur' }],
      },
      banDialogFormVisible: false,
      isDragging: false,
      path,
      avatarFileList: [],
      previewImagePath: '',
      previewImageVisible: false,
      videoVisible: false,
      videoUrl: '',
      personalityLabelsOptions: [],
    }
  },
  watch: {
    user_id: {
      handler(val) {
        console.log('user_id handler', val)
        if (val) {
          this.showUserDialog(val)
        }
      },
      immediate: true,
    },
  },
  methods: {
    changeChekBox(element, value, type, item) {
      // if(type===1){

      // }
      // element.imgType=
      // console.log(item)
      // console.log(type)
      // console.log(element)
      // console.log(value)

      // element.imgType = [item.id]

      if(element.imgType.length>1){
          // 只能选择一个
          element.imgType = [item.id]
          
      }

      // 照片类型不为空
      // if(value.length>0){
      //     op_type 1-图片 2-视频
      // key_id // 对应的主键id
      // tag_id // 标签id // id 1-没有标签  2-性感  3-普通

      let reqData = {
        op_type: type,
        key_id: element.id,
        user_id: this.formData.id,
        tag_id: element.imgType.length > 0 ? element.imgType[0] : 1,
      }

      console.log(reqData)
      // return
      updateAuditTags(reqData).then((res) => {
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '设置成功',
          })
        }
      })
    },

    closeInfoDialog(done) {
      this.$emit('closeFunc')
      done()
    },
    async initAllCoverUrlList() {
      let photoAlbums = this.formData.photoAlbums || []
      if (photoAlbums.length === 0) {
        return
      }
      let res = await getAllCoverUrl({ anchor_id: this.formData.id })
      if (res.code === 0) {
        let coverUrlList = res.data || []
        if (coverUrlList.length === 0) {
          return
        }
        photoAlbums.forEach(function(value, index, array) {
          coverUrlList.forEach(function(value1, index1, array1) {
            if (value1.cover_url.includes(value.key)) {
              array[index].recover = true
            }
          })
        })
        this.formData.photoAlbums = photoAlbums
      }
    },
    async initAllVideoUrlList() {
      let albumVideo = this.formData.albumVideo || []
      if (albumVideo.length === 0) {
        return
      }
      let res = await findAllVideoUrl({ user_id: this.formData.id })
      if (res.code === 0) {
        let coverUrlList = res.data || []
        if (coverUrlList.length === 0) {
          return
        }
        albumVideo.forEach(function(value, index, array) {
          coverUrlList.forEach(function(value1, index1, array1) {
            if (value.video === value1.video_url) {
              array[index].recover = true
            }
          })
        })
        this.formData.albumVideo = albumVideo
      }
    },
    imgRecoverChange(element) {
      if (element.recover) {
        coverDetailsSave({
          anchor_id: this.formData.id,
          cover_url: element.url,
        }).then((res) => {
          if (res.code === 0) {
            this.$message({
              type: 'success',
              message: '成功',
            })
          }
        })
      } else {
        coverDetailsDelete({
          anchor_id: this.formData.id,
          cover_url: element.url,
        }).then((res) => {
          if (res.code === 0) {
            this.$message({
              type: 'success',
              message: '成功',
            })
          }
        })
      }
    },
    videoRecoverChange(element) {
      if (element.recover) {
        saveUserRecommendVideoRd({
          user_id: this.formData.id,
          video_url: element.video,
        }).then((res) => {
          if (res.code === 0) {
            this.$message({
              type: 'success',
              message: '成功',
            })
          }
        })
      } else {
        deleteUserRecommendVideoRd({
          user_id: this.formData.id,
          video_url: element.video,
        }).then((res) => {
          if (res.code === 0) {
            this.$message({
              type: 'success',
              message: '成功',
            })
          }
        })
      }
    },
    async enterBanDialog() {
      this.$refs.formBanDataRef.validate(async (v) => {
        if (!v) {
          return
        }
        let res = await banAnchor(this.formBanData)
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: '成功',
          })
          let userRes = await findUsers({ id: this.formBanData.ban_id })
          if (userRes.code === 0) {
            this.formData = userRes.data.reusers
          }
          this.closeBanDialog()
        }
      })
    },
    closeBanDialog() {
      this.banDialogFormVisible = false
      this.$refs.formBanDataRef.resetFields()
    },
    async enterDialog() {
      let res
      if(!this.formData.removeAvatar){
        this.formData.removeAvatar = false
      }
      switch (this.type) {
        case 'create':
          res = await createUsers(this.formData)
          break
        case 'update':
          res = await updateUsers(this.formData)
          break
        default:
          res = await createUsers(this.formData)
          break
      }
      if (res.code === 0) {
        this.$message({
          type: 'success',
          message: '创建/更改成功',
        })
        this.closeDialog()
        // this.getTableData()
      }
    },
    closeDialog() {
      this.avatarFileList = []
      this.userId = 0
      this.dialogFormVisible = false
      this.formData = {}
      this.$emit('closeFunc')
      // this.getTableData()
    },
    showBanAnchorDialog(user_id) {
      findOneUserWallet({ user_id }).then((res) => {
        if (res.code === 0) {
          this.formBanData = {
            record_type: 1,
            ban_id: user_id,
            deduction_ratio: 100,
            ban_days: 0,
            ban_reason: '',
            balance: res.data.un_diamonds,
          }
          this.banDialogFormVisible = true
        }
      })
    },
    unsealAnchor() {
      unsealUser({ userId: this.formData.id }).then((res) => {
        if (res.code === 0) {
          this.$message.success('成功')
          this.dialogFormVisible = false
        }
      })
    },
    cancelAuth(formData) {
      this.handleProgressLoading()
      cancelAuditUsers({ userId: formData.id }).then((res) => {
        // 关闭loading
        this.progressLoading.close()
        if (res.code === 0) {
          this.$message({
            type: 'success',
            message: res.msg,
          })
          this.closeDialog()
        } else {
          this.$message({
            type: 'danger',
            message: res.msg,
          })
        }
      })
    },
    onChangeUserTags(e) {
      this.formData.userTags.forEach(function(value, index, array) {
        if (value.id === e.id) {
          array[index].checked = !e.checked
        }
      })
    },
    async onChangeUserFake(e) {
      console.log('onChangeUserFake', this.formData.id)
      const res = await updateUsersFake(this.formData)
      if (res.code === 0) {
        console.log('==更新fake成功')
      }
    },
    async getPersonalityLabelsList() {
      const res = await getPersonalityLabelsTree()
      this.personalityLabelsOptions = res.data.list
    },
    handleConstellationVideoSuccess(response, file, fileList) {
      this.formData.constellationVideo = []
      fileList.forEach((item, i) => {
        if (item.response !== undefined) {
          this.formData.constellationVideo.push(item.response.data)
        } else {
          this.formData.constellationVideo.push(item)
        }
      })
      this.progressLoading.close()
      this.$refs['handleConstellationVideo'].clearFiles()
    },
    handleConstellationVideoRemove(file, fileList) {
      this.formData.constellationVideo = []
      fileList.forEach((item, i) => {
        this.formData.constellationVideo.push(item)
      })
    },
    handleCallVideoRemove(file, fileList) {
      this.formData.callVideo = this.formData.callVideo.filter(function(age) {
        return age.url !== file.url
      })
    },
    handleCallVideoSuccess(response, file, fileList) {
      if (!this.formData.callVideo) {
        this.formData.callVideo = []
      }
      this.formData.callVideo.push(file.response.data)
      const temp = []
      this.formData.callVideo.forEach(function(a) {
        const check = temp.every(function(b) {
          return a.url !== b.url
        })
        check ? temp.push(a) : []
      })
      this.formData.callVideo = temp
      this.progressLoading.close()
      this.$refs['handleCallVideo'].clearFiles()
    },
    handleVideoVisible(file) {
      this.videoVisible = true
      this.videoUrl = file.video
    },
    handleAlbumVideoRemove(file, fileList) {
      this.formData.albumVideo = this.formData.albumVideo.filter(function(age) {
        return age.url !== file.url
      })
    },
    handleAlbumVideoSuccess(response, file, fileList) {
      if (!this.formData.albumVideo) {
        this.formData.albumVideo = []
      }
      this.formData.albumVideo.push(file.response.data)
      const temp = []
      this.formData.albumVideo.forEach(function(a) {
        const check = temp.every(function(b) {
          return a.url !== b.url
        })
        check ? temp.push(a) : []
      })
      this.formData.albumVideo = temp
      this.progressLoading.close()
      this.$refs['handleAlbumVideo'].clearFiles()
    },
    async updatePhotoAlbumsList(e) {
      const newIndex = e.newIndex // 新位置下标
      const oldIndex = e.oldIndex // 原始位置下标
    },
    handlePhotoAlbumsRemove(file) {
      this.formData.photoAlbums = this.formData.photoAlbums.filter(function(age) {
        return age.url !== file.url
      })
    },
    async handlePhotoAlbumsSuccess(response, file, fileList) {
      if (!this.formData.photoAlbums) {
        this.formData.photoAlbums = []
      }
      this.formData.photoAlbums.push(file.response.data)
      // 数据去重
      const temp = []
      this.formData.photoAlbums.forEach(function(a) {
        const check = temp.every(function(b) {
          return a.key !== b.key
        })
        check ? temp.push(a) : []
      })

      this.formData.photoAlbums = temp
      this.progressLoading.close()
      this.$refs['handlePhotoAlbums'].clearFiles()
    },
    handleImagePreview(file) {
      this.previewImagePath = file.url
      this.previewImageVisible = true
    },
    handleImageSuccess(res) {
      this.avatarFileList = []
      const { data } = res
      if (data.url) {
        this.formData.avatar = data.url
        this.formData.removeAvatar = false
        this.avatarFileList.push(data)
      }
      this.progressLoading.close()
    },
    handleImageRemove(file, fileList) {
      this.formData.avatar = ''
      this.formData.removeAvatar = true
      this.avatarFileList = []
    },
    async showUserDialog(id) {
      console.log('showUserDialog', id)
      await this.updateUsers(id)
      await this.initAllVideoUrlList()
      await this.initAllCoverUrlList()
    },
    async updateUsers(id) {
      console.log('updateUsers', id)
      const res = await findUsers({ id: id })
      this.type = 'update'
      this.avatarFileList = []
      if (res.code === 0) {
        this.userId = id
        this.formData = res.data.reusers

        // 处理图片
        if(this.formData.photoAlbums){
          this.formData.photoAlbums.forEach((item) => {
          // item.imgTypeList = [
          //   {
          //     id: 2,
          //     name: '性感',
          //     selected: false, // 是否选中
          //   },
          //   {
          //     id: 3,
          //     name: '普通',
          //     selected: false, // 是否选中
          //   },
          // ]

          const params = item.audit_tags.find((items) => items.selected)
          if (params) {
            item.imgType = [params.id]
          } else {
            item.imgType = []
          }
        })
        }
       

          // 处理视频

          if(this.formData.albumVideo){
            this.formData.albumVideo.forEach((item) => {
          // item.imgTypeList = [
          //   {
          //     id: 2,
          //     name: '性感',
          //     selected: false, // 是否选中
          //   },
          //   {
          //     id: 3,
          //     name: '普通',
          //     selected: false, // 是否选中
          //   },
          // ]

          const params = item.audit_tags.find((items) => items.selected)
          if (params) {
            item.imgType = [params.id]
          } else {
            item.imgType = []
          }
        })
          }
       
        console.log(this.formData)

        if (this.formData.avatar !== '') {
          this.avatarFileList.push({ name: '', url: this.formData.avatar })
        } 
        // else {
        //   this.avatarFileList.push({ name: '', url: this.formData.avatarVerify })
        // }
        this.dialogFormVisible = true
      }
    },
  },
}
</script>

<style lang="scss">
.el-upload-list__items {
  overflow: hidden;
  background-color: #fff;
  // border: 1px solid #c0ccda;
  border-radius: 6px;
  box-sizing: border-box;
  width: 148px;
  height: auto;
  margin: 0 8px 8px 0;
  display: inline-block;
}

.extra-item {
  display: flex;
  align-items: center;
  span {
    // margin-right: 4px;
  }
  .el-radio-group {
    display: flex;
  }
  .el-radio {
    margin-right: 4px;
  }

  .el-radio__label {
    padding-left: 4px;
  }
}

.extra-item1 {
  .el-checkbox {
    margin-right: 4px;
  }
  .el-checkbox__label {
    padding-left: 4px;
  }
  .el-checkbox-group {
    display: flex;
  }
  .el-checkbox-group{
  flex-wrap: wrap;
}
}

.checkBoxs{
  margin-bottom: 88px;
}
</style>
