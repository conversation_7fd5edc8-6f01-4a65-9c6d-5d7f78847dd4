<template>
  <div>
    <el-dialog
      v-model="dialogVisible"
      :title="`主播ID:${anchorId}`"
      width="90%"
      top="5px"
    >
      <el-row>
        <el-col :span="24">
          <p>当前待结算：{{ anchorRecentData.wallet.un_diamonds }}， 当前余额：{{ anchorRecentData.wallet.diamonds }}</p>
        </el-col>
        <el-col :span="24">
          <p>基础信息：本月用户等级：<span style="color: red">{{ anchorRecentData.nowMonthLevel }}</span>&nbsp;&nbsp;&nbsp;&nbsp;上月用户等级：<span
            style="color: red">{{ anchorRecentData.lastMonthLevel }}</span>&nbsp;&nbsp;&nbsp;&nbsp;视频等级：<span
            style="color: red">L{{ anchorRecentData.userLevels.video_level }}</span>&nbsp;&nbsp;&nbsp;&nbsp;
            消息等级：<span style="color: red">L{{ anchorRecentData.userLevels.msg_level }}</span>&nbsp;&nbsp;&nbsp;&nbsp;100人收益:
            {{ anchorRecentData.income100 }}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;付费100人收益:
            {{ anchorRecentData.intimate100 }}</p>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="4">
          <div class="flex justify-start">
            <h1 class="title-2">今日</h1>
            <div class="flex flex-col ml-12">
              <p>活跃数据</p>
              <p>在线时长：{{ formateSeconds(anchorRecentData.todayData.online_duration) }}</p>
              <p>收入：{{ anchorRecentData.todayData.income }}</p>
              <p>推送：{{ anchorRecentData.matchData.today }}</p>
              <p>亲密值：{{ anchorRecentData.intiData.today }}</p>
              <p>收益占比：{{ anchorRecentData.incomeRate.today }}</p>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="flex flex-col justify-center">
            <p>直呼数据</p>
            <div class="mt-6">
              <span>[次]呼叫:{{ anchorRecentData.todayData.call_total }}</span>
              <span class="ml-12">接听: {{ anchorRecentData.todayData.answer_total }}</span>
            </div>
            <p class="mt-6 c-red">接听率: {{ anchorRecentData.todayData.call_rate }}</p>
            <div class="mt-16">
              <span>[人]呼叫:{{ anchorRecentData.todayData.call_user_count }}</span>
              <span class="ml-12">接听:{{ anchorRecentData.todayData.answer_user_count }}</span>
            </div>
            <p class="mt-6 c-red">接听率:{{ anchorRecentData.todayData.call_user_rate }}</p>
            <div class="mt-6">
              <span>时长:{{ anchorRecentData.todayData.duration }}</span>
              <span class="ml-12">次均:{{ anchorRecentData.todayData.duration_per }}</span>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="flex flex-col justify-center">
            <p>匹配数据</p>
            <div class="mt-6">
              <span>[次]呼叫:{{ anchorRecentData.todayData.mate_call_total }}</span>
              <span class="ml-12">接听:{{ anchorRecentData.todayData.mate_answer_total }}</span>
            </div>
            <p class="mt-6 c-red">接听率:{{ anchorRecentData.todayData.mate_rate }}</p>
            <div class="mt-16">
              <span>[人]呼叫:{{ anchorRecentData.todayData.mate_call_user_count }}</span>
              <span class="ml-12">接听:{{ anchorRecentData.todayData.mate_answer_user_count }}</span>
            </div>
            <p class="mt-6 c-red">接听率:{{ anchorRecentData.todayData.mate_user_rate }}</p>
            <div class="mt-6">
              <span>时长:{{ anchorRecentData.todayData.mate_duration }}</span>
              <span class="ml-12">次均:{{ anchorRecentData.todayData.mate_duration_per }}</span>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          <p>礼物数据</p>
          <div class="flex mt-6 flex-col">
            <p class="mt-6">[次数]:{{ anchorRecentData.todayData.gift_count }}</p>
            <p class="mt-6">[人数]:{{ anchorRecentData.todayData.gift_user_count }}</p>
            <p class="mt-6">[累积]:{{ anchorRecentData.todayData.gift_income }}</p>
            <p class="mt-6">[次均]:{{ anchorRecentData.todayData.gift_income_per }}</p>
            <p class="mt-6">[人均]:{{ anchorRecentData.todayData.gift_income_user_per }}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <p>文字消息数据({{anchorRecentData.matchData.today}})</p>
          <div class="flex flex-col">
            <p>[条数]:{{ anchorRecentData.todayData.msg_count }}</p>
            <p>[人数]:{{ anchorRecentData.todayData.msg_user_count }}</p>
            <p>[累积]:{{ anchorRecentData.todayData.msg_income }}</p>
            <p>[人均]:{{ anchorRecentData.todayData.msg_income_user_per }}</p>
          </div>
          <p class="mt-6">私密图片数据</p>
          <div class="flex flex-col">
            <p>[条数]:{{ anchorRecentData.todayData.secret_count }}</p>
            <p>[人数]:{{ anchorRecentData.todayData.secret_user_count }}</p>
            <p>[累积]:{{ anchorRecentData.todayData.secret_income }}</p>
            <p>[人均]:{{ anchorRecentData.todayData.secret_income_user_per }}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <p>亲密关系好友({{anchorRecentData.intimateData.today}})</p>
          <div class="flex mt-6 flex-col">
            <p>[总人数]:{{ anchorRecentData.todayData.level_count_all }}</p>
            <p>[1级]:{{ anchorRecentData.todayData.level_count_one }}</p>
            <p>[2级]:{{ anchorRecentData.todayData.level_count_two }}</p>
            <p>[3级]:{{ anchorRecentData.todayData.level_count_three }}</p>
            <p>[4级]:{{ anchorRecentData.todayData.level_count_four }}</p>
            <p>[5级]:{{ anchorRecentData.todayData.level_count_five }}</p>
            <p>[6级]:{{ anchorRecentData.todayData.level_count_six }}</p>
            <p>[7级]:{{ anchorRecentData.todayData.level_count_seven }}</p>
            <p>[8级]:{{ anchorRecentData.todayData.level_count_eight }}</p>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="4">
          <div class="flex justify-start">
            <h1 class="title-2">昨日</h1>
            <div class="flex flex-col ml-12">
              <p>活跃数据</p>
              <p>在线时长：{{ formateSeconds(anchorRecentData.yesterdayData.online_duration) }}</p>
              <p>收入：{{ anchorRecentData.yesterdayData.income }}</p>
              <p>推送：{{ anchorRecentData.matchData.yesterday }}</p>
              <p>亲密值：{{ anchorRecentData.intiData.yesterday }}</p>
              <p>收益占比：{{ anchorRecentData.incomeRate.yesterday }}</p>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="flex flex-col justify-center">
            <p>直呼数据</p>
            <div class="mt-6">
              <span>[次]呼叫:{{ anchorRecentData.yesterdayData.call_total }}</span>
              <span class="ml-12">接听: {{ anchorRecentData.yesterdayData.answer_total }}</span>
            </div>
            <p class="mt-6 c-red">接听率: {{ anchorRecentData.yesterdayData.call_rate }}</p>
            <div class="mt-16">
              <span>[人]呼叫:{{ anchorRecentData.yesterdayData.call_user_count }}</span>
              <span class="ml-12">接听:{{ anchorRecentData.yesterdayData.answer_user_count }}</span>
            </div>
            <p class="mt-6 c-red">接听率:{{ anchorRecentData.yesterdayData.call_user_rate }}</p>
            <div class="mt-6">
              <span>时长:{{ anchorRecentData.yesterdayData.duration }}</span>
              <span class="ml-12">次均:{{ anchorRecentData.yesterdayData.duration_per }}</span>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="flex flex-col justify-center">
            <p>匹配数据</p>
            <div class="mt-6">
              <span>[次]呼叫:{{ anchorRecentData.yesterdayData.mate_call_total }}</span>
              <span class="ml-12">接听:{{ anchorRecentData.yesterdayData.mate_answer_total }}</span>
            </div>
            <p class="mt-6 c-red">接听率:{{ anchorRecentData.yesterdayData.mate_rate }}</p>
            <div class="mt-16">
              <span>[人]呼叫:{{ anchorRecentData.yesterdayData.mate_call_user_count }}</span>
              <span class="ml-12">接听:{{ anchorRecentData.yesterdayData.mate_answer_user_count }}</span>
            </div>
            <p class="mt-6 c-red">接听率:{{ anchorRecentData.yesterdayData.mate_user_rate }}</p>
            <div class="mt-6">
              <span>时长:{{ anchorRecentData.yesterdayData.mate_duration }}</span>
              <span class="ml-12">次均:{{ anchorRecentData.yesterdayData.mate_duration_per }}</span>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          <p>礼物数据</p>
          <div class="flex mt-6 flex-col">
            <p class="mt-6">[次数]:{{ anchorRecentData.yesterdayData.gift_count }}</p>
            <p class="mt-6">[人数]:{{ anchorRecentData.yesterdayData.gift_user_count }}</p>
            <p class="mt-6">[累积]:{{ anchorRecentData.yesterdayData.gift_income }}</p>
            <p class="mt-6">[次均]:{{ anchorRecentData.yesterdayData.gift_income_per }}</p>
            <p class="mt-6">[人均]:{{ anchorRecentData.yesterdayData.gift_income_user_per }}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <p>文字消息数据({{anchorRecentData.matchData.yesterday}})</p>
          <div class="flex flex-col">
            <p>[条数]:{{ anchorRecentData.yesterdayData.msg_count }}</p>
            <p>[人数]:{{ anchorRecentData.yesterdayData.msg_user_count }}</p>
            <p>[累积]:{{ anchorRecentData.yesterdayData.msg_income }}</p>
            <p>[人均]:{{ anchorRecentData.yesterdayData.msg_income_user_per }}</p>
          </div>
          <p class="mt-6">私密图片数据</p>
          <div class="flex flex-col">
            <p>[条数]:{{ anchorRecentData.yesterdayData.secret_count }}</p>
            <p>[人数]:{{ anchorRecentData.yesterdayData.secret_user_count }}</p>
            <p>[累积]:{{ anchorRecentData.yesterdayData.secret_income }}</p>
            <p>[人均]:{{ anchorRecentData.yesterdayData.secret_income_user_per }}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <p>亲密关系好友({{anchorRecentData.intimateData.yesterday}})</p>
          <div class="flex mt-6 flex-col">
            <p>[总人数]:{{ anchorRecentData.yesterdayData.level_count_all }}</p>
            <p>[1级]:{{ anchorRecentData.yesterdayData.level_count_one }}</p>
            <p>[2级]:{{ anchorRecentData.yesterdayData.level_count_two }}</p>
            <p>[3级]:{{ anchorRecentData.yesterdayData.level_count_three }}</p>
            <p>[4级]:{{ anchorRecentData.yesterdayData.level_count_four }}</p>
            <p>[5级]:{{ anchorRecentData.yesterdayData.level_count_five }}</p>
            <p>[6级]:{{ anchorRecentData.yesterdayData.level_count_six }}</p>
            <p>[7级]:{{ anchorRecentData.yesterdayData.level_count_seven }}</p>
            <p>[8级]:{{ anchorRecentData.yesterdayData.level_count_eight }}</p>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="4">
          <div class="flex justify-start">
            <h1 class="title-2">近7天</h1>
            <div class="flex flex-col ml-12">
              <p>活跃数据</p>
              <p>在线时长：{{ formateSeconds(anchorRecentData.recent7DayData.online_duration) }}</p>
              <p>收入：{{ anchorRecentData.recent7DayData.income }}</p>
              <p>推送：{{ anchorRecentData.matchData.recent7Day }}</p>
              <p>亲密值：{{ anchorRecentData.intiData.recent7Day }}</p>
              <p>收益占比：{{ anchorRecentData.incomeRate.recent7Day }}</p>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="flex flex-col justify-center">
            <p>直呼数据</p>
            <div class="mt-6">
              <span>[次]呼叫:{{ anchorRecentData.recent7DayData.call_total }}</span>
              <span class="ml-12">接听: {{ anchorRecentData.recent7DayData.answer_total }}</span>
            </div>
            <p class="mt-6 c-red">接听率: {{ anchorRecentData.recent7DayData.call_rate }}</p>
            <div class="mt-16">
              <span>[人]呼叫:{{ anchorRecentData.recent7DayData.call_user_count }}</span>
              <span class="ml-12">接听:{{ anchorRecentData.recent7DayData.answer_user_count }}</span>
            </div>
            <p class="mt-6 c-red">接听率:{{ anchorRecentData.recent7DayData.call_user_rate }}</p>
            <div class="mt-6">
              <span>时长:{{ anchorRecentData.recent7DayData.duration }}</span>
              <span class="ml-12">次均:{{ anchorRecentData.recent7DayData.duration_per }}</span>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="flex flex-col justify-center">
            <p>匹配数据</p>
            <div class="mt-6">
              <span>[次]呼叫:{{ anchorRecentData.recent7DayData.mate_call_total }}</span>
              <span class="ml-12">接听:{{ anchorRecentData.recent7DayData.mate_answer_total }}</span>
            </div>
            <p class="mt-6 c-red">接听率:{{ anchorRecentData.recent7DayData.mate_rate }}</p>
            <div class="mt-16">
              <span>[人]呼叫:{{ anchorRecentData.recent7DayData.mate_call_user_count }}</span>
              <span class="ml-12">接听:{{ anchorRecentData.recent7DayData.mate_answer_user_count }}</span>
            </div>
            <p class="mt-6 c-red">接听率:{{ anchorRecentData.recent7DayData.mate_user_rate }}</p>
            <div class="mt-6">
              <span>时长:{{ anchorRecentData.recent7DayData.mate_duration }}</span>
              <span class="ml-12">次均:{{ anchorRecentData.recent7DayData.mate_duration_per }}</span>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          <p>礼物数据</p>
          <div class="flex mt-6 flex-col">
            <p class="mt-6">[次数]:{{ anchorRecentData.recent7DayData.gift_count }}</p>
            <p class="mt-6">[人数]:{{ anchorRecentData.recent7DayData.gift_user_count }}</p>
            <p class="mt-6">[累积]:{{ anchorRecentData.recent7DayData.gift_income }}</p>
            <p class="mt-6">[次均]:{{ anchorRecentData.recent7DayData.gift_income_per }}</p>
            <p class="mt-6">[人均]:{{ anchorRecentData.recent7DayData.gift_income_user_per }}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <p>文字消息数据({{anchorRecentData.matchData.recent7Day}})</p>
          <div class="flex flex-col">
            <p>[条数]:{{ anchorRecentData.recent7DayData.msg_count }}</p>
            <p>[人数]:{{ anchorRecentData.recent7DayData.msg_user_count }}</p>
            <p>[累积]:{{ anchorRecentData.recent7DayData.msg_income }}</p>
            <p>[人均]:{{ anchorRecentData.recent7DayData.msg_income_user_per }}</p>
          </div>
          <p class="mt-6">私密图片数据</p>
          <div class="flex flex-col">
            <p>[条数]:{{ anchorRecentData.recent7DayData.secret_count }}</p>
            <p>[人数]:{{ anchorRecentData.recent7DayData.secret_user_count }}</p>
            <p>[累积]:{{ anchorRecentData.recent7DayData.secret_income }}</p>
            <p>[人均]:{{ anchorRecentData.recent7DayData.secret_income_user_per }}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <p>亲密关系好友({{anchorRecentData.intimateData.recent7Day}})</p>
          <div class="flex mt-6 flex-col">
            <p>[总人数]:{{ anchorRecentData.recent7DayData.level_count_all }}</p>
            <p>[1级]:{{ anchorRecentData.recent7DayData.level_count_one }}</p>
            <p>[2级]:{{ anchorRecentData.recent7DayData.level_count_two }}</p>
            <p>[3级]:{{ anchorRecentData.recent7DayData.level_count_three }}</p>
            <p>[4级]:{{ anchorRecentData.recent7DayData.level_count_four }}</p>
            <p>[5级]:{{ anchorRecentData.recent7DayData.level_count_five }}</p>
            <p>[6级]:{{ anchorRecentData.recent7DayData.level_count_six }}</p>
            <p>[7级]:{{ anchorRecentData.recent7DayData.level_count_seven }}</p>
            <p>[8级]:{{ anchorRecentData.recent7DayData.level_count_eight }}</p>
          </div>
        </el-col>
      </el-row>
      <el-row :gutter="10">
        <el-col :span="4">
          <div class="flex justify-start">
            <h1 class="title-2">近30天</h1>
            <div class="flex flex-col ml-12">
              <p>活跃数据</p>
              <p>在线时长：{{ formateSeconds(anchorRecentData.recent30DayData.online_duration) }}</p>
              <p>收入：{{ anchorRecentData.recent30DayData.income }}</p>
              <p>推送：{{ anchorRecentData.matchData.recent30Day }}</p>
              <p>亲密值：{{ anchorRecentData.intiData.recent30Day }}</p>
              <p>收益占比：{{ anchorRecentData.incomeRate.recent30Day }}</p>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="flex flex-col justify-center">
            <p>直呼数据</p>
            <div class="mt-6">
              <span>[次]呼叫:{{ anchorRecentData.recent30DayData.call_total }}</span>
              <span class="ml-12">接听: {{ anchorRecentData.recent30DayData.answer_total }}</span>
            </div>
            <p class="mt-6 c-red">接听率: {{ anchorRecentData.recent30DayData.call_rate }}</p>
            <div class="mt-16">
              <span>[人]呼叫:{{ anchorRecentData.recent30DayData.call_user_count }}</span>
              <span class="ml-12">接听:{{ anchorRecentData.recent30DayData.answer_user_count }}</span>
            </div>
            <p class="mt-6 c-red">接听率:{{ anchorRecentData.recent30DayData.call_user_rate }}</p>
            <div class="mt-6">
              <span>时长:{{ anchorRecentData.recent30DayData.duration }}</span>
              <span class="ml-12">次均:{{ anchorRecentData.recent30DayData.duration_per }}</span>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          <div class="flex flex-col justify-center">
            <p>匹配数据</p>
            <div class="mt-6">
              <span>[次]呼叫:{{ anchorRecentData.recent30DayData.mate_call_total }}</span>
              <span class="ml-12">接听:{{ anchorRecentData.recent30DayData.mate_answer_total }}</span>
            </div>
            <p class="mt-6 c-red">接听率:{{ anchorRecentData.recent30DayData.mate_rate }}</p>
            <div class="mt-16">
              <span>[人]呼叫:{{ anchorRecentData.recent30DayData.mate_call_user_count }}</span>
              <span class="ml-12">接听:{{ anchorRecentData.recent30DayData.mate_answer_user_count }}</span>
            </div>
            <p class="mt-6 c-red">接听率:{{ anchorRecentData.recent30DayData.mate_user_rate }}</p>
            <div class="mt-6">
              <span>时长:{{ anchorRecentData.recent30DayData.mate_duration }}</span>
              <span class="ml-12">次均:{{ anchorRecentData.recent30DayData.mate_duration_per }}</span>
            </div>
          </div>
        </el-col>
        <el-col :span="4">
          <p>礼物数据</p>
          <div class="flex mt-6 flex-col">
            <p class="mt-6">[次数]:{{ anchorRecentData.recent30DayData.gift_count }}</p>
            <p class="mt-6">[人数]:{{ anchorRecentData.recent30DayData.gift_user_count }}</p>
            <p class="mt-6">[累积]:{{ anchorRecentData.recent30DayData.gift_income }}</p>
            <p class="mt-6">[次均]:{{ anchorRecentData.recent30DayData.gift_income_per }}</p>
            <p class="mt-6">[人均]:{{ anchorRecentData.recent30DayData.gift_income_user_per }}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <p>文字消息数据({{anchorRecentData.matchData.recent30Day}})</p>
          <div class="flex flex-col">
            <p>[条数]:{{ anchorRecentData.recent30DayData.msg_count }}</p>
            <p>[人数]:{{ anchorRecentData.recent30DayData.msg_user_count }}</p>
            <p>[累积]:{{ anchorRecentData.recent30DayData.msg_income }}</p>
            <p>[人均]:{{ anchorRecentData.recent30DayData.msg_income_user_per }}</p>
          </div>
          <p class="mt-6">私密图片数据</p>
          <div class="flex flex-col">
            <p>[条数]:{{ anchorRecentData.recent30DayData.secret_count }}</p>
            <p>[人数]:{{ anchorRecentData.recent30DayData.secret_user_count }}</p>
            <p>[累积]:{{ anchorRecentData.recent30DayData.secret_income }}</p>
            <p>[人均]:{{ anchorRecentData.recent30DayData.secret_income_user_per }}</p>
          </div>
        </el-col>
        <el-col :span="4">
          <p>亲密关系好友({{anchorRecentData.intimateData.recent30Day}})</p>
          <div class="flex mt-6 flex-col">
            <p>[总人数]:{{ anchorRecentData.recent30DayData.level_count_all }}</p>
            <p>[1级]:{{ anchorRecentData.recent30DayData.level_count_one }}</p>
            <p>[2级]:{{ anchorRecentData.recent30DayData.level_count_two }}</p>
            <p>[3级]:{{ anchorRecentData.recent30DayData.level_count_three }}</p>
            <p>[4级]:{{ anchorRecentData.recent30DayData.level_count_four }}</p>
            <p>[5级]:{{ anchorRecentData.recent30DayData.level_count_five }}</p>
            <p>[6级]:{{ anchorRecentData.recent30DayData.level_count_six }}</p>
            <p>[7级]:{{ anchorRecentData.recent30DayData.level_count_seven }}</p>
            <p>[8级]:{{ anchorRecentData.recent30DayData.level_count_eight }}</p>
          </div>
        </el-col>
      </el-row>
    </el-dialog>
  </div>
</template>

<script>
import {findAnchorRecentData} from "@/api/anchorViolationStatis";
import infoList from "@/mixins/infoList";

export default {
  mixins: [infoList],
  props: {
    dst_id: {
      type: Number,
      default: 0
    },
  },
  data() {
    return {
      dialogVisible: false,
      anchorRecentData: {
        income100: 0,
        intimate100: 0,
        lastMonthLevel: 0,
        nowMonthLevel: 0,
        incomeRate: {
          today: 0,
          yesterday: 0,
          recent7Day: 0,
          recent30Day: 0,
        },
        recent7DayData: {},
        recent30DayData: {},
        todayData: {},
        userLevels: {},
        wallet: {},
        yesterdayData: {},
        intimateData: {
          today: 0,
          yesterday: 0,
          recent7Day: 0,
          recent30Day: 0,
        },
        intiData: {
          today: 0,
          yesterday: 0,
          recent7Day: 0,
          recent30Day: 0,
        },
        matchData: {
          today: 0,
          yesterday: 0,
          recent7Day: 0,
          recent30Day: 0,
        },
      },
      anchorId: '',
    }
  },
  watch: {
    dst_id: {
      handler(val) {
        if (val) {
          this.showAnchorDataDialog(val)
        }
      },
      immediate: true
    }
  },
  methods: {
    showAnchorDataDialog(dst_id) {
      findAnchorRecentData({dst_id: dst_id}).then(res => {
        if (res.code === 0) {
          this.anchorId = dst_id
          this.anchorRecentData = res.data
          this.anchorRecentData.incomeRate = {
            today: this.percent2(this.div(this.anchorRecentData.todayData.income, 1000), this.div(this.anchorRecentData.intiData.today, 100)),
            yesterday: this.percent2(this.div(this.anchorRecentData.yesterdayData.income, 1000), this.div(this.anchorRecentData.intiData.yesterday, 100)),
            recent7Day: this.percent2(this.div(this.anchorRecentData.recent7DayData.income, 1000), this.div(this.anchorRecentData.intiData.recent7Day, 100)),
            recent30Day: this.percent2(this.div(this.anchorRecentData.recent30DayData.income, 1000), this.div(this.anchorRecentData.intiData.recent30Day, 100)),
          }
          this.anchorRecentData.todayData.call_rate = this.percent2(this.anchorRecentData.todayData.answer_total, this.anchorRecentData.todayData.call_total)
          this.anchorRecentData.todayData.call_user_rate = this.percent2(this.anchorRecentData.todayData.answer_user_count, this.anchorRecentData.todayData.call_user_count)
          this.anchorRecentData.todayData.duration_per = this.div2(this.anchorRecentData.todayData.duration, this.anchorRecentData.todayData.answer_total)
          this.anchorRecentData.todayData.mate_rate = this.percent2(this.anchorRecentData.todayData.mate_answer_total, this.anchorRecentData.todayData.mate_call_total)
          this.anchorRecentData.todayData.mate_user_rate = this.percent2(this.anchorRecentData.todayData.mate_answer_user_count, this.anchorRecentData.todayData.mate_call_user_count)
          this.anchorRecentData.todayData.mate_duration_per = this.div2(this.anchorRecentData.todayData.mate_duration, this.anchorRecentData.todayData.mate_answer_total)
          this.anchorRecentData.todayData.gift_income_per = this.div2(this.anchorRecentData.todayData.gift_income, this.anchorRecentData.todayData.gift_count)
          this.anchorRecentData.todayData.gift_income_user_per = this.div2(this.anchorRecentData.todayData.gift_income, this.anchorRecentData.todayData.gift_user_count)
          this.anchorRecentData.todayData.msg_income_user_per = this.div2(this.anchorRecentData.todayData.msg_income, this.anchorRecentData.todayData.msg_user_count)
          this.anchorRecentData.todayData.secret_income_user_per = this.div2(this.anchorRecentData.todayData.secret_income, this.anchorRecentData.todayData.secret_user_count)
          this.anchorRecentData.todayData.level_count_all = this.anchorRecentData.todayData.level_count_one + this.anchorRecentData.todayData.level_count_two + this.anchorRecentData.todayData.level_count_three + this.anchorRecentData.todayData.level_count_four + this.anchorRecentData.todayData.level_count_five + this.anchorRecentData.todayData.level_count_six + this.anchorRecentData.todayData.level_count_seven + this.anchorRecentData.todayData.level_count_eight

          this.anchorRecentData.yesterdayData.call_rate = this.percent2(this.anchorRecentData.yesterdayData.answer_total, this.anchorRecentData.yesterdayData.call_total)
          this.anchorRecentData.yesterdayData.call_user_rate = this.percent2(this.anchorRecentData.yesterdayData.answer_user_count, this.anchorRecentData.yesterdayData.call_user_count)
          this.anchorRecentData.yesterdayData.duration_per = this.div2(this.anchorRecentData.yesterdayData.duration, this.anchorRecentData.yesterdayData.answer_total)
          this.anchorRecentData.yesterdayData.mate_rate = this.percent2(this.anchorRecentData.yesterdayData.mate_answer_total, this.anchorRecentData.yesterdayData.mate_call_total)
          this.anchorRecentData.yesterdayData.mate_user_rate = this.percent2(this.anchorRecentData.yesterdayData.mate_answer_user_count, this.anchorRecentData.yesterdayData.mate_call_user_count)
          this.anchorRecentData.yesterdayData.mate_duration_per = this.div2(this.anchorRecentData.yesterdayData.mate_duration, this.anchorRecentData.yesterdayData.mate_answer_total)
          this.anchorRecentData.yesterdayData.gift_income_per = this.div2(this.anchorRecentData.yesterdayData.gift_income, this.anchorRecentData.yesterdayData.gift_count)
          this.anchorRecentData.yesterdayData.gift_income_user_per = this.div2(this.anchorRecentData.yesterdayData.gift_income, this.anchorRecentData.yesterdayData.gift_user_count)
          this.anchorRecentData.yesterdayData.msg_income_user_per = this.div2(this.anchorRecentData.yesterdayData.msg_income, this.anchorRecentData.yesterdayData.msg_user_count)
          this.anchorRecentData.yesterdayData.secret_income_user_per = this.div2(this.anchorRecentData.yesterdayData.secret_income, this.anchorRecentData.yesterdayData.secret_user_count)
          this.anchorRecentData.yesterdayData.level_count_all = this.anchorRecentData.yesterdayData.level_count_one + this.anchorRecentData.yesterdayData.level_count_two + this.anchorRecentData.yesterdayData.level_count_three + this.anchorRecentData.yesterdayData.level_count_four + this.anchorRecentData.yesterdayData.level_count_five + this.anchorRecentData.yesterdayData.level_count_six + this.anchorRecentData.yesterdayData.level_count_seven + this.anchorRecentData.yesterdayData.level_count_eight

          this.anchorRecentData.recent7DayData.call_rate = this.percent2(this.anchorRecentData.recent7DayData.answer_total, this.anchorRecentData.recent7DayData.call_total)
          this.anchorRecentData.recent7DayData.call_user_rate = this.percent2(this.anchorRecentData.recent7DayData.answer_user_count, this.anchorRecentData.recent7DayData.call_user_count)
          this.anchorRecentData.recent7DayData.duration_per = this.div2(this.anchorRecentData.recent7DayData.duration, this.anchorRecentData.recent7DayData.answer_total)
          this.anchorRecentData.recent7DayData.mate_rate = this.percent2(this.anchorRecentData.recent7DayData.mate_answer_total, this.anchorRecentData.recent7DayData.mate_call_total)
          this.anchorRecentData.recent7DayData.mate_user_rate = this.percent2(this.anchorRecentData.recent7DayData.mate_answer_user_count, this.anchorRecentData.recent7DayData.mate_call_user_count)
          this.anchorRecentData.recent7DayData.mate_duration_per = this.div2(this.anchorRecentData.recent7DayData.mate_duration, this.anchorRecentData.recent7DayData.mate_answer_total)
          this.anchorRecentData.recent7DayData.gift_income_per = this.div2(this.anchorRecentData.recent7DayData.gift_income, this.anchorRecentData.recent7DayData.gift_count)
          this.anchorRecentData.recent7DayData.gift_income_user_per = this.div2(this.anchorRecentData.recent7DayData.gift_income, this.anchorRecentData.recent7DayData.gift_user_count)
          this.anchorRecentData.recent7DayData.msg_income_user_per = this.div2(this.anchorRecentData.recent7DayData.msg_income, this.anchorRecentData.recent7DayData.msg_user_count)
          this.anchorRecentData.recent7DayData.secret_income_user_per = this.div2(this.anchorRecentData.recent7DayData.secret_income, this.anchorRecentData.recent7DayData.secret_user_count)
          this.anchorRecentData.recent7DayData.level_count_all = this.anchorRecentData.recent7DayData.level_count_one + this.anchorRecentData.recent7DayData.level_count_two + this.anchorRecentData.recent7DayData.level_count_three + this.anchorRecentData.recent7DayData.level_count_four + this.anchorRecentData.recent7DayData.level_count_five + this.anchorRecentData.recent7DayData.level_count_six + this.anchorRecentData.recent7DayData.level_count_seven + this.anchorRecentData.recent7DayData.level_count_eight

          this.anchorRecentData.recent30DayData.call_rate = this.percent2(this.anchorRecentData.recent30DayData.answer_total, this.anchorRecentData.recent30DayData.call_total)
          this.anchorRecentData.recent30DayData.call_user_rate = this.percent2(this.anchorRecentData.recent30DayData.answer_user_count, this.anchorRecentData.recent30DayData.call_user_count)
          this.anchorRecentData.recent30DayData.duration_per = this.div2(this.anchorRecentData.recent30DayData.duration, this.anchorRecentData.recent30DayData.answer_total)
          this.anchorRecentData.recent30DayData.mate_rate = this.percent2(this.anchorRecentData.recent30DayData.mate_answer_total, this.anchorRecentData.recent30DayData.mate_call_total)
          this.anchorRecentData.recent30DayData.mate_user_rate = this.percent2(this.anchorRecentData.recent30DayData.mate_answer_user_count, this.anchorRecentData.recent30DayData.mate_call_user_count)
          this.anchorRecentData.recent30DayData.mate_duration_per = this.div2(this.anchorRecentData.recent30DayData.mate_duration, this.anchorRecentData.recent30DayData.mate_answer_total)
          this.anchorRecentData.recent30DayData.gift_income_per = this.div2(this.anchorRecentData.recent30DayData.gift_income, this.anchorRecentData.recent30DayData.gift_count)
          this.anchorRecentData.recent30DayData.gift_income_user_per = this.div2(this.anchorRecentData.recent30DayData.gift_income, this.anchorRecentData.recent30DayData.gift_user_count)
          this.anchorRecentData.recent30DayData.msg_income_user_per = this.div2(this.anchorRecentData.recent30DayData.msg_income, this.anchorRecentData.recent30DayData.msg_user_count)
          this.anchorRecentData.recent30DayData.secret_income_user_per = this.div2(this.anchorRecentData.recent30DayData.secret_income, this.anchorRecentData.recent30DayData.secret_user_count)
          this.anchorRecentData.recent30DayData.level_count_all = this.anchorRecentData.recent30DayData.level_count_one + this.anchorRecentData.recent30DayData.level_count_two + this.anchorRecentData.recent30DayData.level_count_three + this.anchorRecentData.recent30DayData.level_count_four + this.anchorRecentData.recent30DayData.level_count_five + this.anchorRecentData.recent30DayData.level_count_six + this.anchorRecentData.recent30DayData.level_count_seven + this.anchorRecentData.recent30DayData.level_count_eight
          console.log(this.anchorRecentData)
        }
      })
    },
  }
}
</script>

<style lang="scss">

</style>
