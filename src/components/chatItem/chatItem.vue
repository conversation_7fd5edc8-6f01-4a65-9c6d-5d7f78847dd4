<template>
  <div>
    <div v-if="parseInt(data.sender_id) !== parseInt(senderUser.id)" class="displayFlex chat-customer">
      <img :src="receiverUser.avatar" class="chat-avatar" alt="">
      <div class="chat-content chat-customer-content">
        <!-- 目前：1是文字消息  2 是图片  3是视频   4是音频  点击图片和视频放大的功能可以根据你的框架加一下就行  -->
        <p v-if="parseInt(data.message_type) === 1">{{ data.message_content }}</p>
        <p v-else-if="parseInt(data.message_type) === 2">
          <el-image
            class="chat-img"
            :src="data.media_url"
            :lazy="true"
            :preview-src-list="[data.media_url]"
          />
        </p>
        <p v-else-if="parseInt(data.message_type) === 5">
          <video :src="data.media_url" class="chat-img" controls="controls" />
        </p>
        <p v-else-if="parseInt(data.message_type) === 3"><audio :src="data.media_url" /></p>
      </div>
    </div>
    <div v-else class="displayFlex chat-self">
      <div class="chat-content chat-self-content">
        <p v-if="parseInt(data.message_type) === 1">{{ data.message_content }}</p>
        <p v-else-if="parseInt(data.message_type) === 2">
          <el-image
            class="chat-img"
            :src="data.media_url"
            :lazy="true"
            :preview-src-list="[data.media_url]"
          />
        </p>
        <p v-else-if="parseInt(data.message_type) === 5">
          <video :src="data.media_url" class="chat-img" controls="controls" />
        </p>
        <p v-else-if="parseInt(data.message_type) === 3"><audio :src="data.media_url" controls="controls" /></p>
      </div>
      <img :src="senderUser.avatar" class="chat-avatar" alt="">
    </div>
  </div>
</template>

<script>
export default {
  name: 'ChatItem',
  filters: {},
  props: ['data', 'senderUser', 'receiverUser'],
  data() {
    return {
      videoVisible: false,
      videoUrl: '',
    }
  },
  mounted() {

  },
  created() {

  },
  methods: {
  },
}
</script>

<style lang="scss" scoped>
.displayFlex {
  display: flex;
  align-items: center;
}
.chat-customer {
  justify-content: flex-start;
}
.chat-self {
  justify-content: flex-end;
}
.chat-avatar {
  width: 50px;
  height: 50px;
  border-radius: 50%;
}
.chat-content {
  max-width: 80%;
  border-radius: 6px;
  padding: 10px;
}
.chat-customer-content {
  margin-left: 12px;
  background: #fff;
}
.chat-self-content {
  margin-right: 12px;
  color: #333;
  background: #6babe5;
}
.chat-img {
  width: 150px;
  height: 200px;
}
</style>
<style lang="scss">
</style>
