.clearflex {
  *zoom: 1;
}

.clearflex:after {
  content: '';
  display: block;
  height: 0;
  visibility: hidden;
  clear: both;
}

.fl-left {
  float: left;
}

.fl-right {
  float: right;
}

.mg {
  margin: 10px !important;
}

.left-mg-xs {
  margin-left: 6px !important;
}

.left-mg-sm {
  margin-left: 10px !important;
}

.left-mg-md {
  margin-left: 14px !important;
}

.top-mg-lg {
  margin-top: 20px !important;
}

.tb-mg-lg {
  margin: 20px 0 !important;
}

.bottom-mg-lg {
  margin-bottom: 20px !important;
}

.left-mg-lg {
  margin-left: 18px !important;
}

.title-1 {
  text-align: center;
  font-size: 32px;
}

.title-2 {
  text-align: center;
  font-size: 22px;
}

.title-3 {
  text-align: center;
}
.flex-box{
  display: flex;
}
.a-c {
  align-items: center;
}
.f-c {
  flex-direction: column;
}
.d-f {
  display: flex;
  flex-direction: column;
}

.d-f-r {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}

.d-f-r-c {
  display: flex;
  flex-direction: row;
  justify-content: center;
}

.chat-content{
  display: flex;
}

.c-box-item {
  display: flex;
  margin: 0 6px;
}

.c-box-left {
  justify-content: flex-start;
  .chat-content{
    align-items: flex-start;
  }
  .chat-box {
    border-top-left-radius: 0;
    background-color: #ffffff !important;
  }
  .send-time {
    padding: 8px 0 8px 13px;
    color: #999999;
  }
}

.c-box-right {
  justify-content: flex-end;
  .chat-content{
    align-items: flex-end;
  }
  .chat-box {
    border-top-right-radius: 0;
    background-color: #FF7877 !important;
    color: #FFFFFF !important;
  }
  .send-time {
    padding: 8px 13px 8px 0;
    color: #999999;
  }
}

.chat-box {
  background-color: #fff;
  padding: 13px;
  border-radius: 8px;
  color: #333333;
}

.chat-msg {
  font-size: 16px;
  line-height: 18px;
}
