.c-red {
  color: red;
}
.t-center {
  text-align: center;
}
.fs-18 {
  font-size: 18px;
}

.mt-6 {
  margin-top: 6px;
}
.mt-16 {
  margin-top: 16px;
}
.mb-6 {
  margin-bottom: 6px;
}
.ml-6 {
  margin-left: 6px;
}
.ml-12 {
  margin-left: 12px;
}
.w100 {
  width: 100%;
}
.flex {
  display: -webkit-box;
  display: -ms-flexbox;
  display: -webkit-flex;
  display: flex;
}

.flex-row {
  flex-direction: row
}

.flex-row-reverse {
  flex-direction: row-reverse
}

.flex-col {
  flex-direction: column
}

.flex-col-reverse {
  flex-direction: column-reverse
}

.flex-wrap {
  flex-wrap: wrap
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse
}

.flex-nowrap {
  flex-wrap: nowrap
}

.justify-start {
  justify-content: flex-start
}

.justify-end {
  justify-content: flex-end
}

.justify-center {
  justify-content: center
}

.justify-between {
  justify-content: space-between
}

.justify-around {
  justify-content: space-around
}

.justify-evenly {
  justify-content: space-evenly
}

.justify-items-start {
  justify-items: start
}

.justify-items-end {
  justify-items: end
}

.justify-items-center {
  justify-items: center
}

.justify-items-stretch {
  justify-items: stretch
}

.justify-self-auto {
  justify-self: auto
}

.justify-self-start {
  justify-self: start
}

.justify-self-end {
  justify-self: end
}

.justify-self-center {
  justify-self: center
}

.justify-self-stretch {
  justify-self: stretch
}

.content-center {
  justify-content: center
}

.content-start {
  justify-content: flex-start
}

.content-end {
  justify-content: flex-end
}

.content-between {
  justify-content: space-between
}

.content-around {
  justify-content: space-around
}

.content-evenly {
  justify-content: space-evenly
}

.items-start {
  align-items: flex-start
}

.items-end {
  align-items: flex-end
}

.items-center {
  align-items: center
}

.items-baseline {
  align-items: baseline
}

.items-stretch {
  align-items: stretch
}
