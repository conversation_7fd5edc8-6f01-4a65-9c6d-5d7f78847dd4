import { login, getUserInfo, setUserInfo, ssoLogin } from '@/api/user'
import { jsonInBlacklist } from '@/api/jwt'
import router from '@/router/index'
import { store } from '@/store'
import { ElMessage } from 'element-plus'

export const user = {
  namespaced: true,
  state: {
    userInfo: {
      uuid: '',
      nickName: '',
      headerImg: '',
      authority: {},
      sideMode: 'dark',
      activeColor: '#4D70FF',
      baseColor: '#fff'
    },
    token: '',
    appId: 0,
    appList: [],
    tradeTypeList: [],
    tradeTypeDict: {},
    uniosList: [],
    uniosItemDict: {},
    uniosDict: {},
    payWaysList: [],
    payItemDict: {},
    payWaysDict: {},
    appDict: {},
    appIds: [],
  },
  mutations: {
    setUserInfo(state, userInfo) {
      // 这里的 `state` 对象是模块的局部状态
      state.userInfo = userInfo
    },
    setToken(state, token) {
      // 这里的 `state` 对象是模块的局部状态
      state.token = token
    },
    setAppId(state, appId) {
      // 这里的 `state` 对象是模块的局部状态
      state.appId = appId
    },
    setAppIds(state, appIds) {
      // 这里的 `state` 对象是模块的局部状态
      state.appIds = appIds
    },
    setAppList(state, appList) {
      // 这里的 `state` 对象是模块的局部状态
      state.appList = appList
    },
    setTradeTypeList(state, dataList) {
      // 这里的 `state` 对象是模块的局部状态
      state.tradeTypeList = dataList
    },
    setTradeTypeDict(state, data) {
      // 这里的 `state` 对象是模块的局部状态
      state.tradeTypeDict = data
    },
    setAppDict(state, appDict) {
      state.appDict = appDict
    },
    setUniosDict(state, uniosDict) {
      state.uniosDict = uniosDict
    },
    setUniosList(state, uniosList) {
      let uDict = {}
      uniosList.forEach(item=>{
        uDict[item.id] = item
      })
      state.uniosList = uniosList
      state.uniosItemDict = uDict
    },
    setPayWaysDict(state, payWaysDict) {
      state.payWaysDict = payWaysDict
    },
    setPayWaysList(state, payWaysList) {
      state.payWaysList = payWaysList
    },
    setPayItemDict(state, payItemDict) {
      state.payItemDict = payItemDict
    },
    NeedInit(state) {
      state.userInfo = {}
      state.token = ''
      sessionStorage.clear()
      router.push({ name: 'Init', replace: true })
    },
    LoginOut(state) {
      state.userInfo = {}
      state.token = ''
      sessionStorage.clear()
      router.push({ name: 'Login', replace: true })
      window.location.reload()
    },
    ResetUserInfo(state, userInfo = {}) {
      state.userInfo = { ...state.userInfo,
        ...userInfo
      }
    },
    ChangeSideMode: (state, val) => {
      state.userInfo.sideMode = val
    },
  },
  actions: {
    async SetAppId({ commit, dispatch, rootGetters, getters }, appId) {
      commit('setAppId', appId)
    },
    async SetAppIds({ commit, dispatch, rootGetters, getters }, appIds) {
      commit('setAppIds', appIds)
    },
    async SetAppList({ commit, dispatch, rootGetters, getters }, appList) {
      commit('setAppList', appList)
    },
    async SetTradeTypeList({ commit, dispatch, rootGetters, getters }, dataList) {
      commit('setTradeTypeList', dataList)
    },
    async SetTradeTypeDict({ commit, dispatch, rootGetters, getters }, data) {
      commit('setTradeTypeDict', data)
    },
    async SetAppDict({ commit, dispatch, rootGetters, getters }, appDict) {
      commit('setAppDict', appDict)
    },
    async SetUniosDict({ commit, dispatch, rootGetters, getters }, uniosDict) {
      commit('setUniosDict', uniosDict)
    },
    async SetUniosList({ commit, dispatch, rootGetters, getters }, uniosList) {
      commit('setUniosList', uniosList)
    },
    async SetPayWaysDict({ commit, dispatch, rootGetters, getters }, payWaysDict) {
      commit('setPayWaysDict', payWaysDict)
    },
    async SetPayWaysList({ commit, dispatch, rootGetters, getters }, payWaysList) {
      commit('setPayWaysList', payWaysList)
    },
    async SetPayItemDict({ commit, dispatch, rootGetters, getters }, payItemDict) {
      commit('setPayItemDict', payItemDict)
    },
    async GetUserInfo({ commit }) {
      const res = await getUserInfo()
      if (res.code === 0) {
        commit('setUserInfo', res.data.userInfo)
      }
      return res
    },
    async LoginIn({ commit, dispatch, rootGetters, getters }, loginInfo) {
      const res = await login(loginInfo)
      if (res.code === 0) {
        commit('setUserInfo', res.data.user)
        commit('setToken', res.data.token)
        await dispatch('router/SetAsyncRouter', {}, { root: true })
        const asyncRouters = rootGetters['router/asyncRouters']
        asyncRouters.forEach(asyncRouter => {
          router.addRoute(asyncRouter)
        })
        // const redirect = router.history.current.query.redirect
        // console.log(redirect)
        // if (redirect) {
        //     router.push({ path: redirect })
        // } else {
        router.push({ name: getters['userInfo'].authority.defaultRouter })
        // }
        return true
      }
    },
    async ssoLoginIn({ commit, dispatch, rootGetters, getters }, loginInfo) {
      const res = await ssoLogin(loginInfo)
      if (res.code === 0) {
        commit('setUserInfo', res.data.user)
        commit('setToken', res.data.token)
        await dispatch('router/SetAsyncRouter', {}, { root: true })
        const asyncRouters = rootGetters['router/asyncRouters']
        asyncRouters.forEach(asyncRouter => {
          router.addRoute(asyncRouter)
        })
        // const redirect = router.history.current.query.redirect
        // console.log(redirect)
        // if (redirect) {
        //     router.push({ path: redirect })
        // } else {
        router.push({ name: getters['userInfo'].authority.defaultRouter })
        // }
        return true
      }
    },
    async LoginOut({ commit }) {
      const res = await jsonInBlacklist()
      if (res.code === 0) {
        commit('LoginOut')
      }
    },
    async changeSideMode({ commit, state }, data) {
      const res = await setUserInfo({ sideMode: data, ID: state.userInfo.id })
      if (res.code === 0) {
        commit('ChangeSideMode', data)
        ElMessage({
          type: 'success',
          message: '设置成功'
        })
      }
    },
  },
  getters: {
    userInfo(state) {
      return state.userInfo
    },
    token(state) {
      return state.token
    },
    appId(state) {
      return state.appId
    },
    appIds(state) {
      return state.appIds
    },
    formatAppName:(state) => (id) => {
      let mAppName = state.appDict[id]
      if (mAppName === undefined) {
        return `unKnown(${id})`
      } else {
        return `${mAppName}`
      }
    },
    formatPayWayName:(state) => (id) => {
      let payItem = state.payItemDict[id]
      if (payItem === undefined) {
        return `unKnown(${id})`
      } else {
        return `${payItem.internal_name}(${id})`
      }
    },
    formatUniosName:(state) => (id) => {
      return state.uniosDict[id]
    },
    formatUniosManagerId:(state) => (id) => {
      let u = state.uniosItemDict[id]
      if (u === undefined) {
        return `unKnown(${id})`
      } else {
        return `${u.manager_id}`
      }
    },
    formatTradeType:(state) => (id) => {
      return state.tradeTypeDict[id]
    },
    appList(state) {
      return state.appList
    },
    tradeTypeList(state) {
      return state.tradeTypeList
    },
    tradeTypeDict(state) {
      return state.tradeTypeDict
    },
    appDict(state) {
      return state.appDict
    },
    uniosList(state) {
      return state.uniosList
    },
    uniosDict(state) {
      return state.uniosDict
    },
    payWaysList(state) {
      return state.payWaysList
    },
    payWaysDict(state) {
      return state.payWaysDict
    },
    payItemDict(state) {
      return state.payItemDict
    },
    mode(state) {
      return state.userInfo.sideMode
    },
    sideMode(state) {
      if (state.userInfo.sideMode === 'dark') {
        return '#191a23'
      } else if (state.userInfo.sideMode === 'light') {
        return '#fff'
      } else {
        return state.userInfo.sideMode
      }
    },
    baseColor(state) {
      if (state.userInfo.sideMode === 'dark') {
        return '#fff'
      } else if (state.userInfo.sideMode === 'light') {
        return '#191a23'
      } else {
        return state.userInfo.baseColor
      }
    },
    activeColor(state) {
      if (state.userInfo.sideMode === 'dark' || state.userInfo.sideMode === 'light') {
        return '#4D70FF'
      }
      return state.userInfo.activeColor
    }
  }
}
