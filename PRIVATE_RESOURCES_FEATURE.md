# 私密图片和私密视频功能实现

## 功能概述
在转接假视频下面新增了私密图片功能和私密视频功能，允许主播上传和管理私密资源。

## 实现的功能

### 1. API接口
在 `src/api/users.js` 中新增了三个API接口：

- `uploadSecretResource(data)` - 上传私密资源接口
- `deleteSecretResource(data)` - 删除私密资源接口  
- `getSecretResource(params)` - 查询私密资源接口

### 2. 前端界面
在 `src/components/UserInfoUpdateDialog/index.vue` 中新增了两个功能模块：

#### 私密图片功能
- 位置：转接假视频下方
- 支持拖拽排序
- 支持多图片上传
- 支持图片预览
- 支持删除功能
- 接受格式：.jpg, .jpeg, .png, .gif

#### 私密视频功能  
- 位置：私密图片下方
- 支持拖拽排序
- 支持多视频上传
- 支持视频预览
- 支持删除功能
- 接受格式：.mp4

### 3. 接口调用逻辑

#### 上传流程
1. 用户选择文件后，先调用 `/files/createFiles`（图片）或 `/files/createVideo`（视频）上传文件
2. 上传成功后，调用 `users/uploadSecretResource` 接口保存私密资源信息
3. 接口参数：
   ```javascript
   {
     "anchor_id": 4789,        // 主播ID
     "resource_type": 1,       // 资源类型：1-图片，2-视频
     "img_url": "image/xxx.jpg", // 图片地址或视频封面地址
     "video_url": "video/xxx.mp4" // 视频地址（图片时为空）
   }
   ```

#### 删除流程
1. 用户点击删除按钮
2. 调用 `users/deleteSecretResource` 接口删除资源
3. 接口参数：
   ```javascript
   {
     "anchor_id": 4789,  // 主播ID
     "id": 2            // 资源ID
   }
   ```

#### 查询流程
1. 打开用户信息弹窗时自动调用
2. 调用 `users/getSecretResource?anchor_id=4789` 接口获取私密资源列表
3. 根据 `resource_type` 字段分别显示在私密图片和私密视频区域

### 4. 数据结构
```javascript
formData: {
  secretImages: [],   // 私密图片数组
  secretVideos: []    // 私密视频数组
}
```

每个资源对象包含：
- `id`: 资源ID（服务器返回）
- `img_url`: 图片地址或视频封面地址
- `video_url`: 视频地址（仅视频资源有）
- `resource_type`: 资源类型（1-图片，2-视频）

### 5. 主要方法

#### 私密图片相关
- `handleSecretImageSuccess()` - 图片上传成功处理
- `handleSecretImageRemove()` - 图片删除处理

#### 私密视频相关  
- `handleSecretVideoSuccess()` - 视频上传成功处理
- `handleSecretVideoRemove()` - 视频删除处理
- `handleSecretVideoVisible()` - 视频预览处理

#### 初始化方法
- `initSecretResources()` - 初始化私密资源数据

## 使用说明

1. 在主播管理页面点击"修改信息"按钮
2. 在弹出的对话框中找到"私密图片"和"私密视频"区域
3. 点击"+"号上传图片或视频
4. 上传成功后可以预览和删除
5. 支持拖拽调整顺序

## 注意事项

1. 上传的文件会先通过现有的文件上传接口处理
2. 私密资源信息会单独保存到数据库
3. 删除操作会同时删除数据库记录
4. 页面关闭时会重置私密资源数据
5. 每次打开用户信息时会重新加载私密资源数据

## 技术实现

- 使用 Element Plus 的 Upload 组件
- 使用 vuedraggable 实现拖拽排序
- 使用 async/await 处理异步操作
- 集成现有的文件上传和预览功能
